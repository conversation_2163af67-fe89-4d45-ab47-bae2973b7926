# Built application files
*.apk
*.ap_
*.aab

# Files for the ART/Dalvik VM
*.dex

# Java class files
*.class

# Generated files
bin/
gen/
out/

# Gradle files
.gradle/
build/

# Local configuration file (sdk path, etc)
local.properties

# Proguard folder generated by Eclipse
proguard/

# Log Files
*.log

# Android Studio Navigation editor temp files
.navigation/

# Android Studio captures folder
captures/

# IntelliJ
*.iml
.idea/workspace.xml
.idea/tasks.xml
.idea/gradle.xml
.idea/assetWizardSettings.xml
.idea/dictionaries
.idea/libraries
.idea/caches

# Keystore files
# Uncomment the following lines if you do not want to check your keystore files in.
#*.jks
#*.keystore

# External native build folder generated in Android Studio 2.2 and later
.externalNativeBuild

# Google Services (e.g. APIs or Firebase)
google-services.json

# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# output-metadata.json
release/output-metadata.json
/.idea/other.xml
.kotlin/
.idea/
.DS_Store
.idea/compiler.xml
.idea/deploymentTargetSelector.xml
.idea/gradle.xml
.idea/kotlinc.xml
.idea/migrations.xml
.idea/misc.xml
.idea/vcs.xml
.idea/workspace.xml
.idea/navEditor.xml
.idea/
app/release/output-metadata.json
app/debug/output-metadata.json

# Python cache files
__pycache__/
*.py[cod]
*$py.class
