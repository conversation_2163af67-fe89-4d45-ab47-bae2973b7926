<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:gravity="center_vertical"
    android:id="@+id/item"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="32dp"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

    <TextView
        android:id="@+id/icon_image_view"
        android:gravity="center"
        android:textSize="18sp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <TextView
        android:textSize="10sp"
        android:layout_marginStart="10dp"
        tools:text="asdfasdfasdfa"
        android:id="@+id/text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />
</LinearLayout>