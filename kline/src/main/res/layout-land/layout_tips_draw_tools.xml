<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/tips_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:paddingStart="15dp"
            android:paddingEnd="15dp"
            android:layout_width="310dp"
            android:layout_height="wrap_content">

            <TextView
                android:layout_gravity="center_horizontal"
                android:id="@+id/top_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="水平线段"
                android:textSize="14sp" />

            <TextView
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="4dp"
                android:id="@+id/top_second_tips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="点击2个锚点"
                tools:textSize="12sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>