<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:id="@+id/item"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="MissingDefaultResource">

<!--    <com.upex.common.widget.view.BaseTextView-->
<!--        android:layout_marginTop="2dp"-->
<!--        android:layout_marginBottom="2dp"-->
<!--        android:id="@+id/color_text"-->
<!--        tools:fontFamily="@font/iconfont"-->
<!--        app:isFontText="true"-->
<!--        android:gravity="center"-->
<!--        android:textSize="15sp"-->
<!--        android:textColor="?attr/colorTitle"-->
<!--        tools:text="@string/icon_draw_trend_line"-->
<!--        android:layout_width="16dp"-->
<!--        android:layout_height="16dp" />-->

</LinearLayout>