<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="104dp"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginTop="30dp"
    android:background="@drawable/bg_shape"
    android:paddingBottom="4dp"
    android:orientation="vertical">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="2dp"
        android:paddingTop="2dp">

        <TextView
            android:id="@+id/tv_t_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/time"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView

            android:id="@+id/tv_time"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:paddingTop="4dp"
            android:textColor="@color/kline_info"
            android:textSize="9sp"
            tools:text="201511111" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_open"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/open_price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_open_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_high"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/high_price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_high_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_low"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/low_price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView

            android:id="@+id/tv_low_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/close_price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_close_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/vg_change_e"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_change_e"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/klien_text_zde"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_change_e"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/vg_change_rate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_change_rate"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/change_rate"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_change_rate"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_marginBottom="2dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"

        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_vol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/volume"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_vol"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>
</LinearLayout>