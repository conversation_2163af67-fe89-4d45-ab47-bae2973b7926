<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="104dp"
    android:layout_height="wrap_content"
    android:layout_marginLeft="10dp"
    android:layout_marginRight="10dp"
    android:layout_marginTop="30dp"
    android:background="@drawable/bg_shape"
    android:orientation="vertical"
    android:paddingBottom="4dp"
    android:paddingTop="4dp">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="2dp"
        android:paddingTop="2dp">

        <TextView
            android:id="@+id/tv_t_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/time"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />


        <TextView
            android:id="@+id/tv_time"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="right|center_vertical"
            android:paddingRight="7dp"
            android:textColor="@color/kline_info"
            android:textSize="10sp"
            tools:text="201511111" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_price"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingBottom="1dp"
        android:paddingTop="1dp">

        <TextView
            android:id="@+id/tv_t_vol"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="7dp"
            android:text="@string/volume"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />

        <TextView
            android:id="@+id/tv_vol"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="right"
            android:paddingRight="7dp"
            android:text="@string/price"
            android:textColor="@color/kline_info"
            android:textSize="9sp" />
    </LinearLayout>


    <!--<LinearLayout-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:padding="4dp"-->
    <!--android:orientation="horizontal">-->
    <!--<TextView-->
    <!--android:layout_width="60dp"-->
    <!--android:text="@string/price"-->
    <!--android:textColor="@color/kline_info"-->
    <!--android:textSize="10sp"-->
    <!--android:layout_height="wrap_content"/>-->
    <!--<TextView-->
    <!--android:id="@+id/tv_price"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_weight="1"-->
    <!--android:text="@string/price"-->
    <!--android:gravity="right"-->
    <!--android:paddingRight="7dp"-->
    <!--android:textColor="@color/kline_info"-->
    <!--android:textSize="10sp"-->
    <!--android:layout_height="wrap_content"/>-->
    <!--</LinearLayout>-->
    <!--<LinearLayout-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:padding="4dp"-->
    <!--android:orientation="horizontal">-->
    <!--<TextView-->
    <!--android:layout_width="60dp"-->
    <!--android:text="@string/change_rate"-->
    <!--android:textColor="@color/kline_info"-->
    <!--android:textSize="10sp"-->
    <!--android:layout_height="wrap_content"/>-->
    <!--<TextView-->
    <!--android:id="@+id/tv_change_rate"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_weight="1"-->
    <!--android:text="@string/price"-->
    <!--android:gravity="right"-->
    <!--android:paddingRight="7dp"-->
    <!--android:textColor="@color/kline_info"-->
    <!--android:textSize="10sp"-->
    <!--android:layout_height="wrap_content"/>-->
    <!--</LinearLayout>-->
    <!--<LinearLayout-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:padding="4dp"-->
    <!--android:orientation="horizontal">-->
    <!--<TextView-->
    <!--android:layout_width="60dp"-->
    <!--android:text="@string/volume"-->
    <!--android:textColor="@color/kline_info"-->
    <!--android:textSize="10sp"-->
    <!--android:layout_height="wrap_content"/>-->
    <!--<TextView-->
    <!--android:id="@+id/tv_vol"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_weight="1"-->
    <!--android:text="@string/price"-->
    <!--android:gravity="right"-->
    <!--android:paddingRight="7dp"-->
    <!--android:textColor="@color/kline_info"-->
    <!--android:textSize="10sp"-->
    <!--android:layout_height="wrap_content"/>-->
</LinearLayout>
