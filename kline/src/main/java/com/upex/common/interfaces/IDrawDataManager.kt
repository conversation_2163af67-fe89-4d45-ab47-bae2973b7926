package com.upex.common.interfaces

import androidx.lifecycle.MutableLiveData
import com.upex.common.drawTools.DrawToolShape
import com.upex.common.drawTools.DrawToolsType
import com.upex.common.drawTools.draw.BaseDraw

interface IDrawDataManager {

    fun getInitLiveData() : MutableLiveData<DrawToolShape>

    fun getDrawList(): LinkedHashSet<BaseDraw>

    fun loadDrawData(pairName: String, businessType: String)

    fun deleteAll(pairName: String, businessType: String, action: (()->Unit)?)

    fun deleteOne(currentDraw: BaseDraw?, action: (()->Unit)?)

    fun saveToDb(pairName: String, businessType: String, draw: BaseDraw?)

    fun createNewDraw(drawType: DrawToolsType): BaseDraw
}