package com.upex.common.drawTools.draw

import android.graphics.Canvas
import android.graphics.RectF
import com.upex.common.drawTools.isInSegment


class HorizontalStraightLine: BaseDraw() {
    override fun setTotalPointCount():Int {
        return 1
    }

    override fun onFinalDraw(canvas: Canvas, mRect: RectF) {
        canvas.drawLine(mRect.left, mPointList.first().y, mRect.right, mPointList.first().y,getPaint())
    }

    override fun isSamePre() = false

    override fun isInSelectArea(x:Float,y:Float, mRect: RectF): Boolean {
        val inSegment = isInSegment(
            x.toDouble(),
            y.toDouble(),
            mRect.left.toDouble(),
            mPointList.first().y.toDouble(),
            mRect.right.toDouble(),
            mPointList.last().y.toDouble(),
            SELECT_DISTANCE.toDouble()
        )
        if (inSegment){
            return true
        }
        return super.isInSelectArea(x,y, mRect)
    }
}