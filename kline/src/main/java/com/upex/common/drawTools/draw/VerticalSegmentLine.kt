package com.upex.common.drawTools.draw

import android.graphics.Canvas
import android.graphics.RectF
import com.upex.common.drawTools.isInSegment

class VerticalSegmentLine: BaseDraw() {
    override fun setTotalPointCount():Int {
        return 2
    }

    override fun onSelect(x: Float, y: Float, mRect: RectF): Boolean {
        return super.onSelect(x, y, mRect)
    }

    override fun onUpPoint(index: Int) {
        if (index!=0){
            mPointList.last().x = mPointList.first().x
        }
        isSelect = true
    }

    override fun onEditPoint(editIndex: Int,x: Float,y: Float) {
        mPointList.forEach {
            it.x = x
        }
    }

    override fun onFinalDraw(canvas: Canvas, mRect: RectF) {
        canvas.drawLine(mPointList.last().x, mPointList.last().y, mPointList.first().x, mPointList.first().y,getPaint())
    }

    override fun isSamePre() = true

    override fun isInSelectArea(x:Float,y:Float, mRect: RectF): Boolean {
        val contains = isInSegment(
            x.toDouble(),
            y.toDouble(),
            mPointList.first().x.toDouble(),
            mPointList.first().y.toDouble(),
            mPointList.last().x.toDouble(),
            mPointList.last().y.toDouble(),
            SELECT_DISTANCE.toDouble()
        )
        if (contains){
            return true
        }
        return super.isInSelectArea(x,y,mRect)
    }
}