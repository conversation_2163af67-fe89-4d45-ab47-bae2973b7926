package com.upex.common.drawTools.draw

import android.graphics.Canvas
import android.graphics.RectF
import com.upex.common.utils.display.DensityUtil
import kotlin.math.max
import kotlin.math.min

class Fibonacci: BaseDraw()  {

    override fun setTotalPointCount() = 2

    override fun onFinalDraw(canvas: Canvas, mRect: RectF) {
        //1.0
        //0.786
        //0.618
        //0.5
        //0.382
        //0.236
        //0.0
        val first = mPointList.first()
        val last = mPointList.last()
        canvas.drawRect(first.x,first.y,last.x,last.y,getFillPaint())

        val fl = last.y - first.y

        val left = min(first.x,last.x)

        canvas.drawLine(first.x,first.y,last.x,first.y,getPaint())

        val price = mPointList.first().price
        val round = formatValue(price.toFloat())
        round?.let {
            val roundText = "1.0($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),first.y + textRect.height() /2,getTextPaint())
        }

        val fl1 = first.y + fl * 0.786f
        canvas.drawLine(first.x, fl1,last.x, fl1,getPaint())

        val price1 = mPointList.last().price + (mPointList.first().price - mPointList.last().price) * ( 1 - 0.786f)
        val round1 = formatValue(price1.toFloat())
        round1?.let {
            val roundText = "0.236($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),fl1 + textRect.height() /2,getTextPaint())
        }


        val fl2 = first.y + fl * 0.618f
        canvas.drawLine(first.x, fl2,last.x, fl2,getPaint())

        val price2 = mPointList.last().price + (mPointList.first().price - mPointList.last().price) * ( 1 - 0.618f)
        val round2 = formatValue(price2.toFloat())
        round2?.let {
            val roundText = "0.382($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),fl2 + textRect.height() /2,getTextPaint())
        }

        val fl3 = first.y + fl * 0.5f
        canvas.drawLine(first.x, fl3,last.x, fl3,getPaint())

        val price3 = mPointList.last().price + (mPointList.first().price - mPointList.last().price) *  0.5f
        val round3 = formatValue(price3.toFloat())
        round3?.let {
            val roundText = "0.5($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),fl3 + textRect.height() /2,getTextPaint())
        }

        val fl4 = first.y + fl * 0.382f
        canvas.drawLine(first.x, fl4,last.x, fl4,getPaint())

        val price4 = mPointList.last().price + (mPointList.first().price - mPointList.last().price) * ( 1 - 0.382f)
        val round4 = formatValue(price4.toFloat())
        round4?.let {
            val roundText = "0.618($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),fl4 + textRect.height() /2,getTextPaint())
        }

        val fl5 = first.y + fl * 0.236f
        canvas.drawLine(first.x, fl5,last.x, fl5,getPaint())

        val price5 = mPointList.last().price + (mPointList.first().price - mPointList.last().price) * ( 1 - 0.236f)
        val round5 = formatValue(price5.toFloat())
        round5?.let {
            val roundText = "0.786($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),fl5 + textRect.height() /2,getTextPaint())
        }


        canvas.drawLine(first.x,last.y,last.x,last.y,getPaint())

        val price6 = mPointList.last().price
        val round6 = formatValue(price6.toFloat())
        round6?.let {
            val roundText = "0.0($it)"
            val textRect = getTextRect(roundText,getTextPaint())
            canvas.drawText(roundText,left - textRect.width() - DensityUtil.dp2px(10f),last.y + textRect.height() /2,getTextPaint())
        }
    }

    override fun isSamePre() = false

    override fun isHasFill() = true

    override fun isInSelectArea(x: Float, y: Float, mRect: RectF): Boolean {
        val left = min(mPointList.first().x,mPointList.last().x)
        val right = max(mPointList.first().x,mPointList.last().x)
        val top = min(mPointList.first().y,mPointList.last().y)
        val bottom = max(mPointList.first().y,mPointList.last().y)
        val rectF = RectF(left, top, right, bottom)
        val contains = rectF.contains(x, y)
        if (contains){
            return true
        }
        return super.isInSelectArea(x, y, mRect)
    }
}