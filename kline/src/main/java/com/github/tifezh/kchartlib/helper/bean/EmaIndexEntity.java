package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;
@Keep
public class EmaIndexEntity extends BaseIndexEntity {
    private List<Float> emas = new CopyOnWriteArrayList<>();

    @Override
    public void reset() {
        super.reset();
        emas.clear();
    }

    public void add(Float ma) {
        emas.add(ma);
    }

    public List<Float> getEmas() {
        return emas;
    }

    @NonNull
    @Override
    public String toString() {
        return "EmaIndexEntity{" + "emas=" + emas + '}';
    }
}