package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.*;
import android.util.Pair;

import androidx.annotation.*;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.BaseKChartView;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.chart.EntityImpl.MikeImpl;
import com.github.tifezh.kchartlib.chart.impl.*;

import java.util.*;

/**
 * author：lvy
 * date：2025/02/27
 * desc：主图 MIKE 指标
 */
public class MIKEDraw extends SimpleChartDraw<MikeImpl> {

    private final List<Paint> mPaints = new ArrayList<>();
    private final List<String> mValues = new ArrayList<>();
    private final Context mContext;
    private final Rect mikeRect = new Rect();
    private float totalTextHeight = 0; // 文字总高度，包含换行后的

    public MIKEDraw(Context context) {
        mContext = context;
        float textSize = context.getResources().getDimension(R.dimen.chart_zhibiao_text_size);
        float lineWidth = context.getResources().getDimension(R.dimen.chart_line_width);

        mValues.add("WR");
        mValues.add("MR");
        mValues.add("SR");
        mValues.add("WS");
        mValues.add("MS");
        mValues.add("SS");

        List<Integer> colors = new ArrayList<>(6);
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma1));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma2));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma3));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma4));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma5));
        colors.add(ContextCompat.getColor(context, R.color.kchartlib_ma6));

        for (int i = 0; i < colors.size(); i++) {
            Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
            paint.setColor(colors.get(i));
            paint.setStrokeWidth(lineWidth);
            paint.setStrokeCap(Paint.Cap.ROUND);
            paint.setTextSize(textSize);
            mPaints.add(paint);
        }
    }

    @Override
    public void setTypeFace(Typeface typeFace) {
        if (typeFace == null) return;
        for (Paint paint : mPaints) {
            paint.setTypeface(typeFace);
        }
    }

    @Override
    public void drawTranslated(@Nullable MikeImpl lastPoint, @NonNull MikeImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {

    }

    private float[][] mikeLines;
    private int[] linesLengths;

    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
//        super.drawOnScreen(canvas, view);
        List<Pair<Float, KLineImpl>> mPointList = view.getPointList();
        if (mPointList == null || mPointList.isEmpty()) {
            return;
        }
        int pointSize = mPointList.size();
        int paintSize = mPaints.size();
        if (paintSize < 1) {
            return;
        }
        if (mikeLines == null || mikeLines.length != paintSize) {
            mikeLines = new float[paintSize][];
        }
        if (linesLengths == null || linesLengths.length != pointSize) {
            linesLengths = new int[pointSize];
        }
        boolean isNeedDraw = false;
        int pointsArrSize = pointSize * 4;
        for (int j = 0; j < paintSize; j++) {
            float[] points = mikeLines[j];
            if (points == null || points.length != pointsArrSize) {
                points = new float[pointsArrSize];
                mikeLines[j] = points;
            }
            int idx = 0;
            for (int i = 1; i < pointSize; i++) {
                Pair<Float, KLineImpl> lastPointPair = mPointList.get(i - 1);
                Pair<Float, KLineImpl> cuxPointPair = mPointList.get(i);
                Float lastX = lastPointPair.first;
                MikeImpl lastPoint = lastPointPair.second;
                Float curX = cuxPointPair.first;
                MikeImpl curPoint = cuxPointPair.second;

                if (lastPoint == null || curPoint == null || lastPoint.getMikes().size() <= j || curPoint.getMikes().size() <= j) {
                    continue;
                }
                points[idx++] = lastX;
                points[idx++] = view.getMainY(lastPoint.getMikes().get(j));
                points[idx++] = curX;
                points[idx++] = view.getMainY(curPoint.getMikes().get(j));

                isNeedDraw = true;
            }
            linesLengths[j] = idx;
        }
        if (!isNeedDraw) {
            return;
        }
        for (int i = 0; i < paintSize; i++) {
            int length = linesLengths[i];
            if (mikeLines[i].length > length) {
                canvas.drawLines(mikeLines[i], 0, length, mPaints.get(i));
            } else {
                canvas.drawLines(mikeLines[i], mPaints.get(i));
            }
        }
    }


    @Override
    public void onDrawScreenLine(@Nullable MikeImpl lastPoint, @NonNull MikeImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (lastPoint == null || curPoint == null) {
            return;
        }
        for (int i = 0; i < mPaints.size(); i++) {
            view.drawMainLine(canvas, mPaints.get(i), lastX, lastPoint.getMikes().get(i), curX, curPoint.getMikes().get(i));
        }
    }

    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {
        float originalX = x; // BaseKChartView类的mMainDraw.drawText()方法 x默认值为dp2px(1)，换行后第一个文字的x坐标需要加上这个值，否则会有偏移

        x += MainDraw.mTextMargin;
        totalTextHeight = 0;
        MikeImpl point = (MikeImpl) view.getItem(position);
        int chartWidth = view.getChartWidth();
        int rightTextContentWidth = ((BaseKChartView) view).getRightTextContentWidth();
        int lastWidth = chartWidth - rightTextContentWidth;

        for (int i = 0; i < mPaints.size(); i++) {
            Paint paint = mPaints.get(i);
            // 越界处理
            String value = "--";
            if (point.getMikes().size() > i) {
                value = mValues.get(i);
            }
            String price = "--";
            if (point.getMikes().size() > i) {
                price = view.formatValue(point.getMikes().get(i));
            }

            String text = value + ":" + price;
            paint.getTextBounds(text, 0, text.length(), mikeRect);
            float width = mikeRect.width() + MainDraw.mTextMargin;
            if (x + width > lastWidth) {
                float height = mikeRect.height() + MainDraw.mTextMargin * 0.5f;
                y += height;
                x = MainDraw.mTextMargin + originalX;
                // 累加换行后的文字高度+上间距
                totalTextHeight += height;
            }
            canvas.drawText(text, x, y, paint);
            x += width;
        }
        // 累加第一行文字的高度
        totalTextHeight += mikeRect.height();
    }

    @Override
    public float getMaxValue(MikeImpl point, int positon) {
        float max = Float.MIN_VALUE;
        for (int i = 0; i < point.getMikes().size(); i++) {
            float f = point.getMikes().get(i);
            max = Math.max(max, f);
        }
        return max;
    }

    @Override
    public float getMinValue(MikeImpl point, int positon) {
        float min = Float.MAX_VALUE;
        for (int i = 0; i < point.getMikes().size(); i++) {
            float f = point.getMikes().get(i);
            min = Math.min(min, f);
        }
        return min;
    }


    @Override
    public int getTextH() {
        return (int) totalTextHeight;
    }

    /**
     * MIKE 指标一般为6条线，外部设置时需要设置6种颜色
     */
    public void setColors(List<Integer> colors) {
        for (int i = 0; i < colors.size(); i++) {
            if (mPaints.size() > i) {
                mPaints.get(i).setColor(colors.get(i));
            }
        }
    }

    public void setTextSize(float textSize) {
        for (int i = 0; i < mPaints.size(); i++) {
            mPaints.get(i).setTextSize(textSize);
        }
    }

    public void setStrokeWidth(int strokeWidth) {
        for (int i = 0; i < mPaints.size(); i++) {
            mPaints.get(i).setStrokeWidth(strokeWidth);
        }
    }
}
