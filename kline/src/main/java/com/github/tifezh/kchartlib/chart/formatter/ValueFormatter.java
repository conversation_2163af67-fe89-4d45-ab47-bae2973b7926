package com.github.tifezh.kchartlib.chart.formatter;

import com.github.tifezh.kchartlib.chart.impl.IValueFormatter;

import java.util.Locale;

/**
 * Value格式化类
 * Created by tifezh on 2016/6/21.
 */

public class ValueFormatter implements IValueFormatter {

    private int digit;
    public ValueFormatter(int digit) {
        this.digit = digit;
    }

    @Override
    public int getDigit() {
        return digit;
    }

    @Override
    public String format(float value) {
        return String.format(Locale.ENGLISH, "%."+digit+"f", value);
    }
}
