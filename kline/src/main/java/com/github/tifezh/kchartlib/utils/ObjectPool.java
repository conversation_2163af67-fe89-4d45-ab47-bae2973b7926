package com.github.tifezh.kchartlib.utils;

import android.graphics.Path;
import android.graphics.RectF;

import java.util.ArrayDeque;
import java.util.Queue;

/**
 * Path缓存池
 */
public class ObjectPool {
    private static final int maxSize = 3;
    private static final Queue<Path> pathPool = new ArrayDeque<>(maxSize);

    public static Path obtainPath() {
        return !pathPool.isEmpty() ? pathPool.poll() : new Path();
    }

    public static void recyclePath(Path path) {
        if (path == null) {
            return;
        }
        if (pathPool.size() < maxSize) {
            path.reset();
            pathPool.offer(path);
        }
    }

    private static final Queue<RectF> rectFPool = new ArrayDeque<>(maxSize);

    public static RectF obtainRectF() {
        return !rectFPool.isEmpty() ? rectFPool.poll() : new RectF();
    }

    public static void recycleRectF(RectF rectF) {
        if (rectF == null) {
            return;
        }
        if (rectFPool.size() < maxSize) {
            rectF.set(0f, 0f, 0f, 0f);
            rectFPool.offer(rectF);
        }
    }
}
