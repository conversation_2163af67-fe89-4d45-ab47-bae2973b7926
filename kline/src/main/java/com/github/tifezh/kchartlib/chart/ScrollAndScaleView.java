package com.github.tifezh.kchartlib.chart;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.widget.OverScroller;
import android.widget.RelativeLayout;

import androidx.core.view.GestureDetectorCompat;
import androidx.core.widget.NestedScrollView;
import androidx.recyclerview.widget.RecyclerView;

import com.github.tifezh.kchartlib.select.BitgetSelectImpl;
import com.github.tifezh.kchartlib.select.ISelect;
import com.github.tifezh.kchartlib.select.ISelectCallBack;
import com.github.tifezh.kchartlib.select.ISelectGestureListener;
import com.github.tifezh.kchartlib.select.LongPressSelectImpl;

/**
 * 可以滑动和放大的view
 * Created by tian on 2016/5/3.
 */
public abstract class ScrollAndScaleView extends RelativeLayout implements
        GestureDetector.OnGestureListener,
        ScaleGestureDetector.OnScaleGestureListener, ISelectCallBack {
    protected int mScrollX = 0;
    protected GestureDetectorCompat mDetector;
    protected ScaleGestureDetector mScaleDetector;
    ISelect mSelectImpl = new BitgetSelectImpl(this);
    ISelectGestureListener mSelectGestureListener = (ISelectGestureListener) mSelectImpl;
    boolean mIsSupportChangIndex = false;
    boolean isOnRightSidle = false;

    protected void changeIndex(boolean isSupportChangIndex) {
        mIsSupportChangIndex = isSupportChangIndex;
        //支持点击切换指标的话 把选中模式改为长按
        changeSelectImpl(isSupportChangIndex ? new LongPressSelectImpl(this) : new BitgetSelectImpl(this));
    }

    private void changeSelectImpl(ISelect iSelect) {
        mSelectImpl = iSelect;
        mSelectGestureListener = (ISelectGestureListener) iSelect;
    }

    private final static String TAG = "ScrollAndScaleView";

    OverScroller mScroller;

    protected boolean touch = false;

    public float mScaleX = 1f;

    protected float mScaleXMax = 2.5f;

    protected float mScaleXMin = 0.15f;

    //是否是多指触控
    private boolean mMultipleTouch = false;
    //是否可以滑动
    private boolean mScrollEnable = true;
    //是否可以缩放
    private boolean mScaleEnable = true;

    public ScrollAndScaleView(Context context) {
        super(context);
        init();
    }

    public ScrollAndScaleView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public ScrollAndScaleView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }

    private void init() {
        setWillNotDraw(false);
        mDetector = new GestureDetectorCompat(getContext(), this);
        mScaleDetector = new ScaleGestureDetector(getContext(), this);
        mScroller = new OverScroller(getContext());
    }

    @Override
    public boolean onDown(MotionEvent e) {
        return mSelectGestureListener.onDown(e);
    }

    @Override
    public void onShowPress(MotionEvent e) {

    }

    /**
     * 点击
     *
     * @param e
     * @return
     */
    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        return onClick(e) || mSelectGestureListener.onSingleTapUp(e);
    }

    protected boolean onClick(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        boolean onScroll = mSelectGestureListener.onScroll(e1, e2, distanceX, distanceY);
        if (!onScroll && e2.getPointerCount() == 1) {
            scrollBy(Math.round(distanceX), 0);
        }
        return true;
    }

    @Override
    public void onLongPress(MotionEvent e) {
        mSelectGestureListener.onLongPress(e);
    }

    @Override
    public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
//        if (hasScaled) {
//            hasScaled = false;
//            return true;
//        }
        if (isTouch() || mSelectImpl.isSelected()) {
            return true;
        }

        // TODO: 2025/3/25 此处需优化 
        float scaleX = mScaleX >1 ? 1 :mScaleX;
        mScroller.fling(mScrollX, 0
                , Math.round(velocityX / scaleX), 0,
                Integer.MIN_VALUE, Integer.MAX_VALUE,
                0, 0);
        return true;
    }

    @Override
    public void computeScroll() {
        if (mScroller.computeScrollOffset()) {
            if (!isTouch() || isMultipleTouch()) {
                scrollTo(mScroller.getCurrX(), mScroller.getCurrY());
            } else {
                mScroller.forceFinished(true);
            }
        }
    }

    public void setOffsetX(int offsetX) {
        int x = mScrollX - Math.round(offsetX / mScaleX);
        if (!mScroller.isFinished()) {
            float sigNum = Math.signum(mScroller.getFinalX() - mScroller.getStartX());
            int currVelocity = (int) (mScroller.getCurrVelocity() * sigNum);
            mScroller.forceFinished(true);
            mScroller.fling(x, 0, currVelocity, 0, Integer.MIN_VALUE, Integer.MAX_VALUE, 0, 0);
        } else {
            scrollTo(x, 0);
        }
    }

    @Override
    public void scrollBy(int x, int y) {
        scrollTo(mScrollX - Math.round(x / mScaleX), 0);
    }

    @Override
    public void scrollTo(int x, int y) {
        if (!isScrollEnable()) {
            return;
        }
        int oldX = mScrollX;
        mScrollX = x;
        checkAndFixScrollX();
        onScrollChanged(mScrollX, 0, oldX, 0);
        invalidate();
    }

    boolean hasScaled = false;
    protected float mFocusX;
    protected int mFocusIndex;

    @Override
    public boolean onScale(ScaleGestureDetector detector) {
        if (!isScaleEnable()) {
            return false;
        }

        hasScaled = true;
        mSelectGestureListener.onScale(detector);

        if (mFocusX == 0) {
            mFocusX = detector.getFocusX();
        }
        float oldScale = mScaleX;
        mScaleX *= Math.pow(detector.getScaleFactor(), 2.5);
        mScaleX = fixScaleX(mScaleX);
        onScaleChanged(mScaleX, oldScale);
        return true;
    }

    private float fixScaleX(float scaleX){
        if (Float.isNaN(scaleX)) {
            scaleX = 1.0f;
        }else if (scaleX < mScaleXMin) {
            scaleX = mScaleXMin;
        } else if (scaleX > mScaleXMax) {
            scaleX = mScaleXMax;
        }
        return scaleX;
    }

    protected void onScaleChanged(float scale, float oldScale) {
        invalidate();
    }

    @Override
    public boolean onScaleBegin(ScaleGestureDetector detector) {
        return true;
    }

    @Override
    public void onScaleEnd(ScaleGestureDetector detector) {
        mFocusX = 0;
        mFocusIndex = 0;
    }

    /**
     * 按下时的x,y坐标
     */
    float mDownX, mDownY;
    /**
     * 判断是否已经确认滑动方向了
     */
    boolean isConfirmDirection;
    /**
     * 是不是横向滑动。横向滑动说明是在K线要处理接下来的滑动事件。
     */
    boolean isScrollHorizontal;
    private void disallowParentsInterceptor(boolean disAllow){
        ViewGroup parent = (ViewGroup) this.getParent();
        while (parent != null){
            if (parent instanceof NestedScrollView || parent instanceof RecyclerView){
                parent.requestDisallowInterceptTouchEvent(disAllow);
            }
            ViewParent parentTemp = parent.getParent();
            if (parentTemp instanceof ViewGroup){
                parent = (ViewGroup) parentTemp;
            }else {
                parent = null;
            }
        }
    }
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        switch (event.getAction() & MotionEvent.ACTION_MASK) {
            case MotionEvent.ACTION_DOWN:
                disallowParentsInterceptor(true);
                isConfirmDirection = false;
                mDownX = event.getX();
                mDownY = event.getY();
                touch = true;
                break;
            case MotionEvent.ACTION_MOVE:
                //两个手指禁止上滑
                if (event.getPointerCount() > 1) {
                    onScrollHorizontal();
                }
                if (!isConfirmDirection) {
                    float distX = event.getX() - mDownX;
                    float distY = event.getY() - mDownY;
                    disallowParentsInterceptor(!isOnRightSidle || (distX >= 0) );
                    if (isOnRightSidle && distX <0){
                        return false;
                    }
                    if (Math.abs(distX) > 5 || Math.abs(distY) > 5) {
                        if (Math.abs(distX) >= Math.abs(distY)) {
                            onScrollHorizontal();
                        } else {
                            //纵向滑动
                            disallowParentsInterceptor(false);
                            return false;
                        }
                    }
                } else {
                    if (isScrollHorizontal) {
                        onScrollHorizontal();
                    } else {
                        onScrollVertical();
                        return false;
                    }
                }

                break;
            case MotionEvent.ACTION_POINTER_UP:
                invalidate();
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_CANCEL:
                isConfirmDirection = false;
                touch = false;
                break;
        }
        mSelectGestureListener.onTouchEvent(event);
        mMultipleTouch = event.getPointerCount() > 1;
        this.mDetector.onTouchEvent(event);
        this.mScaleDetector.onTouchEvent(event);

        return true;
    }

    private void onScrollVertical() {
        setUnSelected();
    }

    private void onScrollHorizontal() {
        isConfirmDirection = true;
        isScrollHorizontal = true;
        getParent().requestDisallowInterceptTouchEvent(true);
    }


    /**
     * 滑到了最左边
     */
    abstract public void onLeftSide();

    /**
     * 滑到了最右边
     */
    abstract public void onRightSide();

    /**
     * 是否在触摸中
     *
     * @return
     */
    public boolean isTouch() {
        return touch;
    }

    /**
     * 获取位移的最小值
     *
     * @return
     */
    public abstract int getMinScrollX();

    /**
     * 获取位移的最大值
     *
     * @return
     */
    public abstract int getMaxScrollX();

    /**
     * 设置ScrollX
     *
     * @param scrollX
     */
    public void setScrollX(int scrollX) {
        this.mScrollX = scrollX;
        scrollTo(scrollX, 0);
    }

    /**
     * 是否是多指触控
     *
     * @return
     */
    public boolean isMultipleTouch() {
        return mMultipleTouch;
    }

    protected void checkAndFixScrollX() {
        isOnRightSidle = false;
        if (!isFullScreen()) {
            mScrollX = getMinScrollX();
            onRightSide();
            isOnRightSidle = true;
            mScroller.forceFinished(true);
            return;
        }
        if (mScrollX < getMinScrollX()) {
            mScrollX = getMinScrollX();
            onRightSide();
            isOnRightSidle = true;
            mScroller.forceFinished(true);
        } else if (mScrollX > getMaxScrollX()) {
            mScrollX = getMaxScrollX();
            onLeftSide();
            mScroller.forceFinished(true);
        }
    }

    abstract boolean isFullScreen();

    public float getScaleXMax() {
        return mScaleXMax;
    }

    public float getScaleXMin() {
        return mScaleXMin;
    }

    public boolean isScrollEnable() {
        return mScrollEnable;
    }

    public boolean isScaleEnable() {
        return mScaleEnable;
    }

    /**
     * 设置缩放的最大值
     */
    public void setScaleXMax(float scaleXMax) {
        mScaleXMax = scaleXMax;
    }

    /**
     * 设置缩放的最小值
     */
    public void setScaleXMin(float scaleXMin) {
        mScaleXMin = scaleXMin;
    }

    /**
     * 设置是否可以滑动
     */
    public void setScrollEnable(boolean scrollEnable) {
        mScrollEnable = scrollEnable;
    }

    /**
     * 设置是否可以缩放
     */
    public void setScaleEnable(boolean scaleEnable) {
        mScaleEnable = scaleEnable;
    }

    public void setKChartScaleX(float scaleX) {
        mScaleX = fixScaleX(scaleX);
    }


    public float getmScaleX() {
        return mScaleX;
    }

    protected void setUnSelected() {
        mSelectImpl.setUnSelected();
    }

    @Override
    public void disallowIntercept(){
        onScrollHorizontal();
    }


}
