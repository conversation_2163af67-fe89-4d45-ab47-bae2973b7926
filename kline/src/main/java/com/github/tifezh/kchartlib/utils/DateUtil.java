package com.github.tifezh.kchartlib.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;
import java.util.TimeZone;

/**
 * 时间工具类
 * Created by tifezh on 2016/4/27.
 */
public class DateUtil {
    public static SimpleDateFormat longTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    public static SimpleDateFormat shortTimeFormat = new SimpleDateFormat("HH:mm");
    public static SimpleDateFormat DateFormat = new SimpleDateFormat("yyyy/MM/dd");

    public static String formatTime(long milliseconds) {

        long oneDay = 24 * 60 * 60 * 1000L;
        long oneHour = 60 * 60 * 1000L;
        long oneMin = 60 * 1000L;
        long oneSecond = 1000L;

        long days = milliseconds / oneDay;
        long hours = milliseconds % oneDay / oneHour;
        long minutes = milliseconds % oneHour / oneMin;
        long seconds = milliseconds % oneMin / oneSecond;

        if (milliseconds < oneHour) { //小于1小时
            return String.format(Locale.getDefault(), "%02d:%02d", minutes, seconds);
        }

        if (milliseconds < oneDay) { //小于1天
            return String.format(Locale.getDefault(), "%02d:%02d:%02d", hours, minutes, seconds);
        }

        return String.format(Locale.getDefault(), "%02dD:%02dH", days, hours);
    }

    public static long getMaxDayOfMonthTimeMillis(Long date) {
        Calendar instance = Calendar.getInstance(TimeZone.getDefault());
        instance.setTimeInMillis(date);
        int maxDayOfMonth = instance.getActualMaximum(Calendar.DAY_OF_MONTH);
        return maxDayOfMonth * 1000L * 60 * 60 * 24;
    }
}
