package com.github.tifezh.kchartlib.utils.klineoption

/**
 * author : wanghui
 * date : 2020/6/28 3:16 PM
 * description :
 */
class KLineOption : Cloneable {
    //K线最大高度
    var klineMaxHeight: Int = 0

    //K线最小高度
    var klineMinHeight: Int = 0

    //K线默认高度
    var klineDefaultHeight: Int? = null

    //默认的百分比
    var klineDefaultHeightPercent: Int? = null

    //横屏时K线高度百分比
    var klineHeightPercent: Int?
        get() = klineHeightPercent_1
        set(value) {
            klineHeightPercent_1 = value
        }

    var klineHeightPercent_1: Int? = null

    //是否显示现价线
    var isKlineCurrentPriceLineShow = true

    //手势切换币种
    var isChangeCoin = false

    //点击切换指标
    var isChangeIndex = false

    //K线风格是否是新的
    var kChartStyleIsNew = true

    //涨跌颜色 正常的涨跌颜色
    var isRateColor = true

    //是不是 UTC+0 默认是
    var isTimeUtc0 = true

    //成交
    var hideDeal = false

    //计划委托线
    var isShowPlanLine = true

    //止盈止损线
    var isShowPlanEndLine = true

    var isShowPositionLine = true

    //最大最小线
    var isShowMaxAndMinLine = true

    //K线十字摘要
    var isAbstract = true

    // 成交记录
    var isShowOrderRecord = true
    var isShowCurrentDelegation = true
    var iShowCountDown = false
    var iShowInstantBuyingSelling = false
    var iShowLatestTransactionPrice = true
    var isShowKline = true

    var mA: KlineOptionOne? = null
    var eMA: KlineOptionOne? = null
    var bOLL: KlineOptionOne? = null
    var vOL: KlineOptionOne? = null
    var mACD: KlineOptionOne? = null
    var kDJ: KlineOptionOne? = null
    var rSI: KlineOptionOne? = null
    var rOC: KlineOptionOne? = null
    var cCI: KlineOptionOne? = null

    var isSelectedShake = true

    @Throws(CloneNotSupportedException::class)
    public override fun clone(): KLineOption {
        return super.clone() as KLineOption
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as KLineOption

        if (klineDefaultHeight != other.klineDefaultHeight) return false
        if (klineDefaultHeightPercent != other.klineDefaultHeightPercent) return false
        if (klineHeightPercent != other.klineHeightPercent) return false
        if (isKlineCurrentPriceLineShow != other.isKlineCurrentPriceLineShow) return false
        if (isChangeCoin != other.isChangeCoin) return false
        if (isChangeIndex != other.isChangeIndex) return false
        if (kChartStyleIsNew != other.kChartStyleIsNew) return false
        if (isRateColor != other.isRateColor) return false
        if (isTimeUtc0 != other.isTimeUtc0) return false
        if (isAbstract != other.isAbstract) return false
        if (hideDeal != other.hideDeal) return false
        if (isShowPlanLine != other.isShowPlanLine) return false
        if (isShowPlanEndLine != other.isShowPlanEndLine) return false
        if (isShowPositionLine != other.isShowPositionLine) return false
        if (isShowMaxAndMinLine != other.isShowMaxAndMinLine) return false
        if (isSelectedShake != other.isSelectedShake) return false
        if (mA != other.mA) return false
        if (eMA != other.eMA) return false
        if (bOLL != other.bOLL) return false
        if (vOL != other.vOL) return false
        if (mACD != other.mACD) return false
        if (kDJ != other.kDJ) return false
        if (rSI != other.rSI) return false
        if (rOC != other.rOC) return false
        if (cCI != other.cCI) return false

        return true
    }

    override fun hashCode(): Int {
        var result = klineDefaultHeight ?: 0
        result = 31 * result + (klineDefaultHeightPercent ?: 0)
        result = 31 * result + (klineHeightPercent ?: 0)
        result = 31 * result + isKlineCurrentPriceLineShow.hashCode()
        result = 31 * result + isChangeCoin.hashCode()
        result = 31 * result + isChangeIndex.hashCode()
        result = 31 * result + kChartStyleIsNew.hashCode()
        result = 31 * result + isRateColor.hashCode()
        result = 31 * result + isTimeUtc0.hashCode()
        result = 31 * result + hideDeal.hashCode()
        result = 31 * result + isShowPlanLine.hashCode()
        result = 31 * result + isShowPlanEndLine.hashCode()
        result = 31 * result + isShowPositionLine.hashCode()
        result = 31 * result + isShowMaxAndMinLine.hashCode()
        result = 31 * result + isShowOrderRecord.hashCode()
        result = 31 * result + iShowCountDown.hashCode()
        result = 31 * result + iShowInstantBuyingSelling.hashCode()
        result = 31 * result + iShowLatestTransactionPrice.hashCode()
        result = 31 * result + isShowKline.hashCode()
        result = 31 * result + isSelectedShake.hashCode()
        result = 31 * result + (mA?.hashCode() ?: 0)
        result = 31 * result + isAbstract.hashCode()
        result = 31 * result + (eMA?.hashCode() ?: 0)
        result = 31 * result + (bOLL?.hashCode() ?: 0)
        result = 31 * result + (vOL?.hashCode() ?: 0)
        result = 31 * result + (mACD?.hashCode() ?: 0)
        result = 31 * result + (kDJ?.hashCode() ?: 0)
        result = 31 * result + (rSI?.hashCode() ?: 0)
        result = 31 * result + (rOC?.hashCode() ?: 0)
        result = 31 * result + (cCI?.hashCode() ?: 0)
        return result
    }

}