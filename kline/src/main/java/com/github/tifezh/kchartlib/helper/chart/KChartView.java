package com.github.tifezh.kchartlib.helper.chart;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.BaseKChartView;
import com.github.tifezh.kchartlib.chart.draw.BBIDraw;
import com.github.tifezh.kchartlib.chart.draw.BOLLDraw;
import com.github.tifezh.kchartlib.chart.draw.CCIDraw;
import com.github.tifezh.kchartlib.chart.draw.DMIDraw;
import com.github.tifezh.kchartlib.chart.draw.EMADraw;
import com.github.tifezh.kchartlib.chart.draw.KDDraw;
import com.github.tifezh.kchartlib.chart.draw.KDJDraw;
import com.github.tifezh.kchartlib.chart.draw.MACDDraw;
import com.github.tifezh.kchartlib.chart.draw.MADraw;
import com.github.tifezh.kchartlib.chart.draw.MIKEDraw;
import com.github.tifezh.kchartlib.chart.draw.MainDraw;
import com.github.tifezh.kchartlib.chart.draw.ROCDraw;
import com.github.tifezh.kchartlib.chart.draw.RSIDraw;
import com.github.tifezh.kchartlib.chart.draw.SARDraw;
import com.github.tifezh.kchartlib.chart.draw.VOLDraw;
import com.github.tifezh.kchartlib.chart.draw.WRDraw;
import com.github.tifezh.kchartlib.chart.impl.IValueFormatter;
import com.github.tifezh.kchartlib.helper.bean.KlineEnum;
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum;
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnum;
import com.github.tifezh.kchartlib.utils.klineoption.KLineOption;
import com.github.tifezh.kchartlib.utils.klineoption.KlineOptionOne;
import com.github.tifezh.kchartlib.utils.klineoption.KlineOptionOneLineValueBean;
import com.github.tifezh.kchartlib.view.KlineBreathingLampView;
import com.google.firebase.crashlytics.FirebaseCrashlytics;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * k线图
 * Created by tian on 2016/5/20.
 */
public class KChartView extends BaseKChartView {

    List<KlineOtherEnum> klineOtherEnums = new ArrayList();
    List<KlineMainEnum> klineMainEnums = new ArrayList();
    //    ProgressBar mProgressBar;
    private boolean isRefreshing = false;
    private boolean isLoadMoreEnd = false;
    private boolean mLastScrollEnable = true;
    private boolean mLastScaleEnable = true;
    private KChartRefreshListener mRefreshListener;
    private MainDraw mMainDraw;
    private VOLDraw mVOLDraw;
    private BOLLDraw mBOLLDraw;
    private BBIDraw mBBIDraw;
    private SARDraw mSARDraw;
    private MIKEDraw mMIKEDraw;
    private MADraw mMADraw;
    private EMADraw mEMADraw;
    private KChartViewOnRefreshInter mKChartViewOnRefreshInter;
    private KlineBreathingLampView mBreathView;
    private KLineOption mKLineOption;
    private MACDDraw mChildMACDDraw;
    private KDJDraw mChildKDJDraw;
    private RSIDraw mChilRSIdDraw;
    //    private WRDraw mChildWRDraw;
    private ROCDraw mChildROCDraw;
    private CCIDraw mChildCCIDraw;
    private KDDraw mChildKDDraw;
    private WRDraw mChildWRDraw;
    private DMIDraw mChildDMIDraw;
    //滑动到边距回调
    private OnKChartViewEdgeListener mOnKChartViewEdgeListener;

    public KChartView(Context context) {
        this(context, null);
    }

    public KChartView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public KChartView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initView();
        initData();
    }

    private void initView() {
        mBreathView = new KlineBreathingLampView(getContext());
        mBreathView.setKchartView(this);
        //        LayoutParams breathViewLayoutParams = new LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
        //        addView(mBreathView, breathViewLayoutParams);

        //        mProgressBar = new ProgressBar(getContext());
        //        LayoutParams layoutParams = new LayoutParams(dp2px(75), dp2px(75));
        //        layoutParams.addRule(CENTER_IN_PARENT);
        //        addView(mProgressBar, layoutParams);
        //        mProgressBar.setVisibility(GONE);
    }

    private void initData() {
        mMainDraw = new MainDraw(getContext());
        klineMainEnums.add(KlineMainEnum.NONE);
        // MA
        mMADraw = new MADraw(getContext());
        mMADraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineMainEnum.MA));
        klineMainEnums.add(KlineMainEnum.MA);
        addMainChildDraw(KlineMainEnum.MA, mMADraw);
        // EMA
        mEMADraw = new EMADraw(getContext());
        mEMADraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineMainEnum.EMA));
        klineMainEnums.add(KlineMainEnum.EMA);
        addMainChildDraw(KlineMainEnum.EMA, mEMADraw);
        // BOLL
        mBOLLDraw = new BOLLDraw(getContext());
        mBOLLDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineMainEnum.BOLL));
        addMainChildDraw(KlineMainEnum.BOLL, mBOLLDraw);
        klineMainEnums.add(KlineMainEnum.BOLL);
        // MIKE
        mMIKEDraw = new MIKEDraw(getContext());
        mMIKEDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineMainEnum.MIKE));
        addMainChildDraw(KlineMainEnum.MIKE, mMIKEDraw);
        klineMainEnums.add(KlineMainEnum.MIKE);
        // BBI
        mBBIDraw = new BBIDraw(getContext());
        mBBIDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineMainEnum.BBI));
        addMainChildDraw(KlineMainEnum.BBI, mBBIDraw);
        klineMainEnums.add(KlineMainEnum.BBI);
        // SAR
        mSARDraw = new SARDraw(getContext());
        mSARDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineMainEnum.SAR));
        addMainChildDraw(KlineMainEnum.SAR, mSARDraw);
        klineMainEnums.add(KlineMainEnum.SAR);
        // VOL
        mVOLDraw = new VOLDraw(getContext());
        mVOLDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.VOL));
        addChildDraw(KlineOtherEnum.VOL, mVOLDraw);
        klineOtherEnums.add(KlineOtherEnum.VOL);
        // MACD
        mChildMACDDraw = new MACDDraw(getContext());
        mChildMACDDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.MACD));
        addChildDraw(KlineOtherEnum.MACD, mChildMACDDraw);
        klineOtherEnums.add(KlineOtherEnum.MACD);
        // KDJ
        mChildKDJDraw = new KDJDraw(getContext());
        mChildKDJDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.KDJ));
        addChildDraw(KlineOtherEnum.KDJ, mChildKDJDraw);
        klineOtherEnums.add(KlineOtherEnum.KDJ);
        // RSI
        mChilRSIdDraw = new RSIDraw(getContext());
        mChilRSIdDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.RSI));
        addChildDraw(KlineOtherEnum.RSI, mChilRSIdDraw);
        klineOtherEnums.add(KlineOtherEnum.RSI);
        //        mChildWRDraw = new WRDraw(getContext());
        //        addChildDraw("WR", mChildWRDraw);
        //        klineOtherEnums.add(KlineOtherEnum.WR);
        // ROC
        mChildROCDraw = new ROCDraw(getContext());
        mChildROCDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.ROC));
        addChildDraw(KlineOtherEnum.ROC, mChildROCDraw);
        klineOtherEnums.add(KlineOtherEnum.ROC);
        // CCI
        mChildCCIDraw = new CCIDraw(getContext());
        mChildCCIDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.CCI));
        addChildDraw(KlineOtherEnum.CCI, mChildCCIDraw);
        klineOtherEnums.add(KlineOtherEnum.CCI);
        // KD
        mChildKDDraw = new KDDraw(getContext());
        mChildKDDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.KD));
        addChildDraw(KlineOtherEnum.KD, mChildKDDraw);
        klineOtherEnums.add(KlineOtherEnum.KD);
        // WR
        mChildWRDraw = new WRDraw(getContext());
        mChildWRDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.WR));
        addChildDraw(KlineOtherEnum.WR, mChildWRDraw);
        klineOtherEnums.add(KlineOtherEnum.WR);
        // DMI
        mChildDMIDraw = new DMIDraw(getContext());
        mChildDMIDraw.setIndicatorList(DataHelper.getIndicatorListByKey(KlineOtherEnum.DMI));
        addChildDraw(KlineOtherEnum.DMI, mChildDMIDraw);
        klineOtherEnums.add(KlineOtherEnum.DMI);

        setMainDraw(mMainDraw);

        mMainDraw.setTypeFace(typeface);
        mVOLDraw.setTypeFace(typeface);
        mChildMACDDraw.setTypeFace(typeface);
        mChildKDJDraw.setTypeFace(typeface);
        mChilRSIdDraw.setTypeFace(typeface);
        //        mChildWRDraw.setTypeFace(typeface);
        mChildROCDraw.setTypeFace(typeface);
        mChildCCIDraw.setTypeFace(typeface);
        mChildKDDraw.setTypeFace(typeface);
    }

    public KlineMainEnum getNextMainIndex(KlineMainEnum currentMain) {
        int currentIndex = klineMainEnums.indexOf(currentMain);
        currentIndex++;
        if(currentIndex > klineMainEnums.size() - 1) {
            currentIndex = 0;
        }
        return klineMainEnums.get(currentIndex);
    }

    public KlineOtherEnum getNextOtherIndex(KlineOtherEnum currentOther) {
        int currentIndex = klineOtherEnums.indexOf(currentOther);
        currentIndex++;
        if(currentIndex > klineOtherEnums.size() - 1) {
            currentIndex = 0;
        }
        return klineOtherEnums.get(currentIndex);
    }

    @Nullable
    public KlineOtherEnum getNextOtherIndex(List<KlineOtherEnum> currentOtherList) {
        if(currentOtherList == null || currentOtherList.isEmpty()) {
            return null;
        }
        if(currentOtherList.size() == klineOtherEnums.size()) {
            return null;
        }
        KlineOtherEnum klineOtherEnum = currentOtherList.get(currentOtherList.size() - 1);
        int currentIndex = klineOtherEnums.indexOf(klineOtherEnum);
        currentIndex++;
        while (true) {
            if(currentIndex > klineOtherEnums.size() - 1) {
                currentIndex = 0;
            }
            KlineOtherEnum nextOtherIndex = klineOtherEnums.get(currentIndex);
            if(currentOtherList.contains(nextOtherIndex)) {
                currentIndex++;
            } else {
                return nextOtherIndex;
            }
        }
    }

    @Override
    public void onLeftSide() {
        //        showLoading();
        if(mOnKChartViewEdgeListener != null) {
            mOnKChartViewEdgeListener.onLeftSide();
        }
    }

    @Override
    public void onRightSide() {
    }

    public void showLoading() {
        if(!isLoadMoreEnd && !isRefreshing) {
            isRefreshing = true;

            if(mKChartViewOnRefreshInter != null) {
                mKChartViewOnRefreshInter.showLoading();
            } else {
                //                if (mProgressBar != null) {
                //                    mProgressBar.setVisibility(View.VISIBLE);
                //                }
            }

            if(mRefreshListener != null) {
                mRefreshListener.onLoadMoreBegin(this);
            }
            mLastScaleEnable = isScaleEnable();
            mLastScrollEnable = isScrollEnable();
            super.setScrollEnable(false);
            super.setScaleEnable(false);
        }
    }

    private void hideLoading() {
        if(mKChartViewOnRefreshInter != null) {
            mKChartViewOnRefreshInter.hideLoading();
        } else {
            //            if (mProgressBar != null) {
            //                mProgressBar.setVisibility(View.GONE);
            //            }
        }

        super.setScrollEnable(mLastScrollEnable);
        super.setScaleEnable(mLastScaleEnable);
    }

    /**
     * 刷新完成
     */
    public void refreshCompelete() {
        isRefreshing = false;
        hideLoading();
    }

    /**
     * 刷新完成，没有数据
     */
    public void refreshEnd() {
        isLoadMoreEnd = true;
        isRefreshing = false;
        hideLoading();
    }

    /**
     * 重置加载更多
     */
    public void resetLoadMoreEnd() {
        isLoadMoreEnd = false;
    }

    public void setKlineMain(KlineMainEnum mainEnum) {
        mMainDraw.setKlineMain(mainEnum);
        invalidate();
    }

    public void setKlineChild(KlineOtherEnum otherEnum) {
        setChildDraw(klineOtherEnums.indexOf(otherEnum));
    }

    /**
     * 设置多个指标
     *
     * @param otherEnums 指标数组
     */
    public void setKlineChild(List<KlineOtherEnum> otherEnums) {
        setChildDrawList(otherEnums);
    }

    public void setKlineMainChild(List<KlineMainEnum> mainEnums) {
        setMainChildDrawList(mainEnums);
    }

    public void setKlineMainChild(KlineMainEnum otherEnum) {
        setMainChildDraw(klineMainEnums.indexOf(otherEnum) - 1);
    }

    public void setVolValueFormatter(IValueFormatter volValueFormatter) {
        //        this.mVolValueFormatter = volValueFormatter;
        if(mVOLDraw != null) {
            mVOLDraw.setValueFormatter(volValueFormatter);
        }
    }
    @Override
    public void setScaleEnable(boolean scaleEnable) {
        if(isRefreshing) {
            throw new IllegalStateException("请勿在刷新状态设置属性");
        }
        super.setScaleEnable(scaleEnable);

    }
    @Override
    public void setScrollEnable(boolean scrollEnable) {
        if(isRefreshing) {
            throw new IllegalStateException("请勿在刷新状态设置属性");
        }
        super.setScrollEnable(scrollEnable);
    }
    public void setMainType(int mainType) {
        mMainDraw.setMainType(mainType);
        invalidate();
    }
    public void startBreathAnimatorWrapper() {
        if(!mMainDraw.isDrawMinute()) {
            return;
        }

        if(mBreathView != null) {
            mBreathView.startBreathAnimatorWrapper();
        }
    }
    public void stopBreathAnimator() {
        if(mBreathView != null) {
            mBreathView.stopBreathAnimator();
        }
    }
    public void setRedColor(@Nullable Integer redColor) {
        this.redColor = redColor;
    }
    public void setGreenColor(@Nullable Integer greenColor) {
        this.greenColor = greenColor;
    }
    public void setBreathCircleColor(int circleColor) {
        mBreathView.setCircleColor(circleColor);
    }
    public void setBreathRadarStartColor(int radarStartColor) {
        mBreathView.setRadarStartColor(radarStartColor);
    }
    public void setRadarEndColor(int radarEndColor) {
        mBreathView.setRadarEndColor(radarEndColor);
    }
    public void setMaxMinTextColor(int maxMinTextColor) {
        mMainDraw.setMaxMinTextColor(maxMinTextColor);
    }
    public void setOnRefreshListener(KChartViewOnRefreshInter onRefreshListener) {
        mKChartViewOnRefreshInter = onRefreshListener;
    }

    /**
     * 设置k线指标
     *
     * @param map
     */
    public void setKlineOption(HashMap<KlineEnum, List<Integer>> map) {
        DataHelper.setKlineSetting(map);
        mMADraw.setIndicatorList(map.get(KlineMainEnum.MA));
        mEMADraw.setIndicatorList(map.get(KlineMainEnum.EMA));
        mBOLLDraw.setIndicatorList(map.get(KlineMainEnum.BOLL));
        mMIKEDraw.setIndicatorList(map.get(KlineMainEnum.MIKE));
        mBBIDraw.setIndicatorList(map.get(KlineMainEnum.BBI));
        mSARDraw.setIndicatorList(map.get(KlineMainEnum.SAR));
        mChildMACDDraw.setIndicatorList(map.get(KlineOtherEnum.MACD));
        mChildKDJDraw.setIndicatorList(map.get(KlineOtherEnum.KDJ));
        mChilRSIdDraw.setIndicatorList(map.get(KlineOtherEnum.RSI));
        mChildWRDraw.setIndicatorList(map.get(KlineOtherEnum.WR));
        mChildROCDraw.setIndicatorList(map.get(KlineOtherEnum.ROC));
        mChildCCIDraw.setIndicatorList(map.get(KlineOtherEnum.CCI));
        mChildKDDraw.setIndicatorList(map.get(KlineOtherEnum.KD));
        mChildDMIDraw.setIndicatorList(map.get(KlineOtherEnum.DMI));
        invalidate();
    }
    public void setKlineOption(KLineOption kLineOption) {
        try {
            if(kLineOption.equals(mKLineOption)) {
                return;
            }

            mKLineOption = kLineOption.clone();

            //计算设置
            DataHelper.setKlineOption(kLineOption);

            setRateColor(mKLineOption.isRateColor());

            //对应线颜色线宽修改
            //        ma
            KlineOptionOne ma = mKLineOption.getMA();
            if(ma != null && mMainDraw != null) {
                List<Integer> colors = new ArrayList<>();
                List<Integer> values = new ArrayList<>();
                for (int i = 0; i < ma.getKlineOptionValues().size(); i++) {
                    KlineOptionOneLineValueBean valueBean = ma.getKlineOptionValues().get(i);
                    if(valueBean.getEnable()) {
                        colors.add(ma.getKlineOptionColors().get(i).getColorValue());
                        values.add(valueBean.getValue());
                    }
                }
                mMADraw.setMAColors(colors);
                mMADraw.setMAValues(values);
                mMADraw.setMaLineWidht(dp2px(ma.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        ema
            KlineOptionOne ema = mKLineOption.getEMA();
            if(ema != null && mMainDraw != null) {
                List<Integer> colors = new ArrayList<>();
                List<Integer> values = new ArrayList<>();
                for (int i = 0; i < ema.getKlineOptionValues().size(); i++) {
                    KlineOptionOneLineValueBean valueBean = ema.getKlineOptionValues().get(i);
                    if(valueBean.getEnable()) {
                        colors.add(ema.getKlineOptionColors().get(i).getColorValue());
                        values.add(valueBean.getValue());
                    }
                }
                mEMADraw.setEMAColors(colors);
                mEMADraw.setIndicatorList(values);
                mEMADraw.setEmaLineWidht(dp2px(ema.getKlineOptionWidth().getLineWidth() / 2f));

            }
            //        boll
            KlineOptionOne boll = mKLineOption.getBOLL();
            if(boll != null && mMainDraw != null) {
                mBOLLDraw.setUpperColor(boll.getKlineOptionColors().get(0).getColorValue());
                mBOLLDraw.setMedianColor(boll.getKlineOptionColors().get(1).getColorValue());
                mBOLLDraw.setLowerColor(boll.getKlineOptionColors().get(2).getColorValue());
                mBOLLDraw.setBollDayValue(boll.getKlineOptionValues().get(0).getValue());
                mBOLLDraw.setBollLineWidth(dp2px(boll.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        vol
            KlineOptionOne vol = mKLineOption.getVOL();
            if(vol != null && mVOLDraw != null) {
                mVOLDraw.setMa5Color(vol.getKlineOptionColors().get(0).getColorValue());
                mVOLDraw.setMa10Color(vol.getKlineOptionColors().get(1).getColorValue());

                mVOLDraw.setMa5Value(vol.getKlineOptionValues().get(0).getValue());
                mVOLDraw.setMa10Value(vol.getKlineOptionValues().get(1).getValue());

                mVOLDraw.setMaLineWidth(dp2px(vol.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        macd
            KlineOptionOne macd = mKLineOption.getMACD();
            if(macd != null && mChildMACDDraw != null) {
                mChildMACDDraw.setDIFColor(macd.getKlineOptionColors().get(0).getColorValue());
                mChildMACDDraw.setDEAColor(macd.getKlineOptionColors().get(1).getColorValue());

                mChildMACDDraw.setKlineOptionList(macd.getKlineOptionValues());

                mChildMACDDraw.setLineWidth(dp2px(macd.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        kdj
            KlineOptionOne kdj = mKLineOption.getKDJ();
            if(kdj != null && mChildKDJDraw != null) {
                mChildKDJDraw.setKColor(kdj.getKlineOptionColors().get(0).getColorValue());
                mChildKDJDraw.setDColor(kdj.getKlineOptionColors().get(1).getColorValue());
                mChildKDJDraw.setJColor(kdj.getKlineOptionColors().get(2).getColorValue());

                mChildKDJDraw.setKlineOptionList(kdj.getKlineOptionValues());

                mChildKDJDraw.setLineWidth(dp2px(kdj.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        rsi
            KlineOptionOne rsi = mKLineOption.getRSI();
            if(rsi != null && mChilRSIdDraw != null) {
                mChilRSIdDraw.setRSI1Color(rsi.getKlineOptionColors().get(0).getColorValue());

                //                mChilRSIdDraw.setRsiValue(rsi.getKlineOptionValues().get(0).getValue());
                mChilRSIdDraw.setKlineOptionList(rsi.getKlineOptionValues());

                mChilRSIdDraw.setLineWidth(dp2px(rsi.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        roc
            KlineOptionOne roc = mKLineOption.getROC();
            if(roc != null && mChildROCDraw != null) {
                mChildROCDraw.setROCColor(roc.getKlineOptionColors().get(0).getColorValue());
                mChildROCDraw.setROCMAColor(roc.getKlineOptionColors().get(1).getColorValue());

                mChildROCDraw.setRocNValue(roc.getKlineOptionValues().get(0).getValue());
                mChildROCDraw.setRocMValue(roc.getKlineOptionValues().get(1).getValue());

                mChildROCDraw.setLineWidth(dp2px(roc.getKlineOptionWidth().getLineWidth() / 2f));
            }
            //        cci

            KlineOptionOne cci = mKLineOption.getCCI();
            if(cci != null && mChildCCIDraw != null) {
                mChildCCIDraw.setCCIColor(cci.getKlineOptionColors().get(0).getColorValue());

                mChildCCIDraw.setCciValue(cci.getKlineOptionValues().get(0).getValue());

                mChildCCIDraw.setLineWidth(dp2px(cci.getKlineOptionWidth().getLineWidth() / 2f));
            }

            //            invalidate(); // 不要刷新
        } catch (Exception e) {
            e.printStackTrace();
            FirebaseCrashlytics.getInstance().recordException(new Exception("KChartView.setKlineOption e" + e.getMessage()));
        }
    }
    /**
     * @param rateColor 是否绿涨红跌
     *                  true 正常 false反过来
     */
    private void setRateColor(boolean rateColor) {
        int tempGreenColor = greenColor != null ? greenColor : ContextCompat.getColor(getContext(), R.color.chart_green);
        int tempRedColor = redColor != null ? redColor : ContextCompat.getColor(getContext(), R.color.chart_red);
        int greenColor = rateColor ? tempGreenColor : tempRedColor;
        int redColor = rateColor ? tempRedColor : tempGreenColor;
        mMainDraw.setGreenColor(greenColor);
        mMainDraw.setRedColor(redColor);
        mVOLDraw.setGreenColor(greenColor);
        mVOLDraw.setRedColor(redColor);
        mChildMACDDraw.setGreenColor(greenColor);
        mChildMACDDraw.setRedColor(redColor);

        mRateColor = greenColor;
        mFallColor = redColor;
    }
    /**
     * 设置蜡烛图涨跌色
     *
     * @param risingColor  涨
     * @param FallingColor 跌
     */
    public void setCandleColor(int risingColor, int FallingColor) {
        mMainDraw.setGreenColor(risingColor);
        mMainDraw.setRedColor(FallingColor);
    }
    public void setMinuteWhiteColor(int color) {
        mMainDraw.setMinuteWhiteColor(color);
    }
    public void setShowMaxAndMin(boolean showMaxAndMin) {
        mMainDraw.setShowMaxAndMin(showMaxAndMin);
        invalidate();
    }
    public void setChangeIndex(boolean changeIndex) {
        changeIndex(changeIndex);
        invalidate();
    }
    public void setOnKChartViewEdgeListener(OnKChartViewEdgeListener onKChartViewEdgeListener) {
        this.mOnKChartViewEdgeListener = onKChartViewEdgeListener;
    }
    public void setMainIndicatorListData(List<Integer> list) {
        // todo
    }

    public void setSimpleChartDrawTextColor(int textColor) {
        mBBIDraw.setTextColor(textColor);
    }

    public interface KChartRefreshListener {
        /**
         * 加载更多
         *
         * @param chart
         */
        void onLoadMoreBegin(KChartView chart);
    }
}
