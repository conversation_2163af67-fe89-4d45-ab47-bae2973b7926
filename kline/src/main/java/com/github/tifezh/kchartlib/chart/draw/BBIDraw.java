package com.github.tifezh.kchartlib.chart.draw;

import android.content.Context;
import android.graphics.*;
import android.util.Pair;

import androidx.annotation.*;
import androidx.core.content.ContextCompat;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.EntityImpl.BbiImpl;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;
import com.github.tifezh.kchartlib.chart.impl.*;

import java.util.List;

/**
 * author：lvy
 * date：2025/02/27
 * desc：主图 BBI 指标
 */
public class BBIDraw extends SimpleChartDraw<BbiImpl> {

    private final Paint mTextPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
    private final Paint mLinePaint = new Paint(Paint.ANTI_ALIAS_FLAG);

    private final String bbiText = "BBI";
    private float bbiTextWidth = 0f;
    private final Context mContext;
    private Rect bbiRect;

    public BBIDraw(Context context) {
        mContext = context;
        float textSize = context.getResources().getDimension(R.dimen.chart_zhibiao_text_size);
        int textColor = ContextCompat.getColor(context, R.color.black); // FIXME: 2025/2/27 这里文字颜色需要做双主题适配
        float lineWidth = context.getResources().getDimension(R.dimen.chart_line_width);
        int lineColor = ContextCompat.getColor(context, R.color.kchartlib_bbi);

        mTextPaint.setColor(textColor);
        mTextPaint.setTextSize(textSize);

        mLinePaint.setColor(lineColor);
        mLinePaint.setStrokeWidth(lineWidth);
        mLinePaint.setStrokeCap(Paint.Cap.ROUND);
        mLinePaint.setTextSize(textSize);
    }

    @Override
    public void setTypeFace(Typeface typeFace) {
        if (typeFace == null) return;
        mTextPaint.setTypeface(typeFace);
        mLinePaint.setTypeface(typeFace);
    }

    @Override
    public void drawTranslated(@Nullable BbiImpl lastPoint, @NonNull BbiImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {

    }
    private float[] bbiLines;

    @Override
    public void drawOnScreen(Canvas canvas, @NonNull IKChartView view) {
//        super.drawOnScreen(canvas, view);
        List<Pair<Float, KLineImpl>> mPointList = view.getPointList();
        if (mPointList == null || mPointList.isEmpty()) {
            return;
        }
        int pointSize = mPointList.size();
        int pointsArrSize = pointSize * 4;

        if (bbiLines == null || bbiLines.length != pointsArrSize) {
            bbiLines = new float[pointsArrSize];
        }
        boolean isNeedDraw = false;
        int idx = 0;
        for (int i = 1; i < pointSize; i++) {
            Pair<Float, KLineImpl> lastPointPair = mPointList.get(i - 1);
            Pair<Float, KLineImpl> cuxPointPair = mPointList.get(i);

            Float lastX = lastPointPair.first;
            BbiImpl lastPoint = lastPointPair.second;
            Float curX = cuxPointPair.first;
            BbiImpl curPoint = cuxPointPair.second;

            if (lastPoint == null || curPoint == null) {
                continue;
            }
            bbiLines[idx++] = lastX;
            bbiLines[idx++] = view.getMainY(lastPoint.getBbi());
            bbiLines[idx++] = curX;
            bbiLines[idx++] = view.getMainY(curPoint.getBbi());
            isNeedDraw = true;
        }
        if (isNeedDraw) {
            //处理 lastPoint == null 或者 curPoint == null的情况
            if (pointsArrSize > idx) {
                canvas.drawLines(bbiLines, 0, idx, mLinePaint);
            } else {
                canvas.drawLines(bbiLines, mLinePaint);
            }
        }
    }

    @Override
    public void onDrawScreenLine(@Nullable BbiImpl lastPoint, @NonNull BbiImpl curPoint, float lastX, float curX, @NonNull Canvas canvas, @NonNull IKChartView view, int position) {
        if (lastPoint == null || curPoint == null) {
            return;
        }
        view.drawMainLine(canvas, mLinePaint, lastX, lastPoint.getBbi(), curX, curPoint.getBbi());
    }

    @Override
    public void drawText(@NonNull Canvas canvas, @NonNull IKChartView view, int position, float x, float y) {
        x += MainDraw.mTextMarginStart;
        BbiImpl point = (BbiImpl) view.getItem(position);

        if (bbiRect == null) {
            bbiRect = new Rect();
            mTextPaint.getTextBounds(bbiText, 0, bbiText.length(), bbiRect);
        }
        canvas.drawText(bbiText, x, y, mTextPaint);

        if (bbiTextWidth <= 0f) {
            bbiTextWidth = mLinePaint.measureText(bbiText);
        }
        x += bbiTextWidth + MainDraw.mTextMargin;
        String text = "BBI:" + view.formatValue(point.getBbi());
        canvas.drawText(text, x, y, mLinePaint);
    }

    @Override
    public float getMaxValue(BbiImpl point, int positon) {
        return Math.max(Float.MIN_VALUE, point.getBbi());
    }

    @Override
    public float getMinValue(BbiImpl point, int positon) {
        return Math.min(Float.MAX_VALUE, point.getBbi());
    }

    @Override
    public int getTextH() {
        return bbiRect.height();
    }

    public void setTextColor(@ColorInt int color) {
        mTextPaint.setColor(color);
    }

    public void setLineColor(@ColorInt int color) {
        mLinePaint.setColor(color);
    }

    public void setTextSize(float textSize) {
        mTextPaint.setTextSize(textSize);
        mLinePaint.setTextSize(textSize);
    }

    public void setLineWidth(int lineWidth) {
        mLinePaint.setStrokeWidth(lineWidth);
    }
}
