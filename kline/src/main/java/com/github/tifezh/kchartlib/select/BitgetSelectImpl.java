package com.github.tifezh.kchartlib.select;

import android.text.TextUtils;
import android.util.Log;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;

/**
 * author : wanghui
 * date : 2020/7/25 4:22 PM
 * description :
 */
public class BitgetSelectImpl implements ISelect, ISelectGestureListener {
    private static final String TAG = "BitgetSelectImpl";

    //选中线没有状态
    protected final static String SHOW_SELECTED_TYPE_NO = "show_Selected_type_no";
    //选中线处于点击状态
    protected final static String SHOW_SELECTED_TYPE_CLICK = "show_Selected_type_click";
    //选中线处于长按状态
    protected final static String SHOW_SELECTED_TYPE_LONG_PRESS = "show_Selected_type_long_press";
    //选中线的状态
    protected String showSelectedType = SHOW_SELECTED_TYPE_NO;

    private ISelectCallBack callBack;

    public BitgetSelectImpl(ISelectCallBack callBack) {
        this.callBack = callBack;
    }

    //切换选中类型
    protected void changeShowSelectedType(String type, MotionEvent event) {
        showSelectedType = type;
        if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_NO)) {
            onUnSelected();
        } else {
            onSelected(event);
        }
    }

    @Override
    public void onSelected(MotionEvent event) {
        if (callBack != null) {
            callBack.showSelectedLine(event);
        }
    }

    @Override
    public void onUnSelected() {
        if (callBack != null) {
            callBack.showUnSelectedLine();
        }
    }

    private boolean checkSelected(MotionEvent event) {
        return callBack.checkSelected(event);
    }

    @Override
    public void setUnSelected() {
        onUnSelected();
    }

    @Override
    public boolean isSelected() {
        return !SHOW_SELECTED_TYPE_NO.equals(showSelectedType);
    }

    @Override
    public boolean isAllowOtherEvnent() {
        return SHOW_SELECTED_TYPE_LONG_PRESS.equals(showSelectedType);
    }

    @Override
    public boolean onDown(MotionEvent e) {
        return false;
    }

    @Override
    public boolean onSingleTapUp(MotionEvent e) {
        if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_NO)) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_CLICK, e);
        } else {
            boolean selected = checkSelected(e);
            if (selected) {
                changeShowSelectedType(SHOW_SELECTED_TYPE_CLICK, e);
            } else {
                changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
            }
        }

        return false;
    }

    @Override
    public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
        if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_LONG_PRESS)) {
//            onSelected(e2);
            return true;
        } else if (TextUtils.equals(showSelectedType, SHOW_SELECTED_TYPE_CLICK)) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
            return false;
        } else {
            return false;
        }
    }

    @Override
    public void onLongPress(MotionEvent e) {
        if (e.getPointerCount() == 1) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_LONG_PRESS, e);

            if (callBack != null) {
                callBack.disallowIntercept();
            }
        }
    }

    @Override
    public boolean onScale(ScaleGestureDetector detector) {
        if (isSelected()) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
        }
        return false;
    }

    @Override
    public boolean onTouchEvent(MotionEvent e) {
        if (isSelected() && e.getPointerCount() > 1) {
            changeShowSelectedType(SHOW_SELECTED_TYPE_NO, null);
        }
        switch (e.getAction()) {
//            case MotionEvent.ACTION_DOWN:
//                mDownX = e.getX();
//                break;
            case MotionEvent.ACTION_DOWN:
                //长按状态时，重新触发actionDown，改为点击状态保持十字线为最后停留的位置，后续重新计算滑动状态，
                if (isAllowOtherEvnent()) {
                    changeShowSelectedType(SHOW_SELECTED_TYPE_CLICK, null);
                }
                break;
            case MotionEvent.ACTION_MOVE:
                if (SHOW_SELECTED_TYPE_LONG_PRESS.equals(showSelectedType)) {
                    //需要检查
//                    if (mCheckShouldShowMove) {
//                        //大于指定值
//                        if (e.getX() - mDownX > 2) {
//                            mCheckShouldShowMove = false;
                    onSelected(e);
//                        }
                }
                break;
        }
        return false;
    }
}
