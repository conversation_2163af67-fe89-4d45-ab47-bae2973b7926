package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;

import com.example.myapplication.R;
import com.github.tifezh.kchartlib.chart.EntityImpl.KLineImpl;

import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;


/**
 * K线实体
 */
@Keep
public class KLineEntity implements KLineImpl {
    public final static long START_XVALUE = Long.MAX_VALUE / 1000000000;
    public float lastPrice;
    public float lastClosePrice;
    public float avPrice;
    // 蜡烛数据 ------------------------------------
    public String timestamp;
    public long timestampLong = 0L; //用于缓存时间戳转换之后的值，提高性能
    public String timestampFormat; //用于缓存时间戳转换之后的值，提高性能
    public float open;
    public float high;
    public float low;
    public float close;
    public Double prevClose;
    public float volume;
    public long period;
    // ---------------------------------------------

    public long xValue;

    public MaIndexEntity maIndexEntity = new MaIndexEntity();
    public EmaIndexEntity emaIndexEntity = new EmaIndexEntity();

    //boll使用

    public BoolMaIndexEntity boolMaIndexEntity = new BoolMaIndexEntity();

    // 主图 boll
    public BoolIndexEntity boolIndexEntity = new BoolIndexEntity();
    // 主图 bbi
    public BbiIndexEntity bbiIndexEntity = new BbiIndexEntity();
    // 主图 mike
    public MikeIndexEntity mikeIndexEntity = new MikeIndexEntity();
    // 主图 sar
    public SarIndexEntity sarIndexEntity = new SarIndexEntity();

//    public float EMA5Price;
//    public float EMA10Price;
//    public float EMA30Price;

    // MACD
    public MacdIndexEntity macdIndexEntity = new MacdIndexEntity();
    // KDJ
    public KdjIndexEntity kdjIndexEntity = new KdjIndexEntity();
    // RSI
    public RsiIndexEntity rsiIndexEntity = new RsiIndexEntity();
    // VOL
    public VolIndexEntity volIndexEntity = new VolIndexEntity();
    // WR
    public WrIndexEntity wrIndexEntity = new WrIndexEntity();
    // ROC
    public RocIndexEntity rocIndexEntity = new RocIndexEntity();
    // CCI
    public CciIndexEntity cciIndexEntity = new CciIndexEntity();
    // DMI
    public DmiIndexEntity dmiIndexEntity = new DmiIndexEntity();


    public String getDatetime() {
        return timestamp;
    }

    public String getDateFormat() {
        return timestampFormat;
    }

    public long getDatetimeLong() {
        if (timestampLong <= 0L) {
            try {
                timestampLong = Long.parseLong(timestamp);
            } catch (Exception e) {
                timestampLong = 0L;
            }
        }
        return timestampLong;
    }

    public float getOpenPrice() {
        return open;
    }

    public float getHighPrice() {
        return high;
    }

    public float getLowPrice() {
        return low;
    }

    public float getClosePrice() {
        return close;
    }

    @Override
    public float getVOL() {
        return volume;
    }

    public float getMA20Price() {
        return boolMaIndexEntity.getMA20Price();
    }


    public float getDea() {
        return macdIndexEntity.dea;
    }

    public float getDif() {
        return macdIndexEntity.dif;
    }

    public float getMacd() {
        return macdIndexEntity.macd;
    }

    public float getK() {
        return kdjIndexEntity.k;
    }

    public float getD() {
        return kdjIndexEntity.d;
    }

    public float getJ() {
        return kdjIndexEntity.j;
    }

    public float getRsi() {
        return rsiIndexEntity.rsi;
    }

    public float getUp() {
        return boolIndexEntity.getUp();
    }

    public float getMb() {
        return boolIndexEntity.getMb();
    }

    public float getDn() {
        return boolIndexEntity.getDn();
    }

    @Override
    public float getVMA5() {
        return volIndexEntity.vMA5;
    }

    @Override
    public float getVMA10() {
        return volIndexEntity.vMA10;
    }

    public boolean isMinDraw = false;

    @Override
    public float getCloseDif() {
        if (isMinDraw) {
            return close - lastPrice;// TODO wj lastPrice 无效
        } else {
            return close - open;
        }
    }

    @Override
    public boolean getRate() {
        return close - lastClosePrice >= 0;
    }


    @Override
    public float getWR() {
        return wrIndexEntity.wr;
    }

    @Override
    public float getRoc() {
        return rocIndexEntity.roc;
    }

    @Override
    public float getRocMA() {
        return rocIndexEntity.rocMA;
    }

    @Override
    public float getCCI() {
        return cciIndexEntity.cci;
    }

    //    @Override
//    public float getEMA5Price() {
//        return EMA5Price;
//    }
//
//    @Override
//    public float getEMA10Price() {
//        return EMA10Price;
//    }
//
//    @Override
//    public float getEMA30Price() {
//        return EMA30Price;
//    }


    @Override
    public List<Float> getMAs() {
        return maIndexEntity.getMas();
    }

    @Override
    public List<Float> getEMAs() {
        return emaIndexEntity.getEmas();
    }

    @Override
    public String toString() {
        return "KLineEntity{" +
                "Open=" + open +
                ", High=" + high +
                ", Low=" + low +
                ", Close=" + close +
                ", timestamp=" + timestamp +
                '}';
    }

    @Override
    public float getBbi() {
        return bbiIndexEntity.getBbi();
    }

    @Override
    public List<Float> getMikes() {
        return mikeIndexEntity.getMikes();
    }

    @Override
    public float getSar() {
        return sarIndexEntity.getSar();
    }

    @Override
    public float getDMIPlus() {
        return dmiIndexEntity.plusDMI;
    }

    @Override
    public float getDMIMinus() {
        return dmiIndexEntity.minusDMI;
    }

    @Override
    public float getADXR() {
        return dmiIndexEntity.adxr;
    }

    @Override
    public float getADX() {
        return dmiIndexEntity.adx;
    }

    @Override
    public float getDX() {
        return dmiIndexEntity.dx;
    }
}
