package com.github.tifezh.kchartlib.helper.bean;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

@Keep
public class RocIndexEntity extends BaseIndexEntity {
    // 主图 boll
    public float roc;
    public float rocMA;

    @Override
    public void reset() {
        super.reset();
        roc = 0f;
        rocMA = 0f;
    }

    public void setRoc(float roc, float rocMA) {
        this.roc = roc;
        this.rocMA = rocMA;
        setComputed(true);
    }

    public float getRoc() {
        return roc;
    }
    public float getRocMA() {
        return rocMA;
    }

    @NonNull
    @Override
    public String toString() {
        return "ROCIndexEntity{" +
                "roc=" + roc +
                ", rocMA=" + rocMA +
                '}';
    }
}