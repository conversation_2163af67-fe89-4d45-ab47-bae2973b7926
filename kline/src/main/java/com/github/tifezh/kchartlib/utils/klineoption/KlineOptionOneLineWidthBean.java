package com.github.tifezh.kchartlib.utils.klineoption;

/**
 * author : wanghui
 * date : 2020/7/2 4:27 PM
 * description :
 */
public class KlineOptionOneLineWidthBean implements Cloneable{
    private int lineWidth;
    private int defaultLineWidth;

    public KlineOptionOneLineWidthBean(int lineWidth, int defaultLineWidth) {
        this.lineWidth = lineWidth;
        this.defaultLineWidth = defaultLineWidth;
    }

    public int getLineWidth() {
        return lineWidth;
    }

    public void setLineWidth(int lineWidth) {
        this.lineWidth = lineWidth;
    }

    public int getDefaultLineWidth() {
        return defaultLineWidth;
    }

    public void setDefaultLineWidth(int defaultLineWidth) {
        this.defaultLineWidth = defaultLineWidth;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        KlineOptionOneLineWidthBean widthBean = (KlineOptionOneLineWidthBean) o;

        if (lineWidth != widthBean.lineWidth) return false;
        return defaultLineWidth == widthBean.defaultLineWidth;
    }

    @Override
    public int hashCode() {
        int result = lineWidth;
        result = 31 * result + defaultLineWidth;
        return result;
    }

    @Override
    public KlineOptionOneLineWidthBean clone() throws CloneNotSupportedException {
        return (KlineOptionOneLineWidthBean) super.clone();
    }
}
