<?xml version="1.0" encoding="utf-8"?><!--
 ~ Copyright (C) 2017 <PERSON>.
 ~
 ~ Licensed under the Apache License, Version 2.0 (the "License");
 ~ you may not use this file except in compliance with the License.
 ~ You may obtain a copy of the License at
 ~
 ~      http://www.apache.org/licenses/LICENSE-2.0
 ~
 ~ Unless required by applicable law or agreed to in writing, software
 ~ distributed under the License is distributed on an "AS IS" BASIS,
 ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 ~ See the License for the specific language governing permissions and
 ~ limitations under the License.
 -->
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground">

    <TextView
        android:id="@+id/code"
        android:layout_width="40dp"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:padding="2dp"
        android:textAppearance="@style/Chuck.TextAppearance.ListItem"
        tools:text="200" />

    <TextView
        android:id="@+id/path"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@id/code"
        android:layout_toEndOf="@id/code"
        android:padding="2dp"
        android:textAppearance="@style/Chuck.TextAppearance.ListItem"
        tools:text="GET /path/to/some/resource?goes=here" />

    <TextView
        android:textColor="#337CC4"
        android:layout_below="@+id/path"
        android:id="@+id/className"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@id/code"
        android:layout_toEndOf="@id/code"
        android:padding="2dp"
        tools:text="BatchRequest" />

    <TextView
        android:textColor="#CC99C4"
        android:layout_below="@+id/className"
        android:id="@+id/mActionInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toRightOf="@id/code"
        android:layout_toEndOf="@id/code"
        android:padding="2dp"
        tools:text="actionParams" />

    <TextView
        android:id="@+id/host"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/mActionInfo"
        android:layout_toRightOf="@id/code"
        android:layout_toEndOf="@id/code"
        android:padding="2dp"
        tools:text="example.com" />

    <ImageView
        android:id="@+id/ssl"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_toRightOf="@id/host"
        android:layout_toEndOf="@id/host"
        android:layout_below="@id/mActionInfo"
        android:layout_marginTop="4dp"
        android:contentDescription="@string/kit_chuck_ssl"
        android:src="@drawable/kit_chuck_ic_https_grey_24dp" />

    <LinearLayout
        android:id="@+id/meta"
        android:layout_below="@id/host"
        android:layout_marginLeft="40dp"
        android:layout_marginStart="40dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/start"
            android:layout_width="72dp"
            android:layout_height="wrap_content"
            android:padding="2dp"
            tools:text="18:29:07" />

        <TextView
            android:id="@+id/duration"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:padding="2dp"
            android:gravity="end"
            tools:text="8023 ms" />

        <TextView
            android:id="@+id/size"
            android:layout_width="80dp"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:padding="2dp"
            tools:text="16.45 KB" />

    </LinearLayout>

</RelativeLayout>