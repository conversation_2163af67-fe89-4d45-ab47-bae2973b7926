<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_content"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="1dp">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageView
            android:id="@+id/close"
            android:layout_width="5dp"
            android:layout_height="5dp"
            android:layout_gravity="center_vertical"
            android:background="#000"
            android:visibility="gone" />

        <TextView
            android:id="@+id/package_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="start"
            android:textColor="#555555"

            android:textSize="6sp"
            tools:text="packageNamepackageNamepackageNamepackageName" />

    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:layout_marginTop="2dp"
        android:background="#777777"
        android:visibility="gone" />

    <TextView
        android:id="@+id/class_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="start"
        android:background="#55ffffff"
        android:textColor="#333333"
        android:textSize="10sp"
        tools:text="ClassName \nClassName \nClassName" />


</LinearLayout>
