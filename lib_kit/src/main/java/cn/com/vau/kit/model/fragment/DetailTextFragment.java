package cn.com.vau.kit.model.fragment;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.activity.UserInfoActivity;
import cn.com.vau.kit.chunk.internal.view.ExpandCombineLayout;
import cn.com.vau.kit.constant.Constants;
import cn.com.vau.kit.util.KitMainHandler;



public class DetailTextFragment extends BaseKitFragment {


    private String textValue;

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        Bundle arguments = getArguments();
        if (arguments != null) {
            textValue = arguments.getString(Constants.BUNDLE_STRING_KEY);
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.kit_detail_text_fragment, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        final ExpandCombineLayout mExpandCombineView = view.findViewById(R.id.mExpandCombineView);
        ExpandCombineLayout.invalidateView2(getBaseActivity(), textValue, mExpandCombineView, true);
    }
}