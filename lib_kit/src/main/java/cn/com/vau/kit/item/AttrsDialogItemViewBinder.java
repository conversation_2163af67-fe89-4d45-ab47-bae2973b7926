package cn.com.vau.kit.item;

import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.view.KitAttrsDialog;

public abstract class AttrsDialogItemViewBinder<T extends Item, VH extends KitAttrsDialog.Adapter.BaseViewHolder<T>> implements ItemViewBinder<T, VH> {

    protected KitAttrsDialog.AttrDialogCallback getAttrDialogCallback(RecyclerView.Adapter adapter) {
        KitAttrsDialog.AttrDialogCallback callback = null;
        if (adapter instanceof KitAttrsDialog.Adapter) {
            callback = ((KitAttrsDialog.Adapter) adapter).getAttrDialogCallback();
        }
        return callback;
    }
}
