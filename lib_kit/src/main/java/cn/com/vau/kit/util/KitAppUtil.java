package cn.com.vau.kit.util;

import android.app.Activity;
import android.content.Intent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;

import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.activity.KITUITransparentActivity;
import cn.com.vau.kit.manager.UETool;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class KitAppUtil {

    public static void open(@KITUITransparentActivity.Type int type) {
        Activity currentTopActivity = getCurrentActivity();
        if (currentTopActivity == null) {
            return;
        } else if (currentTopActivity.getClass() == KITUITransparentActivity.class) {
            currentTopActivity.finish();
            return;
        }
        Intent intent = new Intent(currentTopActivity, KITUITransparentActivity.class);
        intent.putExtra(KITUITransparentActivity.EXTRA_TYPE, type);
        currentTopActivity.startActivity(intent);
        currentTopActivity.overridePendingTransition(0, 0);
        UETool.getInstance().setTargetActivity(currentTopActivity);
    }

    public static Activity getCurrentActivity() {
        try {
            Class activityThreadClass = Class.forName("android.app.ActivityThread");
            Method currentActivityThreadMethod = activityThreadClass.getMethod("currentActivityThread");
            Object currentActivityThread = currentActivityThreadMethod.invoke(null);
            Field mActivitiesField = activityThreadClass.getDeclaredField("mActivities");
            mActivitiesField.setAccessible(true);
            Map activities = (Map) mActivitiesField.get(currentActivityThread);
            for (Object record : activities.values()) {
                Class recordClass = record.getClass();
                Field pausedField = recordClass.getDeclaredField("paused");
                pausedField.setAccessible(true);
                if (!(boolean) pausedField.get(record)) {
                    Field activityField = recordClass.getDeclaredField("activity");
                    activityField.setAccessible(true);
                    return (Activity) activityField.get(record);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    //  获取当前 fragment 类名
    @Nullable
    public static String getCurrentFragmentName(View targetView) {

        Fragment fragment = getCurrentFragment(targetView);

        if (fragment != null) {
            return fragment.getClass().getName();
        }

        return null;
    }



    //  获取当前 view 所在的最上层 fragment
    @Nullable
    public static Fragment getCurrentFragment(View targetView) {

        Activity activity = UETool.getInstance().getTargetActivity();
        if (activity instanceof FragmentActivity) {
            List<Fragment> fragments = collectVisibleFragment(((FragmentActivity) activity).getSupportFragmentManager());
            for (int i = fragments.size() - 1; i >= 0; i--) {
                Fragment fragment = fragments.get(i);
                if (findTargetView(fragment.getView(), targetView)) {
                    return fragment;
                }
            }
        }

        return null;
    }

    //  收集所有可见 fragment
    private static List<Fragment> collectVisibleFragment(FragmentManager fragmentManager) {
        List<Fragment> fragments = new ArrayList<>();

        for (Fragment fragment : fragmentManager.getFragments()) {
            if (fragment.isVisible()) {
                fragments.add(fragment);
                fragments.addAll(collectVisibleFragment(fragment.getChildFragmentManager()));
            }
        }

        return fragments;
    }

    //  获取当前 view 的 view holder 类名
    public static String getViewHolderName(View targetView) {
        View currentView = targetView;
        while (currentView != null) {
            ViewParent parent = currentView.getParent();
            if (parent instanceof RecyclerView) {
                return ((RecyclerView) parent).getChildViewHolder(currentView).getClass().getName();
            }
            currentView = parent instanceof View ? (View) parent : null;
        }
        return null;
    }

    //  遍历目标 view 是否在指定 view 内
    private static boolean findTargetView(View view, View targetView) {
        if (view == targetView) {
            return true;
        }
        if (view instanceof ViewGroup) {
            ViewGroup parent = (ViewGroup) view;
            for (int i = 0; i < parent.getChildCount(); i++) {
                if (findTargetView(parent.getChildAt(i), targetView)) {
                    return true;
                }
            }
        }
        return false;
    }
}
