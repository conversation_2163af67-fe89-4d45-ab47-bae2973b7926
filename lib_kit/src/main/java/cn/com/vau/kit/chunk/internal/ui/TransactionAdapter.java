/*
 * Copyright (C) 2017 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.com.vau.kit.chunk.internal.ui;

import android.content.Context;
import android.database.Cursor;
import android.graphics.Color;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.text.style.RelativeSizeSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Keep;
import androidx.cursoradapter.widget.CursorAdapter;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.biz.APiFilterBean;
import cn.com.vau.kit.biz.AppCallBackItf;
import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.chunk.internal.data.HttpTransaction;
import cn.com.vau.kit.chunk.internal.data.LocalCupboard;
import cn.com.vau.kit.chunk.internal.listener.OnListFragmentInteractionListener;
import cn.com.vau.kit.view.ApiGroupFilterDialog;

import java.util.List;

@Keep
class TransactionAdapter extends RecyclerView.Adapter<TransactionAdapter.ViewHolder> {

    private final Context context;
    private final OnListFragmentInteractionListener listener;
    private final CursorAdapter cursorAdapter;

    private final int colorDefault;
    private final int color400;

    TransactionAdapter(Context context, OnListFragmentInteractionListener listener) {
        this.listener = listener;
        this.context = context;
        colorDefault = Color.parseColor("#212121");
        color400 = Color.parseColor("#FF9800");

        cursorAdapter = new CursorAdapter(TransactionAdapter.this.context, null, CursorAdapter.FLAG_REGISTER_CONTENT_OBSERVER) {
            @Override
            public View newView(Context context, Cursor cursor, ViewGroup parent) {
                View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.kit_chuck_list_item_transaction, parent, false);
                ViewHolder holder = new ViewHolder(itemView);
                itemView.setTag(holder);
                return itemView;
            }

            @Override
            public void bindView(View view, final Context context, Cursor cursor) {
                final HttpTransaction transaction = LocalCupboard.getInstance().withCursor(cursor).get(HttpTransaction.class);
                final ViewHolder holder = (ViewHolder) view.getTag();



                if (TextUtils.isEmpty(transaction.getActionParams())) {
                    holder.mActionInfo.setVisibility(View.GONE);
                }else {
                    holder.mActionInfo.setVisibility(View.VISIBLE);
                    holder.mActionInfo.setText("action: "+transaction.getActionParams());
                }

                holder.className.setText(transaction.getRequestSimpleClassName());
                holder.host.setText(transaction.getHost());
                holder.start.setText(transaction.getRequestStartTimeString());
                holder.ssl.setVisibility(transaction.isSsl() ? View.VISIBLE : View.GONE);
                if (transaction.getStatus() == HttpTransaction.Status.Complete) {
                    holder.code.setText(String.valueOf(transaction.getResponseCode()));
                    holder.duration.setText(transaction.getDurationString());
                    holder.size.setText(transaction.getTotalSizeString());
                } else {
                    holder.code.setText(null);
                    holder.duration.setText(null);
                    holder.size.setText(null);
                }
                if (transaction.getStatus() == HttpTransaction.Status.Failed) {
                    holder.code.setText("!!!");
                }


                setStatusColor(holder, transaction);

                holder.transaction = transaction;
                holder.view.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        if (null != TransactionAdapter.this.listener) {
                            TransactionAdapter.this.listener.onListFragmentInteraction(holder.transaction);
                        }
                    }
                });
            }

            private void setStatusColor(ViewHolder holder, HttpTransaction transaction) {

                String value = transaction.getMethod() + " " + transaction.getPath();

                AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
                if (appCallBackItf != null) {
                    if ("GET".equals(transaction.getMethod())) {
                        value = appCallBackItf.decryHttpGetParams(value, transaction.getPath());
                    } else {
                        value = appCallBackItf.decryHttpPostParam(value, transaction.getPath());
                    }

                }

                holder.path.setText(value);

                if (transaction.isBusinessSuccess()) {
                    holder.code.setTextColor(colorDefault);
                    holder.path.setTextColor(colorDefault);

                    String path = transaction.getPath();
                    if (path.contains("batchRunV2")) {

                        String batchErrorPart = transaction.getBatchErrorPart();
                        if (batchErrorPart != null) {
                            String[] splitError = batchErrorPart.split(",");
                            holder.path.setText(getHighLightUrl(value, splitError));
                        }
                    } else {
                        holder.path.setText(getHighLightUrl(value, null));
                    }
                } else {

                    holder.code.setTextColor(color400);
                    holder.path.setTextColor(color400);
                    holder.path.setText(getHighLightUrl(value, null));

                }


            }
        };
    }

    private SpannableString getHighLightUrl(String value, String[] errorPath) {

        //过滤掉client信息，显示信息太多了
        int cStartIndex = value.indexOf("client_info={");
        if (cStartIndex > -1) {
            value = value.substring(0, cStartIndex);
        }

        SpannableString spannableString = new SpannableString(value);
        if (!TextUtils.isEmpty(filterValue)) {
            final int index = value.indexOf(filterValue);
            if (index >= 0) {
                int startIndex = index;
                int endIndex = startIndex + filterValue.length();
                spannableString.setSpan(new ForegroundColorSpan(Color.RED), index, endIndex, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }

        if (errorPath != null) {
            for (String err : errorPath) {
                int startIndex = value.indexOf(err);
                if (startIndex == -1) continue;
                int endIndex = startIndex + err.length();
                spannableString.setSpan(new ForegroundColorSpan(Color.RED), startIndex, endIndex, SpannableString.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        }

        //分组筛选的接口数据加粗
        APiFilterBean selectFilterBean = ApiGroupFilterDialog.getSelectFilterBean();
        if (selectFilterBean != null) {
            List<APiFilterBean.APiUrlBean> aPiUrlBeans = selectFilterBean.getaPiUrlList();
            if (aPiUrlBeans != null) {
                for (APiFilterBean.APiUrlBean aPiUrlBean : aPiUrlBeans) {
                    if (aPiUrlBean == null) continue;
                    String likeUrl = aPiUrlBean.getLikeUrl();
                    if (TextUtils.isEmpty(likeUrl)) continue;
                    String highlightText = likeUrl;
                    if (likeUrl.startsWith("%") && likeUrl.endsWith("%")) {
                        highlightText = highlightText.substring(1, likeUrl.length() - 1);
                    }
                    if (value.contains(highlightText)) {
                        int startIndex = value.indexOf(highlightText);
                        int endIndex = startIndex + highlightText.length();
                        spannableString.setSpan(new RelativeSizeSpan(1.3f), startIndex, endIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
                    }
                }
            }
        }

        return spannableString;
    }

    @Override
    public int getItemCount() {
        return cursorAdapter.getCount();
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        cursorAdapter.getCursor().moveToPosition(position);
        cursorAdapter.bindView(holder.itemView, context, cursorAdapter.getCursor());
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View v = cursorAdapter.newView(context, cursorAdapter.getCursor(), parent);
        return new ViewHolder(v);
    }

    void swapCursor(Cursor newCursor) {
        cursorAdapter.swapCursor(newCursor);
        notifyDataSetChanged();
    }

    private String filterValue;

    public void setFilterValue(String filterValue) {
        this.filterValue = filterValue;
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        public final View view;
        public final TextView code;
        public final TextView path;
        public final TextView host;
        public final TextView start;
        public final TextView duration;
        public final TextView size;
        public final TextView className;
        public final TextView mActionInfo;
        public final ImageView ssl;
        HttpTransaction transaction;

        ViewHolder(View view) {
            super(view);
            this.view = view;
            code = (TextView) view.findViewById(R.id.code);
            path = (TextView) view.findViewById(R.id.path);
            host = (TextView) view.findViewById(R.id.host);
            start = (TextView) view.findViewById(R.id.start);
            size = (TextView) view.findViewById(R.id.size);
            ssl = (ImageView) view.findViewById(R.id.ssl);
            className = (TextView) view.findViewById(R.id.className);
            mActionInfo = (TextView) view.findViewById(R.id.mActionInfo);
            duration = (TextView) view.findViewById(R.id.duration);
        }
    }
}
