package cn.com.vau.kit.adapter.appinfo;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.adapter.ViewAndHolderItem;
import cn.com.vau.kit.adapter.base.BaseKitHolder;
import cn.com.vau.kit.bean.AppInfo;



public class AppTitleHolderView implements ViewAndHolderItem<AppInfo, AppTitleHolderView.TitleHolder> {


    @Override
    public boolean isSupport(AppInfo appInfo) {
        return appInfo.getType()==AppInfo.TYPE_TITLE;
    }

    @Override
    public TitleHolder createHolder(View convertView) {

        return new TitleHolder(convertView);
    }


    @Override
    public View createConvertView() {
        return LayoutInflater.from(Kit.getInstance().getApplication()).inflate(R.layout.kit_app_info_title_item, null);
    }

    public static class TitleHolder extends BaseKitHolder<AppInfo> {

        private TextView mTitleView;

        public TitleHolder(View convertView) {
            mTitleView = convertView.findViewById(R.id.mTitle);
        }

        @Override
        protected void initValue(AppInfo appInfo) {
            mTitleView.setText(appInfo.getTitle());
        }

    }
}
