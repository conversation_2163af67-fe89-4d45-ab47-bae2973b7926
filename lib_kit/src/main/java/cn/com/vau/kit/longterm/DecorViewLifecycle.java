package cn.com.vau.kit.longterm;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * 模拟长文本，decorView生命周期处理
 *
 */
public class DecorViewLifecycle implements Application.ActivityLifecycleCallbacks {

    private OnLongTermTouchEventCallback callback;
    private Activity topActivity;

    public DecorViewLifecycle(OnLongTermTouchEventCallback callback) {
        this.callback = callback;
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        topActivity = activity;
        ViewGroup decorViewGroup = (ViewGroup) activity.getWindow().getDecorView();
        if (decorViewGroup != null) {
            if (decorViewGroup.getChildCount() > 0 && !isReplace(decorViewGroup)) {
                ProxyContentLayout proxyFrameLayout = new ProxyContentLayout(activity, callback);
                int size = decorViewGroup.getChildCount();
                for (int i = 0; i < size; i++) {
                    View view = decorViewGroup.getChildAt(i);
                    if (view != null) {
                        ViewGroup.LayoutParams lp = view.getLayoutParams();
                        decorViewGroup.removeView(view);
                        proxyFrameLayout.addView(view, lp);
                    }
                }
                // 替换掉DecorView原有的contentParent容器
                decorViewGroup.addView(proxyFrameLayout);
            }
        }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {
    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {
    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        if (callback != null) {
            callback.onActivityDestroyed(activity.getClass().getCanonicalName());
        }
    }

    public Activity getTopActivity() {
        return topActivity;
    }

    private boolean isReplace(ViewGroup parentView) {
        int childCount = parentView.getChildCount();

        for (int i = 0; i < childCount; i++) {
            View child = parentView.getChildAt(i);
            if (child.getClass() == ProxyContentLayout.class) {
                return true;
            }
        }
        return false;
    }

}
