package cn.com.vau.kit.util;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import cn.com.vau.kit.biz.AppCallBackItf;
import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.model.fragment.SwitchConfigFragment;

import java.lang.ref.WeakReference;
import java.util.List;



public class H5CoverUtil {


    private WeakReference<Activity> weakReference;


    private void setReference(Activity activity) {
        weakReference = new WeakReference<>(activity);
    }


    private Activity getReference() {
        if (weakReference != null) {
            return weakReference.get();
        }
        return null;
    }

    /**
     * 检测是否h5页面
     */
    public void checkIsH5Activity(Activity mAct) {

        setReference(mAct);

        Activity activity = getReference();
        if (activity == null) {
            return;
        }

        boolean isH5Guide = SwitchConfigFragment.isOpenH5WaterMark();
        ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
        //检测是否h5页面
        if (isH5Guide && checkIsWebViewActivity(activity)) {
            //判断当前viewGroup里面是否有封面
            int childCount = decorView.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View child = decorView.getChildAt(i);
                if (child == null) continue;
                if (mH5CoverView(activity) == child) return;
            }

            //从父容器里面删除
            removeParent(activity);
            //添加封面到顶部view
            decorView.addView(mH5CoverView(activity));
        } else {
            //删除h5封面提示
            decorView.removeView(mH5CoverView(activity));
        }
    }


    /**
     * 从父容器里面删除
     *
     * @param activity
     */
    private void removeParent(Activity activity) {
        ViewGroup decorView = (ViewGroup) activity.getWindow().getDecorView();
        int childCount = decorView.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = decorView.getChildAt(i);
            if (child == null) continue;
            Object tag = child.getTag();
            if (tag != null) {
                if (tag.toString().equals(TAG)) {
                    decorView.removeView(child);
                    break;
                }
            }
        }
    }

    private static final String TAG = "H5CoverUtil";

    private View mH5CoverView(Context context) {
        View mH5TextView = LayoutInflater.from(context).inflate(R.layout.kit_h5_cover, null);
        mH5TextView.setClickable(false);
        mH5TextView.setOnClickListener(null);
        mH5TextView.setOnTouchListener(null);
        mH5TextView.setTag(TAG);
        return mH5TextView;
    }

    /**
     * 检测是否h5页面
     *
     * @param activity
     * @return
     */
    private boolean checkIsWebViewActivity(Activity activity) {
        if (activity == null) return false;
        String actSimpleName = activity.getClass().getSimpleName();

        AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
        if (appCallBackItf != null) {
            List<Class<?>> webViewActivityList = appCallBackItf.getWebViewActivityList();
            if (webViewActivityList != null) {
                for (Class<?> aClass : webViewActivityList) {
                    String simpleName = aClass.getSimpleName();
                    if (TextUtils.equals(actSimpleName, simpleName)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

} 