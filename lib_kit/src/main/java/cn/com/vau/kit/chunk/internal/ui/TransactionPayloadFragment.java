/*
 * Copyright (C) 2017 <PERSON>.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package cn.com.vau.kit.chunk.internal.ui;

import android.graphics.Color;
import android.os.Bundle;
import android.text.Html;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.Keep;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import cn.com.vau.kit.biz.AppCallBackItf;
import cn.com.vau.kit.Kit;
import cn.com.vau.kit.R;
import cn.com.vau.kit.chunk.internal.data.HttpTransaction;
import cn.com.vau.kit.chunk.internal.view.ExpandCombineLayout;

import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.List;

@Keep
public class TransactionPayloadFragment extends Fragment implements TransactionFragment {

    public static final int TYPE_REQUEST = 0;
    public static final int TYPE_RESPONSE = 1;

    private static final String ARG_TYPE = "type";

    TextView headers;
    TextView mTextView;
    ExpandCombineLayout mExpandCombineLayout;

    private int type;
    private HttpTransaction transaction;

    public TransactionPayloadFragment() {
    }

    public static TransactionPayloadFragment newInstance(int type) {
        TransactionPayloadFragment fragment = new TransactionPayloadFragment();
        Bundle b = new Bundle();
        b.putInt(ARG_TYPE, type);
        fragment.setArguments(b);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        type = getArguments().getInt(ARG_TYPE);
        setRetainInstance(true);
    }

    public static String format(String jsonStr) {
        int level = 0;
        StringBuffer jsonForMatStr = new StringBuffer();
        for (int i = 0; i < jsonStr.length(); i++) {
            char c = jsonStr.charAt(i);
            if (level > 0 && '\n' == jsonForMatStr.charAt(jsonForMatStr.length() - 1)) {
                jsonForMatStr.append(getLevelStr(level));
            }
            switch (c) {
                case '{':
                case '[':
                    jsonForMatStr.append(c + "\n");
                    level++;
                    break;
                case ',':
                    jsonForMatStr.append(c + "\n");
                    break;
                case '}':
                case ']':
                    jsonForMatStr.append("\n");
                    level--;
                    jsonForMatStr.append(getLevelStr(level));
                    jsonForMatStr.append(c);
                    break;
                default:
                    jsonForMatStr.append(c);
                    break;
            }
        }

        return jsonForMatStr.toString();

    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        populateUI();
    }

    @Override
    public void transactionUpdated(HttpTransaction transaction) {
        this.transaction = transaction;
        if (hasAdd) return;
        populateUI();
        hasAdd = true;
    }

    private boolean hasAdd;


    private SpannableString getSplitText(String decodeUrl) {
        if (decodeUrl == null) return null;
        //构建新的数据
        final StringBuilder stringBuilder = new StringBuilder();

        SpannableString spannableString = null;
        try {
            //存储高亮索引
            final List<TransactionOverviewFragment.IndexBean> indexBeanList = new ArrayList<>();

            String[] split = decodeUrl.split("&");
            int length = split.length;
            for (int i = 0; i < length; i++) {
                String item = split[i];
                String[] param = item.split("=");
                if (param.length == 2) {
                    String key = param[0];
                    String value = param[1];
                    indexBeanList.add(TransactionOverviewFragment.IndexBean.getIndexBean(stringBuilder, key));

                    stringBuilder.append(key);
                    stringBuilder.append(":");
                    stringBuilder.append(URLDecoder.decode(value));
                    stringBuilder.append("\n");
                }
            }

            spannableString = new SpannableString(stringBuilder);
            for (TransactionOverviewFragment.IndexBean indexBean : indexBeanList) {
                spannableString.setSpan(new ForegroundColorSpan(Color.parseColor("#FF9800")), indexBean.start, indexBean.end, Spanned.SPAN_INCLUSIVE_EXCLUSIVE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return spannableString;
    }

    private static final String TAG = "ExpandCombineLayout";

    private void populateUI() {
        Log.d(TAG, "populateUI:= " + this);
        if (isAdded() && transaction != null) {
            switch (type) {
                case TYPE_REQUEST:
                    String headText = transaction.getRequestHeadersString(true);
                    String bodyText = transaction.getFormattedRequestBody();
                    headers.setVisibility((TextUtils.isEmpty(headText) ? View.GONE : View.VISIBLE));
                    headers.setText(Html.fromHtml(headText));

                    AppCallBackItf appCallBackItf = Kit.getInstance().getAppCallBackItf();
                    if (appCallBackItf != null) {

                        if ("GET".equals(transaction.getMethod())) {
                            mTextView.setText(getSplitText(appCallBackItf.decryHttpGetParams(bodyText,transaction.getUrl())));
                        } else {
                            mTextView.setText(getSplitText(appCallBackItf.decryHttpPostParam(bodyText,transaction.getUrl())));
                        }

                    } else {
                        mTextView.setText(getSplitText(bodyText));
                    }

                    break;
                case TYPE_RESPONSE:
                    setText(transaction.getResponseHeadersString(true),
                            transaction.getFormattedResponseBody(), transaction.responseBodyIsPlainText());
                    break;
            }
        }
    }

    private static String getLevelStr(int level) {
        StringBuffer levelStr = new StringBuffer();
        for (int levelI = 0; levelI < level; levelI++) {
            levelStr.append("\t");
        }
        return levelStr.toString();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container,
                             @Nullable Bundle savedInstanceState) {
        if (type == TYPE_REQUEST) {
            View view = inflater.inflate(R.layout.kit_chuck_fragment_transaction_payload1, container, false);
            headers = view.findViewById(R.id.headers);
            mTextView = view.findViewById(R.id.mTextView);
            return view;
        } else {
            View view = inflater.inflate(R.layout.kit_chuck_fragment_transaction_payload, container, false);
            headers = view.findViewById(R.id.headers);
            mExpandCombineLayout = view.findViewById(R.id.mExpandCombineView);
            return view;
        }

    }


    public static String bodyText;

    private void setText(String headersString, final String bodyString, boolean isPlainText) {
        bodyText = bodyString;

        headers.setVisibility((TextUtils.isEmpty(headersString) ? View.GONE : View.VISIBLE));
        headers.setText(Html.fromHtml(headersString));
        if (!isPlainText) {
            mExpandCombineLayout.setStartTagText(getString(R.string.kit_chuck_body_omitted));
        } else {
            try {
                if (!TextUtils.isEmpty(bodyString)) {
                    mExpandCombineLayout.clearAllContent();
                    ExpandCombineLayout.invalidateView(getContext(), bodyString, mExpandCombineLayout, true);
                }

            } catch (Exception e) {
                if (!TextUtils.isEmpty(bodyString)) {
                    mExpandCombineLayout.setStartTagText(bodyString);
                }
            }

        }
//        mExpandCombineLayout.setOnLongClickListener(new View.OnLongClickListener() {
//            @Override
//            public boolean onLongClick(View v) {
//                ClipBoardUtil.copyToClipBoard(bodyString);
//                Toast.makeText(getActivity(), "复制成功", Toast.LENGTH_LONG).show();
//                return false;
//            }
//        });
    }
}