package cn.com.vau.kit.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.item.AttrsDialogItemViewBinder;
import cn.com.vau.kit.item.EditTextItem;
import cn.com.vau.kit.view.KitAttrsDialog;


/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class EditTextItemBinder extends AttrsDialogItemViewBinder<EditTextItem, KitAttrsDialog.Adapter.EditTextViewHolder<EditTextItem>> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.EditTextViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.EditTextViewHolder.newInstance(parent);
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.EditTextViewHolder holder, @NonNull EditTextItem item) {
        holder.bindView(item);
    }
}
