package cn.com.vau.kit.binder;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import cn.com.vau.kit.item.AttrsDialogItemViewBinder;
import cn.com.vau.kit.view.KitAttrsDialog;
import cn.com.vau.kit.item.BitmapItem;

/**
 * @author: weishenhong <a href="mailto:<EMAIL>">contact me.</a>
 * @date: 2019-07-08 23:46
 */
public class BitmapItemBinder extends AttrsDialogItemViewBinder<BitmapItem, KitAttrsDialog.Adapter.BitmapInfoViewHolder> {
    @NonNull
    @Override
    public KitAttrsDialog.Adapter.BitmapInfoViewHolder onCreateViewHolder(@NonNull LayoutInflater inflater, @NonNull ViewGroup parent, RecyclerView.Adapter adapter) {
        return KitAttrsDialog.Adapter.BitmapInfoViewHolder.newInstance(parent);
    }

    @Override
    public void onBindViewHolder(@NonNull KitAttrsDialog.Adapter.BitmapInfoViewHolder holder, @NonNull BitmapItem item) {
        holder.bindView(item);
    }
}
