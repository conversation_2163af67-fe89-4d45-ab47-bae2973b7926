buildscript {
    ext {
        agp_version = '8.1.1'
        sumsub_version = "1.32.1"
    }
    repositories {
        google()
        gradlePluginPortal()
        maven { url "https://plugins.gradle.org/m2/" }
        gradlePluginPortal()
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
    }
    dependencies {
        classpath 'com.google.gms:google-services:4.4.2'
        classpath "com.android.tools.build:gradle:$agp_version"
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:2.0.0'
        classpath 'org.greenrobot:greendao-gradle-plugin:3.3.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.2'
        classpath 'com.sensorsdata.analytics.android:android-gradle-plugin2:4.0.2' //神策
        classpath 'com.google.firebase:perf-plugin:1.4.2' //Performance Monitoring Gradle 插件（搜集网络）
//        classpath "io.gitlab.arturbosch.detekt:detekt-gradle-plugin:1.23.8"
        classpath "org.jlleitschuh.gradle:ktlint-gradle:12.2.0"
    }
}

allprojects {
    repositories {
        // 添加下面的内容
        flatDir {
            dirs 'libs'
        }
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
        maven { url 'https://maven.aliyun.com/nexus/content/groups/public/' }
        maven { url 'https://maven.aliyun.com/nexus/content/repositories/releases/' }
        maven {
            url 'https://public-n3.advai.net/repository/maven-releases/'
            allowInsecureProtocol = true
        }
        maven {
            url "https://zendesk.jfrog.io/artifactory/repo"
        }
        maven { url "https://maven.sumsub.com/repository/maven-public/" }
        maven { url 'https://developer.huawei.com/repo/' } //华为
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}