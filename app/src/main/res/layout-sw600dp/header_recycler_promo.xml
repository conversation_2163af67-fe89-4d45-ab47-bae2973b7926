<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="6dp"
    android:paddingBottom="@dimen/margin_vertical_base">

    <!-- 轮播图  380:120 19:6 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardBackgroundColor="?attr/color_c1f1e1e1e_c1fffffff"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="h,696:210"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.youth.banner.Banner
            android:id="@+id/mBanner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="0.5dp"
            android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
            app:banner_indicator_height="0dp"
            app:banner_indicator_normal_width="0dp"
            app:banner_loop_time="3000"
            app:banner_radius="10dp"
            tools:background="@drawable/shape_placeholder" />
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBannerEventTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="22dp"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cardView"
        tools:text="Consectetur Adip Iscing Elit Consectetur Adip Iscing Elit" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBannerEventTime"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="16dp"
        android:layout_marginTop="4dp"
        android:gravity="center_vertical|start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBannerEventTitle"
        tools:text="Aug 26, 2021 - Oct 15, 2021" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBannerEventState"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:minWidth="60dp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="3dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBannerEventTime"
        tools:text="Coming Soon" />

    <cn.com.vau.common.view.custom.BannerIndicatorView
        android:id="@+id/mIndicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBannerEventState" />

</androidx.constraintlayout.widget.ConstraintLayout>
