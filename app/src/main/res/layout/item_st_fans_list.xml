<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="64dp"
    android:layout_marginBottom="4dp"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingHorizontal="12dp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:shapeAppearanceOverlay="@style/circleImageStyle" />

    <TextView
        android:id="@+id/tvNick"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        tools:text="Lewis Hamilton" />
</LinearLayout>