<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAccountCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_top_card_title"
        android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/splitView"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="51dp"
            android:background="?attr/color_c1f1e1e1e_c1fffffff"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAccountType"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:background="@drawable/shape_cff8e5c_r100"
            android:gravity="center"
            android:minWidth="40dp"
            android:paddingHorizontal="6dp"
            android:text="@string/in_review"
            android:textColor="@color/cffffff"
            android:textSize="11dp"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvProcessedNote"
            style="@style/gilroy_500"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_horizontal"
            android:paddingTop="18dp"
            android:paddingBottom="22dp"
            android:text="@string/your_live_account_processed"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            android:visibility="visible"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/splitView" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>