<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <View
        android:id="@+id/firstView"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100" />

    <View
        android:id="@+id/secondView"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100" />

    <View
        android:id="@+id/thirdView"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100" />

    <View
        android:id="@+id/fourthView"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100" />

    <View
        android:id="@+id/fifthView"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100" />

    <View
        android:visibility="gone"
        android:id="@+id/sixthView"
        android:layout_width="0dp"
        android:layout_height="3dp"
        android:layout_marginStart="16dp"
        android:layout_weight="1"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100" />

</LinearLayout>