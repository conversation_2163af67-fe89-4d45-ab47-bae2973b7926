<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginVertical="16dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="5dp"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/detail"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="@string/title" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivArrow"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/detail"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/ivArrow"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_goneMarginEnd="12dp"
        tools:layout_constraintEnd_toStartOf="@id/rightStatus"
        tools:text="detail"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/rightStatus"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginEnd="29.7dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/draw_bitmap2_info16x16_c731e1e1e_c61ffffff"
        tools:visibility="visible" />

    <View
        android:id="@+id/itemLine"
        android:layout_width="0dp"
        android:layout_height="@dimen/cut_off_line_height"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />
</merge>