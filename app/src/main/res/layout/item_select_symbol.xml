<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="44dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="ALL" />

    <!--    <androidx.appcompat.widget.AppCompatImageView-->
    <!--        android:id="@+id/icon"-->
    <!--        android:layout_width="14dp"-->
    <!--        android:layout_height="14dp"-->
    <!--        android:layout_marginEnd="12dp"-->
    <!--        android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"-->
    <!--        app:layout_constraintBottom_toBottomOf="parent"-->
    <!--        app:layout_constraintEnd_toEndOf="parent"-->
    <!--        app:layout_constraintTop_toTopOf="parent" />-->

    <CheckBox
        android:id="@+id/mCheckBox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@null"
        android:button="@drawable/select_checkbox_agreement"
        android:checked="false"
        android:clickable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        style="@style/cut_off_line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>