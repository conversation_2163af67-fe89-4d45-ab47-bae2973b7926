<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="180dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/draw_shape_cffffff_c262930_r10"
    android:paddingVertical="@dimen/margin_horizontal_base">

    <TextView
        android:id="@+id/tvClassic"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:layout_marginTop="4dp"
        android:drawablePadding="@dimen/margin_horizontal_base"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingStart="20dp"
        android:text="@string/classic"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/img_classic_selected" />

    <TextView
        android:id="@+id/tvBuySell"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="34dp"
        android:drawablePadding="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:maxLines="1"
        android:paddingStart="20dp"
        android:text="@string/buy_sell"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="?attr/imgAskUnselected" />
</LinearLayout>