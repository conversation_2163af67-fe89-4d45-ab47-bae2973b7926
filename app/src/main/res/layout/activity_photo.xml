<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/c1a1d20">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivBack"
        android:layout_width="wrap_content"
        android:layout_height="40dp"
        android:layout_centerVertical="true"
        android:layout_marginTop="10dp"
        android:contentDescription="@null"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:src="@drawable/bitmap2_arrow_start_16x16_cebffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNum"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:textColor="@color/cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivBack"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivBack"
        tool:text="1/6" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

</androidx.constraintlayout.widget.ConstraintLayout>