<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="HardcodedText">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/account_opening" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/margin_top_title"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:animateLayoutChanges="true"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/padding_horizontal_base"
            android:paddingBottom="@dimen/margin_bottom_layout">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlMetaTrader"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:visibility="visible"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvMetaTraderDes"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="4dp"
                    android:paddingVertical="@dimen/padding_vertical_base"
                    android:text="@string/platform"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvMetaTraderType"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingStart="18dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="0dp"
                    android:paddingBottom="4dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@+id/tvMetaTraderDes"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvMetaTraderDes"
                    tools:text="mt4" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlAccountType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:paddingHorizontal="@dimen/padding_horizontal_base">

                <TextView
                    android:id="@+id/tvAccountTypeDesc"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="4dp"
                    android:paddingVertical="@dimen/padding_vertical_base"
                    android:text="@string/account_type"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvAccountType"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawablePadding="8dp"
                    android:gravity="end|center_vertical"
                    android:maxWidth="190dp"
                    android:paddingStart="18dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="0dp"
                    android:paddingBottom="4dp"
                    android:text=""
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@+id/tvAccountTypeDesc"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvAccountTypeDesc"
                    tools:text="Standard STP" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvMt4CentAccountTips"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:lineSpacingMultiplier="1.2"
                android:text="@string/please_note_the_14_days"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                android:visibility="gone"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlCurrencyType"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                android:paddingHorizontal="@dimen/padding_horizontal_base">

                <TextView
                    android:id="@+id/tvCurrencyDesc"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingVertical="@dimen/padding_vertical_base"
                    android:text="@string/currency"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvCurrencyType"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:drawablePadding="8dp"
                    android:gravity="center"
                    android:paddingStart="18dp"
                    android:paddingTop="4dp"
                    android:paddingEnd="0dp"
                    android:paddingBottom="4dp"
                    android:text=""
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                    app:layout_constraintBottom_toBottomOf="@+id/tvCurrencyDesc"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/tvCurrencyDesc"
                    tools:text="AUD" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/llNotes"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:orientation="vertical"
                android:padding="@dimen/padding_card_base">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNotesTitle"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/additional_notes"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp" />

                <androidx.core.widget.NestedScrollView
                    android:id="@+id/nsView"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:focusable="true"
                    android:focusableInTouchMode="true">

                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/etNotes"
                        style="@style/gilroy_400"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:gravity="start"
                        android:maxLength="50"
                        android:minHeight="100dp"
                        android:scrollbars="vertical"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                        android:textSize="14dp"
                        tools:text="我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说我说" />
                </androidx.core.widget.NestedScrollView>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCharCount"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:text="0/50"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp" />
            </LinearLayout>

            <View
                android:id="@+id/viewSplit1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="16dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/llNotes" />

            <ViewStub
                android:id="@+id/vsWalletDesc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout="@layout/layout_wallet_description"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/viewSplit1" />

            <View
                android:id="@+id/viewSplit2"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="16dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/vsWalletDesc" />

            <!--加这个布局是因为父布局因ViewStub不得不改为LinearLayout(否则显示不正常)，所以下面这个容器作为下面部分是含有约束条件的布局-->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clProtocol"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base">

                <CheckBox
                    android:id="@+id/cbAgreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@null"
                    android:button="@drawable/select_checkbox_agreement"
                    android:paddingStart="0dp"
                    android:paddingEnd="8dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/tvAgreementTitle"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/please_read_and_agreements"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="@+id/cbAgreement"
                    app:layout_constraintStart_toEndOf="@+id/cbAgreement"
                    app:layout_constraintTop_toTopOf="@+id/cbAgreement" />

                <TextView
                    android:id="@+id/tvAgreement1"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="1. I understand that Vantage Global Prime Pty Ltd, ACN *********, AFSL No. 428901, is the issuer of the products (Foreign Exchange Contract “FX“ or “Forex” and Contracts-for-Difference “CFDs“)."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/cbAgreement" />

                <cn.com.vau.common.view.system.LinkSpanTextView
                    android:id="@+id/tvAgreement2"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/gilroy_regular"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="2. I acknowledge that I have read, understood, and agreed to be bound by Vantage Global Prime Pty Ltd\'s Financial Services Guide (“FSG“), Product Disclosure Statement (“PDS“), Terms and Conditions (“T&amp;Cs“), Privacy Policy, and Vantage FX Pty Ltd\'s Privacy Policy. I confirm that I am part of Vantage Global Prime Pty Ltd\'s target market as determined by reviewing the Target Market Determination (“TMD“)."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement1" />

                <TextView
                    android:id="@+id/tvAgreement3"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="3. I understand and accept the risks associated with trading margin Forex and CFDs. I am prepared to accept a high degree of risk in pursuit of my investment goals and can afford to suffer a loss up to the monetary value of my trading account."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement2" />

                <TextView
                    android:id="@+id/tvAgreement4"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="4. I expressly consent to receive electronic communications from Vantage Global Prime Pty Ltd, including but not limited to trade confirmation statements, records of fund receipts, and marketing material related to trading in our products, including general market commentary."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement3" />

                <TextView
                    android:id="@+id/tvAgreement5"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="5. I confirm that the information provided by me and inserted in this form is true and correct. I acknowledge that I am obliged to notify Vantage Global Prime Pty Ltd if any of my information changes."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement4" />

                <cn.com.vau.common.view.system.LinkSpanTextView
                    android:id="@+id/tvWalletAgreement6"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/gilroy_regular"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/i_we_acknowledge_that_understood_crypto_wallet"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement5" />

                <TextView
                    android:id="@+id/tvAgreement6"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="6. I consent to Vantage Global Prime Pty Ltd authorising a third party to conduct electronic verification of my identity and perform a credit check as part of the account application process."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement5"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/tvAgreement7"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="7. I confirm that I have acted in my name as specified in this application and not on behalf of a third party in respect of all matters related to this client relationship. Accordingly, all funds to be deposited and traded on the account with Vantage are my funds."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement6"
                    tools:visibility="visible" />

                <cn.com.vau.common.view.system.LinkSpanTextView
                    android:id="@+id/tvAgreement8"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:fontFamily="@font/gilroy_regular"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="8. I have read, understood, and agreed to be bound by Vantage\'s deposits and withdrawals policy."
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="@+id/tvAgreementTitle"
                    app:layout_constraintTop_toBottomOf="@+id/tvAgreement7"
                    tools:visibility="visible" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:text="@string/submit" />

</LinearLayout>
