<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="36dp"
    app:layout_constraintEnd_toEndOf="parent"
    app:layout_constraintStart_toStartOf="parent">

    <View
        android:id="@+id/viewTop"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="W,1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:background="@drawable/img_share_up" />

    <cn.com.vau.util.widget.WaterView
        android:id="@+id/waterView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/viewTop"
        app:layout_constraintEnd_toEndOf="@+id/viewTop"
        app:layout_constraintStart_toStartOf="@+id/viewTop"
        app:layout_constraintTop_toTopOf="@+id/viewTop"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewAvatarBg"
        android:layout_width="34dp"
        android:layout_height="34dp"
        android:background="@drawable/draw_shape_cffffff_circle"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintEnd_toEndOf="@+id/ivAvatar"
        app:layout_constraintStart_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="20dp"
        app:layout_constraintStart_toStartOf="@id/viewTop"
        app:layout_constraintTop_toTopOf="@id/viewTop"
        app:shapeAppearanceOverlay="@style/circleImageStyle"
        tools:srcCompat="@mipmap/ic_launcher" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUserName"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/cebffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintEnd_toStartOf="@+id/tvAccountType"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar"
        tools:text="John Doe" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAccountType"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:background="@drawable/shape_cebffffff_r100"
        android:paddingHorizontal="6dp"
        android:paddingVertical="3dp"
        android:text="Demo"
        android:textColor="@color/c1e1e1e"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSymbol"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:lineSpacingExtra="4dp"
        android:textColor="@color/cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="@id/ivAvatar"
        app:layout_constraintTop_toBottomOf="@id/ivAvatar"
        tools:text="EURUSD" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvType"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="12dp"
        android:layout_marginStart="8dp"
        android:gravity="center"
        android:paddingHorizontal="6dp"
        android:textColor="@color/cffffff"
        android:textSize="8dp"
        app:layout_constraintBottom_toBottomOf="@id/tvSymbol"
        app:layout_constraintStart_toEndOf="@id/tvSymbol"
        app:layout_constraintTop_toTopOf="@+id/tvSymbol"
        tools:text="SELL" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="20dp"
        android:lineSpacingExtra="4dp"
        android:maxLines="1"
        android:text="@string/unrealised_roi"
        android:textAlignment="viewStart"
        android:textColor="@color/c99ffffff"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@id/tvSymbol"
        app:layout_constraintTop_toBottomOf="@id/tvSymbol"
        tools:text="@string/unrealised_pl" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail1"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="4dp"
        android:textDirection="ltr"
        android:textSize="36dp"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle"
        tools:text="+49.72%"
        tools:textColor="@color/c00c79c" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail2"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="4dp"
        android:textDirection="ltr"
        android:textSize="20dp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@id/tvDetail1"
        app:layout_constraintTop_toBottomOf="@+id/tvDetail1"
        tools:text="+56.44 USD"
        tools:textColor="@color/c00c79c" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitleEntryPrice"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/entry_price"
        android:textColor="@color/c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@+id/tvDetail2"
        app:layout_constraintTop_toBottomOf="@+id/tvDetail2" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitleCurrentPrice"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/current_price"
        android:textColor="@color/c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="@+id/tvTitleEntryPrice"
        app:layout_constraintTop_toBottomOf="@+id/tvTitleEntryPrice" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEntryPrice"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitleEntryPrice"
        app:layout_constraintStart_toEndOf="@+id/tvTitleEntryPrice"
        app:layout_constraintTop_toTopOf="@+id/tvTitleEntryPrice"
        tools:text="1.12345" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrentPrice"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:textColor="@color/cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitleCurrentPrice"
        app:layout_constraintStart_toEndOf="@+id/tvTitleCurrentPrice"
        app:layout_constraintTop_toTopOf="@+id/tvTitleCurrentPrice"
        tools:text="2.23456" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="20dp"
        android:textColor="@color/c99ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewTop"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Updated on 2023/07/20Z" />

    <View
        android:id="@+id/viewBottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/cffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,4:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewTop" />

    <TextView
        android:id="@+id/tvBottomTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="@color/c1e1e1e"
        android:textDirection="ltr"
        android:textSize="12dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@+id/viewQrCode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBottom"
        tools:text="Referral Code: ABC234" />

    <TextView
        android:id="@+id/tvHint"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:maxLines="2"
        android:paddingTop="2dp"
        android:paddingBottom="4dp"
        android:text="@string/keep_the_returns_trade_x_app"
        android:textColor="@color/ca61e1e1e"
        android:textSize="10dp"
        app:autoSizeMaxTextSize="10dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toTopOf="@+id/ivLogo"
        app:layout_constraintEnd_toEndOf="@+id/tvBottomTitle"
        app:layout_constraintStart_toStartOf="@id/tvBottomTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvBottomTitle"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Keep the returns coming! Trade on Vantage App." />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLogo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginBottom="8dp"
        android:scaleType="fitStart"
        android:src="@drawable/img_share_logo_svg"
        app:layout_constraintBottom_toBottomOf="@id/viewBottom"
        app:layout_constraintStart_toStartOf="@id/viewBottom" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivQrCode"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginVertical="6dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="@id/viewBottom"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/viewBottom"
        tools:src="@drawable/img_invite_default_code" />

    <View
        android:id="@+id/viewQrCode"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/draw_shape_stroke_c731e1e1e_r2"
        app:layout_constraintBottom_toBottomOf="@+id/ivQrCode"
        app:layout_constraintEnd_toEndOf="@+id/ivQrCode"
        app:layout_constraintStart_toStartOf="@+id/ivQrCode"
        app:layout_constraintTop_toTopOf="@+id/ivQrCode" />

</androidx.constraintlayout.widget.ConstraintLayout>