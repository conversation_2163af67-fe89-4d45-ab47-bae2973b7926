<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        app:hb_titleText="@string/identity_verification"
        app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:hb_endIcon1="?attr/icon1Cs"
        app:hb_isShowBack="false"
        app:layout_constraintTop_toTopOf="parent"/>

    <cn.com.vau.common.view.StepOpenAccountView
        android:id="@+id/stepView"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginTop="10dp"
        android:layout_marginHorizontal="@dimen/padding_horizontal_base"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        app:step_num="1"
        app:step_num_total="2" />

    <TextView
        android:id="@+id/tvSecondTitle"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:text="@string/upload_id"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepView" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/fold_content_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSecondTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/padding_horizontal_base">

            <TextView
                android:id="@+id/tvOpenAccountProgressDesc"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/please_choose_one_below"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="13dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvBankStatement"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:minHeight="50dp"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:text="@string/passport"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c731e1e1e_c61ffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvOpenAccountProgressDesc"
                app:layout_goneMarginEnd="30dp" />

            <TextView
                android:id="@+id/tvUtilityBills"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:minHeight="50dp"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:text="@string/drivers_licence"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c731e1e1e_c61ffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvBankStatement"
                app:layout_goneMarginEnd="30dp" />

            <TextView
                android:id="@+id/tvLetterIssued"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c1fffffff_r14"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:minHeight="50dp"
                android:paddingStart="20dp"
                android:paddingTop="10dp"
                android:paddingEnd="20dp"
                android:paddingBottom="10dp"
                android:text="@string/government_issued_photo_id"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_end10x10_c731e1e1e_c61ffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvUtilityBills"
                app:layout_goneMarginEnd="30dp" />

            <TextView
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="@string/as_part_of_all_for_and_account_related_issues"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvLetterIssued" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <View
        android:id="@+id/keepOutView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?mainLayoutBg"
        tools:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>
