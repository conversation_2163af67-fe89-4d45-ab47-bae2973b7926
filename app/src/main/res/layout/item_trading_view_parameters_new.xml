<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tabRV"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="6"
        tools:listitem="@layout/item_recycler_trading_view_parameters_tab" />

    <cn.com.vau.util.widget.KlineSettingEditView
        android:id="@+id/kseView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_base"
        app:layout_constraintTop_toBottomOf="@+id/tabRV" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNoData"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:gravity="center"
        android:minHeight="270dp"
        android:text="@string/this_indicator_doesn_any_configuration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/nsView" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvReset"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="10dp"
        android:paddingTop="10dp"
        android:text="@string/reset"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/kseView"
        app:layout_constraintEnd_toEndOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/nsView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="12dp"
        android:background="?attr/color_cffffff_c262930"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tabRV"
        tools:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDetail"
            style="@style/regular_font"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:minHeight="98dp"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="locale"
            android:textSize="12dp"
            tools:text="Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively.Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively.Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively.Also known as Bollinger bands, BOLL is basically a banded channel consisting of three bands (one in the middle, one placed above, and one placed below). The middle band is the average cost of the price, and the upper and lower band can be regarded as the resistance and support band respectively." />
    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>