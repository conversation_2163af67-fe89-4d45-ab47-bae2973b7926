<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/link_your_account" />

    <cn.com.vau.common.view.login.LoginInputContentView
        android:id="@+id/emailView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        app:icv_hintText="@string/email"
        app:icv_isInputEmail="true" />

    <cn.com.vau.common.view.login.LoginInputPwdView
        android:id="@+id/pwdView"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        app:ipv_hintText="@string/_8_16_characters" />

    <TextView
        android:id="@+id/tvForgetPwd"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:paddingVertical="12dp"
        android:text="@string/forgot_password"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/login_tvNext_style"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/verify" />
</LinearLayout>