<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvLv3Title"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/lv3_poa_authentication"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvLv3TitleReq"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:text="@string/requirements"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tvLv3Title"
        app:layout_constraintTop_toBottomOf="@+id/tvLv3Title" />

    <TextView
        android:id="@+id/tvLv3VerifiedStatus"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_ce8e8e8_c414348_r100"
        android:drawablePadding="4dp"
        android:gravity="center"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/unverified"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/tvLv3TitleReq"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvLv3TitleReq"
        tools:text="Unverified" />

    <TextView
        android:id="@+id/tvLv3Re1"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/poa_information"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvLv3TitleReq"
        app:layout_constraintTop_toBottomOf="@id/tvLv3TitleReq" />

    <TextView
        android:id="@+id/tvLv3Re2"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/poa_photo"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvLv3Re1"
        app:layout_constraintTop_toBottomOf="@id/tvLv3Re1" />

    <View
        android:id="@+id/viewLineLv3"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="15dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvLv3Re2" />

    <TextView
        android:id="@+id/tvLv3TitlePer"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/permissions"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@+id/tvLv3TitleReq"
        app:layout_constraintTop_toBottomOf="@+id/viewLineLv3" />

    <TextView
        android:id="@+id/tvLv3TitleWithdrawal"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:text="@string/withdrawal"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:drawableStartCompat="?attr/imgPermissionWithdrawal"
        app:layout_constraintStart_toStartOf="@+id/tvLv3TitlePer"
        app:layout_constraintTop_toBottomOf="@+id/tvLv3TitlePer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLv3VerifyNow"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:paddingVertical="8dp"
        android:text="@string/verify"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLv3TitleWithdrawal"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewBottomLv3"
        android:layout_width="wrap_content"
        android:layout_height="8dp"
        android:layout_marginTop="20dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLv3VerifyNow" />

    <TextView
        android:id="@+id/tvBankTitle"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:text="@string/bank_channel_authentication"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/viewBottomLv3" />

    <TextView
        android:id="@+id/tvBankTitleReq"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:text="@string/requirements"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@id/tvBankTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvBankTitle" />

    <TextView
        android:id="@+id/tvBankVerifiedStatus"
        style="@style/regular_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@drawable/draw_shape_ce8e8e8_c414348_r100"
        android:drawablePadding="4dp"
        android:gravity="center"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/unverified"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@id/tvBankTitleReq"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvBankTitleReq"
        tools:text="Unverified" />

    <TextView
        android:id="@+id/tvBankRe1"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/financial_work_information"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvBankTitleReq"
        app:layout_constraintTop_toBottomOf="@id/tvBankTitleReq" />

    <TextView
        android:id="@+id/tvBankRe2"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/id_authentication"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvBankRe1"
        app:layout_constraintTop_toBottomOf="@id/tvBankRe1" />

    <TextView
        android:id="@+id/tvBankRe3"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="14dp"
        android:drawablePadding="15dp"
        android:gravity="center"
        android:text="@string/poa_authentication"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap_circle_right_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="@id/tvBankRe2"
        app:layout_constraintTop_toBottomOf="@id/tvBankRe2" />

    <View
        android:id="@+id/viewLineBank"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:layout_marginTop="15dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvBankRe3" />

    <TextView
        android:id="@+id/tvBankTitlePer"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/permissions"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintStart_toStartOf="@+id/tvBankRe3"
        app:layout_constraintTop_toBottomOf="@+id/viewLineBank" />

    <TextView
        android:id="@+id/tvBankPer"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawablePadding="10dp"
        android:gravity="center"
        android:text="@string/bank_channel_transation"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:drawableStartCompat="@drawable/img_source_permission_bank"
        app:drawableTint="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintStart_toStartOf="@+id/tvBankTitlePer"
        app:layout_constraintTop_toBottomOf="@+id/tvBankTitlePer" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBankVerifyNow"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:paddingHorizontal="20dp"
        android:paddingVertical="8dp"
        android:text="@string/verify"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBankPer"
        tools:visibility="visible" />

    <View
        android:id="@+id/viewBottomBank"
        android:layout_width="wrap_content"
        android:layout_height="8dp"
        android:layout_marginTop="20dp"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvBankVerifyNow" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBank"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="viewBottomBank,tvBankPer,tvBankTitlePer,viewLineBank,tvBankRe3,tvBankRe2,tvBankRe1,tvBankTitleReq,tvBankVerifiedStatus,tvBankTitle,tvBankVerifyNow"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>