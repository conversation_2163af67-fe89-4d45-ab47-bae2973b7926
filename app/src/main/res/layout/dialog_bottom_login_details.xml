<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/rootView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    tools:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20">

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/measureRv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_max="170dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:itemCount="4"
        tools:listitem="@layout/item_login_details_account" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="15dp"
        android:gravity="start"
        android:text="@string/login_details"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/subtitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/subtitle"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="12dp"
        android:gravity="start"
        android:text="@string/select_an_account_to_log"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@id/tvAccount"
        app:layout_constraintEnd_toEndOf="@id/title"
        app:layout_constraintStart_toStartOf="@id/title"
        app:layout_constraintTop_toBottomOf="@id/title" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/tvAccount"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
        android:button="@null"
        android:checked="false"
        android:drawableEnd="@drawable/select_checkbox_arrow_vertical"
        android:gravity="center_vertical"
        android:hint="@string/select_an_account"
        android:paddingStart="12dp"
        android:paddingEnd="20dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="@id/subtitle"
        app:layout_constraintStart_toStartOf="@id/subtitle"
        app:layout_constraintTop_toBottomOf="@id/subtitle" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAccount"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="12dp"
        app:layout_constraintBottom_toTopOf="@id/tvPrompt"
        app:layout_constraintTop_toBottomOf="@+id/tvAccount">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvAccount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?attr/color_cf3f3f3_c262930"
            android:fadeScrollbars="false"
            android:nestedScrollingEnabled="false"
            android:scrollbarFadeDuration="0"
            android:scrollbarSize="4dp"
            android:scrollbarStyle="outsideOverlay"
            android:scrollbarThumbVertical="@drawable/draw_shape_c4d1e1e1e_c4dffffff_r2"
            android:scrollbars="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_max="170dp"
            app:layout_constraintTop_toTopOf="parent"
            tools:itemCount="4"
            tools:listitem="@layout/item_login_details_account" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPrompt"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:gravity="start"
        android:lineSpacingExtra="8dp"
        android:text="@string/send_email_prompt"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        android:textStyle="normal"
        app:layout_constraintBottom_toTopOf="@+id/tvCancel"
        app:layout_constraintEnd_toEndOf="@id/tvAccount"
        app:layout_constraintStart_toStartOf="@id/tvAccount" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCancel"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:text="@string/cancel"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/tvSendNow"
        app:layout_constraintStart_toStartOf="@id/clAccount" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSendNow"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:text="@string/send_now"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        android:textStyle="normal"
        app:layout_constraintEnd_toEndOf="@id/tvPrompt"
        app:layout_constraintStart_toEndOf="@+id/tvCancel"
        app:layout_constraintTop_toTopOf="@+id/tvCancel" />

</androidx.constraintlayout.widget.ConstraintLayout>