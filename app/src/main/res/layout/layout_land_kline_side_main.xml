<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/kline_land_side_width"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:layout_width="@dimen/kline_land_side_width">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShowDrawing"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginVertical="12dp"
        android:src="?attr/icon2KlineDrawToolsDraw" />

    <View
        android:layout_width="20dp"
        android:layout_height="1dp"
        android:layout_gravity="center_horizontal"
        android:background="?attr/color_c1f1e1e1e_c1fffffff" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvMain"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scrollbars="none"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />
</LinearLayout>