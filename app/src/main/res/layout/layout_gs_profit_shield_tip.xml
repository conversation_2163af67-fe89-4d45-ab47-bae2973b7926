<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProfitShieldInfo"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:text="@string/growth_shield_enhances_simply_exclusive_benefits"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.SerialTextView
        android:id="@+id/stvDesc1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textAlignment="viewStart"
        android:gravity="start"
        app:content_text="@string/fund_growth_boost_earn_eligible_funds"
        app:layout_constraintTop_toBottomOf="@id/tvProfitShieldInfo"
        app:serial_text_color="?attr/color_c1e1e1e_cebffffff" />

    <cn.com.vau.common.view.SerialTextView
        android:id="@+id/stvDesc2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textAlignment="viewStart"
        android:gravity="start"
        app:content_text="@string/loss_protection_get_selected_strategies"
        app:layout_constraintTop_toBottomOf="@id/stvDesc1"
        app:serial_text_color="?attr/color_c1e1e1e_cebffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTC"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:textAlignment="viewStart"
        android:gravity="start"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/stvDesc2"
        tools:text="Terms and conditions apply" />


</androidx.constraintlayout.widget.ConstraintLayout>