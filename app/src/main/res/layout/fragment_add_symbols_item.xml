<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.common.view.system.MyRecyclerView
        android:id="@+id/myRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <TextView
        android:id="@+id/tvNoAssets"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:text="@string/no_symbols"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="16dp"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>