<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:gravity="center_vertical">

    <TextView
        android:id="@+id/tvCurrent"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="10dp"
        tools:text="1"/>

    <TextView
        android:id="@+id/tvTotal"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="10dp"
        tools:text="/3"/>

</LinearLayout>