<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    android:paddingBottom="12dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSetting"
        style="@style/DialogBottomTitleStyle"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        android:text="@string/chart_setting"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvKNewGuide"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:background="@drawable/shape_c1f007fff_r4"
        android:drawableStart="@drawable/icon2_degree_hat"
        android:drawablePadding="4dp"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp"
        android:text="@string/guide"
        android:textColor="@color/c007fff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvSetting"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvSetting" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvChartDisplayTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="25dp"
        android:text="@string/chart_display"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSetting" />

    <cn.com.vau.trade.view.SelectableGridView
        android:id="@+id/rvChartDisplay"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvChartDisplayTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOrderDisplayTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:text="@string/order_display"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvChartDisplay" />

    <cn.com.vau.trade.view.SelectableGridView
        android:id="@+id/rvOrderDisplay"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvOrderDisplayTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvChartModeTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:text="@string/chart_mode"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvOrderDisplay" />

    <cn.com.vau.trade.view.SelectableGridView
        android:id="@+id/rvChartMode"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvChartModeTitle" />

</androidx.constraintlayout.widget.ConstraintLayout>