<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="0dp"
    android:layout_height="28dp"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_weight="1"
    android:orientation="horizontal"
    android:gravity="center_vertical">

    <LinearLayout
        android:id="@+id/llContainer"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:orientation="vertical"
        android:gravity="center_horizontal">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="wrap_content"
            android:layout_height="27dp"
            tool:text="分时"
            android:textSize="11dp"
            android:gravity="center"
            android:textColor="@color/cd72d2b"/>

        <View
            android:id="@+id/view_bottom"
            android:layout_width="16dp"
            android:layout_height="1dp"
            android:background="@color/cd72d2b" />

    </LinearLayout>

    <View
        android:id="@+id/rightView"
        android:layout_width="0.5dp"
        android:layout_height="12dp"
        android:background="#e6e6e6"
        android:visibility="gone"/>

</LinearLayout>
