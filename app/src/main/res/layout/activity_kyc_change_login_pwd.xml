<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/change_log_in_password"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tvTips"
                style="@style/gilroy_500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/c1fe35728"
                android:lineSpacingMultiplier="1.2"
                android:padding="@dimen/padding_horizontal_base"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                android:visibility="gone"
                tools:text="123"
                tools:visibility="visible" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCurrentPwd"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:text="@string/current_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputPwdView
                android:id="@+id/currentPwdView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="8dp"
                app:ipv_hintText="@string/current_password" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvNewPwd"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:text="@string/new_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputPwdView
                android:id="@+id/newPwdView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="8dp"
                app:ipv_hintText="@string/_8_16_characters" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvConfirmPwd"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:text="@string/confirm_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputPwdView
                android:id="@+id/confirmPwdView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="8dp"
                app:ipv_hintText="@string/re_enter_password" />

            <include
                android:id="@+id/layoutPasswordCheck"
                layout="@layout/include_password_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp" />

            <TextView
                android:id="@+id/tvNext"
                style="@style/login_tvNext_style"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="16dp"
                android:text="@string/change_password" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>