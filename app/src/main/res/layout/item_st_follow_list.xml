<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="68dp"
    android:foreground="@drawable/ripple_c0a1e1e1e_c0ab2b2b2"
    android:paddingHorizontal="12dp"
    tools:ignore="ContentDescription,Smalldp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:shapeAppearanceOverlay="@style/roundImageStyle10" />

    <TextView
        android:id="@+id/tvNick"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="3dp"
        android:ellipsize="end"
        android:gravity="start"
        android:maxLines="1"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toStartOf="@+id/ivCollect"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar"
        tools:text="Lewis Hamilton" />

    <TextView
        android:id="@+id/tvReturnKey"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        android:text="@string/return_3m"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintStart_toStartOf="@+id/tvNick" />

    <TextView
        android:id="@+id/tvReturn"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:textColor="@color/c00c79c"
        android:textSize="12dp"
        app:layout_constraintStart_toEndOf="@+id/tvReturnKey"
        app:layout_constraintTop_toTopOf="@+id/tvReturnKey"
        tools:text="593.00%" />

    <TextView
        android:id="@+id/tvDelisted"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="3dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="8dp"
        android:paddingVertical="2dp"
        android:text="@string/delisted"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintStart_toStartOf="@+id/tvNick"
        tools:visibility="gone" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCollect"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:padding="12dp"
        android:src="@drawable/draw_bitmap2_favorite12x12_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>