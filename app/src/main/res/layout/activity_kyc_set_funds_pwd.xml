<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/set_funds_password" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:animateLayoutChanges="true"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/padding_horizontal_base">

            <TextView
                android:id="@+id/tvPwdTips"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title"
                android:text="@string/funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputPwdView
                android:id="@+id/pwdView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                app:ipv_hintText="@string/set_a_8_to_16_digit_funds_password" />

            <TextView
                android:id="@+id/tvConfirmPwdTips"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/confirm_your_funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp" />

            <cn.com.vau.common.view.login.LoginInputPwdView
                android:id="@+id/confirmPwdView"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                app:ipv_hintText="@string/confirm_your_funds_password" />

            <TextView
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:lineSpacingExtra="6dp"
                android:text="@string/funds_password_is_and_please_in_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp" />

            <include
                android:id="@+id/layoutPasswordCheck"
                layout="@layout/include_password_check"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp" />

            <TextView
                android:id="@+id/tvNext"
                style="@style/login_tvNext_style"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="16dp"
                android:text="@string/set_funds_password" />
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>