<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_root"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom"
    tools:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:paddingHorizontal="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/filters"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:overScrollMode="never"
        app:layout_constraintTop_toBottomOf="@+id/title" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRightButton"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:paddingVertical="5dp"
        android:text="@string/clear_all"
        android:textColor="@color/ce35728"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/title" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvButton"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:background="@drawable/draw_shape_c1e1e1e_cebffffff_r100"
        android:gravity="center"
        android:paddingVertical="14dp"
        android:text="@string/confirm"
        android:textColor="?attr/color_cebffffff_c1e1e1e"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/rvList" />

</androidx.constraintlayout.widget.ConstraintLayout>