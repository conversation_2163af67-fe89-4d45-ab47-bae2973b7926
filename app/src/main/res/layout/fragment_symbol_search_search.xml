<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clSearchRoot"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/etSearch"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r100"
        android:drawablePadding="8dp"
        android:drawableTint="?attr/color_c731e1e1e_c61ffffff"
        android:gravity="center_vertical|start"
        android:hint="@string/search_instruments"
        android:imeOptions="actionSearch"
        android:inputType="text"
        android:maxLines="1"
        android:paddingHorizontal="12dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:drawableStartCompat="@drawable/draw_bitmap2_search_c731e1e1e_c61ffffff"
        app:layout_constraintEnd_toStartOf="@+id/tvCancel"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseCompatTextViewDrawableXml" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClear"
        android:layout_width="22dp"
        android:layout_height="22dp"
        android:layout_marginEnd="8dp"
        android:padding="4dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@id/etSearch"
        app:layout_constraintEnd_toEndOf="@id/etSearch"
        app:layout_constraintTop_toTopOf="@id/etSearch"
        app:layout_goneMarginEnd="8dp"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvCancel"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:text="@string/cancel"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/etSearch"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/etSearch"
        app:layout_constraintTop_toTopOf="@+id/etSearch" />

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="0dp"
        android:layout_height="33dp"
        android:layout_marginTop="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etSearch" />

    <View
        android:id="@+id/vTabBottomLine"
        style="@style/TabLayoutBottomLineStyle"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mTabLayout" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clSortToolbar"
        android:layout_width="0dp"
        android:layout_height="38dp"
        android:layout_marginTop="8dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/vTabBottomLine">

        <TextView
            android:id="@+id/tvSearchSymbol"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center"
            android:paddingHorizontal="12dp"
            android:text="@string/symbol"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvPriceRose"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:drawableEnd="?attr/imgNotSort"
            tools:text="@string/percent_change" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mViewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clSortToolbar"
        app:layout_constraintVertical_bias="0" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupSearchResult"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="mViewPager,clSortToolbar,vTabBottomLine,mTabLayout"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintTop_toBottomOf="@+id/etSearch" />

</androidx.constraintlayout.widget.ConstraintLayout>