<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/icon"
        app:layout_constraintStart_toStartOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title"
            style="@style/regular_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginEnd="5dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            tools:text="610261026102610"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tvLabel"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constrainedWidth="true"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed" />

        <TextView
            android:id="@+id/tvLabel"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:gravity="center"
            android:paddingHorizontal="8dp"
            android:paddingVertical="3dp"
            android:textColor="@color/cffffff"
            android:textSize="10dp"
            android:visibility="gone"
            tools:visibility="visible"
            tools:background="@drawable/shape_ce35728_r100"
            tools:text="Recommended"
            app:layout_constraintStart_toEndOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            app:layout_constraintBottom_toBottomOf="@id/title"/>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>