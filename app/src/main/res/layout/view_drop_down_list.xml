<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <EditText
        android:id="@+id/mEditText"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="38dp"
        android:singleLine="true"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="Autofill,LabelFor" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/mImageArrow"
        android:layout_width="wrap_content"
        android:layout_height="6dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="@+id/mEditText"
        app:layout_constraintEnd_toEndOf="@+id/mEditText"
        app:layout_constraintTop_toTopOf="@+id/mEditText" />

</androidx.constraintlayout.widget.ConstraintLayout>