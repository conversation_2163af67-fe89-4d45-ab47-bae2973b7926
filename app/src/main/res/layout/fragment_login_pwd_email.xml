<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="@dimen/margin_horizontal_base"
    tools:ignore="HardcodedText,RtlCompat">

    <EditText
        android:id="@+id/etEmail"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/select_login_et_bg"
        android:hint="@string/email"
        android:importantForAutofill="no"
        android:inputType="textEmailAddress"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="40dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClearEmail"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="4dp"
        android:padding="8dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/etEmail"
        app:layout_constraintEnd_toEndOf="@+id/etEmail"
        app:layout_constraintTop_toTopOf="@+id/etEmail"
        tools:visibility="visible" />

    <EditText
        android:id="@+id/etPwd"
        style="@style/gilroy_500"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@drawable/select_login_et_bg"
        android:gravity="center_vertical"
        android:importantForAutofill="no"
        android:inputType="textPassword"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="70dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etEmail" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClearPwd"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:padding="8dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/etPwd"
        app:layout_constraintEnd_toStartOf="@+id/ivShowPwd"
        app:layout_constraintTop_toTopOf="@+id/etPwd"
        app:layout_goneMarginEnd="8dp"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShowPwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/etPwd"
        app:layout_constraintEnd_toEndOf="@+id/etPwd"
        app:layout_constraintTop_toTopOf="@+id/etPwd"
        app:srcCompat="@drawable/draw_bitmap2_password_hide_c731e1e1e_c61ffffff" />

    <TextView
        android:id="@+id/tvEmailLoginHint"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:paddingTop="12dp"
        android:text="@string/after_applying_you_by_email"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etPwd" />

    <TextView
        android:id="@+id/tvForgetPwd"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingTop="12dp"
        android:text="@string/forgot_password"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvEmailLoginHint" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginTop="24dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/log_in"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvForgetPwd" />

    <TextView
        android:id="@+id/tvRegisterLeft"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:paddingVertical="8dp"
        android:paddingStart="0dp"
        android:paddingEnd="5dp"
        android:text="@string/new_user"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="13dp"
        app:layout_constraintEnd_toStartOf="@+id/tvRegister"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvNext" />

    <TextView
        android:id="@+id/tvRegister"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="55dp"
        android:paddingVertical="8dp"
        android:text="@string/sign_up"
        android:textColor="@color/ce35728"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/tvRegisterLeft"
        app:layout_constraintTop_toTopOf="@+id/tvRegisterLeft" />

    <ImageView
        android:id="@+id/ivTelegram"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="50dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/icon2_third_login_telegram"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <TextView
        android:id="@+id/tvThirdLogin"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:text="@string/or_log_in_with"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="13dp"
        app:layout_constraintBottom_toTopOf="@+id/ivTelegram"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <View
        android:id="@+id/viewBtoLine1"
        android:layout_width="0dp"
        android:layout_height="0.75dp"
        android:layout_marginEnd="29dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvThirdLogin"
        app:layout_constraintEnd_toStartOf="@+id/tvThirdLogin"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvThirdLogin" />

    <View
        android:id="@+id/viewBtoLine2"
        android:layout_width="0dp"
        android:layout_height="0.75dp"
        android:layout_marginStart="29dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvThirdLogin"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvThirdLogin"
        app:layout_constraintTop_toTopOf="@+id/tvThirdLogin" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupBottomView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="ivTelegram,tvThirdLogin,viewBtoLine1,viewBtoLine2" />
</androidx.constraintlayout.widget.ConstraintLayout>