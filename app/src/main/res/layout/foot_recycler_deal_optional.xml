<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <TextView
        android:id="@+id/tvAddSymbolFoot"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginVertical="@dimen/margin_vertical_base"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:drawableStart="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
        android:drawablePadding="8dp"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="6dp"
        android:text="@string/add_symbol"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        tools:ignore="UseCompatTextViewDrawableXml" />

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="1" />

</androidx.appcompat.widget.LinearLayoutCompat>