<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="54dp"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewTrade"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/viewOrder"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewOrder"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/viewDiscover"
        app:layout_constraintStart_toEndOf="@+id/viewTrade"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewDiscover"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/viewPromo"
        app:layout_constraintStart_toEndOf="@+id/viewOrder"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewPromo"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/viewProfile"
        app:layout_constraintStart_toEndOf="@+id/viewDiscover"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/viewProfile"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/viewPromo"
        app:layout_constraintTop_toTopOf="parent" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavTrade"
        android:layout_width="22dp"
        android:layout_height="22dp"
        app:layout_constraintBottom_toTopOf="@+id/tvHome"
        app:layout_constraintEnd_toEndOf="@+id/viewTrade"
        app:layout_constraintStart_toStartOf="@+id/viewTrade"
        app:layout_constraintTop_toTopOf="@+id/viewTrade"
        app:layout_constraintVertical_chainStyle="packed"
        tools:lottie_rawRes="@raw/trades" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHome"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/home"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewTrade"
        app:layout_constraintEnd_toEndOf="@+id/viewTrade"
        app:layout_constraintStart_toStartOf="@+id/viewTrade"
        app:layout_constraintTop_toBottomOf="@+id/lavTrade" />

    <ViewStub
        android:id="@+id/vbMsgCountTrade"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/shape_ce35728_r20"
        android:layout="@layout/layout_main_bottom_red_dot"
        app:layout_constraintEnd_toEndOf="@id/lavTrade"
        app:layout_constraintTop_toTopOf="@id/lavTrade"
        tools:visibility="visible" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavOrder"
        android:layout_width="22dp"
        android:layout_height="22dp"
        app:layout_constraintBottom_toTopOf="@+id/tvTrade"
        app:layout_constraintEnd_toEndOf="@+id/viewOrder"
        app:layout_constraintStart_toStartOf="@+id/viewOrder"
        app:layout_constraintTop_toTopOf="@+id/viewOrder"
        app:layout_constraintVertical_chainStyle="packed"
        tools:lottie_rawRes="@raw/orders" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTrade"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/trades"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewOrder"
        app:layout_constraintEnd_toEndOf="@+id/viewOrder"
        app:layout_constraintStart_toStartOf="@+id/viewOrder"
        app:layout_constraintTop_toBottomOf="@+id/lavOrder" />

    <ViewStub
        android:id="@+id/vbMsgCountOrder"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/shape_ce35728_r20"
        android:layout="@layout/layout_main_bottom_red_dot"
        app:layout_constraintEnd_toEndOf="@id/lavOrder"
        app:layout_constraintTop_toTopOf="@id/lavOrder"
        tools:visibility="visible" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavDiscover"
        android:layout_width="22dp"
        android:layout_height="22dp"
        app:layout_constraintBottom_toTopOf="@+id/tvDiscover"
        app:layout_constraintEnd_toEndOf="@+id/viewDiscover"
        app:layout_constraintStart_toStartOf="@+id/viewDiscover"
        app:layout_constraintTop_toTopOf="@+id/viewDiscover"
        app:layout_constraintVertical_chainStyle="packed"
        tools:lottie_rawRes="@raw/discover" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDiscover"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/discover"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewDiscover"
        app:layout_constraintEnd_toEndOf="@+id/viewDiscover"
        app:layout_constraintStart_toStartOf="@+id/viewDiscover"
        app:layout_constraintTop_toBottomOf="@+id/lavDiscover" />

    <ViewStub
        android:id="@+id/vbMsgCountDiscover"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/shape_ce35728_r20"
        android:layout="@layout/layout_main_bottom_red_dot"
        app:layout_constraintEnd_toEndOf="@id/lavDiscover"
        app:layout_constraintTop_toTopOf="@id/lavDiscover"
        tools:visibility="visible" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavPromo"
        android:layout_width="22dp"
        android:layout_height="22dp"
        app:layout_constraintBottom_toTopOf="@+id/tvPromo"
        app:layout_constraintEnd_toEndOf="@+id/viewPromo"
        app:layout_constraintStart_toStartOf="@+id/viewPromo"
        app:layout_constraintTop_toTopOf="@+id/viewPromo"
        app:layout_constraintVertical_chainStyle="packed"
        tools:lottie_rawRes="@raw/promo" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvPromo"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/promo"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewPromo"
        app:layout_constraintEnd_toEndOf="@+id/viewPromo"
        app:layout_constraintStart_toStartOf="@+id/viewPromo"
        app:layout_constraintTop_toBottomOf="@+id/lavPromo" />

    <ViewStub
        android:id="@+id/vbMsgCountPromo"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/shape_ce35728_r20"
        android:layout="@layout/layout_main_bottom_red_dot"
        app:layout_constraintEnd_toEndOf="@id/lavPromo"
        app:layout_constraintTop_toTopOf="@id/lavPromo"
        tools:visibility="visible" />

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/lavProfile"
        android:layout_width="22dp"
        android:layout_height="22dp"
        app:layout_constraintBottom_toTopOf="@+id/tvProfile"
        app:layout_constraintEnd_toEndOf="@+id/viewProfile"
        app:layout_constraintStart_toStartOf="@+id/viewProfile"
        app:layout_constraintTop_toTopOf="@+id/viewProfile"
        app:layout_constraintVertical_chainStyle="packed"
        tools:lottie_rawRes="@raw/home" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvProfile"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="@string/profile"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewProfile"
        app:layout_constraintEnd_toEndOf="@+id/viewProfile"
        app:layout_constraintStart_toStartOf="@+id/viewProfile"
        app:layout_constraintTop_toBottomOf="@+id/lavProfile" />

    <ViewStub
        android:id="@+id/vbMsgCountProfile"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:background="@drawable/shape_ce35728_r20"
        android:layout="@layout/layout_main_bottom_red_dot"
        app:layout_constraintEnd_toEndOf="@id/lavProfile"
        app:layout_constraintTop_toTopOf="@id/lavProfile"
        tools:visibility="visible" />
</merge>