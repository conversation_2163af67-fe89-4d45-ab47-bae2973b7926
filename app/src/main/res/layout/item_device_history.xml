<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="@dimen/margin_horizontal_base">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Huawei Mate 30 (HarmonyOS)" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvIPAddress"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        tools:text="IP Address: ***************" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvLocation"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="@id/tvTitle"
        app:layout_constraintStart_toStartOf="@id/tvTitle"
        app:layout_constraintTop_toBottomOf="@id/tvIPAddress"
        tools:text="Login Location: Kuala Lumpur" />

    <TextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="@+id/tvLocation"
        app:layout_constraintStart_toStartOf="@+id/tvLocation"
        app:layout_constraintTop_toBottomOf="@+id/tvLocation"
        tools:text="16/11/2023 10:46:34" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrentDevice"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/current_device"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="10dp"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/tvTime"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTime" />
</androidx.constraintlayout.widget.ConstraintLayout>