<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/padding_horizontal_base"
    tools:ignore="SpUsage">

    <TextView
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingMultiplier="1.2"
        android:text="@string/platform_4th_offers_limited_product_diversity"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="216dp"
        android:layout_marginTop="12dp"
        android:contentDescription="@string/app_name"
        android:scaleType="fitXY"
        android:src="?attr/bgMt4Confirm" />

    <TextView
        android:id="@+id/tvPlatform5"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/shape_ce35728_r100"
        android:gravity="center"
        android:text="@string/switch_to_platform_5th"
        android:textColor="@color/cffffff"
        android:textSize="16dp" />

    <TextView
        android:id="@+id/tvPlatform4"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:paddingVertical="16dp"
        android:text="@string/continue_with_platform_4th"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp" />
</LinearLayout>