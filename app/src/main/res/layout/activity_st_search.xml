<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <cn.com.vau.util.widget.HeaderBar
            android:id="@+id/mHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:hb_titleText="@string/search"
            app:layout_constraintTop_toTopOf="parent" />

        <include layout="@layout/merge_search" />

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="16dp"
            android:background="?attr/color_c0a1e1e1e_c0affffff"
            app:layout_constraintTop_toBottomOf="@id/etSearch" />

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/mViewPager"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/view" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>