<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="false"
        app:hb_titleText="@string/fx_tv"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.cardview.widget.CardView
        android:id="@+id/cardView"
        android:layout_width="match_parent"
        android:layout_height="190dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <cn.jzvd.JzvdStd
            android:id="@+id/videoPlayer"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/cardView"
        tools:text="Leveraged use in foreign" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVideoContent"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvVideoTitle"
        tools:text="You have just invested in a new vehicle, and you take it on a trip across Canada." />

    <TextView
        android:id="@+id/tvVideoTime"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="10dp"
        app:layout_constraintStart_toStartOf="@+id/tvVideoContent"
        app:layout_constraintTop_toBottomOf="@+id/tvVideoContent"
        tools:text="01/03/2018" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvViews"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:drawablePadding="4dp"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="10dp"
        app:drawableStartCompat="@drawable/img_source2_eye_12x12"
        app:drawableTint="?attr/color_c331e1e1e_c33ffffff"
        app:layout_constraintEnd_toEndOf="@+id/tvVideoContent"
        app:layout_constraintTop_toTopOf="@+id/tvVideoTime"
        tools:text="9999999" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:background="?attr/color_c331e1e1e_c33ffffff"
        app:layout_constraintTop_toBottomOf="@+id/tvVideoTime" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager" />
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
