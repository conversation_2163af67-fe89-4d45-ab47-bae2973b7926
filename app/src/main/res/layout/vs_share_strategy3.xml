<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTotalTradeTitle"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:lineSpacingExtra="4dp"
        android:text="@string/total_trade"
        android:textColor="@color/c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWinRateTitle"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="4dp"
        android:text="@string/win_rate"
        android:textColor="@color/c99ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvTotalTradeTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTotalTrade"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:lineSpacingExtra="4dp"
        android:textColor="@color/cebffffff"
        android:textSize="28dp"
        app:layout_constraintStart_toStartOf="@+id/tvTotalTradeTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvTotalTradeTitle"
        tools:text="46" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWinRate"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:lineSpacingExtra="4dp"
        android:textDirection="ltr"
        android:textSize="28dp"
        app:layout_constraintEnd_toEndOf="@+id/tvWinRateTitle"
        app:layout_constraintTop_toTopOf="@+id/tvTotalTrade"
        tools:text="+144.92%"
        tools:textColor="@color/c00c79c" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFrequentlyTradedTitle"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:text="@string/frequently_traded"
        android:textColor="@color/c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@+id/tvTotalTrade"
        app:layout_constraintTop_toBottomOf="@+id/tvTotalTrade" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvWinRateSubtitle"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/win_rate"
        android:textColor="@color/c99ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@+id/tvWinRate"
        app:layout_constraintTop_toTopOf="@+id/tvFrequentlyTradedTitle" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle1"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:textColor="@color/c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@+id/tvFrequentlyTradedTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvFrequentlyTradedTitle"
        tools:text="@string/metals" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail1"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle1"
        app:layout_constraintEnd_toEndOf="@+id/tvWinRateSubtitle"
        app:layout_constraintTop_toTopOf="@+id/tvTitle1"
        tools:text="100.12%"
        tools:textColor="@color/c00c79c" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle2"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:gravity="center_vertical"
        android:textColor="@color/c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@+id/tvTitle1"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle1"
        tools:text="@string/indices" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail2"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle2"
        app:layout_constraintEnd_toEndOf="@+id/tvDetail1"
        app:layout_constraintTop_toTopOf="@+id/tvTitle2"
        tools:text="100.12%"
        tools:textColor="@color/c00c79c" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle3"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="28dp"
        android:gravity="center_vertical"
        android:textColor="@color/c99ffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="@+id/tvTitle2"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle2"
        tools:text="@string/forex" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail3"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle3"
        app:layout_constraintEnd_toEndOf="@+id/tvDetail2"
        app:layout_constraintTop_toTopOf="@+id/tvTitle3"
        tools:text="100.12%"
        tools:textColor="@color/c00c79c" />
</androidx.constraintlayout.widget.ConstraintLayout>