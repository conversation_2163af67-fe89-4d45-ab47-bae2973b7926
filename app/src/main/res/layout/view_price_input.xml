<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clPrice"
        android:layout_width="0dp"
        android:layout_height="48dp"
        android:background="@drawable/select_login_et_bg"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivSub"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="12dp"
            android:src="?attr/icon2Sub"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvPriceTitle"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:textAlignment="center"
            android:textColor="?attr/color_c731e1e1e_c61ffffff"
            android:textDirection="ltr"
            android:textSize="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@+id/etPrice"
            app:layout_constraintEnd_toStartOf="@id/ivAdd"
            app:layout_constraintStart_toEndOf="@id/ivSub"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            tools:text="@string/price"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/etPrice"
            style="@style/gilroy_500"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:background="@null"
            android:hint="@string/not_set"
            android:inputType="numberDecimal"
            android:singleLine="true"
            android:textAlignment="center"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivAdd"
            app:layout_constraintStart_toEndOf="@id/ivSub"
            app:layout_constraintTop_toBottomOf="@+id/tvPriceTitle"
            app:layout_goneMarginTop="0dp"
            tools:text="1.3881" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivAdd"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:padding="12dp"
            android:src="@drawable/draw_bitmap2_add_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/tvTips"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="16dp"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="12dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/clPrice"
        tools:text="Min value: 105,598.81"
        tools:visibility="visible" />

</merge>