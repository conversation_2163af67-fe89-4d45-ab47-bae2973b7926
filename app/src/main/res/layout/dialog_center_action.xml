<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_r20"
    android:minHeight="168dp"
    android:paddingHorizontal="12dp"
    android:paddingVertical="20dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/DialogCenterTitleStyle"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="标题"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDetail"
        style="@style/DialogCenterContentStyle"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/tvEnd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTitle"
        app:layout_goneMarginTop="0dp"
        tools:text="hecking_hecking_\nhecking_hecking_\nhecking_hecking_\nhecking_hecking_\nhecking_hecking_\nhecking_hecking_\nhecking_hecking_\nhecking_hecking_hecking_hecking_\nhecking_hecking_hecking_hecking_\nhecking_hecking_hecking_hecking_"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvStart"
        style="@style/DialogCenterLeftButtonStyle"
        android:visibility="gone"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@id/tvEnd"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvEnd"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEnd"
        style="@style/DialogCenterRightButtonStyle"
        android:visibility="gone"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvStart"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>