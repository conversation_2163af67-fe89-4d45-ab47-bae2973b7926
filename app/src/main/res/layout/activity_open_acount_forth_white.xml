<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="cn.com.vau.page.user.openAccountForth.OpenAccountForthActivity">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:hb_endIcon1="?attr/icon1Cs"
        app:hb_titleText="@string/open_live_account"
        app:layout_constraintTop_toTopOf="parent" />

    <cn.com.vau.common.view.StepOpenAccountView
        android:id="@+id/stepView"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginHorizontal="@dimen/padding_horizontal_base"
        android:layout_marginTop="10dp"
        app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
        app:step_num="5" />

    <TextView
        android:id="@+id/tvSecondTitle"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="30dp"
        android:text="@string/declaration"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stepView" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/fold_content_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSecondTitle">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/padding_horizontal_base"
            android:paddingBottom="100dp">

            <CheckBox
                android:id="@+id/cbAgreement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:background="@null"
                android:button="@drawable/select_checkbox_agreement"
                android:checked="false"
                android:gravity="center"
                android:padding="4dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvDeclarationDesc"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/by_ticking_this_box"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintBottom_toBottomOf="@+id/cbAgreement"
                app:layout_constraintStart_toEndOf="@+id/cbAgreement"
                app:layout_constraintTop_toTopOf="@+id/cbAgreement" />

            <TextView
                android:id="@+id/tvDeclaration1"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="1. I understand that Vantage Global Prime Pty Ltd, ACN *********, AFSL No. 428901, is the issuer of the products (Foreign Exchange Contract “FX“ or “Forex” and Contracts-for-Difference “CFDs“)."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/cbAgreement"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclarationDesc"
                tools:text="abc 01" />

            <cn.com.vau.common.view.system.LinkSpanTextView
                android:id="@+id/tvDeclaration2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/gilroy_regular"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="2. I acknowledge that I have read, understood, and agreed to be bound by Vantage Global Prime Pty Ltd\'s Financial Services Guide (“FSG“), Product Disclosure Statement (“PDS“), Terms and Conditions (“T&amp;Cs“), Privacy Policy, and Vantage FX Pty Ltd\'s Privacy Policy."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration1"
                tools:text="abc 02" />

            <TextView
                android:id="@+id/tvDeclaration3"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="3. I understand and accept the risks associated with trading margin Forex and CFDs. I am prepared to accept a high degree of risk in pursuit of my investment goals and can afford to suffer a loss up to the monetary value of my trading account."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration2"
                tools:text="abc 03" />

            <TextView
                android:id="@+id/tvDeclaration4"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="4. I expressly consent to receive electronic communications from Vantage Global Prime Pty Ltd, including but not limited to trade confirmation statements, records of fund receipts, and marketing material related to trading in our products, including general market commentary."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration3"
                tools:text="abc 04" />

            <TextView
                android:id="@+id/tvDeclaration5"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="5. I confirm that the information provided by me and inserted in this form is true and correct. I acknowledge that I am obliged to notify Vantage Global Prime Pty Ltd if any of my information changes."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration4"
                tools:text="abc 05" />

            <TextView
                android:id="@+id/tvDeclaration6"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="6. I consent to Vantage Global Prime Pty Ltd authorising a third party to conduct electronic verification of my identity and perform a credit check as part of the account application process."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration5"
                tools:text="abc 06" />

            <TextView
                android:id="@+id/tvDeclaration7"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="7. I confirm that I have acted in my name as specified in this application and not on behalf of a third party in respect of all matters related to this client relationship. Accordingly, all funds to be deposited and traded on the account with Vantage are my funds."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration6"
                tools:text="abc 07" /> <!--产品经理Lyu Pin 不给其他语言文案-->

            <cn.com.vau.common.view.system.LinkSpanTextView
                android:id="@+id/tvDeclaration8"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/gilroy_regular"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="8. I have read, understood, and agreed to be bound by Vantage\'s deposits and withdrawals policy."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration7"
                tools:text="abc 08" />

            <cn.com.vau.common.view.system.LinkSpanTextView
                android:id="@+id/tvDeclaration9"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:fontFamily="@font/gilroy_regular"
                android:gravity="center_vertical|start"
                android:lineSpacingExtra="@dimen/line_spacing_extra"
                android:text="@string/st_agreements_text"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvDeclaration1"
                app:layout_constraintTop_toBottomOf="@+id/tvDeclaration8"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvNext"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="30dp"
        android:background="@drawable/bitmap_icon2_next_inactive"
        android:gravity="center"
        android:textSize="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

