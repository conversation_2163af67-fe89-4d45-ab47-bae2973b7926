<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/llNavLetterRoot"
    android:layout_width="wrap_content"
    android:layout_height="18dp"
    android:orientation="vertical"
    android:paddingStart="14dp"
    android:paddingEnd="12dp">

    <TextView
        android:id="@+id/tvNavLetterRoot"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="10dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="A" />

</androidx.constraintlayout.widget.ConstraintLayout>