<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/tvNext"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:id="@+id/viewTips"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:background="@color/c1fff8e5c"
                app:layout_constraintBottom_toBottomOf="@+id/linkTvNotificationEnableTip"
                app:layout_constraintTop_toTopOf="parent" />

            <cn.com.vau.common.view.system.LinkSpanTextView
                android:id="@+id/linkTvNotificationEnableTip"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:padding="12dp"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toStartOf="@+id/ivCloseTip"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@string/unable_to_receive_app_notifications" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivCloseTip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/margin_horizontal_base"
                android:src="@drawable/bitmap2_close10x10_ce35728"
                app:layout_constraintBottom_toBottomOf="@id/viewTips"
                app:layout_constraintEnd_toEndOf="@id/viewTips"
                app:layout_constraintTop_toTopOf="@id/viewTips" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupTips"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="viewTips,linkTvNotificationEnableTip,ivCloseTip" />

            <LinearLayout
                android:id="@+id/llSell"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="6dp"
                app:layout_constraintEnd_toStartOf="@id/tvSpread"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/linkTvNotificationEnableTip">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvSell"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/sell"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="20dp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvSellPrice"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="4dp"
                    android:textColor="@color/c00c79c"
                    app:drawableRightCompat="@drawable/img_price_up"
                    tools:text="109.575" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivSellSelected"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/margin_vertical_button"
                android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
                app:layout_constraintEnd_toEndOf="@id/llSell"
                app:layout_constraintStart_toStartOf="@id/llSell"
                app:layout_constraintTop_toBottomOf="@id/llSell" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSpread"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="10dp"
                app:layout_constraintBottom_toBottomOf="@id/llSell"
                app:layout_constraintEnd_toStartOf="@id/llBuy"
                app:layout_constraintStart_toEndOf="@id/llSell"
                app:layout_constraintTop_toTopOf="@id/llSell"
                tools:text="100" />

            <LinearLayout
                android:id="@+id/llBuy"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="12dp"
                android:layout_marginEnd="12dp"
                android:background="@drawable/draw_shape_stroke_c00c79c_solid_c0a1e1e1e_c262930_r10"
                android:gravity="center"
                android:orientation="vertical"
                android:paddingVertical="6dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvSpread"
                app:layout_constraintTop_toBottomOf="@id/linkTvNotificationEnableTip">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvBuy"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/buy"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="20dp" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvBuyPrice"
                    style="@style/gilroy_600"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="4dp"
                    android:textColor="@color/c00c79c"
                    app:drawableRightCompat="@drawable/img_price_up"
                    tools:text="109.575" />

            </LinearLayout>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivBuySelected"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:paddingVertical="@dimen/margin_vertical_button"
                android:src="@drawable/icon2_cb_tick_circle_c15b374"
                app:layout_constraintEnd_toEndOf="@id/llBuy"
                app:layout_constraintStart_toStartOf="@id/llBuy"
                app:layout_constraintTop_toBottomOf="@id/llBuy" />

            <View
                android:id="@+id/line"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/ivBuySelected"
                app:layout_goneMarginTop="@dimen/margin_vertical_button" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAlterTypeLabel"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:drawablePadding="4dp"
                android:text="@string/alert_type"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSelectType"
                style="@style/gilroy_500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:gravity="center_vertical|start"
                android:minHeight="48dp"
                android:paddingHorizontal="12dp"
                android:text="@string/price_rises_above"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvAlterTypeLabel" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvValueLabel"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="12dp"
                android:text="@string/value"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvSelectType" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clValue"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="8dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvValueLabel">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/etValue"
                    style="@style/gilroy_500"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_marginHorizontal="10dp"
                    android:background="@null"
                    android:gravity="center"
                    android:inputType="numberSigned|numberDecimal"
                    android:minHeight="48dp"
                    android:paddingVertical="6dp"
                    android:textAlignment="gravity"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/tvPercent"
                    app:layout_constraintStart_toEndOf="@id/ivValueDown"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="109.890" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPercent"
                    style="@style/gilroy_500"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="17dp"
                    android:text="%"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/ivValueUp"
                    app:layout_constraintStart_toEndOf="@id/etValue"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:ignore="HardcodedText" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivValueDown"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:padding="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/draw_bitmap2_sub_stroke_circle_c1e1e1e_cebffffff" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivValueUp"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:padding="8dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/draw_bitmap2_add_stroke_circle_c1e1e1e_cebffffff" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvPriceChange"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="8dp"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textDirection="ltr"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/clValue"
                tools:text="Price change : -104 (-0.05%)" />

            <View
                android:id="@+id/line2"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginTop="12dp"
                android:background="?attr/color_c0a1e1e1e_c0affffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvPriceChange" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvFrequencyLabel"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="12dp"
                android:layout_marginTop="12dp"
                android:drawablePadding="4dp"
                android:text="@string/frequency"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="18dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/line2" />

            <TextView
                android:id="@+id/tvFrequency"
                style="@style/gilroy_500"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:gravity="center_vertical"
                android:minHeight="48dp"
                android:paddingHorizontal="12dp"
                android:text="@string/once_only"
                android:textColor="?color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvFrequencyLabel" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvNotificationTip"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="12dp"
                android:layout_marginTop="8dp"
                android:gravity="start"
                android:text="@string/notifications_may_network_interruption"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvFrequency" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="12dp"
        android:text="@string/add_alert"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tvDelete"
        app:layout_constraintVertical_chainStyle="packed" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDelete"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:gravity="center"
        android:text="@string/delete"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/tvNext"
        app:layout_constraintEnd_toStartOf="@id/tvNext"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvNext" />

</androidx.constraintlayout.widget.ConstraintLayout>
