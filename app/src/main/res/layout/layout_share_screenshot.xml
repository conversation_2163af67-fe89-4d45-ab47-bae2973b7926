<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginHorizontal="75dp"
    android:background="?attr/mainLayoutBg">

    <View
        android:id="@+id/viewTop"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="?attr/color_cffffff_c1a1d20"
        app:layout_constraintDimensionRatio="W,8:4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivScreenshot"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="20dp"
        android:layout_marginTop="20dp"
        android:scaleType="fitXY"
        app:layout_constraintDimensionRatio="W,21:10"
        app:layout_constraintEnd_toEndOf="@id/viewTop"
        app:layout_constraintStart_toStartOf="@id/viewTop"
        app:layout_constraintTop_toTopOf="@id/viewTop" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="6dp"
        app:layout_constraintBottom_toBottomOf="@+id/viewTop"
        app:layout_constraintStart_toStartOf="parent"
        tools:text="Updated on 2023/07/20Z" />

    <View
        android:id="@+id/viewBottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@color/c1e1e1e"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="W,202:66"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewTop" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvBottomTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="8dp"
        android:maxLines="1"
        android:textColor="@color/cebffffff"
        android:textSize="10dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintEnd_toStartOf="@+id/ivQrCode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewBottom"
        app:layout_constraintVertical_chainStyle="spread"
        tools:text="Referral Code: ABC234" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHint"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="2dp"
        android:layout_marginEnd="8dp"
        android:layout_marginBottom="6dp"
        android:lineSpacingExtra="4dp"
        android:maxLines="2"
        android:text="@string/scan_the_qr_x_trading_journey"
        android:textAlignment="viewStart"
        android:textColor="@color/cebffffff"
        android:textSize="8dp"
        app:autoSizeMaxTextSize="8dp"
        app:autoSizeMinTextSize="6dp"
        app:autoSizeTextType="uniform"
        app:layout_constraintBottom_toTopOf="@+id/ivLogo"
        app:layout_constraintEnd_toStartOf="@+id/ivQrCode"
        app:layout_constraintStart_toStartOf="@id/tvBottomTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvBottomTitle"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Check out %1$s\'s latest price movement on %2$s App!" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivLogo"
        android:layout_width="48dp"
        android:layout_height="12dp"
        android:layout_marginStart="8dp"
        android:layout_marginBottom="8dp"
        android:scaleType="fitStart"
        android:src="@drawable/img_share_logo"
        app:layout_constraintBottom_toBottomOf="@id/viewBottom"
        app:layout_constraintStart_toStartOf="@id/viewBottom" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivQrCode"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginVertical="8dp"
        android:layout_marginEnd="8dp"
        app:layout_constraintBottom_toBottomOf="@id/viewBottom"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/viewBottom"
        tools:src="@drawable/img_invite_default_code" />

</androidx.constraintlayout.widget.ConstraintLayout>