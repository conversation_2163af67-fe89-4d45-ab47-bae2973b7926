<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:focusable="true"
    android:focusableInTouchMode="true">

    <TextView
        android:id="@+id/tvOrderVolumeDesc"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:text="@string/volume"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvOrderVolumeRange"
        style="@style/medium_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="22dp"
        android:text="(0.01-100)"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="@+id/tvOrderVolumeDesc"
        app:layout_constraintTop_toBottomOf="@+id/tvOrderVolumeDesc"
        tools:ignore="HardcodedText" />

    <ImageView
        android:id="@+id/ivHandCountUp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/draw_bitmap2_add_stroke_circle_c1e1e1e_cebffffff"
        app:layout_constraintStart_toStartOf="@+id/guidelineEnd"
        app:layout_constraintTop_toTopOf="parent" />

    <EditText
        android:id="@+id/etVolume"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/transparent"
        android:gravity="center"
        android:inputType="numberDecimal"
        android:text="0.01"
        android:textAlignment="gravity"
        android:textColor="@color/cf44040"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountUp"
        app:layout_constraintEnd_toEndOf="@+id/guidelineEnd"
        app:layout_constraintStart_toStartOf="@+id/guidelineCenter"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountUp"
        tools:ignore="HardcodedText" />

    <ImageView
        android:id="@+id/ivHandCountDown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="@string/app_name"
        android:src="@drawable/draw_bitmap2_sub_stroke_circle_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/ivHandCountUp"
        app:layout_constraintEnd_toEndOf="@+id/guidelineCenter"
        app:layout_constraintTop_toTopOf="@+id/ivHandCountUp" />

    <TextView
        android:id="@+id/tvCountEnd"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="16dp"
        android:background="@drawable/draw_select_text_bg_order_add"
        android:gravity="center"
        android:paddingHorizontal="10dp"
        android:paddingVertical="4dp"
        android:text="+1.00"
        android:textAlignment="center"
        android:textColor="@color/selector_new_order_point_txt_colors"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guidelineEnd"
        app:layout_constraintTop_toBottomOf="@+id/ivHandCountUp"
        app:layout_constraintVertical_chainStyle="packed"
        tools:ignore="HardcodedText" />

    <TextView
        android:id="@+id/tvCountCenter"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="5dp"
        android:background="@drawable/draw_select_text_bg_order_add"
        android:gravity="center"
        android:paddingHorizontal="10dp"
        android:paddingVertical="4dp"
        android:text="+1.00"
        android:textAlignment="center"
        android:textColor="@color/selector_new_order_point_txt_colors"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvCountEnd"
        app:layout_constraintEnd_toEndOf="@+id/guidelineEnd"
        app:layout_constraintStart_toStartOf="@+id/guidelineCenter"
        app:layout_constraintTop_toTopOf="@+id/tvCountEnd"
        tools:ignore="HardcodedText" />

    <TextView
        android:id="@+id/tvCountStart"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="5dp"
        android:background="@drawable/draw_select_text_bg_order_add"
        android:gravity="center"
        android:paddingHorizontal="10dp"
        android:paddingVertical="4dp"
        android:text="+1.00"
        android:textAlignment="center"
        android:textColor="@color/selector_new_order_point_txt_colors"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvCountEnd"
        app:layout_constraintEnd_toEndOf="@+id/guidelineCenter"
        app:layout_constraintStart_toStartOf="@+id/guidelineStart"
        app:layout_constraintTop_toTopOf="@+id/tvCountEnd"
        tools:ignore="HardcodedText" />

    <View
        android:id="@+id/viewVolume"
        style="@style/cut_off_line"
        android:layout_marginTop="19dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvOrderVolumeRange" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineEnd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.77" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineCenter"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.54" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guidelineStart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.31" />

</androidx.constraintlayout.widget.ConstraintLayout>