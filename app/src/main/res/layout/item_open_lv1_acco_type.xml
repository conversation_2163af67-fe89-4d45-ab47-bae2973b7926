<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="6dp"
    android:padding="2dp"
    tools:background="@drawable/draw_shape_stroke_c1e1e1e_cebffffff_r10">

    <ImageView
        android:id="@+id/iv_acco_type"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:contentDescription="@string/app_name"
        android:scaleType="fitXY"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>