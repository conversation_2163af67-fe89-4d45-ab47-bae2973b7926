<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/passkeyTapIv"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="?attr/imgSecurityPasskeyAuth"
        app:layout_constraintBottom_toTopOf="@id/clickIntroduceTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/clickIntroduceTv"
        style="@style/bold_semi_font"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="42dp"
        android:text="@string/tap_to_authenticate"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="16dp"
        app:layout_constraintBottom_toTopOf="@id/clickSubIntroduceTv"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/passkeyTapIv" />

    <TextView
        android:id="@+id/clickSubIntroduceTv"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="@string/verify_your_identity_a_passkey"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/clickIntroduceTv" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/switchTv"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/switch_authentication_method"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>