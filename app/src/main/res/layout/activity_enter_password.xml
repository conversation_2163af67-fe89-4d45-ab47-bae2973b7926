<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="LabelFor">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="true"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/enter_password"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title"
        android:text="@string/enter_your_login_password"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <EditText
        android:id="@+id/etPwd"
        style="@style/gilroy_500"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:background="@drawable/select_login_et_bg"
        android:gravity="center_vertical"
        android:importantForAutofill="no"
        android:inputType="textPassword"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="70dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTips"
        tools:hint="@string/_8_16_characters" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClearPwd"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:padding="8dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/etPwd"
        app:layout_constraintEnd_toStartOf="@+id/ivShowPwd"
        app:layout_constraintTop_toTopOf="@+id/etPwd"
        app:layout_goneMarginEnd="8dp"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivShowPwd"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="@+id/etPwd"
        app:layout_constraintEnd_toEndOf="@+id/etPwd"
        app:layout_constraintTop_toTopOf="@+id/etPwd"
        app:srcCompat="@drawable/draw_bitmap2_password_hide_c731e1e1e_c61ffffff" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/gilroy_600"
        android:layout_width="0px"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/submit"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etPwd" />
</androidx.constraintlayout.widget.ConstraintLayout>