<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tvCopyMode"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:text="@string/copy_mode"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCopyModeTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:src="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintBottom_toBottomOf="@id/tvCopyMode"
        app:layout_constraintStart_toEndOf="@id/tvCopyMode"
        app:layout_constraintTop_toTopOf="@id/tvCopyMode" />

    <cn.com.vau.common.view.DropDownListView
        android:id="@+id/dropListView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/tvCopyMode"
        app:popMode="bottom" />

    <TextView
        android:id="@+id/tvLeverageSelf"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dropListView"
        tools:text="Your leverage ratio: 20" />

    <TextView
        android:id="@+id/tvLeverageStrategy"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvLeverageSelf"
        tools:text="Strategy leverage ratio: 3" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/tvLeverageDiffTip"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvLeverageStrategy"
        tools:text="@string/discrepancies_between_copiers_different_order_sizes" />

    <TextView
        android:id="@+id/tvInvestment"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title_card"
        android:text="@string/investment"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvLeverageDiffTip" />

    <cn.com.vau.common.view.CurrencyFormatEditText
        android:id="@+id/etInvestmentAmount"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:ellipsize="end"
        android:gravity="center_vertical|start"
        android:inputType="numberDecimal"
        android:maxLines="1"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="48dp"
        android:singleLine="true"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvInvestment"
        tools:ignore="Autofill,LabelFor"
        tools:text="50.00" />

    <TextView
        android:id="@+id/tvInvestmentCurrency"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etInvestmentAmount"
        app:layout_constraintEnd_toEndOf="@id/etInvestmentAmount"
        app:layout_constraintTop_toTopOf="@id/etInvestmentAmount"
        tools:text="USD" />

    <TextView
        android:id="@+id/tvAvailableInvestment"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:drawablePadding="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etInvestmentAmount"
        tools:text="Available Investment: 1000.00 USD" />

    <TextView
        android:id="@+id/tvUsedCredit"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:drawablePadding="4dp"
        android:text="@string/used_credit"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableInvestment" />

    <TextView
        android:id="@+id/tvUsedCreditCurrency"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvUsedCredit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvUsedCredit"
        tools:text="USD" />

    <TextView
        android:id="@+id/tvUsedCreditAmount"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvUsedCreditCurrency"
        app:layout_constraintEnd_toStartOf="@id/tvUsedCreditCurrency"
        app:layout_constraintTop_toTopOf="@id/tvUsedCreditCurrency"
        tools:text="5.00" />

    <TextView
        android:id="@+id/tvAvailableCredit"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvUsedCredit"
        tools:text="Available Credit: 1000.00 USD" />

    <TextView
        android:id="@+id/tvUsedBalance"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:drawablePadding="4dp"
        android:text="@string/used_balance"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableCredit" />

    <TextView
        android:id="@+id/tvUsedBalanceCurrency"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvUsedBalance"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvUsedBalance"
        tools:text="USD" />

    <TextView
        android:id="@+id/tvUsedBalanceAmount"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="4dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/tvUsedBalanceCurrency"
        app:layout_constraintEnd_toStartOf="@id/tvUsedBalanceCurrency"
        app:layout_constraintTop_toTopOf="@id/tvUsedBalanceCurrency"
        tools:text="5.00" />

    <TextView
        android:id="@+id/tvAvailableBalance"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvUsedBalance"
        tools:text="Available Balance: 1000.00 USD" />

    <TextView
        android:id="@+id/tvGSUserBalanceTip"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:drawablePadding="4dp"
        android:gravity="start"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        android:visibility="gone"
        app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvAvailableBalance"
        tools:text="@string/to_qualify_for_the_x_other_currencies"
        tools:visibility="visible" />

    <!-- Equivalent Used Margin -->
    <TextView
        android:id="@+id/tvUsedMarginMultiples"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:text="@string/used_margin_multiples"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvGSUserBalanceTip" />

    <cn.com.vau.common.view.EditTextVerifyComponent
        android:id="@+id/etUsedMarginMultiples"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:inputType="numberDecimal"
        android:maxLines="1"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="48dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvUsedMarginMultiples"
        tools:ignore="Autofill,LabelFor"
        tools:text="1" />

    <TextView
        android:id="@+id/tvUsedMarginMultiplesUnit"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="X"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etUsedMarginMultiples"
        app:layout_constraintEnd_toEndOf="@id/etUsedMarginMultiples"
        app:layout_constraintTop_toTopOf="@id/etUsedMarginMultiples" />

    <TextView
        android:id="@+id/tvUsedMarginMultiplesTip"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etUsedMarginMultiples"
        tools:text="The margin percentage used for each position opening is %s times the strategy\'s default." />

    <!-- Fixed Lots -->
    <TextView
        android:id="@+id/tvLotsPerOrder"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_top_title_card"
        android:text="@string/lots_per_order"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvGSUserBalanceTip" />

    <cn.com.vau.common.view.EditTextVerifyComponent
        android:id="@+id/etLots"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:inputType="numberDecimal"
        android:maxLines="1"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="48dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvLotsPerOrder"
        tools:ignore="Autofill,LabelFor"
        tools:text="0.01" />

    <TextView
        android:id="@+id/tvLotsTitle"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="@string/lots"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etLots"
        app:layout_constraintEnd_toEndOf="@id/etLots"
        app:layout_constraintTop_toTopOf="@id/etLots" />

    <!-- Fixed Multiples -->
    <TextView
        android:id="@+id/tvMultiplesPerOrder"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_horizontal_base"
        android:text="@string/multiples_per_order"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvGSUserBalanceTip" />

    <cn.com.vau.common.view.EditTextVerifyComponent
        android:id="@+id/etMultiplesPerOrder"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
        android:ellipsize="end"
        android:gravity="center_vertical"
        android:inputType="numberDecimal"
        android:maxLines="1"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="48dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintTop_toBottomOf="@id/tvMultiplesPerOrder"
        tools:ignore="Autofill,LabelFor"
        tools:text="10" />

    <TextView
        android:id="@+id/tvMultiplesTitle"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="X"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/etMultiplesPerOrder"
        app:layout_constraintEnd_toEndOf="@id/etMultiplesPerOrder"
        app:layout_constraintTop_toTopOf="@id/etMultiplesPerOrder" />

    <TextView
        android:id="@+id/tvMultiplesPerOrderTip"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:text="@string/the_position_opened_x_the_strategy"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/etMultiplesPerOrder" />

    <!-- Groups -->
    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLeverageTip"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvLeverageSelf,tvLeverageStrategy,tvLeverageDiffTip"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupInvestment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="visible"
        app:constraint_referenced_ids="tvInvestment,etInvestmentAmount,tvInvestmentCurrency,tvAvailableInvestment,tvUsedCredit,tvUsedCreditCurrency,tvUsedCreditAmount,tvAvailableCredit,tvUsedBalance,tvUsedBalanceCurrency,tvUsedBalanceAmount,tvAvailableBalance"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupUsedMarginMultiples"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvUsedMarginMultiples,etUsedMarginMultiples,tvUsedMarginMultiplesUnit,tvUsedMarginMultiplesTip"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupLotsPerOrder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvLotsPerOrder,etLots,tvLotsTitle"
        tools:visibility="gone" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupMultiplesPerOrder"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="tvMultiplesPerOrder,etMultiplesPerOrder,tvMultiplesTitle,tvMultiplesPerOrderTip"
        tools:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>