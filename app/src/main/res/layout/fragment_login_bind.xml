<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_backClickAutoFinishDisallow="true"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/verify_phone_number"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvLoginType"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar"
        tools:text="hi,<EMAIL>" />

    <TextView
        android:id="@+id/tvForgetPwd"
        style="@style/regular_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:text="@string/after_verifying_your_you_or_email"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="13dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvLoginType" />

    <View
        android:id="@+id/bgView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/select_login_et_bg"
        app:layout_constraintBottom_toBottomOf="@id/etMobile"
        app:layout_constraintEnd_toEndOf="@id/etMobile"
        app:layout_constraintStart_toStartOf="@id/tvAreaCode"
        app:layout_constraintTop_toTopOf="@id/etMobile" />

    <TextView
        android:id="@+id/tvAreaCode"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:drawablePadding="10dp"
        android:minWidth="38dp"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:paddingVertical="@dimen/padding_vertical_base"
        android:text="+86 "
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="14dp"
        app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvForgetPwd"
        tools:ignore="HardcodedText" />

    <View
        android:layout_width="1.5dp"
        android:layout_height="23dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintStart_toStartOf="@id/etMobile"
        app:layout_constraintTop_toTopOf="@id/etMobile"
        app:layout_constraintBottom_toBottomOf="@id/etMobile"/>

    <EditText
        android:id="@+id/etMobile"
        style="@style/gilroy_500"
        android:layout_width="0px"
        android:layout_height="0px"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:background="@null"
        android:gravity="center_vertical"
        android:hint="@string/phone_number"
        android:importantForAutofill="no"
        android:inputType="number"
        android:paddingStart="@dimen/padding_horizontal_base"
        android:paddingEnd="40dp"
        android:singleLine="true"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvAreaCode"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvAreaCode"
        app:layout_constraintTop_toTopOf="@+id/tvAreaCode" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClearMobile"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginEnd="4dp"
        android:padding="8dp"
        android:src="?attr/icon2CloseCircle"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="@+id/etMobile"
        app:layout_constraintEnd_toEndOf="@+id/etMobile"
        app:layout_constraintTop_toTopOf="@+id/etMobile"
        tools:visibility="visible" />


    <TextView
        android:id="@+id/tvSendEms"
        style="@style/medium_font"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="24dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center"
        android:text="@string/send_otp_via_sms"
        android:textColor="?attr/color_c731e1e1e_c61ffffff"
        android:textSize="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/etMobile" />

    <LinearLayout
        android:id="@+id/llWhatsApp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_button"
        android:background="@drawable/shape_c3325d366_r100"
        android:gravity="center_horizontal"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvSendEms">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSendWhatsApp"
            style="@style/medium_font"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/height_button_main"
            android:drawablePadding="8dp"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:text="@string/send_otp_via_whatsapp"
            android:textColor="@color/cffffff"
            android:textSize="16dp"
            app:drawableEndCompat="@drawable/img_whatsapp"
            app:layout_constraintEnd_toStartOf="@+id/ivWhatsApp"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="@+id/llWhatsApp"
            app:layout_constraintTop_toTopOf="@id/llWhatsApp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>