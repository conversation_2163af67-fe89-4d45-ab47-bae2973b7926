<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingHorizontal="6dp">

    <View
        android:id="@+id/viewHeader"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="@+id/barrierBottom"
        app:layout_constraintEnd_toEndOf="@+id/ivHeaderMore"
        app:layout_constraintStart_toStartOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/ivAvatar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="@+id/viewHeader"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/viewHeader"
        app:shapeAppearanceOverlay="@style/circleImageStyle"
        tools:ignore="NotSibling"
        tools:src="@mipmap/ic_launcher" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNickName"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toTopOf="@+id/tvUid"
        app:layout_constraintEnd_toStartOf="@+id/ivTelegram"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/ivAvatar"
        app:layout_constraintTop_toTopOf="@+id/viewHeader"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="Vantage Vantage Vantage Vantage Vantage Vantage Vantage Vantage Vantage " />

    <ImageView
        android:id="@+id/ivTelegram"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="8dp"
        android:contentDescription="@string/app_name"
        android:src="@drawable/img_telegram_liked"
        android:visibility="gone"
        app:layout_constrainedWidth="true"
        app:layout_constraintBottom_toBottomOf="@+id/tvNickName"
        app:layout_constraintEnd_toStartOf="@+id/ivHeaderMore"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintStart_toEndOf="@+id/tvNickName"
        app:layout_constraintTop_toTopOf="@+id/tvNickName"
        app:layout_constraintVertical_chainStyle="packed"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUid"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:drawablePadding="4dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:drawableEndCompat="?attr/icon2ProfileUidCopy"
        app:layout_constraintBottom_toTopOf="@+id/llOther"
        app:layout_constraintStart_toStartOf="@+id/tvNickName"
        app:layout_constraintTop_toBottomOf="@+id/tvNickName"
        tools:text="UID:38463847" />

    <LinearLayout
        android:id="@+id/llOther"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/viewHeader"
        app:layout_constraintStart_toStartOf="@+id/tvUid"
        app:layout_constraintTop_toBottomOf="@+id/tvUid"
        tools:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvVerified"
            style="@style/gilroy_400"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
            android:drawablePadding="4dp"
            android:gravity="center"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:textSize="10dp"
            android:visibility="gone"
            tools:text="安全认证"
            tools:visibility="visible" />

        <!--       会员积分等级         -->
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivMembershipLevel"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:scaleType="fitCenter"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@+id/tvVerified"
            app:layout_constraintStart_toEndOf="@+id/tvVerified"
            app:layout_constraintTop_toTopOf="@+id/tvVerified"
            tools:src="@drawable/img_new_label"
            tools:visibility="visible" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivHeaderMore"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/ivAvatar" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrierBottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="ivAvatar,llOther" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@+id/viewHeader">

        <ViewStub
            android:id="@+id/vsBanner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base"
            android:layout="@layout/vs_layout_banner"
            tools:visibility="visible" />

        <ViewStub
            android:id="@+id/vsStCenter"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_vertical_base"
            android:layout="@layout/vs_layout_st_center"
            app:layout_constraintTop_toBottomOf="@+id/vsBanner"
            tools:visibility="visible" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>