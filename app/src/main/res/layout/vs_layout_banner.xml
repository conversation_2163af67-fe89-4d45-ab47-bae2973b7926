<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:id="@+id/mBannerCardView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:cardBackgroundColor="?attr/color_c1f1e1e1e_c1fffffff"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp"
        app:layout_constraintDimensionRatio="h,393:72"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.youth.banner.Banner
            android:id="@+id/mBanner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="0.5dp"
            app:banner_indicator_height="0dp"
            app:banner_indicator_normal_width="0dp"
            app:banner_loop_time="3000"
            app:banner_radius="10dp" />

    </androidx.cardview.widget.CardView>

    <cn.com.vau.common.view.custom.BannerIndicatorView
        android:id="@+id/mIndicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="4dp"
        android:gravity="center"
        app:layout_constraintBottom_toBottomOf="@+id/mBannerCardView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
