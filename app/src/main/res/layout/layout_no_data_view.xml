<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:ignore="MissingDefaultResource"
    tools:orientation="vertical"
    tools:gravity="center"
    tools:parentTag="android.widget.LinearLayout">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIconNd"
        android:layout_width="80dp"
        android:layout_height="80dp"
        app:layout_constraintBottom_toTopOf="@+id/tvMsgNd"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tvMsgNd"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="center_horizontal"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toTopOf="@+id/viewStubBottomBtn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivIconNd"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_goneMarginBottom="260dp"
        tools:text="默认文案" />

    <ViewStub
        android:id="@+id/viewStubBottomBtn"
        android:layout_width="wrap_content"
        android:layout_height="32dp"
        android:layout_marginTop="12dp"
        android:layout="@layout/layout_no_data_view_bottom_btn"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvMsgNd"
        app:layout_constraintVertical_chainStyle="packed" />

</merge>

