<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/settings"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/tvLogout"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:paddingBottom="10dp">

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutLanguage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_detail_text_color="?attr/color_c1e1e1e_cebffffff"
                app:item_detail_text_size="16dp"
                app:item_line_show="true"
                app:item_title="@string/language"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:item_detail="English" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutTheme"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_detail_text_color="?attr/color_c1e1e1e_cebffffff"
                app:item_detail_text_size="16dp"
                app:item_line_show="true"
                app:item_title="@string/theme"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutLanguage"
                tools:item_detail="@string/light_theme" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvScreenshotShare"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp"
                android:drawablePadding="4dp"
                android:ellipsize="end"
                android:gravity="center_vertical|start"
                android:maxWidth="250dp"
                android:maxLines="1"
                android:paddingVertical="16dp"
                android:paddingStart="12dp"
                android:text="@string/screenshot_share_popup"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_info12x12_c731e1e1e_c61ffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutTheme" />

            <cn.com.vau.util.widget.SwitchButton
                android:id="@+id/sbScreenshotShare"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                app:layout_constraintBottom_toBottomOf="@+id/tvScreenshotShare"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvScreenshotShare" />

            <View
                android:id="@+id/lineScreenshotShare"
                android:layout_width="0dp"
                android:layout_height="@dimen/cut_off_line_height"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvScreenshotShare" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutAccountActivity"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:item_line_show="true"
                app:item_title="@string/account_activity"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/lineScreenshotShare"
                tools:visibility="visible" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutFeedback"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:item_line_show="true"
                app:item_title="@string/feedback"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutAccountActivity"
                tools:visibility="visible" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutAboutUs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="@string/about_us"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutFeedback" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutClearCache"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:item_detail_text_color="?attr/color_c1e1e1e_cebffffff"
                app:item_detail_text_size="16dp"
                app:item_line_show="true"
                app:item_show_right_arrow="false"
                app:item_title="@string/clear_cache"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutAboutUs"
                tools:item_detail="2.28M"
                tools:visibility="visible" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutVersion"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_detail_text_color="?attr/color_c1e1e1e_cebffffff"
                app:item_detail_text_size="16dp"
                app:item_line_show="true"
                app:item_show_right_arrow="false"
                app:item_title="@string/version"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutClearCache"
                tools:item_detail="3.46.3" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutTnc"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_detail_text_color="?attr/color_c1e1e1e_cebffffff"
                app:item_detail_text_size="16dp"
                app:item_line_show="true"
                app:item_show_right_arrow="true"
                app:item_title="@string/account_terms_and_conditions"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutVersion" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutSwitchNoTradeLine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="切换非交易线路"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutTnc" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutSwitchTradeLine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="切换交易线路"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutSwitchNoTradeLine" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutSwitchStTradeLine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="切换跟单交易线路"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutSwitchTradeLine" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutSwitchWsLine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="切换ws线路"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutSwitchStTradeLine" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutSwitchStWsLine"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="切换跟单ws线路"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutSwitchWsLine" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutSwitchH5Line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="切换h5线路"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutSwitchStWsLine" />

            <cn.com.vau.common.view.custom.SettingItemView
                android:id="@+id/layoutJumpH5Url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:item_line_show="true"
                app:item_title="跳转h5页面"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutSwitchH5Line" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvLogout"
        style="@style/main_bottom_button_theme"
        android:layout_width="0dp"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="4dp"
        android:text="@string/log_out"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvDeleteAccount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scrollView"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvDeleteAccount"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:padding="8dp"
        android:text="@string/delete_account"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />
</androidx.constraintlayout.widget.ConstraintLayout>
