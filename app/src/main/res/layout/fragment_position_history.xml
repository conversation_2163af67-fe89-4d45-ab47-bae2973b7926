<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSymbols"
        android:layout_width="wrap_content"
        android:layout_height="16dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp"
        android:drawableEnd="@drawable/draw_bitmap2_triangle_down_tab_ca61e1e1e_c99ffffff"
        android:drawablePadding="4dp"
        android:text="@string/symbols"
        style="@style/gilroy_500"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivFilter"
        android:layout_width="12dp"
        android:layout_height="24dp"
        android:layout_marginEnd="12dp"
        android:paddingTop="6dp"
        android:paddingBottom="6dp"
        android:text="@string/symbols"
        app:layout_constraintBottom_toBottomOf="@id/tvSymbols"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvSymbols"
        app:srcCompat="@drawable/draw_bitmap2_icon_sourse2_community_filters_ca61e1e1e_c99ffffff" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTimePeriod"
        android:layout_width="0dp"
        android:layout_height="29dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="12dp"
        style="@style/gilroy_400"
        android:layout_marginEnd="12dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r100"
        android:gravity="center"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSymbols"
        tools:text="12/11/2024 - 20/12/2024" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvTimePeriod">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                tools:itemCount="2"
                tools:listitem="@layout/item_position_history" />

            <ViewStub
                android:id="@+id/mEmptyView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                android:layout="@layout/vs_layout_no_data" />

            <ViewStub
                android:id="@+id/mErrorView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginTop="12dp"
                android:layout="@layout/vs_layout_no_data" />

        </FrameLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
