<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/consParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:context="cn.com.vau.MainActivity">

    <FrameLayout
        android:id="@+id/containerFrameLayout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/lottieNavView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/popView"
        android:layout_width="match_parent"
        android:layout_height="0.1dp"
        app:layout_constraintBottom_toTopOf="@+id/lottieNavView" />

    <cn.com.vau.common.view.LottieBottomNavigationView
        android:id="@+id/lottieNavView"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivNewcomerEvent"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_marginBottom="106dp"
        android:contentDescription="@string/app_name"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideline_v80"
        app:layout_constraintStart_toStartOf="@+id/guideline_v80"
        tools:background="@color/c00c79c"
        tools:visibility="visible" />

    <ViewStub
        android:id="@+id/vsInApp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="4dp"
        android:layout="@layout/vs_layout_in_app"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_v80"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.863" />

</androidx.constraintlayout.widget.ConstraintLayout>