<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingBottom="8dp">

    <View
        android:id="@+id/viewLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="4dp"
        android:background="?attr/color_c331e1e1e_c33ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTime"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/viewLine"
        tools:text="Applied on 13/03/2024 11:12:00" />

    <TextView
        android:id="@+id/tvStrategyNameKey"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/strategy"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTime"
        app:layout_goneMarginTop="16dp" />

    <TextView
        android:id="@+id/tvStrategyName"
        style="@style/gilroy_500"
        android:layout_width="0px"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:ellipsize="end"
        android:gravity="end"
        android:maxLines="1"
        android:textAlignment="viewEnd"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tvStrategyNameKey"
        app:layout_constraintTop_toTopOf="@+id/tvStrategyNameKey"
        tools:text="Strategy 1" />

    <TextView
        android:id="@+id/tvInvestmentKey"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:text="@string/investment"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvStrategyNameKey" />

    <TextView
        android:id="@+id/tvInvestment"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvInvestmentKey"
        tools:text="100.00 USD" />
</androidx.constraintlayout.widget.ConstraintLayout>