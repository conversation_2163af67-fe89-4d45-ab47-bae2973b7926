<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="6dp"
    android:layout_marginTop="16dp"
    android:minHeight="56dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="?attr/imgProfileCryptoWallet" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        android:layout_marginStart="14dp"
        android:minWidth="10dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/ivIcon"
        app:layout_constraintStart_toStartOf="@+id/ivIcon"
        app:layout_constraintTop_toTopOf="@+id/ivIcon"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTop"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="10dp"
        android:layout_marginStart="14dp"
        android:background="@drawable/shape_ce35728_r100"
        android:gravity="center"
        android:minWidth="10dp"
        android:paddingHorizontal="3dp"
        android:textColor="@color/cffffff"
        android:textSize="6dp"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/ivIcon"
        app:layout_constraintStart_toStartOf="@+id/ivIcon"
        app:layout_constraintTop_toTopOf="@+id/ivIcon"
        tools:text="NEW"
        tools:visibility="visible" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_500"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivIcon"
        tools:text="Vantage Rewards" />
</androidx.constraintlayout.widget.ConstraintLayout>

