<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/transparent">

    <cn.com.vau.common.view.tablayout.DslTabLayout
        android:id="@+id/mTabLayout"
        android:layout_width="match_parent"
        android:layout_height="33dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        android:id="@+id/dealProdAttrType"
        layout="@layout/include_deal_product_attr_type"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@id/mTabLayout" />

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/mViewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dealProdAttrType" />

    <ViewStub
        android:id="@+id/mVsGuide1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout="@layout/vs_layout_trades_guide_1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dealProdAttrType" />

    <ViewStub
        android:id="@+id/mVsGuide2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout="@layout/vs_layout_trades_guide_2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dealProdAttrType" />

    <ViewStub
        android:id="@+id/mVsNoDataScroll2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/dealProdAttrType" />

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>