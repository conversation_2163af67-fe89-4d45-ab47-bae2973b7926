<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="48dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="6102969" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/info"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="4dp"
        android:layout_marginEnd="50dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintBottom_toBottomOf="@id/title"
        app:layout_constraintEnd_toStartOf="@id/icon"
        app:layout_constraintStart_toEndOf="@id/title"
        app:layout_constraintTop_toTopOf="@id/title"
        tools:text="SourceAAA AcountNameeeeee..." />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/icon"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:layout_marginEnd="12dp"
        android:src="@drawable/draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="?attr/color_c1f1e1e1e_c1fffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>