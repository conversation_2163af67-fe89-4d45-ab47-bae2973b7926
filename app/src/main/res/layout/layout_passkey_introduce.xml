<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/passkeyIntroduce"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/passkeyIconIv"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:src="?attr/imgSecurityPasskeyIcon"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvIntroduceTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="42dp"
        android:layout_marginEnd="24dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:text="@string/add_passkey"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/passkeyIconIv"/>

    <TextView
        android:id="@+id/introduceTv"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginEnd="24dp"
        android:gravity="center"
        android:text="@string/enable_passkeys_to_account_protection"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvIntroduceTitle" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/fingerIconIv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="64dp"
        android:src="@drawable/draw_bitmap_img_source_2fa_finger_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/introduceTv" />

    <TextView
        android:id="@+id/loginTitleTv"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="@string/login_using_biometric_passkeys"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginEnd="24dp"
        app:layout_constraintStart_toEndOf="@id/fingerIconIv"
        app:layout_constraintTop_toTopOf="@id/fingerIconIv" />

    <TextView
        android:id="@+id/loginSubTitleTv"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:text="@string/enter_your_account_fingerprint_id"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/fingerIconIv"
        app:layout_constraintTop_toBottomOf="@id/loginTitleTv" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/safeIconIv"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="50dp"
        android:layout_marginTop="64dp"
        android:src="@drawable/draw_bitmap_img_source_safe_shield_icon_c731e1e1e_c61ffffff"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/loginSubTitleTv" />

    <TextView
        android:id="@+id/safeTitleTv"
        style="@style/bold_semi_font"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:text="@string/prevent_hacking"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/safeIconIv"
        app:layout_constraintTop_toTopOf="@id/safeIconIv" />

    <TextView
        android:id="@+id/sageSubTitleTv"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="24dp"
        android:text="@string/safeguard_your_account_and_leaks"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/safeIconIv"
        app:layout_constraintTop_toBottomOf="@id/safeTitleTv" />

    <cn.com.vau.common.view.system.LinkSpanTextView
        android:id="@+id/moreTv"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/learn_more_about_passkeys"
        android:textSize="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
