<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context="cn.com.vau.trade.fragment.order.OpenTradesFragment">

    <TextView
        android:id="@+id/tvFreeInfo"
        style="@style/gilroy_400"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_c0a1e1e1e_c0affffff"
        android:gravity="center_horizontal"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        android:paddingVertical="12dp"
        android:text="@string/free_orders_are_floating_pnL"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

        <cn.com.vau.common.view.system.MyRecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvFreeInfo"
            android:orientation="vertical"
            app:layoutManager="cn.com.vau.common.view.WrapContentLinearLayoutManager"/>

    <ViewStub
        android:id="@+id/mVsNoDataScroll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout="@layout/vs_layout_no_data_scroll"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
