<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    tools:ignore="HardcodedText">

    <View
        android:id="@+id/viewBg"
        android:layout_width="292dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:background="@drawable/draw_shape_stroke_c1f1e1e1e_c262930_solid_cffffff_c262930_r8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/ivTop" />

    <ImageView
        android:id="@+id/ivTop"
        android:layout_width="16dp"
        android:layout_height="8dp"
        android:layout_marginStart="20dp"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="-2dp"
        android:contentDescription="@string/app_name"
        android:src="?attr/imgTradesGuideTop"
        app:layout_constraintBottom_toTopOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:gravity="start"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:text="@string/switch_list_mode"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        app:layout_constraintEnd_toEndOf="@+id/viewBg"
        app:layout_constraintStart_toStartOf="@+id/viewBg"
        app:layout_constraintTop_toTopOf="@+id/viewBg" />

    <TextView
        android:id="@+id/tvContent"
        style="@style/gilroy_400"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="start"
        android:lineSpacingMultiplier="1.5"
        android:paddingHorizontal="@dimen/padding_horizontal_base"
        android:text="@string/click_to_toggle_drop_down_list"
        android:textAlignment="viewStart"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        app:layout_constraintEnd_toEndOf="@+id/tvTitle"
        app:layout_constraintStart_toStartOf="@+id/tvTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvNext"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:layout_marginBottom="12dp"
        android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
        android:paddingHorizontal="14dp"
        android:paddingVertical="3dp"
        android:text="@string/next"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/tvTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvContent" />

    <TextView
        android:id="@+id/tvStep1"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:text="1/2"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvNext"
        app:layout_constraintEnd_toStartOf="@+id/tvNext"
        app:layout_constraintTop_toTopOf="@+id/tvNext" />

    <TextView
        android:id="@+id/tvSkip"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp"
        android:text="@string/skip"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvNext"
        app:layout_constraintStart_toStartOf="@+id/tvTitle"
        app:layout_constraintTop_toTopOf="@+id/tvNext" />
</androidx.constraintlayout.widget.ConstraintLayout>