<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/account_activity" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ctlTime"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginTop="@dimen/margin_top_title"
        android:background="?attr/color_c1f1e1e1e_c1fffffff">

        <TextView
            android:id="@+id/tvTime"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            tools:ignore="HardcodedText"
            tools:text="Jun 27, 2018" />

        <TextView
            android:id="@+id/ivSelectTime"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:contentDescription="@string/filters"
            android:gravity="center"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:text="@string/filters"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="10dp"
        android:layout_weight="1">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="@string/submit" />

</LinearLayout>