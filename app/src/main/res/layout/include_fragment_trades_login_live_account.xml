<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:ignore="MissingConstraints"
    tools:parentTag="androidx.constraintlayout.widget.ConstraintLayout">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/logInCard"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tvEquityTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:text="@string/equity"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvEquity"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginTop="4dp"
            android:text="..."
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="26dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvEquityTitle"
            tools:text="-123456" />

        <TextView
            android:id="@+id/tvCurrency"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintBaseline_toBaselineOf="@id/tvEquity"
            app:layout_constraintBottom_toBottomOf="@+id/tvEquity"
            app:layout_constraintStart_toEndOf="@+id/tvEquity"
            tools:text="USD" />

        <TextView
            android:id="@+id/tvFloatingPnLTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginTop="12dp"
            android:text="@string/floating_pnl"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvEquity" />

        <TextView
            android:id="@+id/tvFloatingPnL"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="@dimen/margin_horizontal_base"
            android:textAlignment="viewStart"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="16dp"
            app:layout_constraintBottom_toTopOf="@id/viewSplit"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvFloatingPnLTitle"
            tools:text="-12345678" />

        <TextView
            android:id="@+id/tvMarginLevelTitle"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:text="@string/margin_level"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="14dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvFloatingPnLTitle" />

        <TextView
            android:id="@+id/tvMarginLevel"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/tvFloatingPnL"
            tools:text="-12345678%" />

        <View
            android:id="@+id/viewSplit"
            style="@style/signal_profile_cut_off_line"
            android:layout_width="match_parent"
            android:layout_marginBottom="16dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvFloatingPnL" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</merge>