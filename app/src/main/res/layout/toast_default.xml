<?xml version="1.0" encoding="utf-8"?><!--自定义Toast布局-->
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/toast_bg"
    android:gravity="center"
    android:orientation="vertical">

    <TextView
        android:id="@+id/text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="8dp"
        android:lineSpacingExtra="@dimen/line_spacing_extra"
        android:textColor="@color/cffffff"
        android:gravity="center"
        android:textSize="14dp"
        tool:text="Incorrect phone number. Please try again.Incorrect phone number. Please try again." />

</LinearLayout>

