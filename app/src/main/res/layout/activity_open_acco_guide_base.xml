<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data></data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_root"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <cn.com.vau.util.widget.HeaderBar
            android:id="@+id/mHeaderBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:hb_endIcon="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
            app:hb_endIcon1="?attr/icon1Cs"
            app:layout_constraintTop_toTopOf="parent"/>

        <TextView
            android:id="@+id/tv_title"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            android:layout_marginTop="@dimen/margin_top_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/mHeaderBar"
            tools:text="Lv1. Account Opening" />

        <TextView
            android:id="@+id/tv_title_tip"
            style="@style/regular_font"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_horizontal_base"
            android:layout_marginTop="10dp"
            android:textColor="?attr/color_ca61e1e1e_c99ffffff"
            android:textSize="12dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            tools:text="Complete a few steps to open an account and deposit money" />

        <LinearLayout
            android:id="@+id/ll_tablayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="20dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_title_tip">

            <LinearLayout
                android:id="@+id/ll_tab1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_tab1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    tools:src="@drawable/img_open_step_personal" />

                <TextView
                    android:id="@+id/tv_tab1"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:ellipsize="end"
                    android:gravity="center_horizontal"
                    android:maxLines="2"
                    android:paddingHorizontal="@dimen/padding_horizontal_base"
                    android:textColor="@drawable/select_open_acco_guide_tab_textcolor"
                    android:textSize="10dp"
                    tools:text="Personal InformationPersonal InformationPersonal InformationPersonal InformationPersonal InformationPersonal InformationPersonal Information" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_tab2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/iv_tab2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    tools:src="@drawable/img_open_step_personal" />

                <TextView
                    android:id="@+id/tv_tab2"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:ellipsize="end"
                    android:gravity="center_horizontal"
                    android:maxLines="2"
                    android:paddingHorizontal="@dimen/padding_horizontal_base"
                    android:textColor="@drawable/select_open_acco_guide_tab_textcolor"
                    android:textSize="10dp"

                    tools:text="Personal InformationPersonal InformationPersonal InformationPersonal InformationPersonal Information" />

            </LinearLayout>

            <LinearLayout
                android:id="@+id/ll_tab3"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                android:visibility="visible"
                tools:visibility="gone">

                <ImageView
                    android:id="@+id/iv_tab3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="@string/app_name"
                    tools:src="@drawable/img_open_step_personal" />

                <TextView
                    android:id="@+id/tv_tab3"
                    style="@style/medium_font"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:ellipsize="end"
                    android:gravity="center_horizontal"
                    android:maxLines="2"
                    android:paddingHorizontal="@dimen/padding_horizontal_base"
                    android:textColor="@drawable/select_open_acco_guide_tab_textcolor"
                    android:textSize="10dp"

                    tools:text="Personal InformationPersonal InformationPersonal InformationPersonal InformationPersonal Information" />

            </LinearLayout>

        </LinearLayout>

        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/mViewPager"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ll_tablayout" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>