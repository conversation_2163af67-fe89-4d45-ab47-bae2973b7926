<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/tvTab"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_gravity="center"
        android:minWidth="38dp"
        android:paddingHorizontal="8dp"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="1D" />

</FrameLayout>