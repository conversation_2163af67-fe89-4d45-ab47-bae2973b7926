<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingHorizontal="@dimen/margin_horizontal_base"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:id="@+id/bgView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/select_login_et_bg"
                app:layout_constraintBottom_toBottomOf="@id/tvMobile"
                app:layout_constraintEnd_toEndOf="@id/tvMobile"
                app:layout_constraintStart_toStartOf="@id/tvAreaCode"
                app:layout_constraintTop_toTopOf="@id/tvMobile" />

            <TextView
                android:id="@+id/tvAreaCode"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title"
                android:drawablePadding="10dp"
                android:minWidth="38dp"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:paddingVertical="@dimen/padding_vertical_base"
                android:text="+86 "
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="14dp"
                app:drawableEndCompat="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="HardcodedText" />

            <View
                android:layout_width="1.5dp"
                android:layout_height="23dp"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintBottom_toBottomOf="@id/tvMobile"
                app:layout_constraintStart_toStartOf="@id/tvMobile"
                app:layout_constraintTop_toTopOf="@id/tvMobile" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMobile"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:paddingVertical="@dimen/padding_vertical_base"
                android:singleLine="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                app:layout_constraintBottom_toBottomOf="@+id/tvAreaCode"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/tvAreaCode"
                app:layout_constraintTop_toTopOf="@+id/tvAreaCode"
                tools:text="1234567890" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSecurityCodePrompt"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:text="@string/funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/tvAreaCode"
                app:layout_constraintTop_toBottomOf="@+id/tvAreaCode" />

            <cn.com.vau.common.view.ClearAndHideEditText
                android:id="@+id/etSecurityCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:etFontFamily="medium"
                app:etTextSize="14"
                app:hint="@string/set_a_8_to_16_digit_funds_password"
                app:inputType="textPassword"
                app:is_show="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvSecurityCodePrompt"
                app:layout_constraintTop_toBottomOf="@+id/tvSecurityCodePrompt"
                app:rootBackground="@drawable/select_login_et_bg"
                app:textColor="?attr/color_c1e1e1e_cebffffff"
                app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvConfirmSecurityCodePrompt"
                style="@style/gilroy_500"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:text="@string/confirm_your_funds_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="14dp"
                app:layout_constraintStart_toStartOf="@+id/etSecurityCode"
                app:layout_constraintTop_toBottomOf="@+id/etSecurityCode" />

            <cn.com.vau.common.view.ClearAndHideEditText
                android:id="@+id/etConfirmSecurityCode"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                app:etFontFamily="medium"
                app:etTextSize="14"
                app:hint="@string/confirm_your_funds_password"
                app:inputType="textPassword"
                app:is_show="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@+id/tvConfirmSecurityCodePrompt"
                app:layout_constraintTop_toBottomOf="@+id/tvConfirmSecurityCodePrompt"
                app:rootBackground="@drawable/select_login_et_bg"
                app:textColor="?attr/color_c1e1e1e_cebffffff"
                app:textColorHint="?attr/color_c731e1e1e_c61ffffff" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvInputPrompt"
                style="@style/gilroy_400"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:lineSpacingExtra="6dp"
                android:text="@string/funds_password_is_and_please_in_password"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/etConfirmSecurityCode" />

            <include
                android:id="@+id/layoutPasswordCheck"
                layout="@layout/include_password_check"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_button"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvInputPrompt" />

            <TextView
                android:id="@+id/tvSendEms"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="24dp"
                android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
                android:gravity="center"
                android:paddingVertical="@dimen/padding_vertical_base"
                android:text="@string/send_otp_via_sms"
                android:textColor="?attr/color_c731e1e1e_c61ffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/layoutPasswordCheck" />

            <View
                android:id="@+id/viewWhatsApp"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:background="@drawable/shape_c3325d366_r100"
                app:layout_constraintBottom_toBottomOf="@+id/tvSendWhatsApp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvSendEms" />

            <TextView
                android:id="@+id/tvSendWhatsApp"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingVertical="@dimen/padding_vertical_base"
                android:text="@string/send_otp_via_whatsapp"
                android:textColor="@color/cffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toStartOf="@+id/ivWhatsApp"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="@+id/viewWhatsApp"
                app:layout_constraintTop_toTopOf="@id/viewWhatsApp" />

            <ImageView
                android:id="@+id/ivWhatsApp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:src="@drawable/img_whatsapp"
                app:layout_constraintBottom_toBottomOf="@+id/tvSendWhatsApp"
                app:layout_constraintEnd_toEndOf="@+id/viewWhatsApp"
                app:layout_constraintStart_toEndOf="@+id/tvSendWhatsApp"
                app:layout_constraintTop_toTopOf="@+id/tvSendWhatsApp" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupWhatsApp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:constraint_referenced_ids="ivWhatsApp,viewWhatsApp,tvSendWhatsApp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>