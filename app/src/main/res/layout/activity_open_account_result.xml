<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".page.user.openAccoGuide.result.OpenAccountLvResultActivity">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivClose"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:paddingHorizontal="12dp"
        android:paddingVertical="14dp"
        android:src="@drawable/draw_bitmap2_close16x16_c731e1e1e_c61ffffff"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        app:layout_constraintBottom_toTopOf="@+id/tvNext"
        app:layout_constraintTop_toBottomOf="@+id/ivClose">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlPromo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvPromoHint"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="8dp"
                    android:gravity="start"
                    android:text="@string/view_more_exclusive_promotions"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="16dp"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.cardview.widget.CardView
                    android:id="@+id/mCardView"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    app:cardBackgroundColor="?attr/color_c1f1e1e1e_c1fffffff"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintDimensionRatio="h,8:5"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvPromoHint">

                    <com.youth.banner.Banner
                        android:id="@+id/mBanner"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_margin="0.5dp"
                        android:background="@drawable/shape_placeholder_r10"
                        app:banner_indicator_height="0dp"
                        app:banner_indicator_normal_width="0dp"
                        app:banner_loop_time="3000" />

                </androidx.cardview.widget.CardView>

                <cn.com.vau.common.view.custom.BannerIndicatorView
                    android:id="@+id/mIndicator"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/indicator_margin_top"
                    android:gravity="center"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/mCardView" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/ctlCoupon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/ctlPromo"
                tools:visibility="visible">

                <TextView
                    android:id="@+id/tvCouponHint"
                    style="@style/medium_font"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="@dimen/margin_vertical_base"
                    android:gravity="start"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:paddingHorizontal="@dimen/padding_horizontal_base"
                    android:text="@string/this_coupon_has_been_issued_to_you"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintTop_toTopOf="parent" />

                <include
                    android:id="@+id/layoutCoupon"
                    layout="@layout/include_item_coupon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                    android:layout_marginTop="20dp"
                    app:layout_constraintTop_toBottomOf="@+id/tvCouponHint" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <include
                android:id="@+id/layoutBottom"
                layout="@layout/include_result_open_account_bottom"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/ctlCoupon"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="@dimen/margin_vertical_base"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/tvNextLink"
        tools:text="Deposit Preparation"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/tvNextLink"
        style="@style/bold_semi_font"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="20dp"
        android:gravity="center_horizontal"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="14dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:text="Complete ID Verification"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>