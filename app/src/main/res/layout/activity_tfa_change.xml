<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Cs"
        app:hb_titleText="@string/two_factor_authentication"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivOtp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="20dp"
        android:src="?attr/img2faPhoneOtpSelect"
        app:layout_constraintEnd_toStartOf="@id/iv2FA"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/iv2FA" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv2FA"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="@dimen/margin_vertical_base"
        android:src="?attr/img2faUnselect"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ivOtp"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvOtp"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="@string/phone_otp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="@id/ivOtp"
        app:layout_constraintStart_toStartOf="@+id/ivOtp"
        app:layout_constraintTop_toBottomOf="@+id/ivOtp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv2FA"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="2FA"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="10dp"
        app:layout_constraintEnd_toEndOf="@id/iv2FA"
        app:layout_constraintStart_toStartOf="@+id/iv2FA"
        app:layout_constraintTop_toBottomOf="@+id/iv2FA"
        tools:ignore="HardcodedText" />

    <View
        android:id="@+id/line1"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginHorizontal="8dp"
        android:background="?attr/color_c1e1e1e_cebffffff"
        app:layout_constraintBottom_toBottomOf="@id/iv2FA"
        app:layout_constraintEnd_toStartOf="@id/iv2FA"
        app:layout_constraintStart_toEndOf="@id/ivOtp"
        app:layout_constraintTop_toTopOf="@+id/iv2FA" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTop"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:constraint_referenced_ids="ivOtp,iv2FA,tvOtp,tv2FA,line1"
        tools:visibility="visible" />

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/changeNav"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="32dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvOtp"
        app:layout_goneMarginTop="0dp"
        app:navGraph="@navigation/tfa_change_navigation"
        tools:ignore="MissingConstraints" />
</androidx.constraintlayout.widget.ConstraintLayout>
