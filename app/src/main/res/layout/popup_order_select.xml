<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/draw_shape_cffffff_c1a1d20_top_r20"
    android:orientation="vertical"
    android:paddingStart="26dp"
    android:paddingEnd="26dp"
    android:paddingBottom="20dp">

    <View
        android:id="@+id/viewLine"
        android:layout_width="32dp"
        android:layout_height="4dp"
        android:layout_marginTop="6dp"
        android:background="@drawable/draw_shape_c4d1e1e1e_c4dffffff_r4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvTitle"
        style="@style/gilroy_600"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:layout_marginEnd="60dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="18dp"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="Title" />

    <TextView
        android:id="@+id/tvDone"
        style="@style/gilroy_500"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingStart="15dp"
        android:paddingVertical="10dp"
        android:textColor="@color/ce35728"
        android:textSize="16dp"
        android:text="@string/done"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@+id/tvTitle"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="parent" />

    <cn.com.vau.common.view.system.MaxLimitRecyclerView
        android:id="@+id/mRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:limit_maxHeight="192dp"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" /> <!-- 192dp = 48 * 4dp -->

</androidx.constraintlayout.widget.ConstraintLayout>
