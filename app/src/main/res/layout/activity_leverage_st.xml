<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/clParent"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/leverage"
        app:layout_constraintTop_toTopOf="parent" />

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/mSmartRefreshLayout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@+id/tvConfirm"
        app:layout_constraintTop_toBottomOf="@+id/mHeaderBar">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title"
                android:paddingHorizontal="@dimen/padding_horizontal_base">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlCurrentLeverage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tvCurrencyLeverageTitle"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:gravity="center_vertical"
                        android:text="@string/current_leverage"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvCurrencyLeverage"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:text="100:1" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ctlLeverageChange"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r10"
                    app:layout_constraintTop_toBottomOf="@+id/ctlCurrentLeverage">

                    <TextView
                        style="@style/medium_font"
                        android:layout_width="0dp"
                        android:layout_height="48dp"
                        android:layout_marginStart="@dimen/margin_horizontal_base"
                        android:layout_marginEnd="8dp"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="@string/leverage_change"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toStartOf="@+id/tvLeverageChange"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tvLeverageChange"
                        style="@style/gilroy_500"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/margin_horizontal_base"
                        android:drawableEnd="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff"
                        android:drawablePadding="8dp"
                        android:textColor="?attr/color_c1e1e1e_cebffffff"
                        android:textSize="14dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:ignore="UseCompatTextViewDrawableXml"
                        tools:text="100:1" />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/textView7"
                    style="@style/regular_font"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:text="@string/your_account_must_leverag"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintTop_toBottomOf="@+id/ctlLeverageChange"
                    app:layout_goneMarginTop="10dp" />

                <CheckBox
                    android:id="@+id/cbAgreement"
                    style="@style/medium_font"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:background="@null"
                    android:button="@drawable/select_checkbox_agreement"
                    android:checked="false"
                    android:paddingStart="10dp"
                    android:paddingEnd="0dp"
                    android:text="@string/terms_and_conditions"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView7" />

                <TextView
                    android:id="@+id/textView6"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:text="@string/stop_limit_price"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="14dp"
                    android:visibility="gone"
                    app:layout_constraintTop_toBottomOf="@+id/cbAgreement"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/textView8"
                    style="@style/regular_font"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:lineSpacingExtra="@dimen/line_spacing_extra"
                    android:paddingBottom="30dp"
                    android:text="@string/by_requesting_a_have_deposited"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/textView6" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <TextView
        android:id="@+id/tvConfirm"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:gravity="center"
        android:text="@string/confirm"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
