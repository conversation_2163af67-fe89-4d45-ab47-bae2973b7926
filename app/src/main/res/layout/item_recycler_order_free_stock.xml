<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tool="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ifvBg"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:scaleType="fitXY"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tvProdName"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginTop="12dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textDirection="ltr"
        android:textSize="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tool:text="VAU-TEST" />

    <ImageView
        android:id="@+id/ivKLine"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="2dp"
        android:contentDescription="@string/app_name"
        android:padding="2dp"
        android:src="@drawable/draw_bitmap2_view_chart_ca61e1e1e_c99ffffff"
        app:layout_constraintBottom_toBottomOf="@+id/tvProdName"
        app:layout_constraintStart_toEndOf="@+id/tvProdName"
        app:layout_constraintTop_toTopOf="@+id/tvProdName" />

    <TextView
        android:id="@+id/tvPnlTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:text="@string/pnl"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvProdName"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 浮动盈亏 -->
    <TextView
        android:id="@+id/tvPnl"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="@color/ce35728"
        android:textSize="18dp"
        app:layout_constraintBaseline_toBaselineOf="@+id/tvVolume"
        app:layout_constraintEnd_toEndOf="parent"
        tool:text="1.234567" />

    <View
        android:id="@+id/mView"
        android:layout_width="0dp"
        android:layout_height="20dp"
        android:layout_marginTop="4dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvProdName" />

    <TextView
        android:id="@+id/tvExpiringSoon"
        style="@style/gilroy_600"
        android:layout_width="wrap_content"
        android:layout_height="20dp"
        android:background="@drawable/shape_c1ff44040_r100"
        android:gravity="center_vertical"
        android:paddingHorizontal="8dp"
        android:text="@string/expiring_soon"
        android:textColor="@color/cf44040"
        android:textSize="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mView"
        app:layout_constraintStart_toStartOf="@+id/tvProdName"
        app:layout_constraintTop_toTopOf="@+id/mView"
        tool:ignore="Smalldp"
        tool:visibility="visible" />

    <TextView
        android:id="@+id/tvFreeStock"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvExpiringSoon"
        app:layout_constraintStart_toEndOf="@+id/tvExpiringSoon"
        app:layout_constraintTop_toTopOf="@+id/tvExpiringSoon"
        app:layout_goneMarginStart="0dp"
        tool:text="免费订单" />

    <!-- 手数 -->
    <TextView
        android:id="@+id/tvVolume"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_horizontal_base"
        android:layout_marginBottom="0dp"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvExpiringSoon"
        app:layout_constraintStart_toEndOf="@+id/tvFreeStock"
        app:layout_constraintTop_toTopOf="@+id/tvExpiringSoon"
        tool:text="0.20Lots" />

    <TextView
        android:id="@+id/tvRedeemedDateTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:singleLine="true"
        android:text="@string/redeemed_date"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="11dp"
        app:layout_constraintStart_toStartOf="@+id/tvProdName"
        app:layout_constraintTop_toBottomOf="@+id/mView"
        tool:ignore="Smalldp" />

    <TextView
        android:id="@+id/tvDate"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:gravity="end"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvRedeemedDateTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mBarrier"
        app:layout_constraintTop_toTopOf="@+id/tvRedeemedDateTitle"
        tool:text="#Date time" />

    <TextView
        android:id="@+id/tvExpiryDateTitle"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:singleLine="true"
        android:text="@string/expiry_date"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="11dp"
        app:layout_constraintStart_toStartOf="@+id/tvProdName"
        app:layout_constraintTop_toBottomOf="@+id/tvRedeemedDateTitle"
        tool:ignore="Smalldp" />

    <TextView
        android:id="@+id/tvExpiryDate"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:gravity="end"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvExpiryDateTitle"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/mBarrier"
        app:layout_constraintTop_toTopOf="@+id/tvExpiryDateTitle"
        tool:text="#Date time" />

    <TextView
        android:id="@+id/tvWithdrawIn"
        style="@style/gilroy_400"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="12dp"
        android:ellipsize="end"
        android:maxWidth="100dp"
        android:singleLine="true"
        android:text="@string/trade_requirement"
        android:textColor="?attr/color_ca61e1e1e_c99ffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toTopOf="@+id/tvClose"
        app:layout_constraintStart_toStartOf="@+id/tvExpiryDateTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvExpiryDateTitle"
        tool:ignore="Smalldp" />

    <TextView
        android:id="@+id/tvUnlockLot"
        style="@style/gilroy_500"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="@dimen/margin_horizontal_base"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="11dp"
        app:layout_constraintBottom_toBottomOf="@+id/tvWithdrawIn"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@+id/tvWithdrawIn"
        tool:ignore="Smalldp"
        tool:text="0.5/1 Lots" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/mBarrier"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="end"
        app:constraint_referenced_ids="tvRedeemedDateTitle,tvExpiryDateTitle,tvWithdrawIn" />

    <TextView
        android:id="@+id/tvClose"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginBottom="20dp"
        android:background="@drawable/draw_shape_c0a1e1e1e_c0affffff_r100"
        android:gravity="center_horizontal"
        android:paddingVertical="8dp"
        android:text="@string/close__position"
        android:textColor="?attr/color_c1e1e1e_cebffffff"
        android:textSize="12dp"
        app:layout_constraintBottom_toTopOf="@+id/offView" />

    <View
        android:id="@+id/offView"
        android:layout_width="match_parent"
        android:layout_height="12dp"
        app:layout_constraintBottom_toBottomOf="parent"
        tool:background="?attr/color_c0a1e1e1e_c0affffff" />

</androidx.constraintlayout.widget.ConstraintLayout>
