<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clAccountCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_top_card_title"
        android:background="@drawable/draw_shape_stroke_c331e1e1e_c33ffffff_r10"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/splitView"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="51dp"
            android:background="?attr/color_c1f1e1e1e_c1fffffff"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvAccountType"
            style="@style/bold_semi_font"
            android:layout_width="wrap_content"
            android:layout_height="20dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:gravity="center"
            android:minWidth="40dp"
            android:paddingHorizontal="6dp"
            android:textColor="@color/cffffff"
            android:textSize="11dp"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:background="@drawable/shape_c00c79c_r100"
            tools:text="@string/live" />

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="5dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:visibility="visible"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintEnd_toStartOf="@id/ivSwitch"
            app:layout_constraintStart_toEndOf="@id/tvAccountType"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvAccountName"
                style="@style/gilroy_400"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:drawablePadding="6dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxWidth="80dp"
                android:maxLines="1"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:drawableEndCompat="?attr/icon2Edit12x12"
                tools:text="324134123411234134" />

        </LinearLayout>

        <ImageView
            android:id="@+id/ivArrow"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_horizontal_base"
            android:paddingStart="6dp"
            android:src="@drawable/draw_bitmap2_arrow_end10x10_c1e1e1e_cebffffff"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvReadOnly"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center_vertical"
            android:paddingStart="3dp"
            android:text="@string/read_only"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintEnd_toStartOf="@id/ivArrow"
            app:layout_constraintTop_toTopOf="parent"
            tools:visibility="visible" />

        <TextView
            android:id="@+id/tvAccountNo"
            style="@style/gilroy_500"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:gravity="center_vertical"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="14dp"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintEnd_toStartOf="@id/tvReadOnly"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="**********" />

        <ImageView
            android:id="@+id/ivSwitch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:contentDescription="@string/app_name"
            android:padding="12dp"
            android:src="@drawable/icon_source2_switch_account"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintEnd_toStartOf="@id/tvAccountNo"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/ce35728"
            tools:visibility="visible" />

        <LinearLayout
            android:id="@+id/llEditNick"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginVertical="2dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:background="?attr/mainLayoutBg"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/splitView"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/ivArrow"
            tools:visibility="gone">

            <EditText
                android:id="@+id/etNick"
                android:layout_width="135dp"
                android:layout_height="match_parent"
                android:layout_marginStart="8dp"
                android:background="@null"
                android:maxLength="20"
                android:hint="@string/set_account_name"
                android:textColorHint="?attr/color_c731e1e1e_c61ffffff"
                android:singleLine="true"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp" />

            <ImageView
                android:id="@+id/ivClose"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:src="@drawable/draw_bitmap2_close10x10_c731e1e1e_c61ffffff" />

            <ImageView
                android:id="@+id/ivSave"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:src="@drawable/draw_bitmap2_confirm10x10_c1e1e1e_cebffffff" />
        </LinearLayout>

        <TextView
            android:id="@+id/tvAccountAmount"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_horizontal_base"
            android:layout_marginTop="10dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textDirection="ltr"
            android:textSize="26dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/splitView"
            tools:text="100,000.00" />

        <TextView
            android:id="@+id/tvAccountAmountUnit"
            style="@style/gilroy_600"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginTop="10dp"
            android:textColor="?attr/color_c1e1e1e_cebffffff"
            android:textSize="18dp"
            app:layout_constraintBaseline_toBaselineOf="@id/tvAccountAmount"
            app:layout_constraintStart_toEndOf="@id/tvAccountAmount"
            tools:text="USD" />

        <TextView
            android:id="@+id/tvReset"
            style="@style/medium_font"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/margin_horizontal_base"
            android:text="@string/reset"
            android:textColor="@color/ce35728"
            android:textSize="14dp"
            android:visibility="gone"
            app:layout_constraintBaseline_toBaselineOf="@id/tvAccountAmount"
            app:layout_constraintEnd_toEndOf="parent"
            tools:visibility="visible" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clAccountInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/llExt"
            app:layout_constraintTop_toBottomOf="@id/tvAccountAmount"
            tools:visibility="visible">

            <TextView
                android:id="@+id/tvWinRate"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_top_title"
                android:text="@string/win_rate"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvWinRateNum"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="5dp"
                android:text="..."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintEnd_toStartOf="@id/gl50"
                app:layout_constraintStart_toStartOf="@id/tvWinRate"
                app:layout_constraintTop_toBottomOf="@id/tvWinRate"
                tools:text="0.00%" />

            <TextView
                android:id="@+id/tvProfit"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/close_profit"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvWinRateNum" />

            <TextView
                android:id="@+id/tvProfitNum"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="5dp"
                android:text="..."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintEnd_toStartOf="@id/gl50"
                app:layout_constraintStart_toStartOf="@id/tvProfit"
                app:layout_constraintTop_toBottomOf="@id/tvProfit"
                tools:text="0.00 USD" />

            <TextView
                android:id="@+id/tvType"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/type"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tvMarginLevelNum" />

            <TextView
                android:id="@+id/tvTypeNum"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="5dp"
                android:text="..."
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toStartOf="@id/gl50"
                app:layout_constraintStart_toStartOf="@id/tvType"
                app:layout_constraintTop_toBottomOf="@id/tvType"
                tools:text="Pro ECN" />

            <TextView
                android:id="@+id/tvFreeMargin"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title"
                android:text="@string/free_margin"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toEndOf="@id/gl50"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tvFreeMarginNum"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="5dp"
                android:text="..."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvFreeMargin"
                app:layout_constraintTop_toBottomOf="@id/tvFreeMargin"
                tools:text="100,000.00 USD" />

            <TextView
                android:id="@+id/tvMarginLevel"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/margin_level"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toEndOf="@id/gl50"
                app:layout_constraintTop_toBottomOf="@id/tvFreeMarginNum" />

            <TextView
                android:id="@+id/tvMarginLevelNum"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="5dp"
                android:text="..."
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textDirection="ltr"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvMarginLevel"
                app:layout_constraintTop_toBottomOf="@id/tvMarginLevel"
                tools:text="100,000.00 USD" />

            <TextView
                android:id="@+id/tvLeverage"
                style="@style/regular_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:text="@string/leverage"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintStart_toEndOf="@id/gl50"
                app:layout_constraintTop_toBottomOf="@id/tvProfitNum" />

            <TextView
                android:id="@+id/tvLeverageNum"
                style="@style/medium_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:layout_marginEnd="5dp"
                android:text="..."
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="@id/tvLeverage"
                app:layout_constraintTop_toBottomOf="@id/tvLeverage"
                tools:text="500:1" />

            <TextView
                android:id="@+id/tvLoginTime"
                style="@style/regular_font"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:layout_marginEnd="5dp"
                android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toStartOf="@id/gl50"
                app:layout_constraintStart_toStartOf="@id/tvTypeNum"
                app:layout_constraintTop_toBottomOf="@id/tvTypeNum"
                tools:text="Last Login 22/04/2023" />

            <TextView
                android:id="@+id/tvDemoReset"
                style="@style/medium_font"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/margin_horizontal_base"
                android:text="@string/reset"
                android:textColor="@color/ce35728"
                android:textSize="14dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/tvLoginTime"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvLoginTime"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvLiveUpgrade"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_vertical_base"
                android:background="@drawable/draw_shape_c1f1e1e1e_c1fffffff_r100"
                android:paddingHorizontal="17dp"
                android:paddingVertical="7dp"
                android:text="@string/upgrade_to_x"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="14dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvLoginTime"
                tools:visibility="visible" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/gl50"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.5" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/llExt"
            android:layout_width="match_parent"
            android:layout_height="37dp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/clAccountInfo">

            <ImageView
                android:id="@+id/ivExtent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>