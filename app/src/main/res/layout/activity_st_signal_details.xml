<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:ignore="HardcodedText">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_endIcon="?attr/icon1Faq" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/appbarLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent"
            android:orientation="vertical"
            app:elevation="0px">

            <!-- 需要被推走的布局 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginVertical="@dimen/margin_top_title"
                app:layout_scrollFlags="scroll">

                <com.google.android.material.imageview.ShapeableImageView
                    android:id="@+id/ivAvatar"
                    android:layout_width="64dp"
                    android:layout_height="64dp"
                    android:scaleType="centerCrop"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:shapeAppearanceOverlay="@style/circleImageStyle"
                    tools:src="@mipmap/ic_launcher" />

                <TextView
                    android:id="@+id/tvNick"
                    style="@style/gilroy_600"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="9dp"
                    android:layout_marginBottom="4dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintBottom_toTopOf="@+id/tvLocation"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@+id/ivAvatar"
                    app:layout_constraintTop_toTopOf="@+id/ivAvatar"
                    app:layout_constraintVertical_chainStyle="packed"
                    tools:text="Signal Provider Name" />

                <TextView
                    android:id="@+id/tvLocation"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="4dp"
                    android:gravity="center"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:drawableStartCompat="@drawable/img_source_location"
                    app:drawableTint="?attr/color_c731e1e1e_c61ffffff"
                    app:layout_constraintBottom_toTopOf="@+id/tvProviderId"
                    app:layout_constraintStart_toStartOf="@+id/tvNick"
                    app:layout_constraintTop_toBottomOf="@+id/tvNick"
                    tools:text="Singapore" />

                <TextView
                    android:id="@+id/tvProviderId"
                    style="@style/gilroy_400"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textDirection="ltr"
                    android:textSize="12dp"
                    app:layout_constraintBottom_toBottomOf="@+id/ivAvatar"
                    app:layout_constraintStart_toStartOf="@+id/tvNick"
                    app:layout_constraintTop_toBottomOf="@+id/tvLocation"
                    tools:text="Provider ID：1234567" />

                <cn.com.vau.common.view.expandabletextview.ExpandableTextView
                    android:id="@+id/tvIntro"
                    style="@style/gilroy_400"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:lineSpacingMultiplier="1.2"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    android:visibility="gone"
                    app:ep_end_color="?attr/color_c731e1e1e_c61ffffff"
                    app:ep_expand_text="@string/view_more"
                    app:ep_max_line="2"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ivAvatar"
                    tools:text="@string/copiers" />

                <TextView
                    android:id="@+id/tvCopiersKey"
                    style="@style/gilroy_400"
                    android:layout_width="0px"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:ellipsize="end"
                    android:gravity="start"
                    android:maxLines="1"
                    android:text="@string/copiers"
                    android:textAlignment="viewStart"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvFollowersKey"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvIntro" />

                <TextView
                    android:id="@+id/tvCopiers"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvCopiersKey"
                    app:layout_constraintStart_toStartOf="@+id/tvCopiersKey"
                    app:layout_constraintTop_toBottomOf="@+id/tvCopiersKey"
                    tools:text="33" />

                <TextView
                    android:id="@+id/tvFollowersKey"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@string/followers"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvStrategiesKey"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvCopiersKey"
                    app:layout_constraintTop_toTopOf="@+id/tvCopiersKey" />

                <TextView
                    android:id="@+id/tvFollowers"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvFollowersKey"
                    app:layout_constraintStart_toStartOf="@+id/tvFollowersKey"
                    app:layout_constraintTop_toTopOf="@+id/tvCopiers"
                    tools:text="100" />

                <TextView
                    android:id="@+id/tvStrategiesKey"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@string/strategies"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toStartOf="@+id/tvDaysJoinesKey"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvFollowersKey"
                    app:layout_constraintTop_toTopOf="@+id/tvCopiersKey" />

                <TextView
                    android:id="@+id/tvStrategies"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvStrategiesKey"
                    app:layout_constraintStart_toStartOf="@+id/tvStrategiesKey"
                    app:layout_constraintTop_toTopOf="@+id/tvCopiers"
                    tools:text="5" />

                <TextView
                    android:id="@+id/tvDaysJoinesKey"
                    style="@style/gilroy_400"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:gravity="end"
                    android:maxLines="1"
                    android:text="@string/days_joined"
                    android:textAlignment="viewEnd"
                    android:textColor="?attr/color_ca61e1e1e_c99ffffff"
                    android:textSize="12dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_weight="1"
                    app:layout_constraintStart_toEndOf="@+id/tvStrategiesKey"
                    app:layout_constraintTop_toTopOf="@+id/tvCopiersKey" />

                <TextView
                    android:id="@+id/tvDaysJoines"
                    style="@style/gilroy_600"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:textColor="?attr/color_c1e1e1e_cebffffff"
                    android:textSize="18dp"
                    app:layout_constraintEnd_toEndOf="@+id/tvDaysJoinesKey"
                    app:layout_constraintStart_toStartOf="@+id/tvDaysJoinesKey"
                    app:layout_constraintTop_toTopOf="@+id/tvCopiers"
                    tools:text="200" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 需要被卡住的布局 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <cn.com.vau.common.view.tablayout.DslTabLayout
                    android:id="@+id/mTabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="33dp" />

                <View
                    style="@style/TabLayoutBottomLineStyle"
                    android:layout_gravity="bottom" />
            </FrameLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <!-- 底部可滑动布局 -->
        <androidx.viewpager2.widget.ViewPager2
            android:id="@+id/viewPager2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <TextView
        android:id="@+id/tvNext"
        style="@style/main_bottom_button_theme"
        android:layout_width="match_parent"
        android:layout_height="@dimen/height_button_main"
        android:layout_marginHorizontal="@dimen/margin_horizontal_base"
        android:layout_marginVertical="16dp"
        android:text="@string/signal_provider_center"
        android:visibility="gone"
        tools:visibility="visible" />
</LinearLayout>