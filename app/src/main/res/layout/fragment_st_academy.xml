<?xml version="1.0" encoding="utf-8"?>
<com.scwang.smart.refresh.layout.SmartRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mRefreshLayout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:srlEnableLoadMore="false">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvGlossary"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:paddingVertical="6dp"
                android:text="@string/glossary"
                android:textAlignment="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="10dp"
                app:drawableTint="?attr/color_c1e1e1e_cebffffff"
                app:drawableTopCompat="@drawable/img_source_academy_glossary"
                app:layout_constraintEnd_toStartOf="@id/tvFaqs"
                app:layout_constraintHorizontal_chainStyle="spread"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:background="@drawable/shape_stroke_c331e1e1e_r6" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvFaqs"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:gravity="center"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:paddingVertical="6dp"
                android:text="FAQs"
                android:textAlignment="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="10dp"
                app:drawableTint="?attr/color_c1e1e1e_cebffffff"
                app:drawableTopCompat="@drawable/img_source_academy_faqs"
                app:layout_constraintEnd_toStartOf="@id/tvSocial"
                app:layout_constraintStart_toEndOf="@id/tvGlossary"
                app:layout_constraintTop_toTopOf="@id/tvGlossary"
                tools:background="@drawable/shape_stroke_c331e1e1e_r6" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvSocial"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="56dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginEnd="@dimen/margin_horizontal_base"
                android:gravity="center"
                android:maxWidth="56dp"
                android:maxLines="1"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:paddingVertical="6dp"
                android:text="@string/more_articles"
                android:textAlignment="center"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="10dp"
                app:drawableTint="?attr/color_c1e1e1e_cebffffff"
                app:drawableTopCompat="@drawable/img_source_academy_articles"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tvFaqs"
                app:layout_constraintTop_toTopOf="@id/tvGlossary"
                tools:background="@drawable/shape_stroke_c331e1e1e_r6" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvMustRead"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title_card"
                android:text="@string/must_read_for_new_joiners"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvGlossary"
                app:layout_constraintTop_toBottomOf="@+id/tvGlossary" />

            <View
                android:id="@+id/viewLeft"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="@dimen/margin_top_card_title"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                app:layout_constraintBottom_toBottomOf="@+id/ivLeft"
                app:layout_constraintEnd_toStartOf="@+id/tvRightTop"
                app:layout_constraintStart_toStartOf="@+id/tvMustRead"
                app:layout_constraintTop_toBottomOf="@+id/tvMustRead" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLeft"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/padding_horizontal_base"
                android:gravity="start"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:text="@string/what_is_copy_trading"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:layout_constraintEnd_toEndOf="@+id/viewLeft"
                app:layout_constraintStart_toStartOf="@+id/viewLeft"
                app:layout_constraintTop_toTopOf="@+id/viewLeft" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivLeft"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                android:src="@drawable/img_academy_left"
                app:layout_constraintEnd_toEndOf="@+id/viewLeft"
                app:layout_constraintTop_toBottomOf="@+id/tvLeft" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvRightTop"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:ellipsize="end"
                android:gravity="center_vertical|start"
                android:maxLines="3"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:text="@string/copy_trading_vs_manual_trading"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:drawableEndCompat="?attr/imgAcademyRightTop"
                app:layout_constraintBottom_toTopOf="@+id/tvRightBottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/viewLeft"
                app:layout_constraintTop_toTopOf="@+id/viewLeft" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvRightBottom"
                style="@style/gilroy_500"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:background="@drawable/draw_shape_c0a1e1e1e_c262930_r10"
                android:ellipsize="end"
                android:gravity="center_vertical|start"
                android:maxLines="3"
                android:paddingHorizontal="@dimen/padding_horizontal_base"
                android:text="@string/info_and_support"
                android:textAlignment="viewStart"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="12dp"
                app:drawableEndCompat="?attr/imgAcademyRightBottom"
                app:layout_constraintBottom_toBottomOf="@+id/viewLeft"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@+id/viewLeft"
                app:layout_constraintTop_toBottomOf="@+id/tvRightTop" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvBasics"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title_card"
                android:text="@string/for_copiers"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/viewLeft"
                app:layout_constraintTop_toBottomOf="@+id/viewLeft" />

            <View
                android:id="@+id/line1"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@+id/tvBasics" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvBasic1"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:text="@string/find_the_right_strategies_to_copy"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/img_academy_basic1"
                app:layout_constraintTop_toBottomOf="@+id/line1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvBasic2"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:text="@string/pick_your_ideal_copy_mode"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/img_academy_basic2"
                app:layout_constraintTop_toBottomOf="@+id/tvBasic1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAppUserManual"
                style="@style/gilroy_600"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_top_title_card"
                android:text="@string/for_signal_providers"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:layout_constraintStart_toStartOf="@+id/tvBasic2"
                app:layout_constraintTop_toBottomOf="@+id/tvBasic2" />

            <View
                android:id="@+id/line2"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:background="?attr/color_c1f1e1e1e_c1fffffff"
                app:layout_constraintTop_toBottomOf="@+id/tvAppUserManual" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAppUserManual1"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="@dimen/margin_vertical_button"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:text="@string/what_is_profit_sharing"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/img_academy_app_user1"
                app:layout_constraintTop_toBottomOf="@+id/line2" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvAppUserManual2"
                style="@style/gilroy_600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_horizontal_base"
                android:layout_marginTop="12dp"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:text="@string/build_an_attractive_strategy"
                android:textColor="?attr/color_c1e1e1e_cebffffff"
                android:textSize="16dp"
                app:drawableEndCompat="@drawable/img_academy_app_user2"
                app:layout_constraintTop_toBottomOf="@+id/tvAppUserManual1" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</com.scwang.smart.refresh.layout.SmartRefreshLayout>
