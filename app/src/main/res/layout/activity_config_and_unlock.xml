<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <cn.com.vau.util.widget.HeaderBar
        android:id="@+id/mHeaderBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:hb_titleText="@string/security_code_settings" />

    <include
        android:id="@+id/layoutPatternUnlock"
        layout="@layout/layout_pattern_unlock"
        android:visibility="gone"
        tools:visibility="visible" />

    <include
        android:id="@+id/layoutFingerprintUnlock"
        layout="@layout/popup_fingerprint_unlock"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>