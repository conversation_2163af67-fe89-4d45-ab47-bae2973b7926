{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.5.7", "a": "", "k": "", "d": "", "tc": ""}, "fr": 25, "ip": 79, "op": 114, "w": 800, "h": 800, "nm": "Discover_Dark", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Star", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.769}, "o": {"x": 0.167, "y": 0.231}, "t": 81, "s": [399.658, 436.206, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 84, "s": [399.658, 400.206, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic: \\u4f4d\\u7f6e')(1), 200);\n    freq = $bm_div(effect('Elastic: \\u4f4d\\u7f6e')(2), 30);\n    decay = $bm_div(effect('Elastic: \\u4f4d\\u7f6e')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [-83.677, -72.597, 0], "ix": 1}, "s": {"a": 0, "k": [3500, 3500, 100], "ix": 6}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 位置", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.05, 0.16], [0, 0], [0.11, -0.33], [0, 0], [1.32, -0.44], [0, 0], [-0.33, -0.11], [0, 0], [-0.44, -1.32], [0, 0], [-0.11, 0.33], [0, 0], [-1.32, 0.44], [0, 0], [0.33, 0.11], [0, 0], [0.41, 0.33]], "o": [[-0.07, -0.16], [0, 0], [-0.11, -0.33], [0, 0], [-0.44, 1.32], [0, 0], [-0.33, 0.11], [0, 0], [1.32, 0.44], [0, 0], [0.11, 0.33], [0, 0], [0.44, -1.32], [0, 0], [0.33, -0.11], [0, 0], [-0.52, -0.17], [0, 0]], "v": [[-83.042, -75.352], [-83.232, -75.832], [-83.332, -76.132], [-84.022, -76.132], [-84.122, -75.832], [-86.912, -73.042], [-87.212, -72.942], [-87.212, -72.252], [-86.912, -72.152], [-84.122, -69.361], [-84.022, -69.061], [-83.332, -69.061], [-83.232, -69.361], [-80.442, -72.152], [-80.142, -72.252], [-80.142, -72.942], [-80.442, -73.042], [-81.842, -73.812]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.117647058824, 0.117647058824, 0.117647058824, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1.2, "ix": 5}, "lc": 2, "lj": 1, "ml": 10, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.568], "y": [1]}, "o": {"x": [0.908], "y": [0]}, "t": 76, "s": [0]}, {"t": 90, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 78, "op": 250, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "BG", "sr": 1, "ks": {"o": {"a": 0, "k": 92, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [400, 713.95, 0], "ix": 2}, "a": {"a": 0, "k": [-83.667, -63.632, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 79, "s": [3500, 3350, 100]}, {"t": 84, "s": [3500, 3500, 100]}], "ix": 6, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic: \\u7f29\\u653e 3')(1), 200);\n    freq = $bm_div(effect('Elastic: \\u7f29\\u653e 3')(2), 30);\n    decay = $bm_div(effect('Elastic: \\u7f29\\u653e 3')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}}, "ao": 0, "ef": [{"ty": 5, "nm": "Elastic: 缩放", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 1, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}, {"ty": 5, "nm": "Elastic: 缩放 2", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 2, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}, {"ty": 5, "nm": "Elastic: 缩放 3", "np": 5, "mn": "Pseudo/MDS Elastic Controller", "ix": 3, "en": 1, "ef": [{"ty": 0, "nm": "Amplitude", "mn": "Pseudo/MDS Elastic Controller-0001", "ix": 1, "v": {"a": 0, "k": 20, "ix": 1}}, {"ty": 0, "nm": "Frequency", "mn": "Pseudo/MDS Elastic Controller-0002", "ix": 2, "v": {"a": 0, "k": 40, "ix": 2}}, {"ty": 0, "nm": "Decay", "mn": "Pseudo/MDS Elastic Controller-0003", "ix": 3, "v": {"a": 0, "k": 60, "ix": 3}}]}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -0.87], [0, 0], [-0.76, -0.43], [0, 0], [-0.76, 0.44], [0, 0], [0, 0.87], [0, 0], [0.76, 0.43], [0, 0], [0.76, -0.44], [0, 0]], "o": [[0, 0], [0, 0.88], [0, 0], [0.76, 0.44], [0, 0], [0.76, -0.44], [0, 0], [0, -0.88], [0, 0], [-0.76, -0.44], [0, 0], [-0.76, 0.44]], "v": [[-91.762, -75.862], [-91.762, -69.342], [-90.532, -67.222], [-84.892, -63.962], [-82.442, -63.962], [-76.802, -67.222], [-75.572, -69.342], [-75.572, -75.862], [-76.802, -77.982], [-82.442, -81.242], [-84.892, -81.242], [-90.532, -77.982]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 78, "op": 250, "st": 0, "bm": 0}], "markers": [{"tm": 38, "cm": "1", "dr": 0}, {"tm": 64, "cm": "2", "dr": 0}, {"tm": 142, "cm": "3", "dr": 0}]}