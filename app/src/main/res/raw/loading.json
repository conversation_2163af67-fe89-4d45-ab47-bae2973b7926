{"v": "4.8.0", "meta": {"g": "LottieFiles AE 3.5.2", "a": "", "k": "", "d": "", "tc": ""}, "fr": 25, "ip": 0, "op": 120, "w": 200, "h": 320, "nm": "AU logo loading", "ddd": 0, "assets": [{"id": "comp_0", "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": " 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [100]}, {"t": 90, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [120]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 66, "s": [180]}, {"t": 85, "s": [240]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [100, 180, 0], "to": [0, -7.25, 0], "ti": [0, -3.583, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [100, 136.5, 0], "to": [0, 3.583, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [100, 201.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [100, 136.5, 0], "to": [0, 0, 0], "ti": [0, -10.833, 0]}, {"t": 85, "s": [100, 201.5, 0]}], "ix": 2, "x": "var $bm_rt;\nvar amp, freq, decay, n, n, t, t, v;\ntry {\n    amp = $bm_div(effect('Elastic: \\u4f4d\\u7f6e 2')(1), 200);\n    freq = $bm_div(effect('Elastic: \\u4f4d\\u7f6e 2')(2), 30);\n    decay = $bm_div(effect('Elastic: \\u4f4d\\u7f6e 2')(3), 10);\n    $bm_rt = n = 0;\n    if (numKeys > 0) {\n        $bm_rt = n = nearestKey(time).index;\n        if (key(n).time > time) {\n            n--;\n        }\n    }\n    if (n == 0) {\n        $bm_rt = t = 0;\n    } else {\n        $bm_rt = t = $bm_sub(time, key(n).time);\n    }\n    if (n > 0) {\n        v = velocityAtTime($bm_sub(key(n).time, $bm_div(thisComp.frameDuration, 10)));\n        $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(v, amp), Math.sin($bm_mul($bm_mul($bm_mul(freq, t), 2), Math.PI))), Math.exp($bm_mul(decay, t))));\n    } else {\n        $bm_rt = value;\n    }\n} catch (e$$4) {\n    $bm_rt = value = value;\n}"}, "a": {"a": 0, "k": [-260.587, -300.75, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [31, 31, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 24, "s": [38, 38, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 45, "s": [31, 31, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 66, "s": [38, 38, 100]}, {"t": 85, "s": [31, 31, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-260.587, -119], [-202.546, -219.53], [-156.862, -298.655], [-100.372, -396.5], [-224.913, -396.5], [-310.366, -396.5], [-420.801, -396.5], [-362.74, -295.934], [-326.024, -232.341]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 24, "s": [{"i": [[-42.856, -0.641], [0, 0], [0, 0], [17.457, 29.703], [5.462, 0.044], [17.99, 0.034], [22.849, -39.139], [-1.627, -2.813], [-7.048, -12.152]], "o": [[36.807, 0.551], [0, 0], [0, 0], [-20.744, -35.295], [-16.011, -0.129], [-7.569, -0.014], [-22.758, 38.983], [8.069, 13.955], [1.005, 1.733]], "v": [[-254.012, -184.334], [-201.183, -221.415], [-164.176, -285.357], [-153.742, -359.323], [-218.586, -396.24], [-285.334, -396.479], [-354.885, -358.697], [-348.804, -271.821], [-322.737, -226.786]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 45, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-260.587, -119], [-202.546, -219.53], [-156.862, -298.655], [-100.372, -396.5], [-224.913, -396.5], [-310.366, -396.5], [-420.801, -396.5], [-362.74, -295.934], [-326.024, -232.341]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 66, "s": [{"i": [[-42.105, -0.171], [0, 0], [0, 0], [20.641, 35.002], [0, 0], [0, 0], [20.726, -33.533], [0, 0], [0, 0]], "o": [[42.105, 0.171], [0, 0], [0, 0], [-20.741, -35.171], [0, 0], [0, 0], [-21.365, 34.566], [0, 0], [0, 0]], "v": [[-260.586, -190.053], [-195.905, -231.032], [-162.283, -289.267], [-161.227, -363.605], [-218.643, -396.197], [-309.751, -396.5], [-361.919, -365.25], [-358.748, -289.02], [-325.29, -231.07]], "c": true}]}, {"t": 85, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-260.587, -119], [-202.546, -219.53], [-156.862, -298.655], [-100.372, -396.5], [-224.913, -396.5], [-310.366, -396.5], [-420.801, -396.5], [-362.74, -295.934], [-326.024, -232.341]], "c": true}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0.890196084976, 0.341176480055, 0.156862750649, 1]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [0.890196084976, 0.341176480055, 0.156862750649, 1]}, {"t": 45, "s": [0.011764706112, 0.282352954149, 0.329411774874, 1]}], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "rd", "nm": "圆角 1", "r": {"a": 0, "k": 0, "ix": 1}, "ix": 3, "mn": "ADBE Vector Filter - RC", "hd": false}], "ip": 0, "op": 97, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": " 6", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [101.5, 219, 0], "ix": 2}, "a": {"a": 0, "k": [101.5, 219, 0], "ix": 1}, "s": {"a": 0, "k": [123.611, 123.611, 100], "ix": 6}}, "ao": 0, "w": 200, "h": 320, "ip": 88, "op": 253, "st": 3, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "logo", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 97, "s": [0]}, {"t": 104, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100.042, 217.665, 0], "ix": 2}, "a": {"a": 0, "k": [-235.958, -684.39, 0], "ix": 1}, "s": {"a": 0, "k": [106.148, 106.148, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-250.089, -725.054], [-191.825, -725.054], [-220.818, -671.625], [-220.818, -707.023]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.890196084976, 0.341176480055, 0.156862750649, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-280.09, -725.054], [-258.223, -725.054], [-225.056, -663.814], [-235.957, -643.725]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.011764706112, 0.282352954149, 0.329411774874, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 88, "op": 238, "st": -12, "bm": 0}]}, {"id": "comp_1", "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "oranger", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [112.83, 205.773, 0], "ix": 2}, "a": {"a": 0, "k": [-220.957, -698.34, 0], "ix": 1}, "s": {"a": 0, "k": [85.245, 85.245, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-250.089, -725.054], [-191.825, -725.054], [-220.818, -671.625], [-220.818, -707.023]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"t": 101, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 165, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.890196078431, 0.341176470588, 0.156862745098, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 85, "op": 235, "st": -15, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "tea", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [85.878, 217.665, 0], "ix": 2}, "a": {"a": 0, "k": [-252.573, -684.39, 0], "ix": 1}, "s": {"a": 0, "k": [85.245, 85.245, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-280.09, -725.054], [-258.223, -725.054], [-225.056, -663.814], [-235.957, -643.725]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "组 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 85, "s": [0]}, {"t": 101, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.011764705882, 0.282352941176, 0.329411764706, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}], "ip": 85, "op": 235, "st": -15, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": " 6", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 150, 0], "ix": 2}, "a": {"a": 0, "k": [100, 197, 0], "ix": 1}, "s": {"a": 0, "k": [129.992, 129.992, 100], "ix": 6}}, "ao": 0, "w": 200, "h": 320, "ip": 0, "op": 250, "st": 0, "bm": 0}], "markers": []}