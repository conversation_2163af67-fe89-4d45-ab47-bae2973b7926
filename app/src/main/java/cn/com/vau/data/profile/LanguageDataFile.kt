package cn.com.vau.data.profile

import androidx.annotation.Keep

/**
 * author：lvy
 * date：2024/12/05
 * desc：
 */
@Keep
data class LanguageDataBean(
    val obj: LanguageObjBean?,
)

@Keep
data class LanguageObjBean(
    val defaultLanguageCode: String?, // 当前用户选择的语言代码
    val availableLanguages: MutableList<LanguageBean>?,
)

@Keep
data class LanguageBean(
    val languageCode: String?, // 语言代码
    val sourceLanguageName: String?, // 语言的原名称 (用于前端展示使用）
    val translatedLanguageName: String?, // 语言的翻译名称，app端使用
    val appLanguageName: String?, // APP使用的语言名称 （必须一致）。app端之前本地的语言，暂时用不到

    // 自己设置的属性
    var isSelected: Boolean = false, // 是否选中
    var searchKey: String, // 搜索时输入的内容
)