package cn.com.vau.data.depositcoupon

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean

// Type: 资金管理 && 出金

/**
 * 资金管理查询
 * Created by zhy on 2018/11/19.
 */
@Keep
class ManageFundsBean : BaseBean() {
    var data: ManageFundsData? = null
}

@Keep
class ManageFundsData {
    var obj: ManageFundsObj? = null
}

@Keep
class ManageFundsObj {
    var userId: String? = null
    var accountId: String? = null
    var netWorth = 0.0
    var activeAmount = 0.0
    var creditAmount = 0.0
    var totalProfit = 0.0
    var profit: String? = null
    var floatProfit = 0.0
    var chartDatas: List<ManageFundsObjChartDatas>? = null
    var fundFlows: List<ManageFundsObjFundFlows>? = null
    var discountIcon: String? = null
}

@Keep
class ManageFundsObjChartDatas {
    /**
     * dateTime : 18/11
     * netWorth : 109969.5
     */
    var dateTime: String? = null
    var netWorth = 0.0
}

@Keep
class ManageFundsObjFundFlows {
    var tradeTime: String? = null
    var orderNo: String? = null
    var actionCode: String? = null
    var action: String? = null
    var status: String? = null
    var amountflag: String? = null
    var amount: String? = null
    var createDate: String? = null

    // 创建时间字符串，格式dd/MM/yyyy
    // HH:mm:ss
    var createTimeStr: String? = null

    // 创建时间戳，格式yyyyMMddHHmmss
    var createTime: String? = null
}

/**
 * 资金明细详情
 * Created by zhy on 2018/11/19.
 */
@Keep
class ManageFundsDetailsBean {
    var obj: ManageFundsDetailsObj? = null
}

@Keep
class ManageFundsDetailsObj {
    var orderNo: String? = null //订单号
    var userId: String? = null //用户id
    var userName: String? = null //用户名
    var accountId: String? = null //mt4账户id
    var amount = 0.0 //入金金额
    // <option value="01">Order failed</option>
    // <option value="10">To be paid</option>
    // <option value="13">To be paid</option>
    // <option value="12">Submit to third party</option>
    // <option value="11">Payment failed</option>
    // <option value="15">Payment success</option>  绿色
    // <option value="20">Deposit processing</option>
    // <option value="30">Manually verifying</option>
    // <option value="40">Deposit successful</option> 绿色
    // <option value="50">Processing</option>
    // <option value="60">Rejected</option>
    // <option value="70">Pending</option>
    // 从eric那获取到的 他们那是 01 11 60 显示 x
    var transCode: String? = null //订单状态 code
    var tranStatus: String? = null //订单状态，具体参考附录11
    var channel: String? = null //入金来源，可选值：app，crm
    var payType: String? = null //支付方式，具体参考附录9
    var proccessNote: String? = null //处理备注
    var currency: String? = null //币种
    var createdTime: String? = null //申请时间，格式dd/MM/yyyy HH:mm:ss
    var iconUrl: String? = null //入金icon
    var isShowRetry: Boolean? = null //是否显示Retry按钮 true: 显示，false: 不显示

    var orderNum: String? = null
    var applyTime: String? = null
    var withdrawMethod: String? = null
    var orderStatus: String? = null
    var failReason: String? = null
    var statusCode: String? = null  // 1:Submitted  5:Accepted
}

/**
 * 出金查询是否需要上传地址证明
 * Created by THINKPAD on 2019/10/29.
 */
@Keep
data class NeedUploadAddressProofBean(
    var data: NeedUploadAddressProofData?
): BaseBean()

@Keep
data class NeedUploadAddressProofData (
    var obj: NeedUploadAddressProofObj?
)

@Keep
data class NeedUploadAddressProofObj(
    var needUploadAddressProof: String?,
    var msg: String?
)

/**
 * 检查是否跳转到H5出金 -- 出金
 * Created by THINKPAD on 2020/10/28.
 */
@Keep
data class NeedH5WithdrawBean (
    var data: NeedH5WithdrawData?
): BaseBean()

@Keep
data class NeedH5WithdrawData (
    var obj: NeedH5WithdrawObj?
)

@Keep
data class NeedH5WithdrawObj (
    var h5Url:String?,
    var isH5page:Int?//0代表原生页面，1代表H5页面，当为1时，取h5Url打开
)

/**
 * 获取交易的资金变动历史
 */
@Keep
data class FundHistoryBean(
    val `data`: List<FundHistoryData>?
) : BaseBean()

@Keep
data class FundHistoryData(
    val action: String?,
    val actionCode: String?,
    val amount: String?,
    val amountflag: String?,
    val createDate: String?,
    val createTime: String?,
    val createTimeStr: String?,
    val orderNo: String?,
    val status: String?
)

/**
 * 出金是否需要上传身份地址证明(新)
 */
@Keep
data class NeedUploadIdProofData(
    var data: Data?
) : BaseBean() {
    @Keep
    data class Data(
        var obj: Obj?
    )

    @Keep
    data class Obj(
        var msg: String?,
        /**
         * 是否需要上传身份地址证明，0：不需要，
         * 1，3：需要上传身份证明，2：已上传身份但未审核通过，
         * 4,6：需要上传地址证明
         * 5：已上传地址但未审核通
         */
        var needUploadIdPoaProof: String?
    )

}