package cn.com.vau.data.profile

import androidx.annotation.Keep

/**
 * author：lvy
 * date：2025/03/29
 * desc：
 */

/**
 * 安全中心配置
 */
@Keep
data class AuthConfigDataBean(val obj: AuthConfigObjBean?)

@Keep
data class AuthConfigObjBean(
    val needAuthOptList: List<String>?, // 2FA可以切换的OTP数组。黄金开户新增
    val totpBindStatus: Int?, // 是否绑定totp。0=未绑定，跳转到绑定2FA页面；1=已绑定，跳转到验证2FA页面
    val validateType: Int?, // 0：手机和邮箱都未认证，1: 邮箱已认证，2：手机号已认证。都已认证的情况下接口会返回1。黄金开户新增
    val phoneCountryCode: String?, // 国家代号，如：FR
    val phoneCode: String?, // 手机区号，如：33
    val phone: String?, // 电话，如：00002054
    val email: String? // 邮箱
)