package cn.com.vau.data.depositcoupon

import android.text.TextUtils
import androidx.annotation.Keep
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.data.BaseBean
import cn.com.vau.data.msg.PushBean
import cn.com.vau.page.common.BaseListBean
import java.io.Serializable

// Type: 入金 支付

@Keep
data class H5DepositBean(
    val data: H5DepositData?
) : BaseBean()

@Keep
data class H5DepositData(
    val obj: H5DepositObj?
)

@Keep
data class H5DepositObj(
    val h5Url: String? = null
)

/**
 * Created by roy on 2018/12/19.
 * 入金下单申请
 */
@Keep
data class DepositFundData(
    val data: DepositDataData?
): BaseBean()

@Keep
class DepositDataData(
    val obj: DepositDataObj?
)

@Keep
data class DepositDataObj (
    var actPayType:String?,
    var orderNum:String?,
    var couponSource:String?
)

/**
 * 入金查询用户信息
 * Created by THINKPAD on 2020/5/7.
 */
@Keep
data class DepositUserInfoBean(
    val data: DepositUserInfoData?
): BaseBean()

@Keep
data class DepositUserInfoData(
    var obj: DepositUserInfoObj?
)

@Keep
data class DepositUserInfoObj(
    var userCountry: String?,//居住地编码，4575为加拿大
    var superVision: Int?//	用户所属监管，1为asic，2为cima
)

/**
 * 入金页活动信息查询
 */
@Keep
data class LossActiveData(
    val `data`: Data?,
) : BaseBean() {
    @Keep
    data class Data(
        val obj: Obj?
    )

    @Keep
    data class Obj(
        val activeId: String?,
        val position1Txt: String?,
        val position2Txt: String?,
        val position3Txt: String?,
        val moneyIcon: String?,
        val moneyIconLimit: String?,
        val tcUrl: String?,
        var banner: MutableList<DepositPromoBanner>?,   // 入金优化二期 新增活动轮播图配置
    ) : Serializable

}

@Keep
data class DepositPromoBanner(
    var id: String? = null,         // id
    var eventId: String? = null,    // 活动Id
    var startTime: String? = null,  // 开始时间
    var endTime: String? = null,    // 结束时间
    var imgUrl: String? = null,     // 照片URL
    var imgType: String? = null,    // imageType: 6 (Banner)
    var eventsStatus: String? = null,// 活动状态
    var eventsHot: String? = null,  // 是否为热门活动
    var longTerm: String? = null,   // 是否为长期活动
    var eventsName: String? = null, // 活动名称
    var eventsDesc: String? = null, // 活动desc
    var displayLocation: String? = null, // 展示位置
    var appJumpDefModel: PushBean? = null,
)

/**
 * 获取汇率
 */
@Keep
data class ExchangeRateBean(
    var data: ExchangeRateBeanObj? = null
) : BaseBean()

@Keep
data class ExchangeRateBeanObj(
    var obj: ExchangeRateData? = null
)

@Keep
data class ExchangeRateData(
    var rate: String? = null,
    var currencyFrom: String? = null,
    var currencyTo: String? = null,
    var showExchangeRate: Boolean? = false
)

/**
 * 入金获取支付方式
 * Created by THINKPAD on 2019/6/11.
 */
@Keep
data class DepositMethodData(
    var data: DepositMethodObjList? = null
) : BaseBean()

@Keep
data class DepositMethodObjList(
    var obj: MutableList<DepositMethodObj>?
)

/**
 * 重试按钮 -- 入金详情
 */
@Keep
data class FundDetailRetryData(
    var obj: DepositMethodObj? = null
)

@Keep
data class DepositMethodObj(
    var code: String?,//支付方式码
    var name: String?,//支付方式名称，根据app语言返回多语言
    var iconUrl: String?,//支付方式icon
    var depositType: String?,//入金逻辑处理方式，0代表信用卡直连入金，1代表信用卡3d入金，2代表第三方支付台入金，3代表跳转h5页面入金,4电汇
    var h5Path: String?,//当depositType值为3时跳转的H5页面地址
    var depositAging: String?,//支付方式时效描述
    var status: Int?,//支付方式状态，1正常，0代表关闭
    var vipTemp: String?,//是否临时VIP渠道
    var limitAmountMax: String?,//支付方式最高入金限制，-1代表不限制
    var limitAmountMin: String?,//支付方式最低入金限制，-1代表不限制
    var paymentMethod: String? = null,
    var typeCoupons: MutableList<DepositCouponDetail>?,//用户所有可用优惠券
    var accountTypeName: String? = null,//账户类型名 ("fund/fundDetailsRetry"接口会返回)
    var isSelect: Boolean?,
    var isSpread: Boolean?,
    var typeDescs: List<String>?,//支付方式描述，字符串数组，每一个字符串代表一个段落
    var couponSource: String?,
    var isPassIdPoa: Boolean?,
    var isSumsub :Int?, // 是否需要人脸识别
    var isSumsubFinalReject:Boolean?, //人脸识别直接是否被拒绝过
    var fees: String?, //手续费
    var promotion: MutableList<PromotionDetail>?, //promotion活动详情
): Serializable

@Keep
data class PromotionDetail(
    var promotionTitle: String? = null, // Promo 名称
    var requiredDepositAmount: String? = null, // 需要入金才能参加活动的金额
    var cashbackPercentage: String? = null, // 现金返还百分比
    var jumpModel: PushBean? = null,
): Serializable

/**
 * 获取信用卡列表
 * Created by THINKPAD on 2019/1/17.
 */
@Keep
data class CreditManagerBean (
    var data: CreditManagerData?
): BaseBean()

@Keep
data class CreditManagerData (
    var obj:List<CreditDetailObj>?
)

@Keep
data class CreditDetailObj(
    var cardType: String? = null, // "00",
    var preFourNum: String? = null, // "6023",
    var lastFourNum: String? = null, // "3456",
    var cvv: String? = null, // "890",
    var expireDate: String? = null, // "20/22",
    var cardHolder: String? = null,// "James"
) : Serializable

/**
 * 信用卡支付3d
 * Created by THINKPAD on 2019/9/2.
 */
@Keep
data class PayToDayPreOrderBean(
    var data: PayToDayPreOrderData?
) : BaseBean()

@Keep
data class PayToDayPreOrderData (
    var obj: PayToDayPreOrderObj
)

@Keep
data class PayToDayPreOrderObj(
    var content: String?,//返回内容，h5链接或者html代码
    var dataType: String?,//1代表h5链接，2代表html片段代码
    var orderNum: String?
)

/**
 * 获取用户账户数据
 */
@Keep
data class UserAccountData(
    val `data`: Data?,
) : BaseBean() {
    @Keep
    data class Data(
        val obj: Obj?
    )

    @Keep
    data class Obj(
        val listAccount: List<Account>?,
        val type: Int?,     //类型（1：有交易账户，2：无交易账户）
    )

    @Keep
    class Account : BaseListBean, Serializable {
        var accountId: String? = null   //账户号
        var currencyType: String? = null    //币种
        var serverId: String? = null    //服务器
        var mtsAccount: String? = null
        var name: String? = null  //顯示於畫面用
        var accountType: String? = null     //账户类型号
        var accountTypeName: String? = null     //账户类型名
        var balance: String? = null     //余额
        var updateTime: String? = null     //余额最后更新时间
        override fun getShowItemValue(): String  {
            return if (TextUtils.isEmpty(mtsAccount)) accountId ?: "" else "${
                VauApplication.context.getString(R.string.copy_trading_account)
            } $accountId"
        }
    }
}