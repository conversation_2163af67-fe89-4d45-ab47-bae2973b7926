package cn.com.vau.data.init

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 已跟随策略列表
 */
@Keep
data class StFollowStrategyBean(
    var `data`: StFollowStrategyData?
):BaseBean()

@Keep
data class StFollowStrategyData(
    var followDetailsList:List<StShareStrategyData>?
)

@Keep
data class StShareStrategyData(

    // 盈亏
    var profit: Double = 0.0,
    var isRefresh:Boolean = true,

    // -----------------------------

    var followTotalProfit:Double = 0.0,
    var pnlUI:String?,
    var roi:Double = 0.0,
    // TODO cuifei
    var returnUI:String?,
    var balanceUI:String?,
    var equityUI:String?,
    var investedUI:String?,
    var totalShareProfitUI:String?,

    // -----------------------------

    // 余额
    var balance: String?,
    // 跟随日期
    var copyDate: String?,
    /**
     * 跟随状态
     * 1: 正在跟随
     * 2: 暂停跟随[]
     * 3: 停止跟随
     * 5: Pending Close
     * 6: 跟单临时账户建立中间态
     */
    var followingStatus: String?,
    // 是否有pending order
    var hasPendingOrder: Boolean?,
    // 暂停跟随时间
    var pausedDate:String?,
    // 投资金额
    var investmentAmount: String?,
    // 信用额
    var investmentCredit:String?,
    // 已使用预付款
    var marginUsed: Double = 0.0,
    // 跟单虚拟账户ID
    var portfolioId: String?,
    // 策略头像
    var profilePictureUrl: String?,
    // 分润比例 （ 追加/减少 资金页面使用 ）
    var profitShareRatio: String?,
    // 信号源策略ID
    var strategyId: String?,
    // 策略名称
    var strategyName: String?,
    // 信号源名称 ( 只有分享使用 )
    var stUserNickname:String?,
    // 信号源策略编号
    var strategyNo: String?,
    // 历史总分润金额
    var totalSharedProfit: Double = 0.0,
    var totalHistoryProfit: Double = 0.0,
    // 最小跟单金额
    val minFollowAmount: String?,

    // 跟单账户号下的所有持仓订单
    var positions: CopyOnWriteArrayList<ShareOrderData>?,
    // 跟单账户号下的所有待平仓订单
    var pendingClose: ArrayList<ShareOrderData>?,
    // 跟单账户号下的所有待开仓订单
    var pendingOpen: ArrayList<ShareOrderData>?

)







