package cn.com.vau.data.discover

import androidx.annotation.Keep
import cn.com.vau.data.BaseBean

/**
 * 多策略汇总接口
 */
@Keep
data class StrategyRecommendAllBean(
    var data: StrategyRecommendAllData? = null
): BaseBean()

@Keep
data class StrategyRecommendAllData(
    var profitShield: List<StrategyMostCopied>? = null,
    var mostCopied: List<StrategyMostCopied>? = null,
    var highestReturn: List<StrategyMostCopied>? = null,
    var lowRisk: List<StrategyMostCopied>? = null,
    var highestWinRate: List<StrategyMostCopied>? = null,
)

@Keep
data class StrategyMostCopied(
    var stUserId: String? = null, // 信号源id
    var strategyId: String? = null, // 策略id
    var nickname: String? = null,   // 策略名称
    var avatar: String? = null, // 头像
    var months: Int = 0, // 月份
    var returnRate: String? = null, // 回报率
    var returnRateUI: String? = null, // 回报率
    var winRate: String? = null, // 胜率
    var winRateUI: String? = null, // 胜率
    var riskBandLevel: String? = null, // 风险度
    var comments: String? = null,
    var copiers: Int = 0, // 跟单者数量
    var profitShareRatio: String? = null,   // 分润比例
    var tradeAccountNo: String? = null, // 交易账号
    var timePointList: List<StrategyChartData>? = null, // 收益率曲线
    var followerStatus: Boolean? = null,    // 是否已跟随
    var portfolioId: String? = null,
    var followRequestId: String? = null,
    var pendingApplyApproval: Boolean? = null,  // 是否审核通过
)

@Keep
data class StrategyChartData(
    var time: String? = null,   // 日期
    var value: String? = null,  // 收益率
)