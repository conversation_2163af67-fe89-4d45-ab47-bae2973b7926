package cn.com.vau.data.ib

import androidx.annotation.Keep

// Type:分享
/**
 * st 分享 策略 图表 月回报率
 */
@Keep
data class CategoryObj(
    var commodities: TradeObj? = null,

    var forex: TradeObj? = null,

    var indices: TradeObj? = null,

    var metals: TradeObj? = null,

    var share: TradeObj? = null,

    var crypto: TradeObj? = null,

    var profitableTradesPercent: String? = null,

    var totalTrades: Double? = null,
    val offLine: Boolean? = null,
)

@Keep
data class TradeObj(
    var trades: Double? = null,

    var tradesPercent: Double? = null,

    var name: String? = null
)

/**
 * st 分享 策略 详情图表 月回报率
 */
@Keep
data class MonthlyReturnRate(
    var monthlyReturnRateChartList: ArrayList<SignalDataReturnRate>? = null,
    var returnYTD: String = "0",
    var riskband: String = "0",
    var maxMonthlyReturn: String = "",
    var maxMonthlyLoss: String = ""
)

@Keep
data class SignalDataReturnRate(
    var year: Int = 0,
    var timePoints: ArrayList<TimePoint>? = null,
    var total: Double = 0.0
)

@Keep
data class TimePoint(
    var time: String = "",
    var value: Double = 0.0
)

@Keep
data class ProductChartData(
    val product: String? = null,
    val profitableTradesPercent: Double? = null,
    val trades: Int? = null,
    val tradesPercent: Double? = null
)

@Keep
data class StrategyShareData(
    val currentCopiersCount: String? = null,
    val returnRate: String? = null,
    val signalId: String? = null,
    val totalCopiersCount: String? = null
)