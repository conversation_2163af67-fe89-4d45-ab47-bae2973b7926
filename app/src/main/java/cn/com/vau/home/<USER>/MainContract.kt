package cn.com.vau.home.presenter

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjBooleanBean
import cn.com.vau.data.account.*
import cn.com.vau.data.depositcoupon.*
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.data.init.*
import cn.com.vau.data.msg.PushParam
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * MainActivity
 */
interface MainContract {

    interface Model : BaseModel {

        fun popWindowApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<PopWindowBean>): Disposable
        fun checkMaintenanceV2(imgType: Int, baseObserver: BaseObserver<MaintenanceBean>): Disposable
        fun queryMT4AccountState(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable
        fun tradeSeason(map: HashMap<String, Any>, baseObserver: BaseObserver<SeasonBean>): Disposable
        fun updateAccountLogin(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun updateStAccountLogin(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun recordAdd(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun promoTab(map: HashMap<String, Any>, baseObserver: BaseObserver<PromoTabBean>): Disposable
        fun userActionHasChanges(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjBooleanBean>): Disposable
        fun updateLastActionTime(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun queryUserIsProclient(
            map: HashMap<String, Any>,
            baseObserver: BaseObserver<QueryUserIsProclientData>
        )

        fun getServerBaseUrl(baseObserver: BaseObserver<ServerBaseUrlBean>): Disposable
        fun checkAppVersion(map: HashMap<String, Any>, baseObserver: BaseObserver<AppVersionBean>): Disposable
        fun isShowSt(userId: String?, baseObserver: BaseObserver<StIsShowBean>): Disposable
        fun requestInAppInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<InAppBean>): Disposable
        fun fundCheckDepositStatusApi(map: HashMap<String, Any>, baseObserver: BaseObserver<CheckDepositStatusBean>): Disposable
        fun imgAdvertInfoApi(map: HashMap<String, Any>, baseObserver: BaseObserver<ImgAdvertInfoBean>): Disposable
        fun addressproofWithrawNeedUploadAddressProofApi(map: HashMap<String, Any>, baseObserver: BaseObserver<NeedUploadAddressProofBean>): Disposable
        fun addressproofWithrawNeedUploadIdPoaProofApi(map: HashMap<String, Any>, baseObserver: BaseObserver<NeedUploadIdProofData>): Disposable
        fun fundIsH5WithdrawApi(map: HashMap<String, Any>, baseObserver: BaseObserver<NeedH5WithdrawBean>): Disposable
        fun checkMaintain(type: Int, baseObserver: BaseObserver<MaintenanceBean>): Disposable
        fun userCollectDataSwitch(token: String, baseObserver: BaseObserver<CollectDataBean>): Disposable
        fun notificationMarketingClickCntUpdate(body: RequestBody?, baseObserver: BaseObserver<BaseBean>): Disposable
        fun userQueryUserLevel(map: HashMap<String, Any>, baseObserver: BaseObserver<KycVerifyLevelDataBean>): Disposable
    }

    interface View : BaseView {
        fun showUpdateDialog()
        fun showEventDialog()
        fun showAccountOverdue()
        fun skipOpenAccountActivity(state: EnumLinkSkipState, objData: MT4AccountTypeObj?)
        fun initPromoTabTxt(state: Boolean)
        fun initPromoTabRemind(state: Boolean)
        fun showMaintenanceDialog(maintenanceDetails: MaintenanceObj?)
        fun hideMaintenanceDialog()
        fun handlePopEventAccount(viewType: Int, objData: MT4AccountTypeObj?)
        fun showUpdateView(bean: AppVersionObj?)
        fun forceUpdate(bean: AppVersionObj?)
        fun showInApp()
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun tradeSeason()
        abstract fun updateAccountLogin()
        abstract fun popWindowApi()
        abstract fun queryMT4AccountState(state: EnumLinkSkipState)
        abstract fun promoTab()
        abstract fun userActionHasChanges()
        abstract fun updateLastActionTime()
        abstract fun queryUserIsProclient()
        abstract fun eventsAddClicksCount(eventId: String)
        abstract fun updateAccountLoginTime()
        abstract fun checkAppVersion(onlyCheckForce: Boolean)
        abstract fun queryMT4AccountState(viewType: Int)
        abstract fun isShowSt()
        abstract fun requestInAppInfo()
        abstract fun inAppDismissReport(eventId: String?)
        abstract fun fundCheckDepositStatusApi()
        abstract fun imgAdvertInfoApi()
        abstract fun needUploadAddressProofApi()
        abstract fun checkMaintain(type: Int, isCheckPopWindow: Boolean = false)
        abstract fun userCollectDataSwitch()
        abstract fun notificationMarketingClickCntUpdate(dataBean: PushParam?)
        abstract fun userQueryUserLevel()
    }

}
