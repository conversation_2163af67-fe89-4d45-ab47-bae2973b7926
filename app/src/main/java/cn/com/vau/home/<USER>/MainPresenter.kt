package cn.com.vau.home.presenter

import android.os.Bundle
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.event.TokenErrorData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.inApp.InAppDataUtil
import cn.com.vau.common.vm.MainViewModel
import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjBooleanBean
import cn.com.vau.data.account.KycVerifyLevelDataBean
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.depositcoupon.NeedH5WithdrawBean
import cn.com.vau.data.depositcoupon.NeedUploadAddressProofBean
import cn.com.vau.data.depositcoupon.NeedUploadIdProofData
import cn.com.vau.data.depositcoupon.QueryUserIsProclientData
import cn.com.vau.data.discover.WbpStatusData
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.data.init.AppVersionBean
import cn.com.vau.data.init.CheckDepositStatusBean
import cn.com.vau.data.init.CollectDataBean
import cn.com.vau.data.init.ImgAdvertInfoBean
import cn.com.vau.data.init.InAppBean
import cn.com.vau.data.init.MaintenanceBean
import cn.com.vau.data.init.PopWindowAppEvent
import cn.com.vau.data.init.PopWindowBean
import cn.com.vau.data.init.PopWindowDemoAccount
import cn.com.vau.data.init.PopWindowObj
import cn.com.vau.data.init.PromoTabBean
import cn.com.vau.data.init.SeasonBean
import cn.com.vau.data.init.ServerBaseUrlBean
import cn.com.vau.data.init.StIsShowBean
import cn.com.vau.data.msg.PushBean
import cn.com.vau.data.msg.PushParam
import cn.com.vau.page.coupon.CouponManagerActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.openAccountFifth.OpenFifthAddressSelectActivity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.util.AESUtil
import cn.com.vau.util.AppUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.json
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import com.google.gson.JsonObject
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Created by roy on 2018/11/17.
 * MainActivity
 */
class MainPresenter : MainContract.Presenter() {

    var updateFlag = -1

    var updateVersionName = ""

    var updateUrl = ""

    var updateContent = ""

    var accountError = "-1"

    var accountErrorMsg = ""

    var eventImgUrl = ""

    var appEventPushBean = PushBean()

    var accountApplyPushBean = PushBean()

    var bottomDialogBean: InAppBean.InAppData? = null

    var firstInto = true

    var deepSymbolName = ""

    var popWindowBean: PopWindowObj? = null
    var wbpDataBean: WbpStatusData.Obj? = null

    var viewModel: MainViewModel? = null

    // App 应用链接
    var appLinkType: Int = -1

    // 针对EEA地区用户是否展示Firebase数据收集的弹窗
    var firebaseDataDisplay: Boolean = false

    // 用户当前是否属于EEA国家区域
    var isEEACountry: Boolean = false

    // 用户是否同意开启Firebase数据收集
    var currentSwitch: Boolean = true

    var lastClickBackMilli: Long = 0

    /**
     * 获取弹窗 版本更新弹窗  -->  用户过期弹窗  -->  活动弹窗
     */
    override fun popWindowApi() {
        accountError = "-1"
        val map = hashMapOf<String, Any>()
        // 0启动页  1弹窗
        map["imgType"] = 1
        // 活动弹窗参数:适配手机型号: 0:通用, 1:iphoneX, 2:安卓16-9 3:安卓18-9 4:安卓19.5-9
        map["fitModel"] = 0
        // app版本检查参数:版本号
        map["versionName"] = AppUtil.getVersionName()
        // app版本检查参数:类型: "ios", "android"
        map["apkType"] = "android"

        // 登陆 && 模拟账号  （模拟账号是否过期参数）
        if (UserDataUtil.isLogin()) {
            map["userId"] = UserDataUtil.userId()
            map["token"] = UserDataUtil.loginToken()
            // MT4账号
            map["login"] = UserDataUtil.accountCd()
            // 交易服务器id（2：UK，3：MXT，5：AU，6：DEMO服务器，8：MT5）
            map["serverId"] = UserDataUtil.serverId()
            // 模拟账号是否过期参数:模拟账号密码
            map["passwd"] = UserDataUtil.mt4PWD()
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mModel?.popWindowApi(paramMap, object : BaseObserver<PopWindowBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(popData: PopWindowBean?) {

                if ("********" != popData?.resultCode) {
                    return
                }

                val dataBean = popData.data?.obj

                popWindowBean = dataBean

                SpManager.putSuperviseNum(dataBean?.supervise.ifNull("0"))

                val appVersion = dataBean?.appVersion

                updateFlag = appVersion?.forceFlag ?: -1
                // 版本号
                updateVersionName = appVersion?.versionName ?: ""
                // 下载地址
                updateUrl = appVersion?.dlPath ?: ""
                // 更新内容描述
                updateContent = appVersion?.introduction ?: ""
                // Firebase数据收集相关
                firebaseDataDisplay = dataBean?.display.ifNull()
                isEEACountry = dataBean?.eeaCountry.ifNull()
                currentSwitch = dataBean?.currentSwitch.ifNull(true)

                // 账户开户状态
                accountApplyPushBean = dataBean?.accountApply ?: PushBean()
                bottomDialogBean = dataBean?.bottomGuidePopUp
                // token是否失效
                if ("2" == dataBean?.tokenExpire) {
                    EventBus.getDefault().post(
                        DataEvent(
                            NoticeConstants.WS.LOGIN_ERROR_OF_TOKEN, TokenErrorData(
                                // 请再登录以继续使用您的账户。若您最近没有进行任何账户信息更新操作，请立即通知我们。
                                "1", context.getString(R.string.log_in_to_if_please_know_immediately)
                            )
                        )
                    )
                }

                // 账户过期：是否过期（demoCode=200账号未过期）
                val demoAccount = dataBean?.demoAccount ?: PopWindowDemoAccount()

                if ("********" == demoAccount.demoCode) {
                    accountError = "1"
                    accountErrorMsg = demoAccount.demoMsg
                }
                if (dataBean?.realAccount == true) {
                    accountError = "2"
                    // 该账户已被归档
                    accountErrorMsg = context.getString(R.string.this_account_has_been_archived_please_customer_service)
                }

                // 活动弹窗：
                val appEvent = dataBean?.appEvent
                eventImgUrl = appEvent?.imgUrl ?: ""
                appEventPushBean = appEvent?.appJumpDefModel ?: PushBean()

                mView?.showUpdateDialog()

            }

        })
    }

    /**
     * 获取时令 【需优化】初始化时已获取
     */
    override fun tradeSeason() {
        recordAdd()
        val jsonMap = HashMap<String, Any>()
        mModel?.tradeSeason(jsonMap, object : BaseObserver<SeasonBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager?.add(d)
            }

            override fun onNext(dataBean: SeasonBean) {
                if ("200" != dataBean.code) return
                Constants.season = dataBean.obj?.season ?: 0
            }

        })
    }

    /**
     * 这个方法的调用是公共跳转发116的处理，因为考虑到公共跳转的工具类不适合做接口请求，故放到MainActivity中调用
     * MainActivity中共有2处，一个是
     */
    override fun queryMT4AccountState(viewType: Int) {
        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountState(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(typeBean: MT4AccountTypeBean) {

                mView?.hideNetDialog()

                if (typeBean.resultCode != "V00000") {
                    ToastUtil.showToast(typeBean.msgInfo)
                    return
                }
                val obj = typeBean.data?.obj
                mView?.handlePopEventAccount(viewType, obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")
            }

        })
    }

    /**
     * 是否显示跟单入口接口定义
     */
    override fun isShowSt() {

        SpManager.putShowStEntrance(false)

        mModel?.isShowSt(UserDataUtil.userId(), object : BaseObserver<StIsShowBean>() {
            override fun onNext(data: StIsShowBean?) {

                if ("V00000" != data?.resultCode) return

                SpManager.putShowStEntrance(data.data?.obj?.isShow ?: false)
                EventBus.getDefault().post(NoticeConstants.SHOW_ST_ENTRANCE)

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

        })
    }

    /**
     * 公告查询
     */
    override fun requestInAppInfo() {
        if (TextUtils.isEmpty(UserDataUtil.userId())) return
        val param = HashMap<String, Any>()
        param["imgType"] = "20"
        param["fitModel"] = "0"
        param["apkType"] = "android"
        param["versionName"] = AppUtil.getVersionName()
        param["userId"] = UserDataUtil.userId()
        param["token"] = UserDataUtil.loginToken()
        param["login"] = UserDataUtil.accountCd()
        mModel?.requestInAppInfo(param, object : BaseObserver<InAppBean>() {
            override fun onNext(data: InAppBean?) {
                if ("********" != data?.resultCode) {
                    return
                }
                InAppDataUtil.saveData(data.data)
                mView?.showInApp()
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }

    /**
     * 用户是否入过金
     */
    override fun fundCheckDepositStatusApi() {
        if (UserDataUtil.isLogin()) {
            mModel?.fundCheckDepositStatusApi(hashMapOf("userToken" to UserDataUtil.loginToken()), object : BaseObserver<CheckDepositStatusBean>() {
                override fun onNext(returnData: CheckDepositStatusBean?) {
                    viewModel?.setUserDeposited("0" != returnData?.data?.obj?.isDepositMoney)
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager?.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
        }
    }

    /**
     * 行情首页广告位接口
     */
    override fun imgAdvertInfoApi() {
        val isSt = UserDataUtil.isStLogin()
        val paramMap = hashMapOf<String, Any>(
            "userId" to UserDataUtil.userId(),
            "token" to UserDataUtil.loginToken(),
            "mt4AccountId" to UserDataUtil.accountCd(),
            "imgType" to if (isSt) 11 else 21,    // 0启动页  7广告位   21首页运营广告位   11跟单首页运营位
            "fitModel" to 0     // 适配手机型号: 0:通用, 1:iphoneX, 2:安卓16-9 3:安卓18-9 4:安卓19.5-9
        )
        mModel?.imgAdvertInfoApi(paramMap, object : BaseObserver<ImgAdvertInfoBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: ImgAdvertInfoBean?) {
                if ("********" != dataBean?.resultCode || dataBean.data?.obj?.eventsList.isNullOrEmpty()) {
                    //test
//                    val json = "{\"resultCode\":\"********\",\"resultType\":\"0\",\"msgInfo\":\"Success\",\"data\":{\"obj\":{\"showClose\":false,\"eventsList\":[{\"id\":1709,\"eventId\":1709,\"startTime\":\"20/02/2025\",\"endTime\":\"28/02/2025\",\"imgUrl\":\"https://hytech-vau-test.app-alpha.com/news/2025/2/**************.png\",\"imgType\":21,\"eventsStatus\":0,\"eventsHot\":0,\"longTerm\":0,\"appJumpDefModel\":{\"openType\":\"app\",\"urls\":{},\"titles\":{\"en\":\"\"}},\"eventsName\":\"\",\"eventsDesc\":\"\",\"displayLocation\":\"\",\"frequency\":\"0\",\"userFilter\":\"\",\"uploadType\":1,\"displayTime\":3},{\"id\":1711,\"eventId\":1711,\"startTime\":\"20/02/2025\",\"endTime\":\"28/02/2025\",\"imgUrl\":\"https://hytech-vau-test.app-alpha.com/news/2025/2/20250220161144.png\",\"imgType\":21,\"eventsStatus\":0,\"eventsHot\":0,\"longTerm\":0,\"appJumpDefModel\":{\"openType\":\"app\",\"urls\":{},\"titles\":{\"en\":\"\"}},\"eventsName\":\"\",\"eventsDesc\":\"\",\"displayLocation\":\"\",\"frequency\":\"0\",\"userFilter\":\"\",\"uploadType\":1,\"displayTime\":3},{\"id\":1712,\"eventId\":1712,\"startTime\":\"20/02/2025\",\"endTime\":\"28/02/2025\",\"imgUrl\":\"https://hytech-vau-test.app-alpha.com/news/2025/2/20250220161205.png\",\"imgType\":21,\"eventsStatus\":0,\"eventsHot\":0,\"longTerm\":0,\"appJumpDefModel\":{\"openType\":\"app\",\"urls\":{},\"titles\":{\"en\":\"\"}},\"eventsName\":\"\",\"eventsDesc\":\"\",\"displayLocation\":\"\",\"frequency\":\"0\",\"userFilter\":\"\",\"uploadType\":1,\"displayTime\":3}]}}}"
//                    val testBannerData = GsonUtil.fromJson(json, ImgAdvertInfoBean::class.java)
//                    viewModel?.setBannerData(testBannerData?.data?.obj)

                    viewModel?.setBannerData(null)
                } else {
                    viewModel?.setBannerData(dataBean.data?.obj)
                }
            }
        })
    }

    /**
     * 是否显示 promoTab
     */
    override fun promoTab() {
        HttpUtils.getData(RetrofitHelper.getHttpService().promoTab(HashMap<String, Any>().apply {
            put("userId", UserDataUtil.userId())
        }), object : BaseObserver<PromoTabBean>() {
            override fun onNext(tabData: PromoTabBean?) {
                mView?.initPromoTabTxt(tabData?.data?.promoTab ?: true)
                Constants.PromoTabTxt = tabData?.data?.promoTab ?: true
                EventBus.getDefault().post(NoticeConstants.ASIC_CHANGE_PROMO_TO_INFO)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

        })
    }

    /**
     * 查询是否有新活动
     */
    override fun userActionHasChanges() {
        HttpUtils.getData(RetrofitHelper.getHttpService().userActionHasChanges(HashMap<String, Any>().apply {
            put("token", UserDataUtil.loginToken())
            put("actionType", "1")
        }), object : BaseObserver<DataObjBooleanBean>() {

            override fun onNext(dataBean: DataObjBooleanBean?) {
                mView?.initPromoTabRemind(dataBean?.data?.obj == true)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

        })
    }

    /**
     * 获取双域名配置
     */
    fun getServerBaseUrl() {
        mModel?.getServerBaseUrl(object : BaseObserver<ServerBaseUrlBean>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: ServerBaseUrlBean) {
                if (dataBean.data == null) {
                    return
                }
                if (dataBean.data?.obj != null) {
                    if (HttpUrl.official) {
                        SpManager.putServerBaseUrlProd(dataBean)
                    } else {
                        SpManager.putServerBaseUrlTest(dataBean)
                    }
                    // sso需求，token置换
                    val xToken = dataBean.data?.obj?.xtoken
                    if (!xToken.isNullOrBlank()) {
                        UserDataUtil.setXToken(xToken)
                    }
                }
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    /**
     * 检测用户是否需要更新版本
     */
    override fun checkAppVersion(onlyCheckForce: Boolean) {
        val paramMap = hashMapOf<String, Any>()
        paramMap.put("versionName", AppUtil.getVersionName())
        paramMap.put("apkType", "android")
        mModel?.checkAppVersion(paramMap, object : BaseObserver<AppVersionBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: AppVersionBean) {

                mView?.hideNetDialog()

                if ("********" != dataBean.resultCode) {
                    return
                }

                if (onlyCheckForce) {
                    val objBean = dataBean.data?.obj
                    if (objBean?.id != 1 && objBean?.forceFlag == 1) {  // 强更
                        mView?.forceUpdate(objBean)
                    } else {
                        checkMaintain(13)
                    }
                } else {
                    mView?.showUpdateView(dataBean.data?.obj)
                }

            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * type类型
     * 图片类型，
     * 0：启动页，
     * 1：弹窗，
     * 2：活动，
     * 3：浮动窗口，
     * 4：banner,
     * 5：老拉新“我的”
     * 6：老拉新底图，
     * 7: 广告位，
     * 8：普通邀请底图，
     * 9：默认登录页，
     * 10：直播，
     * 11：ST发现页banner,
     * 12：ST信号源页banner,
     * 13：维护页面，
     * 14：行情维护页面
     * 15：未开户引导
     */
    override fun checkMaintain(type: Int, isCheckPopWindow: Boolean) {
        mModel?.checkMaintain(type, object : BaseObserver<MaintenanceBean>() {
            override fun onNext(returnData: MaintenanceBean?) {
                when (type) {
                    13 -> {
                        val objData = returnData?.data?.obj

                        if ("********" != returnData?.resultCode) {
                            checkMaintain(14)
                            if (isCheckPopWindow) {
                                popWindowApi()
                            }
                            return
                        }
                        if ("1" != objData?.showMaintenance) {
                            checkMaintain(14)
                            if (isCheckPopWindow) {
                                popWindowApi()
                            }
                        }

                        if ("1" == objData?.showMaintenance) {
                            mView?.showMaintenanceDialog(objData)
                        } else {
                            mView?.hideMaintenanceDialog()
                        }
                    }

                    14 -> {
                        if ("********" != returnData?.resultCode) {
                            return
                        }
                        //test
//                        returnData.data?.obj?.showMaintenance = "1"
//                        returnData.data?.obj?.maintenanceMessage = "test"

                        if ("1" != returnData.data?.obj?.showMaintenance) {
                            Constants.MAINTENANCE_MSG = ""
                            Constants.MARKET_MAINTAINING = false
                            return
                        }
                        Constants.MAINTENANCE_MSG = returnData.data?.obj?.maintenanceMessage ?: ""
                        Constants.MARKET_MAINTAINING = true
                    }
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    /**
     * 查询可开通的账户类型(获取申请开通mt4账户号类型)
     */
    override fun queryMT4AccountState(state: EnumLinkSkipState) {
        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountState(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(typeBean: MT4AccountTypeBean) {

                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")

                if (typeBean.resultCode != "V00000") {
                    ToastUtil.showToast(typeBean.msgInfo)
                    return
                }
                val obj = typeBean.data?.obj
                // LogUtil.i("queryMT4AccountState---- 111 ---- ${obj?.applyTpe}")
                mView?.skipOpenAccountActivity(state, obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")
            }

        })
    }

    /**
     * 作用：登录账号后，通知后端登录了什么账号
     * 这个完全可以在【账户列表】切换账号时调用的接口中统计
     * 可以与后端商议
     */
    override fun updateAccountLoginTime() {
        if (!UserDataUtil.isLogin()) return
        if (UserDataUtil.isStLogin()) {
            updateStAccountLogin()
        } else {
            updateAccountLogin()
        }
    }

    /**
     * 记录用户登录数据 【需优化】去除
     */
    private fun recordAdd() {
        val map = hashMapOf<String, Any>()
        map["userId"] = UserDataUtil.userId()
        mModel?.recordAdd(map, object : BaseObserver<BaseBean>() {
            override fun onNext(t: BaseBean?) {}

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }

    /**
     * 作用：登录账号后，通知后端登录了什么账号
     * 这个完全可以在【账户列表】切换账号时调用的接口中统计
     * 可以与后端商议
     */
    private fun updateStAccountLogin() {
        val jsonMap = HashMap<String, Any>()
        jsonMap["userId"] = UserDataUtil.userId()
        jsonMap["mtsAccountId"] = UserDataUtil.stAccountId()
        jsonMap["serverId"] = UserDataUtil.serverId()
        mModel?.updateStAccountLogin(jsonMap, object : BaseObserver<BaseBean>() {
            override fun onNext(t: BaseBean?) {}

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }

    /**
     * 作用：登录账号后，通知后端登录了什么账号
     * 这个完全可以在【账户列表】切换账号时调用的接口中统计
     * 可以与后端商议
     */
    override fun updateAccountLogin() {

        val jsonMap = HashMap<String, Any>()
        jsonMap["userId"] = UserDataUtil.userId()
        jsonMap["acountCd"] = UserDataUtil.accountCd()
        jsonMap["state"] = UserDataUtil.mt4State()
        jsonMap["platform"] = UserDataUtil.platform()
        jsonMap["accountType"] = UserDataUtil.accountType()
        jsonMap["accountServer"] = UserDataUtil.serverId()
        jsonMap["currencyType"] = UserDataUtil.currencyType()
        mModel?.updateAccountLogin(jsonMap, object : BaseObserver<BaseBean>() {
            override fun onNext(t: BaseBean?) {}
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })

    }

    /**
     * 增加点击数量统计 【需优化】去除
     */
    override fun eventsAddClicksCount(eventId: String) {
        HttpUtils.getData(
            RetrofitHelper.getHttpService().eventsAddClicksCount(
                eventId, UserDataUtil.loginToken()
            ), object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {}

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

            })
    }

    /**
     * 用户同意开启Firebase数据收集状态变更
     */
    override fun userCollectDataSwitch() {
        mModel?.userCollectDataSwitch(UserDataUtil.loginToken(), object : BaseObserver<CollectDataBean>() {
            override fun onNext(t: CollectDataBean?) {
                if ("V00000" != t?.resultCode) {
                    return
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

        })
    }

    /**
     * 查询用户是否可以跳转优惠券页面
     */
    override fun queryUserIsProclient() {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userId"] = UserDataUtil.userId()
        mModel?.queryUserIsProclient(paramMap, object : BaseObserver<QueryUserIsProclientData>() {
            override fun onNext(t: QueryUserIsProclientData?) {
                if (true == t?.data?.obj?.proclient) openActivity(CouponManagerActivity::class.java)
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }

        })
    }

    /**
     * 出金查询是否需要上传地址证明 【需优化】
     */
    override fun needUploadAddressProofApi() {
        val supervisionType = SpManager.getSuperviseNum("")
        if (supervisionType != "1") {
            addressproofWithrawNeedUploadIdPoaProofApi()
            return
        }
        mView?.showNetDialog()
        val params = hashMapOf<String, Any>(
            "token" to (UserDataUtil.loginToken())
        )
        mModel?.addressproofWithrawNeedUploadAddressProofApi(params, object : BaseObserver<NeedUploadAddressProofBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(data: NeedUploadAddressProofBean) {
                if ("V00000" != data.resultCode) {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                val needUploadAddressProof = data.data?.obj?.needUploadAddressProof.ifNull()
                if (TextUtils.equals("0", needUploadAddressProof)) {
                    fundIsH5WithdrawApi()
                } else if (TextUtils.equals("3", needUploadAddressProof) || TextUtils.equals("1", needUploadAddressProof)) {
                    val bundle = Bundle()
                    bundle.putInt(Constants.IS_FROM, 1)
                    openActivity(OpenFifthAddressSelectActivity::class.java, bundle)
                    mView?.hideNetDialog()
                } else if (TextUtils.equals("2", needUploadAddressProof)) {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(data.data?.obj?.msg)
                } else {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(data.data?.obj?.msg)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 出金是否需要上传身份地址证明(新) 【需优化】
     */
    private fun addressproofWithrawNeedUploadIdPoaProofApi() {
        mView?.showNetDialog()
        val params = hashMapOf<String, Any>(
            "token" to UserDataUtil.loginToken()
        )
        mModel?.addressproofWithrawNeedUploadIdPoaProofApi(params, object : BaseObserver<NeedUploadIdProofData>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: NeedUploadIdProofData) {
                mView?.hideNetDialog()
                if ("V00000" != dataBean.resultCode) {
                    ToastUtil.showToast(dataBean.msg)
                    return
                }
                val data = dataBean.data?.obj?.needUploadIdPoaProof.ifNull()
                if ("0" == data) {
                    fundIsH5WithdrawApi()
                    return
                }

                val buryHashMap = hashMapOf("Position" to "Withdraw_button")
                // 1/3 lv2
                if ("1" == data || "3" == data) {
                    SumSubJumpHelper.isJumpSumSub(mView?.ac, Constants.SUMSUB_TYPE_POI)
//                    openActivity(OpenAccoGuideLv2Activity::class.java)
                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK, buryHashMap
                    )
                    return
                }

                // 4/6 lv3
                if ("4" == data || "6" == data) {
//                    openActivity(OpenAccoGuideLv3Activity::class.java)
                    SumSubJumpHelper.isJumpSumSub(mView?.ac, Constants.SUMSUB_TYPE_POA)
                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V334.REGISTER_LIVE_LVL3_BUTTON_CLICK, buryHashMap
                    )
                    return
                }
                ToastUtil.showToast(dataBean.data?.obj?.msg)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 检查是否跳转到H5出金 【需优化】
     */
    private fun fundIsH5WithdrawApi() {
        mView?.showNetDialog()
        val params = hashMapOf<String, Any>(
            "userToken" to UserDataUtil.loginToken()
        )
        mModel?.fundIsH5WithdrawApi(params, object : BaseObserver<NeedH5WithdrawBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(data: NeedH5WithdrawBean) {
                mView?.hideNetDialog()
                if ("********" != data.resultCode) {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                var htmlUrl = data.data?.obj?.h5Url.ifNull()
                htmlUrl += if (htmlUrl.contains("?")) "" else "?"
                val socialTradingType = if (UserDataUtil.isStLogin()) "1" else "0"
                if (!htmlUrl.contains("userToken=")) htmlUrl = "$htmlUrl&userToken=${UserDataUtil.loginToken()}"
                if (!htmlUrl.contains("mt4AccountId=")) htmlUrl = "$htmlUrl&mt4AccountId=${UserDataUtil.accountCd()}"
                if (!htmlUrl.contains("currency=")) htmlUrl = "$htmlUrl&currency=${UserDataUtil.currencyType()}"
                if (!htmlUrl.contains("type=")) htmlUrl = "$htmlUrl&type=${SpManager.getSuperviseNum("")}"
                htmlUrl = "$htmlUrl&socialtradingtype=$socialTradingType"

                val bundle = Bundle()
                bundle.putString("url", htmlUrl)
                bundle.putString("title", mView?.ac?.getString(R.string.withdraw))
                bundle.putInt("tradeType", 3)
                bundle.putBoolean("isNoTitleCanBack", true)
                openActivity(HtmlActivity::class.java, bundle)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * in app 点击后的接口请求
     * 这个是通知后台这个通知已点击了，之后一天不会推送相同的通知
     * 【需优化】去除
     */
    override fun inAppDismissReport(eventId: String?) {
        val token = UserDataUtil.loginToken()
        val userId = UserDataUtil.userId()
        HttpUtils.getData(
            RetrofitHelper.getHttpService().inAppInfoRecordClick(
                token = token, imgType = "20", userId = userId, eventId = eventId, accountId = UserDataUtil.accountCd()
            ), object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {

                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
    }

    /**
     * 更新用户查看PromoTab时间 【需优化】去除
     */
    override fun updateLastActionTime() {
        HttpUtils.getData(RetrofitHelper.getHttpService().userActionUpdateLastActionTime(HashMap<String, Any>().apply {
            put("token", UserDataUtil.loginToken())
            put("actionType", "1")
        }), object : BaseObserver<BaseBean>() {
            override fun onNext(t: BaseBean?) {}
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }

    /**
     * 推送点击后调用 【需优化】
     */
    override fun notificationMarketingClickCntUpdate(dataBean: PushParam?) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("ruleId", dataBean?.ruleId ?: "")
        jsonObject.addProperty("pushId", dataBean?.pushId ?: "")
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        mModel.notificationMarketingClickCntUpdate(requestBody, object : BaseObserver<BaseBean>() {
            override fun onNext(t: BaseBean?) {}

            override fun onHandleSubscribe(d: Disposable?) {}
        })
    }

    /**
     * 弹窗曝光事件
     */
    fun sensorsTrackWhenPopupShow(data: PopWindowAppEvent?) {
        data?.let {
            SensorsDataUtil.track(SensorsConstant.V3610.POPUP_EXPOSURE, JSONObject().apply {
                put(SensorsConstant.Key.PLATFORM_TYPE, "Android")
                put(SensorsConstant.Key.ACTIVITY_NAME, data.promoLibraryName)
                put(SensorsConstant.Key.POPUP_NAME, data.originalEventDesc)
                put(SensorsConstant.Key.POPUP_ID, data.eventId)
                put(SensorsConstant.Key.POPUP_TYPE, data.imgType)
            })
        }
    }

    /**
     * 弹窗点击事件
     */
    fun sensorsTrackWhenPopupClick(data: PopWindowAppEvent?, buttonName: String) {
        data?.let {
            SensorsDataUtil.track(SensorsConstant.V3610.POPUP_CLICK, JSONObject().apply {
                put(SensorsConstant.Key.PLATFORM_TYPE, "Android")
                put(SensorsConstant.Key.ACTIVITY_NAME, data.promoLibraryName)
                put(SensorsConstant.Key.POPUP_NAME, data.originalEventDesc)
                put(SensorsConstant.Key.POPUP_ID, data.eventId)
                put(SensorsConstant.Key.POPUP_TYPE, data.imgType)
                put(SensorsConstant.Key.BUTTON_NAME, buttonName)
            })
        }
    }

    override fun userQueryUserLevel() {
        val map = hashMapOf<String, Any>()
        mModel?.userQueryUserLevel(map, object : BaseObserver<KycVerifyLevelDataBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: KycVerifyLevelDataBean) {
                if (dataBean.resultCode != "V00000") {
                    return
                }
                UserDataUtil.setKycLevel(dataBean.data?.obj?.level.ifNull(0).toString())
                // 通知用于同步接口中的其他内容
                EventBus.getDefault().post(DataEvent(NoticeConstants.SYNC_KYC_USER_LEVEL, dataBean.data?.obj))
            }
        })
    }
}
