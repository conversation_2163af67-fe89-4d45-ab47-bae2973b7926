
import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.discover.WbpStatusData
import io.reactivex.disposables.Disposable

interface MainNewComerEventContract {

    interface Model : BaseModel {
        fun newerGiftActivityGetWBPStatusApi(map: HashMap<String, Any>, baseObserver: BaseObserver<WbpStatusData>): Disposable
        fun queryMT4AccountState(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable
    }

    interface View : BaseView {
        fun showNewComerEventView()
        fun skipOpenAccountActivity(objData: MT4AccountTypeObj?, data: WbpStatusData.Activity?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun newerGiftActivityGetWBPStatusApi()
        abstract fun queryMT4AccountState(data: WbpStatusData.Activity?)
    }

}
