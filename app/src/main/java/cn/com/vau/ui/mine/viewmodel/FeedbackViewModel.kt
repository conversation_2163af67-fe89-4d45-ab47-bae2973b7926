package cn.com.vau.ui.mine.viewmodel

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.liveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.util.widget.Transformations
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

class FeedbackViewModel : BaseViewModel() {
    //还没收到新的api
    var feedbacks: String = ""

    private var requestBodyParamLiveData = MutableLiveData<RequestBody>()

    val feedBackLiveData = Transformations.switchMap(requestBodyParamLiveData) {
        liveData {
            val result = try {
                val data = RetrofitHelper.getHttpService().consultLogContent(
                    UserDataUtil.loginToken(),
                    feedbacks
                )
                Result.success(data)
            } catch (e: Exception) {
                e.printStackTrace()
                Result.failure(e)
            }
            emit(result)
        }
    }

    fun sendFeedbacks() {
        val jsonObject = JsonObject()
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestBodyParamLiveData.value = requestBody
    }

}