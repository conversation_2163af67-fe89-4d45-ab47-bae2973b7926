package cn.com.vau.ui.order

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmFragment
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.HintMaintenanceView
import cn.com.vau.databinding.FragmentOrderThemeBinding
import cn.com.vau.history.HistoryTrack
import cn.com.vau.history.ui.HistoryActivity
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.ext.changeTouchSlop
import cn.com.vau.trade.fragment.order.FreeOrderFragment
import cn.com.vau.trade.fragment.order.OpenTradesFragment
import cn.com.vau.trade.fragment.order.PendingOrderFragment
import cn.com.vau.trade.interfac.RefreshInterface
import cn.com.vau.trade.perform.TradeAccountInfoPerformance
import cn.com.vau.trade.perform.TradeProductTitlePerformance
import cn.com.vau.trade.perform.TradeTimeChartPerformance
import cn.com.vau.trade.perform.TradeTitlePerformance
import cn.com.vau.trade.viewmodel.OrderThemeViewModel
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.TabType
import cn.com.vau.util.addFragment
import cn.com.vau.util.ifNull
import cn.com.vau.util.init
import cn.com.vau.util.onClickWithDefaultDelegate
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.setVp
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * 订单
 */
class OrderThemeFragment : BaseMvvmFragment<FragmentOrderThemeBinding, OrderThemeViewModel>(), SDKIntervalCallback {

    private val performManager by lazy {
        PerformManager(this)
    }

    /**
     * 下单ViewModel
     */
    private val orderViewMode: OrderViewModel by viewModels()

    /**
     * 标题
     */
    private var titlePerformance: TradeTitlePerformance? = null

    /**
     * 产品对+账户信息
     */
    private var productTitlePerformance: TradeProductTitlePerformance? = null

    /**
     * 资产
     */
    private var accountInfoPerformance: TradeAccountInfoPerformance? = null

    /**
     * 分时图
     */
    private var timeChartPerformance: TradeTimeChartPerformance? = null

    private var openTradesFragment: OpenTradesFragment? = null
    private var pendingOrderFragment: PendingOrderFragment? = null

    private var freeOrderFragment :FreeOrderFragment? = null

    override fun onCallback() {
        if (Constants.MARKET_MAINTAINING) {
            showMaintenance(true)
            return
        } else {
            showMaintenance(false)
        }
        if (InitHelper.isNotSuccess()) return
        context?.let {
            performManager.onCallback()
            mBinding.openOrderView.onCallback()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        PerfTraceUtil.startTrace(PerfTraceUtil.StartTrace.Perf_v6_Order_Create_First)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        EventBus.getDefault().register(this)
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        PerfTraceUtil.firstFrameTrace(
            mBinding.root,
            PerfTraceUtil.StartTrace.Perf_v6_Order_Create_First,
            null
        )
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        setOpenOrderView()
        addPerformance()
        initTabLayoutTabs()
        refreshAccountInfoAndFreeOrder()
        // 神策自定义埋点(v3500)
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TAB_PAGE_VIEW, JSONObject().apply {
            put(SensorsConstant.Key.TAB_NAME, "Orders")
        })
    }

    /**
     * 初始下单器
     */
    private fun setOpenOrderView() {
        orderViewMode.loadingChange.dialogLiveData.observe(this) {
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }
        mBinding.openOrderView.initOpenOrderView(orderViewMode)
    }

    private fun addPerformance() {
        titlePerformance = TradeTitlePerformance(this, mBinding)
        titlePerformance?.run { performManager.addPerformance(this) }

        accountInfoPerformance = TradeAccountInfoPerformance(this, orderViewMode, mBinding)
        accountInfoPerformance?.run { performManager.addPerformance(this) }

        productTitlePerformance = TradeProductTitlePerformance(this, orderViewMode, mBinding) {
            accountInfoPerformance?.setExpand(it)
        }
        productTitlePerformance?.run { performManager.addPerformance(this) }

        timeChartPerformance = TradeTimeChartPerformance(this, mBinding, orderViewMode)
        timeChartPerformance?.run { performManager.addPerformance(this) }
    }

    @SuppressLint("SetTextI18n")
    private fun refreshAccountInfoAndFreeOrder() {
        if (freeOrderFragment != null) {
            return
        }
        // 查询是否有免费订单
        mViewModel.stockActivityStockListDetail()
    }

    private fun initTabLayoutTabs() {
        freeOrderFragment = null
        mViewModel.fragmentList.clear()
        mViewModel.titleList.clear()
        mViewModel.titleList.add(getString(R.string.positions))
        openTradesFragment = OpenTradesFragment.newInstance(orderViewMode.productName).apply {
            refreshFinished = {
                refreshFinish()
            }
            setOrderViewModel(orderViewMode)
        }
        openTradesFragment?.let {
            mViewModel.fragmentList.add(it)
        }

        mViewModel.titleList.add(getString(R.string.pending_orders))
        pendingOrderFragment = PendingOrderFragment.newInstance(orderViewMode.productName).apply {
            refreshFinished = {
                refreshFinish()
            }
            setOrderViewModel(orderViewMode)
        }
        pendingOrderFragment?.let {
            mViewModel.fragmentList.add(it)
        }
        mBinding.mViewPager2.init(
            mViewModel.fragmentList, mViewModel.titleList, childFragmentManager, this
        )
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, mViewModel.titleList, TabType.LINE_INDICATOR)
        mBinding.mViewPager2.changeTouchSlop()
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            val currentFragment = mViewModel.fragmentList[mBinding.mViewPager2.currentItem]
            (currentFragment as? RefreshInterface)?.refreshData()
        }
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    fun initChildView() {
        // 免费订单
        freeOrderFragment = FreeOrderFragment().apply {
            refreshFinished = {
                refreshFinish()
            }
        }
        freeOrderFragment?.let {
            mBinding.mViewPager2.addFragment(
                it, getString(R.string.free_orders), 1
            )
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.stockListDetailLiveData.observe(this) {
            initChildView()
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.ivEnterHistory.onClickWithDefaultDelegate {
            openActivity(HistoryActivity::class.java)
            HistoryTrack.trackHistoryEntryIcon()
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onStickyEvent(event: StickyEvent) {

        when (event.tag) {
            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_OPEN -> {
                mBinding.mViewPager2.post {
                    if (context != null && isAdded) {
                        mBinding.mViewPager2.currentItem = mViewModel.titleList.indexOfFirst {
                            it == getString(R.string.positions)
                        }
                    }
                    EventBus.getDefault().removeStickyEvent(event)
                }
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_PENDING -> {
                mBinding.mViewPager2.postDelayed({
                    if (context != null && isAdded) {
                        mBinding.mViewPager2.setCurrentItem(mViewModel.titleList.indexOfFirst {
                            it == getString(R.string.pending_orders)
                        }, false)
                    }
                    EventBus.getDefault().removeStickyEvent(event)
                }, 300)
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_HISTORY -> {
                openActivity(HistoryActivity::class.java)
                HistoryTrack.trackHistoryEntryIcon()
                EventBus.getDefault().removeStickyEvent(event)
            }

            NoticeConstants.MAIN_SHOW_ORDERS_ITEM_FREE -> {
                mBinding.mViewPager2.post {
                    if (context != null && isAdded) {
                        var index = mViewModel.titleList.indexOfFirst {
                            it == getString(R.string.free_orders)
                        }
                        if (index == -1)
                            index = 0
                        mBinding.mViewPager2.currentItem = index
                    }

                }
            }

            //下单
            NoticeConstants.OpenOrder.OPEN_ORDER -> {
                val bundle = event.data as? Bundle
                bundle?.let {
                    val produceName = it.getString(Constants.PARAM_PRODUCT_NAME)
                    val cmd = it.getString(Constants.PARAM_ORDER_TYPE)
                    val productData = VAUSdkUtil.symbolList().find { shareProductData ->
                        shareProductData.symbol == produceName
                    }
                    SpManager.putTradeProductSymbol(productData?.symbol ?: "")
                    orderViewMode.setProduceData(productData)
                    orderViewMode.tradeType = cmd.ifNull(OrderViewModel.TRADE_SELL)
                }
                EventBus.getDefault().removeStickyEvent(event)
            }
        }
    }

    @SuppressLint("SetTextI18n")
    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            // 切换账户 || 退出登陆
            NoticeConstants.SWITCH_ACCOUNT, NoticeConstants.AFTER_LOGOUT_RESET -> {
                initTabLayoutTabs()
                refreshAccountInfoAndFreeOrder()
            }

            // 免费订单
            NoticeConstants.WS.CHANGE_OF_ORDER_FREE -> {
                val index = mViewModel.titleList.indexOfFirst {
                    it == getString(R.string.free_orders)
                }
                if (-1 == index) {
                    mViewModel.stockActivityStockListDetail()
                }
            }
        }
    }

    private fun showMaintenance(isShowMaintenance: Boolean) {
        if (isShowMaintenance) {
            mBinding.llOrderTab.visibility = View.GONE
            mBinding.mViewStubMarketMaintenance.setOnInflateListener { stub, inflated ->
                if (inflated is HintMaintenanceView) {
                    val content = if (!TextUtils.isEmpty(Constants.MAINTENANCE_MSG)) "\n" + Constants.MAINTENANCE_MSG + "\n" else "\n"
                    val fullContent = getString(R.string.maintenance_dialog_content_1) + content
                    inflated.setContentText(fullContent)
                }
            }
            mBinding.mViewStubMarketMaintenance.isVisible = true
        } else {
            mBinding.mViewStubMarketMaintenance.isVisible = false
            mBinding.llOrderTab.isVisible = true
        }
    }

    //TODO Felix 暂时这么处理，等MainActivity改成ViewPage2 就不需要这里了
    override fun onHiddenChanged(hidden: Boolean) {
        mViewModel.isHidden = hidden
        if (hidden) {
            SDKIntervalUtil.instance.removeCallBack(this)
        } else if (mViewModel.isPaused.not()) {
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
            titlePerformance?.refreshData()
            refreshAccountInfoAndFreeOrder()
        }
    }

    @SuppressLint("SetTextI18n")
    override fun onResume() {
        super.onResume()
        mViewModel.isPaused = false
        if (mViewModel.isHidden) return
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
        refreshAccountInfoAndFreeOrder()
    }

    override fun onPause() {
        super.onPause()
        mViewModel.isPaused = true
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    private fun refreshFinish() {
        mBinding.mSmartRefreshLayout.finishRefresh()
    }
}