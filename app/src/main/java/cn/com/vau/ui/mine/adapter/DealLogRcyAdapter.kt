package cn.com.vau.ui.mine.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.DealLogInfo

/**
 * Created by roy on 2018/10/25.
 */
class DealLogRcyAdapter(
    var mContext: Context,
    var dataList: ArrayList<DealLogInfo>
) : RecyclerView.Adapter<DealLogRcyAdapter.ViewHolder>() {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_rcy_deal_log, parent, false))

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bindTo(dataList.elementAtOrNull(dataList.size - 1 - position))
    }

    override fun getItemCount(): Int {
        return dataList.size
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {

        @SuppressLint("UseCompatLoadingForDrawables", "SetTextI18n")
        fun bindTo(dataBean: DealLogInfo?) {

            dataBean?.run {

                itemView.findViewById<TextView>(R.id.tvDate).text = dataBean.date ?: ""
                itemView.findViewById<TextView>(R.id.tvLog).text = dataBean.log ?: ""

            }

        }

    }

}