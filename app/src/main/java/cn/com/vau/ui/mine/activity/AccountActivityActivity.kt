package cn.com.vau.ui.mine.activity

import android.annotation.SuppressLint
import android.view.Gravity
import android.view.View
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.DbManager
import cn.com.vau.common.greendao.dbUtils.DealLogInfo
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.CalendarPopWindow
import cn.com.vau.databinding.ActivityAccountActivityBinding
import cn.com.vau.ui.mine.adapter.DealLogRcyAdapter
import cn.com.vau.ui.mine.viewmodel.AccountActivityViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import java.util.Date

/**
 * 账户活动
 */
class AccountActivityActivity : BaseMvvmActivity<ActivityAccountActivityBinding, AccountActivityViewModel>() {

    private var adapter: DealLogRcyAdapter? = null
    private val calendarPopWindow by lazy { CalendarPopWindow(this) }

    override fun initView() {
        mBinding.tvTime.text = TimeUtil.getDateFormatEN(Date(System.currentTimeMillis()))

        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(this)

        adapter = DealLogRcyAdapter(this, mViewModel.dataList)
        mBinding.mRecyclerView.adapter = adapter
        mBinding.mRecyclerView.addItemDecoration(DividerItemDecoration(0.5.dp2px(), dividerColor = AttrResourceUtil.getColor(this, R.attr.color_c1f1e1e1e_c1fffffff)))

        observe()
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvTime.setOnClickListener(this)
        mBinding.ivSelectTime.setOnClickListener(this)
        mBinding.tvNext.setOnClickListener(this)
        mBinding.mRefreshLayout.setOnRefreshListener {
            mBinding.mRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }
        mBinding.mRefreshLayout.setOnLoadMoreListener {
            mBinding.mRefreshLayout.finishLoadMore(Constants.finishRefreshOrMoreTime)
            mBinding.mRefreshLayout.setNoMoreData(true)
        }
        calendarPopWindow.setOnDismissListener {
            setAlpha(1f)
        }
        calendarPopWindow.setOnPopClickListener(object : CalendarPopWindow.OnPopClickListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun onConfirm(dateStr: String) {
                mViewModel.dataList.clear()
                mViewModel.dataList.addAll(DbManager.getInstance().getDealLogList(dateStr) as ArrayList<DealLogInfo>)
                adapter?.notifyDataSetChanged()
                mBinding.tvTime.text = CalendarUtil.getInstance().getFormatDealLogTitle(dateStr)
                mViewModel.likeDateStr = dateStr
            }
        })

    }

    override fun onClick(v: View?) {
        super.onClick(v)
        when (v?.id) {
            R.id.tvTime, R.id.ivSelectTime -> {
                calendarPopWindow.showAtLocation(mBinding.root, Gravity.BOTTOM or Gravity.CENTER_HORIZONTAL, 0, 0)
                setAlpha(0.2f)
            }

            R.id.tvNext -> {
                initPermission()
            }
        }
    }

    private fun observe() {
        mViewModel.addLogLiveData.observe(this, androidx.lifecycle.Observer { result ->
            hideLoadDialog()
            if (result.isFailure) return@Observer
            val dataBean = result.getOrNull() ?: return@Observer
            if ("V00000" != dataBean.resultCode) {
                ToastUtil.showToast(dataBean.msgInfo)
                return@Observer
            }
            CenterActionDialog.Builder(this)
                .setContent(getString(R.string.successful_submission)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.confirm)) //设置单个按钮文本
                .setOnSingleButtonListener { finish() }
                .build()
                .showDialog()
        })
    }

    private fun initPermission() {
        showLoadDialog()
        mViewModel.userLogsAddLog()
    }

}