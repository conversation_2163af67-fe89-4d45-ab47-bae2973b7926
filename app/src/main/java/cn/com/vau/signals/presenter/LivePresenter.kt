package cn.com.vau.signals.presenter

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.data.*
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.discover.*
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.util.*
import io.reactivex.disposables.Disposable
import org.greenrobot.eventbus.EventBus

/**
 * Created by liyang.
 * live
 */
class LivePresenter : LiveContract.Presenter() {

    var messageData: ArrayList<ChartMessage> = arrayListOf()
    var activeData: ArrayList<PromoEventData> = arrayListOf()
    var wbpDataBean: WbpStatusData.Obj? = null

    override fun addAWSLive(userId: String, roomArn: String, channelId: Long) {
        mModel?.addAWSLive(userId, roomArn, channelId, object : BaseObserver<AddAWSData>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: AddAWSData) {
                if ("V00000" != dataBean.resultCode || dataBean.data.obj == null) {
                    return
                }
                mView?.getToken(dataBean.data.obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun getWatchCount(channelId: Long) {
        mModel?.getWatchCount(channelId, object : BaseObserver<LiveInfoData>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: LiveInfoData) {
                if ("V00000" != dataBean.resultCode) {
                    return
                }
                mView?.getLiveInfo(dataBean.data)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun getHistoryWatchCount(channelId: Long) {
        mModel?.getWatchCount(channelId, object : BaseObserver<LiveInfoData>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: LiveInfoData) {
                if ("V00000" != dataBean.resultCode) {
                    return
                }
                mView?.getLiveInfo(dataBean.data)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun filterChatContent(userId: String, roomId: Long, chatContent: String) {
        mModel?.filterChatContent(userId, roomId, chatContent, object : BaseObserver<FilterChartData>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: FilterChartData) {
                if ("V00000" != dataBean.resultCode) {
                    return
                }
                if (dataBean.data.obj.chatCode == "0")
                    mView?.filterChatContentSucceed(dataBean.data.obj.chatContent, dataBean.data.obj.chatId)
                else
                    mView?.filterForbiddenChat(dataBean.data.obj.chatContent)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun giveLikes(isLike: Int, channelId: Long) {
        mModel?.giveLikes(isLike, channelId, object : BaseObserver<LiveLikes>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: LiveLikes) {
                if ("V00000" != dataBean.resultCode || dataBean.data.obj == null) {
                    return
                }
                mView?.giveLikesSucceed(dataBean.data.obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun getChatContent(channelId: Long) {
        mModel?.getChatContent(channelId, object : BaseObserver<HistoryMessageData>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: HistoryMessageData) {
                if ("V50000" == dataBean.resultCode) {
                    ToastUtil.showToast(dataBean.msgInfo)
                } else if ("V00000" == dataBean.resultCode) {
                    val liveList = dataBean.data?.obj as ArrayList<ChartMessage>

                    messageData.addAll(liveList)
                    mView?.getChartSucceed()
                }
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun getChatToken(userId: String, roomArn: String) {
        if (mModel != null) {
            mModel?.getChartToken(userId, roomArn, object : BaseObserver<DataObjStringBean>() {

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(dataBean: DataObjStringBean) {
                    if ("V00000" != dataBean.resultCode) {
                        return
                    }
                    mView?.getChartTokenSucceed(dataBean.data?.obj.ifNull())
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
        }
    }

    override fun queryLivePromo() {
        mModel?.queryLivePromo(object : BaseObserver<LivePromoData>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: LivePromoData) {
                if ("00000000" != dataBean.resultCode) {
                    return
                }
                val promoData = dataBean.data.obj as ArrayList<PromoEventData>
                activeData.clear()
                activeData.addAll(promoData)
                mView?.queryLivePromoSucceed(activeData)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun eventsAddClicksCount(eventId: String) {
        HttpUtils.getData(
            RetrofitHelper.getHttpService().eventsAddClicksCount(
                eventId,
                UserDataUtil.loginToken()

            ), object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {}

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

            })
    }

    override fun exitLive(userId: String, channelId: Long) {
        mModel?.exitLive(userId, channelId, object : BaseObserver<BaseBean>() {

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: BaseBean) {

            }

            override fun onError(e: Throwable?) {
                super.onError(e)
            }
        })
    }

    override fun queryStAccountType(needDialog: Boolean) {
        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountType(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onNext(resMT4AccountTypeModel: MT4AccountTypeBean) {
                mView?.hideNetDialog()
                if (resMT4AccountTypeModel.resultCode == "V00000") {
                    val obj = resMT4AccountTypeModel.data?.obj
                    // 和iOS对过，这里iOS没有判断开通跟单的校验，直接进行公共跳转的开户校验
                    if (mView != null && obj != null)
                        VAUStartUtil.openAccountGuide(mView?.ac, obj, 0)
                } else {
                    ToastUtil.showToast(resMT4AccountTypeModel.msgInfo)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                if (mView != null)
                    mView?.hideNetDialog()
            }
        })
    }

    // TODO: lvyang 没有地方调用这里，所以 LivingPLayerActivity 和 HistoryPlayerActivity 类里面的 skipOpenAccountActivity 也无用
    override fun queryMT4AccountState(state: EnumLinkSkipState) {
        mView?.showNetDialog()
        val map = hashMapOf<String, String>()
        map["token"] = UserDataUtil.loginToken()
        mModel?.queryMT4AccountState(map, object : BaseObserver<MT4AccountTypeBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(typeBean: MT4AccountTypeBean) {

                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")

                if (typeBean.resultCode != "V00000") {
                    ToastUtil.showToast(typeBean.msgInfo)
                    return
                }
                val obj = typeBean.data?.obj
                // LogUtil.i("queryMT4AccountState---- 111 ---- ${obj?.applyTpe}")
                mView?.skipOpenAccountActivity(state, obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
                EventBus.getDefault().post("html_dialog_net_finish")
            }

        })
    }
}