package cn.com.vau.signals.live

import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.ActivityInfo
import android.graphics.Color
import android.os.*
import android.text.TextUtils
import android.view.View
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.SimpleItemAnimator
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.discover.ChartMessage
import cn.com.vau.data.discover.ChartTokenBean
import cn.com.vau.data.discover.LiveInfoBean
import cn.com.vau.data.discover.PromoEventData
import cn.com.vau.data.enums.EnumLinkSkipState
import cn.com.vau.databinding.ActivityNormalVideoBinding
import cn.com.vau.page.AWSMessageData
import cn.com.vau.page.MessageAttributes
import cn.com.vau.page.MessageData
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.signals.adapter.live.MessageHistoryRecyclerAdapter
import cn.com.vau.signals.live.history.Sample
import cn.com.vau.signals.live.history.base.BasePlayer
import cn.com.vau.signals.live.history.base.PlayerConfig
import cn.com.vau.signals.live.history.listener.OnFullScreenChangedListener
import cn.com.vau.signals.live.history.player.GoogleExoPlayer
import cn.com.vau.signals.live.history.view.NormalVideoView
import cn.com.vau.signals.model.LiveModel
import cn.com.vau.signals.presenter.LiveContract
import cn.com.vau.signals.presenter.LivePresenter
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.noRepeat
import cn.com.vau.util.tracking.LogEventUtil
import okhttp3.*
import okhttp3.internal.ws.RealWebSocket
import org.greenrobot.eventbus.EventBus
import java.lang.ref.WeakReference

class HistoryPlayerActivity : BaseFrameActivity<LivePresenter, LiveModel>(), LiveContract.View {

    private val mBinding by lazy { ActivityNormalVideoBinding.inflate(layoutInflater) }

    private var sample: Sample? = null
    private var messageHistoryAdapter: MessageHistoryRecyclerAdapter? = null
    private var bundle: Bundle? = null
    private var channelId: Long = 0
    private var roomId: Long = 0
    private var roomArn: String = ""
    private var messageNode: String = ""
    private var playbackUrl: String = ""
    private var virtualCount: Long = 0
    private var virtualLikeCount: Long = 0
    private var liveStatus: Int = 0
    private var channel: String = ""
    private var userId: String = ""
    private var shareContent: String = ""
    private var socket: RealWebSocket? = null
    private var chatToken: String? = null
    private val inputTextMsgDialog: InputTextMsgDialog by lazy { InputTextMsgDialog.Builder(this).build() }
    private var isGiveLike: Boolean = false
    private var originWidth: Double = 0.0
    private var originHeight = 0.0
    private var onFullScreenChangeListener: OnFullScreenChangedListener? = null

    private val mHandler = MyHandler(WeakReference(this))

    private class MyHandler(val wrFragment: WeakReference<HistoryPlayerActivity>) :
        Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            wrFragment.get()?.run {
                when (msg.what) {
                    0 -> {
                        //updateControls()
                    }
                    //查看观看人数信息
                    1 -> {
                        mPresenter?.getHistoryWatchCount(channelId)
                        mHandler.sendEmptyMessageDelayed(1, 5 * 1000L)
                    }

                    2 -> {
                        messageHistoryAdapter?.notifyItemChanged(messageHistoryAdapter?.itemCount.ifNull(1) - 1)
                        mBinding.rvMessage.post {
                            mBinding.rvMessage.scrollToPosition(
                                messageHistoryAdapter?.itemCount.ifNull(
                                    1
                                ) - 1
                            )
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) { // 5.0
            val window = window
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS) // 確認取消半透明設置。
            window.decorView.systemUiVisibility =
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE // 全螢幕顯示，status bar 不隱藏，activity 上方 layout 會被 status bar 覆蓋。
            // 配合其他 flag 使用，防止 system bar 改變後 layout 的變動。
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS) // 跟系統表示要渲染 system bar 背景。
            window.statusBarColor = Color.TRANSPARENT
        }
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        setContentView(mBinding.root)
        getData()
        sample = Sample()
        initPlayerView()
    }

    private fun initPlayerView() {
        messageHistoryAdapter = MessageHistoryRecyclerAdapter(this, mPresenter.messageData)
        mBinding.rvMessage.layoutManager = WrapContentLinearLayoutManager(this)
        (mBinding.rvMessage.layoutManager as LinearLayoutManager).stackFromEnd = true
        mBinding.rvMessage.adapter = messageHistoryAdapter
        mBinding.rvMessage.setHasFixedSize(true)
        (mBinding.rvMessage.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        mBinding.livingTopHorizontal.clAccountTop.visibility = View.GONE
        mBinding.rvMessage.itemAnimator = null

        mBinding.videoView.setVideoPath(playbackUrl)
        val player: BasePlayer = GoogleExoPlayer(this)

        val renderType: Int =
            if (sample?.renderType == 0) PlayerConfig.RENDER_TEXTURE_VIEW else PlayerConfig.RENDER_SURFACE_VIEW

        //设置全屏策略，设置视频渲染界面类型,设置是否循环播放，设置自定义播放器
        val playerConfig: PlayerConfig = PlayerConfig.Builder()
            .fullScreenMode(sample?.fullscreenMode ?: 0)
            .renderType(renderType)
            .looping(sample?.looping ?: false)
            .player(player) //IjkPlayer,GoogleExoPlayer 需添加对应的依赖
            .build()
        mBinding.videoView.setPlayerConfig(playerConfig)

        if (originHeight == 9.0 && originWidth == 16.0) {
            mBinding.fullTips.visibility = View.VISIBLE
            mBinding.videoView.setFullscreenVisible(View.VISIBLE)
            mBinding.tvGetIt.visibility = View.VISIBLE
        } else {
            mBinding.fullTips.visibility = View.GONE
            mBinding.videoView.setFullscreenVisible(View.GONE)
            mBinding.tvGetIt.visibility = View.GONE
        }

        mBinding.videoView.setOnFullScreenChangeListener { isFullScreen ->
            mBinding.fullTips.visibility = View.GONE
            if (!isFullScreen) {
                mBinding.clTop.visibility = View.VISIBLE
                mBinding.livingTopHorizontal.root.visibility = View.GONE
                mBinding.rvMessage.post { mBinding.rvMessage.scrollToPosition(messageHistoryAdapter?.itemCount.ifNull(1) - 1) }
            } else {
                mBinding.clTop.visibility = View.GONE
                mBinding.livingTopHorizontal.root.visibility = View.VISIBLE
                mBinding.tvGetIt.visibility = View.GONE
                mBinding.rvMessage.post { mBinding.rvMessage.scrollToPosition(messageHistoryAdapter?.itemCount.ifNull(1) - 1) }
            }
        }

        mBinding.videoView.setOnShareListener(object : NormalVideoView.OnShareClickListener {
            override fun onClick() {
                noRepeat {
                    // 系统分享
                    val shareIntent = Intent(Intent.ACTION_SEND)
                    shareIntent.type = "text/plain"
                    if (TextUtils.isEmpty(shareContent)) {
                        shareContent =
                            "Fire away!\n Join our live stream & ask us anything about the markets, Vantage App, promotions or even the weather! \n Download Vantage App: https://vantagemarkets.onelink.me/8SIf/webcbsh3"
                    }
                    shareIntent.putExtra(Intent.EXTRA_TEXT, shareContent)
                    startActivityForResult(shareIntent, 1000)
                    LogEventUtil.setLogEvent("livestream_share_button_click")
                }
            }
        })

        mBinding.videoView.setOnMessageVisibleListener(object :
            NormalVideoView.OnMessageVisibleClickListener {
            override fun onClick(isVisible: Boolean) {
                noRepeat {
                    if (isVisible) {
                        mBinding.rvMessage.visibility = View.VISIBLE
                    } else {
                        mBinding.rvMessage.visibility = View.GONE
                    }
                }
            }
        })

        mBinding.videoView.setMessageListener(object : NormalVideoView.OnMessageClickListener {
            override fun onClick() {
                noRepeat {
                    if (!UserDataUtil.isLogin()) {
                        openActivity(LoginActivity::class.java)
                        return@noRepeat
                    }
                    if (inputTextMsgDialog.isShowDialog() == true) {
                        return@noRepeat
                    }
                    inputTextMsgDialog.setOnTextSendListener(object :
                        InputTextMsgDialog.OnTextSendListener {
                        override fun onTextSend(msg: String?) {
                            if (!msg.isNullOrBlank()) {
                                mPresenter.filterChatContent(userId, roomId, msg.toString())
                            }
                        }

                        override fun dismiss() {
                            if (mBinding.videoView.isFullScreen) {
                                //resetChartView(ScreenUtil.dip2px(context,120f))
                                mBinding.videoView?.startFullScreen(false)
                            } else {
                                //resetChartView(ScreenUtil.dip2px(context,120f))
                            }
                            mHandler.sendEmptyMessageDelayed(2, 100)
                        }

                        override fun show() {
                            if (mBinding.videoView.isFullScreen) {
                                //resetChartView(dp2px(290f))
                            } else {
                                //resetChartView(970)
                            }
                        }
                    })
                    inputTextMsgDialog.show()
                }
            }
        })

        mBinding.videoView.setOnLikeListener(object : NormalVideoView.OnLikeClickListener {
            override fun onClick() {
                LogEventUtil
                    .setLogEvent("livestream_like_button_click", Bundle().apply {
                        putString("like_button", "like button")
                    })
                isGiveLike = !isGiveLike
                mBinding.mHeartLayout.addHeart(ContextCompat.getColor(this@HistoryPlayerActivity, R.color.ce35728))
                mPresenter.giveLikes(1, channelId)
            }
        })

        mBinding.ivFinish.setOnClickListener {
            mBinding.videoView.handleBack()
        }
        mBinding.livingTopHorizontal.ivFinish2.setOnClickListener {
            mBinding.videoView.handleBack()
        }
        mBinding.tvGetIt.setOnClickListener {
            mBinding.fullTips.visibility = View.GONE
            mBinding.tvGetIt.visibility = View.GONE
        }
        mBinding.fullTips.setOnClickListener {
            mBinding.fullTips.visibility = View.GONE
            mBinding.tvGetIt.visibility = View.GONE
            mBinding.videoView.startFullScreen(true)
        }
        mBinding.videoView.start()
    }

    fun getData() {
        bundle = intent.extras
        channelId = bundle?.getLong("channelId") ?: 0
        roomId = bundle?.getLong("roomId") ?: 0
        roomArn = bundle?.getString("roomArn") ?: ""
        messageNode = bundle?.getString("messageNode") ?: ""
        playbackUrl = bundle?.getString("playbackUrl") ?: ""
        virtualCount = bundle?.getLong("virtualCount") ?: 0
        virtualLikeCount = bundle?.getLong("virtualLikeCount") ?: 0
        liveStatus = bundle?.getInt("liveStatus") ?: 0
        channel = bundle?.getString("channel") ?: ""
        originWidth = bundle?.getDouble("width") ?: 0.0
        originHeight = bundle?.getDouble("height") ?: 0.0
        shareContent = bundle?.getString("shareContent") ?: ""
        LogEventUtil.setLogEvent("livestream_page_view", Bundle().apply {
            putString("page_name", channel)
        })
        userId = UserDataUtil.userId()
    }

    override fun initData() {
        super.initData()
        mPresenter.getChatContent(roomId)
    }

    override fun initListener() {
        super.initListener()
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                mBinding.videoView.handleBack()
            }
        })
    }

    override fun initParam() {
        window.clearFlags(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (mBinding.videoView.isFullScreen) {
            mBinding.videoView.startFullScreen(false)
        }
    }

    override fun onDestroy() {
        mBinding.videoView.destroy()
        mHandler.removeCallbacksAndMessages(null)
        EventBus.getDefault().post(NoticeConstants.LIVE_ROOM_EXIT)
        socket?.close(1000, "123")
        super.onDestroy()
    }

    override fun onResume() {
        super.onResume()
        mPresenter.addAWSLive(userId, roomArn, channelId)
        mPresenter.queryLivePromo()
        mHandler.sendEmptyMessageDelayed(1, 500L)
    }

    override fun onPause() {
        super.onPause()
        mHandler.removeCallbacksAndMessages(null)
        mPresenter.exitLive(userId, channelId)
        mBinding.videoView.pause()
    }

    override fun getToken(token: ChartTokenBean) {
        userId = token.userId
        saveToken(token.chatToken, false)
    }

    private fun saveToken(token: String, isRefreshToken: Boolean) {
        chatToken = token
        socket?.close(1000, "123")
        startWebSocketDatafeed()
    }

    override fun getLiveInfo(liveInfoData: LiveInfoBean?) {
        val virtualCount = liveInfoData?.obj?.virtualCount.ifNull()
        val virtualLikeCount = liveInfoData?.obj?.virtualLikeCount.ifNull().toString()
        val text = "$virtualCount ${getString(R.string.live_views)}"
        mBinding.tvWatchCount.text = text
        mBinding.livingTopHorizontal.tvWatchCount2.text = text
        mBinding.videoView.tvLike.text = virtualLikeCount.ifNull()
    }

    override fun filterChatContentSucceed(content: String, chatId: String) {
        val messageData = MessageData(
            "", chatId, "SEND_MESSAGE", content,
            MessageAttributes("", "")
        )
        socket?.send(GsonUtil.buildGson().toJson(messageData))
    }

    override fun filterForbiddenChat(content: String) {
        ToastUtil.showToast(content)
    }

    override fun giveLikesSucceed(count: Long) {
        mBinding.videoView.ivLike.setImageResource(R.drawable.img_live_like_click)
        mBinding.videoView.tvLike.text = count.ifNull(0).toString()
    }

    override fun getChartSucceed() {
        if (mPresenter.messageData.size > 0) {
            mBinding.rvMessage.visibility = View.VISIBLE
            messageHistoryAdapter?.notifyDataSetChanged()
            mBinding.rvMessage.post { mBinding.rvMessage.scrollToPosition(messageHistoryAdapter?.itemCount.ifNull(1) - 1) }
        } else {
            mBinding.rvMessage.visibility = View.GONE
        }
    }

    override fun getChartTokenSucceed(token: String) {
        saveToken(token, true)
    }

    override fun queryLivePromoSucceed(promoData: ArrayList<PromoEventData>) {

    }

    override fun skipOpenAccountActivity(
        linkSkipState: EnumLinkSkipState,
        objData: MT4AccountTypeObj?
    ) {
        // 更新数据库状态，以前逻辑有问题，暂时补 bug
        UserDataUtil.setOpenLiveAccountState(if (objData?.applyTpe == 2) "1" else "0")

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_OPEN_ACCOUNT) {
            if (objData?.status == 2) {
                ToastUtil.showToast(context.getString(R.string.you_have_an_existing_processed))
                return
            }
            //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑
            if (objData?.applyTpe == 2 && objData.regulator == "1") {
                openActivity(AccountManagerActivity::class.java)
//                mPresenter.getWBPStatus()
                return
            }
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_DEPOSIT) {
            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        if (linkSkipState == EnumLinkSkipState.NEWCOMER_EVENT_REFER) {
            if (objData?.status == 2) {
                ToastUtil.showToast(context.getString(R.string.you_have_an_existing_processed))
                return
            }

            if (objData?.status == 5) {
                openActivity(HtmlActivity::class.java, Bundle().apply {
                    putString("url", mPresenter.wbpDataBean?.refer?.activityUrl ?: "")
                    putString("title", mPresenter.wbpDataBean?.refer?.activityName ?: "")
                    putInt("tradeType", 3)
                })
                return
            }

            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        // 默认正常跳转，直接走公共跳转
        if (linkSkipState == EnumLinkSkipState.DEFAULT) {

            //当regulator == "1"为asic监管，走旧版逻辑，其他情况走新版开户逻辑
            if (objData?.applyTpe == 2 && objData.regulator == "1") {
                openActivity(AccountManagerActivity::class.java)
                return
            }

            VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
            return
        }

        val objStatus = objData?.status

        // 成功开户 模拟/返佣 进入金
        if (objStatus == 5) {
            if (linkSkipState == EnumLinkSkipState.GOLDEN) {
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
            }
            return
        }

        if (objStatus == 3) {
            VAUStartUtil.dispatchOpenAccount(this)
            return
        }

        /*
         开户审核中,只打开app
         1：账户未提交 (跳步数)
         2：账户审核，
         3：账户被挂起 {跳第一步}
         4：账户被拒绝(被拒绝，不存在这种情况，被拒绝时被弹出登陆，不能登陆)
         5：账户已通过
         6：账户待审核且身份证明未提交(跳身份证明)  数据不对，也是跳步数
         */
        if (objStatus == 2) {
            ToastUtil.showToast(context.getString(R.string.you_have_an_existing_processed))
            return
        }

        // 跳开户第几步
        VAUStartUtil.openAccountGuide(this, objData ?: MT4AccountTypeObj())
    }

    private fun startWebSocketDatafeed() {
        try {
            val client = OkHttpClient()
            val request = chatToken?.let {
                Request.Builder()
                    .url(messageNode)
                    .header("Sec-WebSocket-Protocol", it)
                    .header("Sec-WebSocket-Version", "13")
                    .build()
            }

            request?.let {
                socket = client.newWebSocket(it, object : WebSocketListener() {
                    override fun onOpen(webSocket: WebSocket, response: Response) {
                        super.onOpen(webSocket, response)
                    }

                    override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                        super.onClosing(webSocket, code, reason)
                        webSocket.close(code, reason)
                    }

                    override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                        super.onClosed(webSocket, code, reason)
                        webSocket.close(code, reason)
                    }

                    override fun onFailure(
                        webSocket: WebSocket,
                        t: Throwable,
                        response: Response?
                    ) {
                        super.onFailure(webSocket, t, response)
                        if (mPresenter != null) {
                            mPresenter.getChatToken(userId, roomArn)
                        }
                    }

                    @SuppressLint("SuspiciousIndentation")
                    override fun onMessage(webSocket: WebSocket, text: String) {
                        super.onMessage(webSocket, text)
                        val mMessage = GsonUtil.buildGson().fromJson(text, AWSMessageData::class.java)
                        if (mMessage.EventName?.contains("DELETE_MESSAGE", true) == true) {
                            run outside@{
                                mPresenter.messageData.forEach { bean ->
                                    if (bean.id == mMessage.RequestId) {
                                        mPresenter.messageData.remove(bean)
                                        mHandler.sendEmptyMessage(2)
                                        return@outside
                                    }
                                }
                            }
                        } else {
                            if (mMessage.Sender.Attributes.userName.isNotEmpty() && mMessage.Content.isNotEmpty()) {
                                val chartContent = ChartMessage(
                                    Constants.PRODUCT_NAME,
                                    0,
                                    mMessage.Content,
                                    0,
                                    mMessage.RequestId,
                                    mMessage.Sender.Attributes.userName,
                                    0,
                                    mMessage?.Attributes?.replyUserName ?: "",
                                )
                                mPresenter.messageData.add(chartContent)
                                mHandler.sendEmptyMessageDelayed(2, 100)
                            }
                            // 这里之前有else 弹出 “您的操作过于频繁，请稍后重试” 的提示，经排查iOS没有这个逻辑，就去掉这里的提示 并去掉多语言文案
                        }
                    }
                }) as RealWebSocket
            }
        } catch (e: Exception) {
            mPresenter.getChatToken(userId, roomArn)
        }
    }
}