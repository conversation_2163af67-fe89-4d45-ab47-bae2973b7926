package cn.com.vau.signals.stsignal.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.account.MT4AccountTypeObj
import cn.com.vau.data.strategy.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * author：lvy
 * date：2024/03/27
 * desc：策略详情
 */
class StStrategyDetailsViewModel : BaseViewModel() {

    // 策略详情页面
    val strategyDetailTopLiveData = MutableLiveData<StrategyBean?>() // 策略详情顶部卡片
    val accountTypeLiveData = MutableLiveData<MT4AccountTypeObj?>() // 获取申请开通mt4账户号类型
    val strategyFansCountLiveData = MutableLiveData<StStrategyFansCountBean>() // 策略粉丝数量
    val followStrategyStatusLiveData = MutableLiveData<Boolean>() // 点击收藏策略的状态

    /**
     * 策略详情顶部卡片
     */
    fun stStrategyDetailTopApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        if (UserDataUtil.isStLogin()) {
            map["accountId"] = UserDataUtil.stAccountId()
            map["stUserId"] = UserDataUtil.stUserId()
        }
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.strategyDetailTopApi(requestBody) }, {
            if (it.isSuccess()) {
                strategyDetailTopLiveData.value = it.data
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        })
    }

    /**
     * 获取申请开通mt4账户号类型
     */
    fun queryStAccountTypeApi() {
        requestNet({ baseService.crmGetMt4AccountApplyTypeApi(UserDataUtil.loginToken()) }, {
            if (it.isSuccess()) {
                val obj = it.data?.obj
                /**applyTpe: 申请类型(0:不能申请, 1:真实账户开通，
                 * 2:同名账户，3:重新申请，
                 * 4：身份证明或地址证明未通过，5：只读账户身份证明未通过，
                 * 6：账户被拒绝，7：未上传身份证明)
                 * 若applyTpe返回2表示Live帳號已完全開通完畢 則此時表示開通跟單帳戶可直接跳一頁式的頁面來開通
                 * 否則遵照原本開通LIVE的判斷來決定下一步頁面*/
                accountTypeLiveData.postValue(obj)
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = true)
    }

    /**
     * 每次点击红心本地都要做刷新状态操作
     */
    fun initStrategyFollow(strategyId: String?) {
        val fansData = strategyFansCountLiveData.value ?: return
        if (fansData.watched == true) {
            stRemoveFollowApi(strategyId)

            fansData.totalCount = fansData.totalCount.mathSub("1")
            fansData.watched = false
            strategyFansCountLiveData.value = fansData
            EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.NOTIFY_STRATEGY_COLLECT_COUNT, "-1"))
        } else {
            stAddFollowApi(strategyId)

            fansData.totalCount = fansData.totalCount.mathAdd("1")
            fansData.watched = true
            strategyFansCountLiveData.value = fansData
            EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.NOTIFY_STRATEGY_COLLECT_COUNT, "1"))
        }
    }

    /**
     * 收藏策略
     */
    private fun stAddFollowApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = UserDataUtil.stUserId()
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.watchedRelationSaveApi(requestBody) }, {
            if (it.isSuccess()) {
                followStrategyStatusLiveData.value = true
            }
        })
    }

    /**
     * 取消收藏策略
     */
    private fun stRemoveFollowApi(strategyId: String?) {
        val map = hashMapOf<String, Any?>()
        map["stUserId"] = UserDataUtil.stUserId()
        map["strategyId"] = strategyId

        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ stTradingService.watchedRelationRemoveApi(requestBody) }, {
            if (it.isSuccess()) {
                followStrategyStatusLiveData.value = false
            }
        })
    }

    /**
     * 策略粉丝数量
     */
    fun stStrategyFansCountApi(strategyId: String?) {
        val stUserId = UserDataUtil.stUserId()
        requestNet({ stTradingService.strategyFansCountApi(stUserId, strategyId) }, {
            if (it.isSuccess()) {
                strategyFansCountLiveData.value = it.data ?: StStrategyFansCountBean("0", false)
            } else {
                strategyFansCountLiveData.value = StStrategyFansCountBean("0", false)
            }
        })
    }

    /**
     * 神策自定义埋点(v3500)
     */
    fun sensorsTrack(mBean: StrategyBean?, isClickBtnTrack: Boolean = false, buttonName: String? = null) {
        if (mBean == null) return
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.STRATEGY_TITLE, mBean.strategyName.ifNull()) // 策略标题
        properties.put(SensorsConstant.Key.PROVIDER_NAME, mBean.nickname.ifNull()) // 信号源名称
        properties.put(SensorsConstant.Key.PROVIDER_ID, mBean.summaryData?.stUserId.ifNull()) // 信号源id
        properties.put(SensorsConstant.Key.RETURN_RATIO, mBean.summaryData?.returnRate.ifNull()) // 收益率
        properties.put(SensorsConstant.Key.RETURN_TYPE, "3M") // 收益类型
        properties.put(SensorsConstant.Key.COPIERS_NUMBER, mBean.summaryData?.copiers.ifNull()) // 跟单者数量
        properties.put(SensorsConstant.Key.RISK_LEVEL, mBean.summaryData?.riskLevel.ifNull()) // 风险度
        properties.put(SensorsConstant.Key.PROFIT_SHARE, mBean.summaryData?.profitShareRatio.ifNull()) // 分润比例
        properties.put(SensorsConstant.Key.IS_SHIELDED_STRATEGY, if (mBean.isProfitShieldStrategy == true) "1" else "0") // 参加活动的策略标识
        if (isClickBtnTrack) {
            properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
            // 策略详情页点击 -> 策略详情页按钮点击时触发
            SensorsDataUtil.track(SensorsConstant.V3500.STRATEGIES_DETAIL_PAGE_CLICK, properties)
        } else {
            // 策略详情页浏览 -> 策略详情页加载完成时触发
            SensorsDataUtil.track(SensorsConstant.V3500.STRATEGIES_DETAIL_PAGE_VIEW, properties)
        }
    }

    /**
     * tab点击事件埋点
     */
    fun sensorsTrackTabClick(mBean: StrategyBean?, tabName: String) {
        if (mBean == null) return
        SensorsDataUtil.track(SensorsConstant.V3710.COPY_TRADING_STRATEGY_TAB_CLICK, JSONObject().apply {
            put(SensorsConstant.Key.TAB_NAME, tabName)
            put(SensorsConstant.Key.TARGET_NAME, mBean.strategyName.ifNull()) // 策略名称
            put(SensorsConstant.Key.STRATEGY_ID, mBean.summaryData?.strategyId.ifNull()) // 策略id
            put(SensorsConstant.Key.COLUMN_NAME, "NA") // 栏目名称
        })
    }
}