package cn.com.vau.signals.stsignal.fragment

import android.os.Bundle
import androidx.core.os.bundleOf
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.enums.EnumStrategyFollowState
import cn.com.vau.data.strategy.SearchStrategyBean
import cn.com.vau.databinding.FragmentRefreshBinding
import cn.com.vau.databinding.HeaderRecyclerSearchSymbolsBinding
import cn.com.vau.signals.stsignal.activity.StSignalDetailsActivity
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.signals.stsignal.adapter.*
import cn.com.vau.signals.stsignal.viewmodel.StSignalSearchViewModel
import cn.com.vau.trade.st.StrategyOrderBaseData
import cn.com.vau.trade.st.activity.StStrategyOrdersActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.NoDataView
import com.chad.library.adapter.base.BaseQuickAdapter
import kotlinx.coroutines.launch

/**
 * Filename: StSearchTopFragment.kt
 * Author: GG
 * Date: 2024/3/28
 * Description:
 */
class StSearchItemFragment : BaseMvvmBindingFragment<FragmentRefreshBinding>() {

    private val mViewModel by activityViewModels<StSignalSearchViewModel>()

    private val type: String? by lazy { arguments?.getString(KEY_TYPE) }
    private var tabType: String? = null

    private val symbolsAdapter by lazy {
        StSearchSymbolAdapter().apply {
            addHeaderView(HeaderRecyclerSearchSymbolsBinding.inflate(layoutInflater).root)
            setEmptyView(createEmptyView())
        }
    }
    private val topStrategiesAdapter by lazy { StSearchTopSignalAdapter(true).apply { setEmptyView(createEmptyView()) } }
    private val topSignalAdapter by lazy { StSearchTopSignalAdapter(false).apply { setEmptyView(createEmptyView()) } }
    private val searchStrategiesAdapter by lazy { StSearchSignalAdapter(isSignal = false, isShowHighStr = true).apply { setEmptyView(createEmptyView()) } }
    private val searchSignalAdapter by lazy { StSearchSignalAdapter(isSignal = true, isShowHighStr = true).apply { setEmptyView(createEmptyView()) } }
    private val searchSymbolsAdapter by lazy { StSearchSignalAdapter(isSignal = false, isShowHighStr = false).apply { setEmptyView(createEmptyView()) } }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        tabType = arguments?.getString(KEY_TAB_TYPE)
    }

    override fun initView() {
        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(requireContext())
        mBinding.root.setEnableRefresh(false)
        if (type == StSearchFragment.TYPE_TOP) {
            mBinding.root.setEnableLoadMore(false)
        }
    }

    override fun initListener() {
        super.initListener()
        symbolsAdapter.setNbOnItemClickListener { _, _, position ->
            mViewModel.searchText = symbolsAdapter.data.getOrNull(position)?.symbol
            mViewModel.searchStrategiesBySymbols(true)
        }
        topStrategiesAdapter.setNbOnItemClickListener { _, _, position ->
            //跳转策略详情
            topStrategiesAdapter.data.getOrNull(position)?.let {
                jumpStrategies(it.clickedStrategyId)
            }
        }
        topSignalAdapter.setNbOnItemClickListener { _, _, position ->
            //跳转信号源详情
            topSignalAdapter.data.getOrNull(position)?.let {
                jumpSignal(it.clickedUserId)
                val bundle = Bundle()
                bundle.putString("Signal_provider_ID", it.clickedUserId)
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SEARCH_SP_BTN_CLICK, bundle)
            }
        }

        //点击item 跳转
        searchStrategiesAdapter.setNbOnItemClickListener { _, _, position ->
            //跳转策略详情
            searchStrategiesAdapter.data.getOrNull(position)?.let {
                jumpStrategies(it.signalId)
                val bundle = Bundle()
                bundle.putString("Search_value", mViewModel.inputSearchLiveData.value?.toString())
                bundle.putString("Result_category", "Strategy")
                bundle.putString("Result", it.signalId)
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SEARCH_RESULT_BTN_CLICK, bundle)
            }
        }
        searchSignalAdapter.setNbOnItemClickListener { _, _, position ->
            //跳转策略详情
            searchSignalAdapter.data.getOrNull(position)?.let {
                jumpSignal(it.stUserId)
                val bundle = Bundle()
                bundle.putString("Search_value", mViewModel.inputSearchLiveData.value?.toString())
                bundle.putString("Result_category", "Signal_provider")
                bundle.putString("Result", it.stUserId)
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SEARCH_RESULT_BTN_CLICK, bundle)
            }
        }
        searchSymbolsAdapter.setNbOnItemClickListener { _, _, position ->
            //跳转策略详情
            searchSymbolsAdapter.data.getOrNull(position)?.let {
                jumpStrategies(it.signalId)
                val bundle = Bundle()
                bundle.putString("Search_value", mViewModel.searchText)
                bundle.putString("Result_category", "Symbol")
                bundle.putString("Result", it.signalId)
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SEARCH_RESULT_BTN_CLICK, bundle)

            }

        }

        //点击button 跳转
        searchStrategiesAdapter.setNbOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.tvButton -> {
                    searchStrategiesAdapter.data.getOrNull(position)?.let {
                        jumpStrategies(it)
                    }
                }
            }
        }
        searchSignalAdapter.setNbOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.tvButton -> {
                    searchSignalAdapter.data.getOrNull(position)?.let {
                        jumpSignal(it.stUserId)
                    }
                }
            }
        }
        searchSymbolsAdapter.setNbOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.tvButton -> {
                    searchSymbolsAdapter.data.getOrNull(position)?.let {
                        jumpStrategies(it)
                    }
                }
            }
        }
        mBinding.root.setOnLoadMoreListener {
            if (type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.strategies)) {
                mViewModel.searchStrategiesPage++
            }
            if (type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.signal_providers)) {
                mViewModel.searchSignalPage++
            }
            if (type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.symbols)) {
                mViewModel.searchStrategiesBySymbolsPage++
            }
            request()
        }
        if (type == StSearchFragment.TYPE_TOP && tabType == getString(R.string.strategies)) {
            LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_COMMUNITY_SEARCH_PAGE_VIEW)
        }
    }

    /**
     * 跳转策略详情
     */
    private fun jumpStrategies(data: SearchStrategyBean?) {
        if (data?.pendingApplyApproval == true) {
            openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                putSerializable("data_strategy", StrategyOrderBaseData().apply {
                    this.type = EnumStrategyFollowState.PENDING_REVIEW
                    this.signalStrategyId = data.signalId
                    this.portfolioId = data.followPortFolioId
                    this.followRequestId = data.followRequestId
                })
            })
        } else {
            if (data?.isFollowed == true) {
                openActivity(StStrategyOrdersActivity::class.java, Bundle().apply {
                    putSerializable("data_strategy", StrategyOrderBaseData().apply {
                        this.type = EnumStrategyFollowState.OPEN
                        this.signalStrategyId = data.signalId
                        this.portfolioId = data.followPortFolioId
                        this.followRequestId = data.followRequestId
                    })
                })
            } else {
                StStrategyDetailsActivity.open(requireContext(), data?.signalId)
                logEvent(data?.signalId)
            }
        }
    }

    /**
     * 跳转策略详情
     */
    private fun jumpStrategies(strategyId: String?) {
        StStrategyDetailsActivity.open(requireContext(), strategyId)
        logEvent(strategyId)
    }

    /**
     * 跳转信号源详情
     */
    private fun jumpSignal(stUserId: String?) {
        StSignalDetailsActivity.open(requireContext(), stUserId)
        LogEventUtil.setLogEvent(
            BuryPointConstant.V348.CT_SP_PAGE_VIEW, bundleOf(
                "Type_of_account" to "Copy Trading",
                "Position" to "Top_searches",
                "Signal_provider_ID" to stUserId.ifNull()
            )
        )
    }

    private fun request(isCheckSearch: Boolean = false, reSearch: Boolean = false) {
        when {
            //置顶排行 策略
            type == StSearchFragment.TYPE_TOP && tabType == getString(R.string.strategies) -> {
                mViewModel.topStrategies()
            }
            //置顶排行 信号源
            type == StSearchFragment.TYPE_TOP && tabType == getString(R.string.signal_providers) -> {
                mViewModel.topSignal()
            }
            //搜索 策略
            type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.strategies) -> {
                if (reSearch) {
                    mViewModel.searchStrategiesPage = 1
                    mViewModel.searchStrategies(true)
                } else {
                    if (isCheckSearch) {
                        if (mViewModel.inputSearchLiveData.value.toString() != searchStrategiesAdapter.searchKey) {
                            mViewModel.searchStrategiesPage = 1
                            mViewModel.searchStrategies(true)
                        }
                    } else {
                        mViewModel.searchStrategies(false)
                    }
                }
            }
            //搜索 信号源
            type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.signal_providers) -> {
                if (reSearch) {
                    mViewModel.searchSignalPage = 1
                    mViewModel.searchSignal(true)
                } else {
                    if (isCheckSearch) {
                        if (mViewModel.inputSearchLiveData.value.toString() != searchSignalAdapter.searchKey) {
                            mViewModel.searchSignalPage = 1
                            mViewModel.searchSignal(true)
                        }
                    } else {
                        mViewModel.searchSignal(false)
                    }
                }
            }
            //搜索 产品
            type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.symbols) -> {
                if (reSearch) {
                    mViewModel.searchStrategiesBySymbolsPage = 1
                    filterSymbols(mViewModel.inputSearchLiveData.value.toString())
                } else {
                    if (isCheckSearch) {
                        if (mViewModel.inputSearchLiveData.value.toString() != symbolsAdapter.searchKey) {
                            mViewModel.searchStrategiesBySymbolsPage = 1
                            filterSymbols(mViewModel.inputSearchLiveData.value.toString())
                        }
                    } else {
                        if (mBinding.mRecyclerView.adapter == symbolsAdapter) {
                            filterSymbols(mViewModel.inputSearchLiveData.value.toString())
                        } else {
                            mViewModel.searchStrategiesBySymbols(false)
                        }
                    }
                }
            }
        }
        KeyboardUtil.hideSoftInput(requireActivity())
    }

    override fun initData() {
        super.initData()
        if (type == StSearchFragment.TYPE_TOP) request()
        when {
            //置顶排行 策略
            type == StSearchFragment.TYPE_TOP && tabType == getString(R.string.strategies) -> {
                mViewModel.topStrategiesLiveData.observeUIState(viewLifecycleOwner, topStrategiesAdapter)
                changeAdapter(topStrategiesAdapter)
            }
            //置顶排行 信号源
            type == StSearchFragment.TYPE_TOP && tabType == getString(R.string.signal_providers) -> {
                mViewModel.topSignalLiveData.observeUIState(viewLifecycleOwner, topSignalAdapter)
                changeAdapter(topSignalAdapter)
            }
            //搜索 策略
            type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.strategies) -> {
                mViewModel.searchStrategiesLiveData.observeUIState(viewLifecycleOwner, searchStrategiesAdapter, mBinding.root, before = {
                    searchStrategiesAdapter.searchKey = mViewModel.inputSearchLiveData.value.toString()
                })
                changeAdapter(searchStrategiesAdapter)
            }
            //搜索 信号源
            type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.signal_providers) -> {
                mViewModel.searchSignalLiveData.observeUIState(viewLifecycleOwner, searchSignalAdapter, mBinding.root, before = {
                    searchSignalAdapter.searchKey = mViewModel.inputSearchLiveData.value.toString()
                })
                changeAdapter(searchSignalAdapter)
            }
            //搜索 产品
            type == StSearchFragment.TYPE_SEARCH && tabType == getString(R.string.symbols) -> {
                mViewModel.searchStrategiesBySymbolsLiveData.observeUIState(viewLifecycleOwner, searchSymbolsAdapter, mBinding.root, before = {
                    changeAdapter(searchSymbolsAdapter)
                })
                filterSymbols(mViewModel.inputSearchLiveData.value.toString())
            }
        }

        lifecycleScope.launch {
            mViewModel.clickEventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collect {
                if (type == StSearchFragment.TYPE_SEARCH) {
                    request(reSearch = true)
                }
            }
        }
    }

    private fun changeAdapter(adapter: BaseQuickAdapter<*, *>) {
        if (mBinding.mRecyclerView.adapter != adapter) {
            mBinding.mRecyclerView.adapter = adapter
        }
        mBinding.root.setEnableLoadMore(!(mBinding.mRecyclerView.adapter == symbolsAdapter || type == StSearchFragment.TYPE_TOP))
    }

    private fun logEvent(strategyId: String?) {
        LogEventUtil.setLogEvent(
            BuryPointConstant.V348.CT_STRATEGY_PAGE_VIEW, bundleOf(
                "Type_of_account" to "Copy Trading",
                "Position" to "Top_searches",
                "Strategy_ID" to strategyId.ifNull()
            )
        )
    }

    override fun onResume() {
        super.onResume()
        if (type == StSearchFragment.TYPE_SEARCH) {
            request(isCheckSearch = true)
        }
    }

    /**
     * 构造空布局样式
     */
    private fun createEmptyView() = NoDataView(requireContext()).apply {
        setHintMessage(getString(R.string.no_records_found))
    }

    /**
     * 根据关键字本地筛选产品
     */
    private fun filterSymbols(key: String) {
        symbolsAdapter.setNewInstance(VAUSdkUtil.symbolList().filter { data -> data.isMatch(key) }.toMutableList())
        symbolsAdapter.searchKey = key
        changeAdapter(symbolsAdapter)
    }

    companion object {

        private const val KEY_TYPE = "type"
        private const val KEY_TAB_TYPE = "tab_type"

        fun newInstance(type: String, tabType: String): StSearchItemFragment {
            val args = Bundle()
            args.putString(KEY_TYPE, type)
            args.putString(KEY_TAB_TYPE, tabType)
            val fragment = StSearchItemFragment()
            fragment.arguments = args
            return fragment
        }
    }
}