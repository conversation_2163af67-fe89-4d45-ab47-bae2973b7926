package cn.com.vau.signals.stsignal.viewmodel

import android.text.TextUtils
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.PersonalInfoObj
import cn.com.vau.data.profile.AuthConfigObjBean
import cn.com.vau.profile.viewmodel.ProfileViewModel.Companion.TAG_AUDIT_STATUS
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import java.io.InputStream

/**
 * author：lvy
 * date：2024/6/3
 * desc：
 */
class PersonalDetailsViewModel : BaseViewModel() {
    var verifiedStatus: String? = null
    var signContent: String? = null

    private val _profileObjLiveData: MutableLiveData<PersonalInfoObj> by lazy { MutableLiveData(null) }
    val profileObjLiveData: LiveData<PersonalInfoObj> get() = _profileObjLiveData
    private val _getAuthConfigSuccessLiveData: MutableLiveData<Pair<String, AuthConfigObjBean?>> by lazy { MutableLiveData(null) }
    val getAuthConfigSuccessLiveData get() = _getAuthConfigSuccessLiveData // 获取安全中心配置信息成功

    /**
     * 是否是kyc验证的账户
     */
    fun isKycAccount() = SpManager.isV1V2()

    /**
     * crm id 用于显示 uid
     */
    fun getUid() = SpManager.getCrmUserId()

    /**
     * 用户信息
     */
    fun getSignalInfoApi() {
        val accountId =
            if (!UserDataUtil.isStLogin()) {
                ""
            } else {
                UserDataUtil.stAccountId()
            }

        requestNet({
            stTradingService.signalGetApi(accountId, UserDataUtil.stAccountId())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_ST_USER_INFO, it.data))
        }, onError = {
            sendEvent(DataEvent(EVENT_SHOW_RECONNECT))
        })
    }

    /**
     * 修改头像（直接上传流）
     */
    fun updateAvatarApi(inputStream: InputStream) {
        requestNet({
            val byteArray = inputStream.readBytes()

            val requestBody = byteArray.toRequestBody("multipart/form-data".toMediaTypeOrNull(), 0, byteArray.size)
            val part = MultipartBody.Part.createFormData("pic", "Avatar${System.currentTimeMillis()}.jpg", requestBody)

            val params = HashMap<String, Any?>()
            params["token"] = UserDataUtil.loginToken()
            params["userNick"] = UserDataUtil.nickname()
            baseService.userformUpdateApi(part, params)
        }, {
            ToastUtil.showToast(it.getResponseMsg())
            if (!it.isSuccess()) {
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_UPDATE_AVATAR, it.data?.obj?.pic))
        }, isShowDialog = true)
    }

    /**
     * 查询个人信息
     */
    fun userformGetUserForumApi() {
        requestNet({
            baseService.userformGetUserForumApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            signContent = it.data?.obj?.signcontent
            setAreaData(it.data?.obj)
            _profileObjLiveData.value = it.data?.obj
        }, onError = {
            sendEvent(DataEvent(EVENT_SHOW_RETRY))
        })
    }

    /**
     * 设置用户国家区号等缓存数据
     */
    private fun setAreaData(bean: PersonalInfoObj?) {
        // 如果是邮箱注册登录，没有手机号，会把之前设置的缓存覆盖为空，所以判断不为空才覆盖
        if (!bean?.email.isNullOrBlank()) {
            UserDataUtil.setEmail(bean.email)
        }
        if (!bean?.phoneStr.isNullOrBlank()) {
            UserDataUtil.setUserTel(bean.phoneStr)
            SpManager.putUserTel(bean.phoneStr.ifNull())
        }
        if (!bean?.phoneCode.isNullOrBlank()) {
            UserDataUtil.setAreaCode(bean.phoneCode)
            SpManager.putCountryNum(bean.phoneCode.ifNull())
        }
        if (!bean?.phoneCountryCode.isNullOrBlank()) {
            UserDataUtil.setCountryCode(bean.phoneCountryCode)
            SpManager.putCountryCode(bean.phoneCountryCode.ifNull())
        }
    }

    /**
     * 更新个人信息/不包含图片上传
     */
    fun userformUpdateApi(userNick: String? = null, signContent: String? = null) {
        requestNet({
            val params = HashMap<String, Any?>()
            params["token"] = UserDataUtil.loginToken()
            if (!TextUtils.isEmpty(userNick)) {
                params["userNick"] = userNick
            }
            params["signcontent"] = signContent ?: ""

            baseService.userformUpdateApi(params)
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }

            if (!userNick.isNullOrBlank()) {
                sendEvent(DataEvent(EVENT_UPDATE_NICKNAME, userNick))
            }
            this.signContent = signContent
            sendEvent(DataEvent(EVENT_UPDATE_SIGN_CONTENT, signContent))
        })
    }

    /**
     * telegram-绑定
     */
    fun telegramBindingApi() {
        requestNet({
            val map = hashMapOf<String, Any?>()
            map["token"] = UserDataUtil.loginToken()
            SpManager.getTelegramH5Data()?.let { bean ->
                map["telegramId"] = bean.id.ifNull()
                map["first_name"] = bean.first_name.ifNull()
                map["last_name"] = bean.last_name.ifNull()
                map["auth_date"] = bean.auth_date.ifNull()
                map["hash"] = bean.hash.ifNull()
                map["username"] = bean.username.ifNull()
                map["photo_url"] = bean.photo_url.ifNull()
            }
            baseService.telegramBindingApi(map)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_SHOW_TELEGRAM_BINDING))
        })
    }

    /**
     * telegram-解绑
     */
    fun telegramUnbindingApi() {
        requestNet({
            baseService.telegramUnbindingApi(UserDataUtil.loginToken())
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_SHOW_TELEGRAM_UNBINDING))
        })
    }

    /**
     * telegram-获取botId
     */
    fun telegramGetBotIdApi() {
        requestNet({
            baseService.telegramGetBotIdApi()
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_TELEGRAM_BOT_ID, it.data?.obj))
        })
    }

    /**
     * kyc-获取安全中心配置
     *
     * @param functionCode modify-email = 修改邮箱；enable-auth-2fa = 启用2FA；modify-auth-2fa = 修改2FA；modify-password = 修改密码；
     *                     modify-phone = 修改手机号；add-fund-code = 添加资金安全码；modify-fund-code = 修改资金安全码；
     *                     reset-fund-code = 忘记资金安全码
     */
    fun getAuthConfigApi(functionCode: String) {
        val map = hashMapOf<String, Any?>()
        map["functionCode"] = functionCode
        requestNet({ baseService.getAuthConfigApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            _getAuthConfigSuccessLiveData.value = Pair(functionCode, it.data?.obj)
        }, isShowDialog = true)
    }

    /**
     * 神策自定义埋点(v3700)
     */
    fun sensorsTrack(buttonName: String?, bindType: Int?) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        properties.put(
            SensorsConstant.Key.FUNCTION_NAME,
            when (bindType) {
                0 -> "Bind"
                1 -> "Verify"
                else -> "Edit"
            }
        ) // 方法名
        properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, "ProfilePage")
        SensorsDataUtil.track(SensorsConstant.V3700.OTP_VERIFY_CLICK, properties)
    }

    /**
     * 获取审核状态
     */
    fun getAuditStatus() {
        requestNet({
            baseService.getAuditStatusApi(token = UserDataUtil.loginToken())
        }, onSuccess = {
            if (!it.isSuccess()) {
                return@requestNet
            }
            sendEvent(DataEvent(tag = TAG_AUDIT_STATUS, data = it.data?.obj))
        }, onError = {
            sendEvent(DataEvent(tag = TAG_AUDIT_STATUS))
        })
    }

    companion object {
        const val EVENT_UPDATE_AVATAR = "EVENT_UPDATE_AVATAR"
        const val EVENT_UPDATE_NICKNAME = "EVENT_UPDATE_NICKNAME"
        const val EVENT_UPDATE_SIGN_CONTENT = "EVENT_UPDATE_SIGN_CONTENT"
        const val EVENT_ST_USER_INFO = "EVENT_ST_USER_INFO"
        const val EVENT_SHOW_RETRY = "EVENT_SHOW_RETRY"
        const val EVENT_SHOW_RECONNECT = "EVENT_SHOW_RECONNECT"
        const val EVENT_TELEGRAM_BOT_ID = "EVENT_TELEGRAM_BOT_ID"
        const val EVENT_SHOW_TELEGRAM_UNBINDING = "EVENT_SHOW_TELEGRAM_UNBINDING"
        const val EVENT_SHOW_TELEGRAM_BINDING = "EVENT_SHOW_TELEGRAM_BINDING"
    }
}