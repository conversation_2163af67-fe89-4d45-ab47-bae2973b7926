package cn.com.vau.signals.adapter

import cn.com.vau.R
import cn.com.vau.data.discover.FxStreetBaseData
import cn.com.vau.util.TimeUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: FxStreetAdapter
 * Author: GG
 * Date: 2024/9/20
 * Description:
 */
class FxStreetAdapter : BaseQuickAdapter<FxStreetBaseData, BaseViewHolder>(R.layout.item_recycler_fx_street) {

    override fun convert(holder: BaseViewHolder, item: FxStreetBaseData) {
        holder.setText(R.id.tvName, item.newsTitle)
            .setText(R.id.tvDetail, item.summary)
            .setText(R.id.tvDate, item.pubTime?.let { TimeUtil.descriptiveData(it) })
    }

}