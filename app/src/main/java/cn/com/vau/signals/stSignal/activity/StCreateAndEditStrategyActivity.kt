package cn.com.vau.signals.stsignal.activity

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.content.res.AppCompatResources
import androidx.appcompat.widget.AppCompatEditText
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.popup.CenterAvatarSelectDialog
import cn.com.vau.common.view.popup.adapter.PlatAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.data.account.AccountBean
import cn.com.vau.data.strategy.SelectAllPicBean
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.databinding.ActivityStCreateAndEditStrategyBinding
import cn.com.vau.page.user.openSameNameAccount.OpenSameNameAccountActivity
import cn.com.vau.profile.adapter.SelectAccountAdapter
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.signals.stsignal.adapter.SelectAccountWithInfoAdapter
import cn.com.vau.signals.stsignal.viewmodel.StCreateAndEditStrategyViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.ImageUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.KycVerifyHelper
import cn.com.vau.util.PermissionUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.copyText
import cn.com.vau.util.createCameraRequestForUri
import cn.com.vau.util.createPhotoRequestForUri
import cn.com.vau.util.createTempImageUri
import cn.com.vau.util.ifNull
import cn.com.vau.util.launchPhoto
import cn.com.vau.util.setRangeAndDecimalPlaces
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.toIntCatching
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.dialog.BottomSelectListDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import cn.com.vau.util.widget.dialog.CenterAgreeRejectDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.chad.library.adapter.base.BaseQuickAdapter
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * 创建新策略 || 编辑策略
 * Author: GG
 * Date: 2024/3/30
 * Description: 文档: 3.10
 * https://suntontech.atlassian.net/wiki/spaces/PRD/pages/1914536007
 */
@SuppressLint("NotifyDataSetChanged")
class StCreateAndEditStrategyActivity : BaseMvvmActivity<ActivityStCreateAndEditStrategyBinding, StCreateAndEditStrategyViewModel>() {

    private val colorC731e1e1eC61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val colorCebffffffC1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    /**
     * 用于记录头像、源账户和付款账户的点击事件 用于网络请求完成前 用户就点击了对应按钮， 所以需要记录下来 等待网络请求完成后再执行
     */
    private var isAvatarClick = false
    private var isSourceClick = false
    private var isPayClick = false

    /**
     * 昵称点击跳转回调
     */
    private val nicknameResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Constants.REQUEST_CODE_NICKNAME) {
            mViewModel.strategy.strategyName = result.data?.getStringExtra(EditNicknameActivity.KEY_NICKNAME)
            updateUI()
        }
    }

    /**
     * 简介点击跳转回调
     */
    private val descriptionResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Constants.REQUEST_CODE_PERSONAL_INFO) {
            mViewModel.strategy.description = result.data?.getStringExtra(EditPersonalInfoActivity.KEY_DESCRIPTION)
            updateUI()
        }
    }

    /**
     * 选择头像弹框数据源
     */
    private val selectAvatarList by lazy {
        arrayListOf(
            getString(R.string.camera),
            getString(R.string.photo_library),
            getString(R.string.recommendation)
        )
    }

    private val pickImage = createPhotoRequestForUri { uri ->
        lifecycleScope.launch {
            ImageUtil.compressImageToStream(uri = uri)?.let { inputStream ->
                mViewModel.uploadAvatarApi(inputStream)
            }
        }
    }

    private val cameraUri by lazy { createTempImageUri() }

    private val pickCamera = createCameraRequestForUri { isSuccess ->
        if (isSuccess) {
            cameraUri?.let { uri ->
                lifecycleScope.launch {
                    ImageUtil.compressImageToStream(uri = uri)?.let { inputStream ->
                        mViewModel.uploadAvatarApi(inputStream)
                    }
                }
            }
        }
    }

    /**
     * 头像选择弹窗，用于app内置的头像选择
     */
    private val avatarSelectPopup by lazy {
        CenterAvatarSelectDialog.Builder(this).build()
    }

    /**
     * 源账户title 点击提示的adapter
     */
    private val sourceAccountPromptPopupAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(
                arrayListOf(
                    HintLocalData(getString(R.string.create_strategy_source_account_prompt))
                )
            )
        }
    }

    /**
     * 源账户列表 选择的adapter
     */
    private val sourceAccountPopupAdapter: SelectAccountWithInfoAdapter by lazy {
        SelectAccountWithInfoAdapter(isShowInfo = true).apply {
            setOnItemClickListener { _, _, position ->
                data.getOrNull(position)?.let { item ->
                    selectTitle = item.accountId
                    // 上传给后台的比例  因为后台要求传的是小数，所以除以100
                    mViewModel.strategy.sourceAccount = item.accountId
                    mViewModel.strategy.sourceAccountCurrency = item.currency
                    mViewModel.strategy.sourceAccountPlatform = item.platform
                    mViewModel.strategy.sourceAccountServerId = item.serverId
                    updateUI()
                    notifyDataSetChanged()
                    selectDialog.dismissDialog()
                }
            }
        }
    }

    /**
     * 交易账户列表 选择的adapter
     */
    private val paymentAccountPopupAdapter: SelectAccountWithInfoAdapter by lazy {
        SelectAccountWithInfoAdapter().apply {
            setOnItemClickListener { _, _, position ->
                data.getOrNull(position)?.let { item ->
                    selectTitle = item.accountId
                    // 上传给后台的比例  因为后台要求传的是小数，所以除以100
                    mViewModel.strategy.paymentAccount = item.accountId
                    mViewModel.strategy.paymentAccountCurrency = item.currency
                    mViewModel.strategy.paymentAccountPlatform = item.platform
                    mViewModel.strategy.paymentAccountServerId = item.serverId
                    updateUI()
                    notifyDataSetChanged()
                    selectDialog.dismissDialog()
                }
            }
        }
    }

    /**
     * 分润比例title 点击提示的adapter
     */
    private val profitSharingRatioPromptPopupAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(
                arrayListOf(
                    HintLocalData(getString(R.string.glossary_signal_provider_2))
                )
            )
        }
    }

    /**
     * 选择分润比例弹框数据源
     */
    private val profitSharingRatioAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>().apply {
            setList(
                arrayListOf(
                    SelectBean(title = "0%", id = "0.00"),
                    SelectBean(title = "5%", id = "0.05"),
                    SelectBean(title = "10%", id = "0.10"),
                    SelectBean(title = "15%", id = "0.15"),
                    SelectBean(title = "20%", id = "0.20"),
                    SelectBean(title = "25%", id = "0.25"),
                    SelectBean(title = "30%", id = "0.30"),
                    SelectBean(title = "35%", id = "0.35"),
                    SelectBean(title = "40%", id = "0.40"),
                    SelectBean(title = "45%", id = "0.45"),
                    SelectBean(title = "50%", id = "0.50")
                )
            )
            selectTitle = "0%"
            setOnItemClickListener { _, _, position ->
                val item = data.getOrNull(position)
                selectTitle = item?.getShowItemValue()
                mViewModel.strategy.profitShareRatio = item?.id.ifNull("0.00")
                updateUI()
                selectDialog.dismissDialog()
            }
        }
    }

    /**
     * 结算周期title 点击提示的adapter
     */
    private val settlementPromptPopupAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(
                arrayListOf(
                    HintLocalData(getString(R.string.the_profit_sharing_amount_settlement_cycle))
                )
            )
        }
    }

    /**
     * 结算周期 选择的adapter
     */
    private val settlementPopupAdapter: SelectAccountAdapter<SelectBean> by lazy {
        SelectAccountAdapter<SelectBean>().apply {
            setOnItemClickListener { _, _, position ->
                val item = data.getOrNull(position)
                selectTitle = item?.getShowItemValue()
                mViewModel.strategy.settlementFrequency = item?.id
                mViewModel.strategy.nextSettlementFrequency = item?.id
                updateUI()
                notifyDataSetChanged()
                selectDialog.dismissDialog()
            }
        }
    }

    private val selectDialog by lazy {
        BottomListDialog.Builder(this)
            .build()
    }

    /**
     * 是否自动审核title 点击提示的adapter
     */
    private val copierReviewPromptPopupAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(arrayListOf(HintLocalData(getString(R.string.create_strategy_copier_review_prompt))))
        }
    }

    /**
     * 自动审核弹框数据源
     */
    private val autoReviewAdapter by lazy {
        SelectAccountAdapter<SelectBean>().apply {
            setList(
                arrayListOf(
                    SelectBean(title = getString(R.string.auto_approve), id = "1"),
                    SelectBean(title = getString(R.string.auto_reject), id = "2")
                )
            )
            setOnItemClickListener { _, _, position ->
                val item = data.getOrNull(position)

                if (!SpManager.getStrategyAutoReject() && getString(R.string.auto_reject) == item?.getShowItemValue()) {
                    autoRejectPopup.showDialog()
                    return@setOnItemClickListener
                }
                selectTitle = item?.getShowItemValue()
                mViewModel.strategy.reviewType = item?.id
                updateUI()
                notifyDataSetChanged()
                selectDialog.dismissDialog()
            }
        }
    }

    /**
     * 自动审核选择自动拒绝的提示弹窗
     */
    private val autoRejectPopup by lazy {
        CenterAgreeRejectDialog.Builder(this)
            .setContent(getString(R.string.selecting_auto_reject_are_auto_reject))
            .setAgreeText(getString(R.string.don_t_show_this_again))
            .setSelectListener { dialog, isAgree ->
                SpManager.putStrategyAutoReject(isAgree)
                mViewModel.strategy.reviewType = "2"
                updateUI()
                dialog.dismissDialog()
                selectDialog.dismissDialog()
            }
            .build()
    }

    /**
     * 最小跟单设置title 点击提示的adapter
     */
    private val thresholdPromptPopupAdapter: PlatAdapter by lazy {
        PlatAdapter().apply {
            setList(
                arrayListOf(
                    HintLocalData(getString(R.string.min_investment_per_copy), getString(R.string.create_strategy_threshold_prompt1)),
                    HintLocalData(getString(R.string.min_lots_per_order), getString(R.string.create_strategy_threshold_prompt2)),
                    HintLocalData(getString(R.string.min_multiples_per_order), getString(R.string.create_strategy_threshold_prompt3))
                )
            )
        }
    }

    /**
     * 编辑 上架的策略 跳转后的回调
     */
    private val editResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Constants.REQUEST_CODE_STRATEGY_EDIT) {
            mViewModel.strategy.minInvestmentPerCopy = result.data?.getStringExtra(StEditStrategyMinActivity.KEY_MIN_INVESTMENT)
            mViewModel.strategy.minLotsPerOrder = result.data?.getStringExtra(StEditStrategyMinActivity.KEY_LOTS) ?: "0.01"
            mViewModel.strategy.minLotsMultiplePerOrder = result.data?.getStringExtra(StEditStrategyMinActivity.KEY_MULTIPLES) ?: "1"
            mViewModel.strategy.minLotsRatioPerOrder = result.data?.getStringExtra(StEditStrategyMinActivity.KEY_EQUIVALENT_MARGIN) ?: "1"
            updateUI()
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.oldStrategy = intent.getParcelableExtra(KEY_DATA) ?: StrategyBean()
        mViewModel.type = intent.getStringExtra(KEY_TYPE).ifNull(TYPE_CREATE)
//        LogUtils.w(GsonUtil.buildGson().toJson(inputStrategyBean))
        if (mViewModel.oldStrategy.minInvestmentPerCopy.isNullOrBlank()) {
            mViewModel.oldStrategy.minInvestmentPerCopy = "${mViewModel.minInvestedValue()}"
        }
        if (mViewModel.oldStrategy.stUserId.isNullOrBlank()) {
            mViewModel.oldStrategy.stUserId = UserDataUtil.stUserId()
        }
        mViewModel.strategy = mViewModel.oldStrategy.copy()
    }

    override fun initView() {
        // 创建新策略 || 编辑策略
        mBinding.mHeaderBar.setTitleText(
            when (mViewModel.type) {
                TYPE_CREATE -> getString(R.string.create_new_strategy)
                else -> getString(R.string.edit_strategy)
            }
        )
        // 根据状态设置显示不同的view
        when (mViewModel.type) {
            TYPE_CREATE -> {
                mBinding.groupId.isVisible = false
                mBinding.layoutEditThreshold.root.isVisible = true
                mBinding.clThresholdInfo.isVisible = false
                mBinding.tvUpdate.isVisible = false
                mBinding.tvSave.isVisible = true
                mBinding.tvPublish.isVisible = true
            }

            TYPE_EDIT -> {
                mBinding.groupId.isVisible = false
                mBinding.layoutEditThreshold.root.isVisible = true
                mBinding.clThresholdInfo.isVisible = false
                mBinding.tvUpdate.isVisible = false
                mBinding.tvSave.isVisible = true
                mBinding.tvPublish.isVisible = true
            }

            TYPE_OPEN_EDIT -> {
                mBinding.groupId.isVisible = true
                mBinding.layoutEditThreshold.root.isVisible = false
                mBinding.viewThresholdInfo.isVisible = false
                mBinding.clThresholdInfo.isVisible = true
                mBinding.tvUpdate.isVisible = true
                mBinding.tvSave.isVisible = false
                mBinding.tvPublish.isVisible = false
                mBinding.tvSourceAccount.setCompoundDrawables(null, null, null, null)
            }
        }

        // 设置最小跟单金额
        mBinding.layoutEditThreshold.etMinInvestment.setHint("${getString(R.string.min_dot)} ${mViewModel.minInvestedValue()}")
        // 设置最小跟单币种
        mBinding.layoutEditThreshold.tvCurrencyType.text = UserDataUtil.currencyType()
        // 设置最小跟单手数限制范围
        mBinding.layoutEditThreshold.etLots.setRangeAndDecimalPlaces(0.01, 100)
        // 设置最小跟单手数倍数
        mBinding.layoutEditThreshold.etMultiples.setRangeAndDecimalPlaces(0.1, 50, 1)
        // 设置等比例占用金模式下最小倍数限制范围
        mBinding.layoutEditThreshold.etEquivalentMargin.setRangeAndDecimalPlaces(1, 50, 1)
        updateUI()
    }

    override fun initData() {
        super.initData()
        // 获取账户信息
        mViewModel.getLiveAccountApi(false)

        // 获取app默认头像
        mViewModel.getAllPicApi(false)

        // 需要获取后台配置的结算周期设置
        mViewModel.strategyGetProfitShareCycleTypeApi()

        // 如果是编辑策略，则获取策略详情
        if (mViewModel.isEditOpenStrategy()) {
            mViewModel.strategyDetailApi(true)
        }
    }

    override fun initListener() {
        mBinding.mHeaderBar.setStartBackIconClickListener {
            checkData()
        }
        mBinding.ivAvatar.clickNoRepeat {
            showSelectPhotoPopupWindow()
        }
        // 头像选择弹窗回调
        avatarSelectPopup.confirmCallback { url ->
            mViewModel.strategy.avatar = url
            loadAvatar(url)
        }
        mBinding.tvID.clickNoRepeat {
            mBinding.tvID.text.copyText(getString(R.string.success))
        }
        // 点击昵称 跳转编辑昵称页面
        mBinding.tvName.clickNoRepeat {
            nicknameResultLauncher.launch(EditNicknameActivity.createIntent(this, nickname = mViewModel.strategy.strategyName, maxCount = "20"))
        }

        // 点击简介 跳转编辑简介页面
        mBinding.tvDescription.clickNoRepeat {
            descriptionResultLauncher.launch(
                EditPersonalInfoActivity.createIntent(
                    this,
                    title = getString(R.string.edit_description),
                    hint = getString(R.string.introduce_your_strategy_to_copiers),
                    description = mViewModel.strategy.description,
                    isNeedCheck = false
                )
            )
        }

        // 源账户 title 点击 展示提示弹窗
        mBinding.tvSourceAccountTitle.clickNoRepeat {
            showPopup(getString(R.string.source_account), sourceAccountPromptPopupAdapter)
        }
        // 源账户 点击弹出源账户选择弹窗 已上架的策略不能编辑原账户
        if (!mViewModel.isEditOpenStrategy()) {
            mBinding.tvSourceAccount.clickNoRepeat {
                if (sourceAccountPopupAdapter.data.isEmpty()) {
                    isSourceClick = true
                    mViewModel.getLiveAccountApi(true)
                } else {
                    sourceAccountPopupAdapter.selectTitle = mBinding.tvSourceAccount.text.toString()
                    showPopup(getString(R.string.source_account), sourceAccountPopupAdapter)
                }
            }
        }

        // 交易账户 点击弹出交易账户选择弹窗
        mBinding.tvPaymentAccount.clickNoRepeat {
            if (sourceAccountPopupAdapter.data.isEmpty()) {
                isPayClick = true
                mViewModel.getLiveAccountApi(true)
            } else {
                paymentAccountPopupAdapter.selectTitle = mBinding.tvPaymentAccount.text.toString()
                showPopup(getString(R.string.payment_account), paymentAccountPopupAdapter)
            }
        }
        // 分润比例 title 点击 展示提示弹窗
        mBinding.tvProfitSharingRatioTitle.clickNoRepeat {
            showPopup(getString(R.string.profit_sharing_ratio), profitSharingRatioPromptPopupAdapter)
        }
        // 分润比例 点击弹出分润比例弹窗
        mBinding.tvProfitSharingRatio.clickNoRepeat {
            showPopup(getString(R.string.profit_sharing_ratio), profitSharingRatioAdapter)
        }

        // 结算周期 title 点击 展示提示弹窗
        mBinding.tvSettlementFrequencyTitle.clickNoRepeat {
            showPopup(getString(R.string.settlement_frequency), settlementPromptPopupAdapter)
        }
        // 当前结算周期 title 点击 展示提示弹窗 编辑策略的时候 会展示这一项
        mBinding.tvCurrentSettlementFrequencyTitle.clickNoRepeat {
            showPopup(getString(R.string.settlement_frequency), settlementPromptPopupAdapter)
        }
        // 结算周期 点击弹出结算周期弹窗
        mBinding.tvSettlementFrequency.clickNoRepeat {
            if (mViewModel.isEditOpenStrategy() && mViewModel.strategy.allowToUpdateSettlementFrequency == false) {
                ToastUtil.showToast(getString(R.string.the_settlement_frequency_available_progressively))
            } else {
                settlementPopupAdapter.selectTitle = mBinding.tvSettlementFrequency.text.toString()
                settlementPopupAdapter.notifyDataSetChanged()
                showPopup(getString(R.string.settlement_frequency), settlementPopupAdapter)
            }
        }
        // 下次结算周期 点击弹出结算周期弹窗
        mBinding.tvNextSettlementFrequency.clickNoRepeat {
            settlementPopupAdapter.selectTitle = mBinding.tvNextSettlementFrequency.text.toString()
            settlementPopupAdapter.notifyDataSetChanged()
            showPopup(getString(R.string.settlement_frequency), settlementPopupAdapter)
        }

        // 是否自动审核 title 点击 展示提示弹窗
        mBinding.tvCopierReviewTitle.clickNoRepeat {
            showPopup(getString(R.string.copier_review), copierReviewPromptPopupAdapter)
        }
        // 是否自动审核 点击切换是否设置自动审核
        mBinding.mSwitchButton.setStateChangeListener { state ->
            mBinding.groupAuto.isVisible = state
            mViewModel.strategy.copierReview = state
            if (state) {
                if (mViewModel.strategy.reviewType == null) {
                    mViewModel.strategy.reviewType = "1"
                }
                autoReviewAdapter.selectTitle = autoReviewAdapter.data.first { it.id == mViewModel.strategy.reviewType }.getShowItemValue()
            } else {
                mViewModel.strategy.reviewType = null
            }
            updateUI()
        }
        // 自动审核 点击弹出自动审核选择弹窗
        mBinding.tvAutoReview.clickNoRepeat {
            showPopup(getString(R.string.auto_review), autoReviewAdapter)
        }
        // 最小跟单设置 title 点击 展示提示弹窗
        mBinding.layoutEditThreshold.tvThresholdTitle.clickNoRepeat {
            showPopup(getString(R.string.threshold_for_copiers), thresholdPromptPopupAdapter)
        }
        // 最小跟单设置 title 点击 展示提示弹窗 编辑策略页面显示这一项
        mBinding.tvUpdateThresholdTitle.clickNoRepeat {
            showPopup(getString(R.string.threshold_for_copiers), thresholdPromptPopupAdapter)
        }
        // 获取输入框内的最小跟单金额 并保存
        mBinding.layoutEditThreshold.etMinInvestment.doAfterTextChanged {
            mViewModel.strategy.minInvestmentPerCopy = it.toString()
        }
        // 获取输入框内的最小跟单手数 并保存
        mBinding.layoutEditThreshold.etLots.doAfterTextChanged {
            mViewModel.strategy.minLotsPerOrder = it.toString()
        }
        // 获取输入框内的最小跟单手数倍数 并保存
        mBinding.layoutEditThreshold.etMultiples.doAfterTextChanged {
            mViewModel.strategy.minLotsMultiplePerOrder = it.toString()
        }
        // 获取输入框内的等比例占用金模式下最小倍数 并保存
        mBinding.layoutEditThreshold.etEquivalentMargin.doAfterTextChanged {
            mViewModel.strategy.minLotsRatioPerOrder = it.toString()
        }

        // 键盘监听 键盘收起时，移除输入框的背景
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                mBinding.root.requestFocus()
                removeFocusBg(mBinding.layoutEditThreshold.etMinInvestment, true)
                removeFocusBg(mBinding.layoutEditThreshold.etLots, true)
                removeFocusBg(mBinding.layoutEditThreshold.etMultiples, true)
                removeFocusBg(mBinding.layoutEditThreshold.etEquivalentMargin, true)
            }
        }
        // 检测焦点变化 如果新的焦点是在输入框，则设置输入框的背景
        mBinding.root.viewTreeObserver.addOnGlobalFocusChangeListener { oldFocus, newFocus ->
            removeFocusBg(mBinding.layoutEditThreshold.etMinInvestment)
            removeFocusBg(mBinding.layoutEditThreshold.etLots)
            removeFocusBg(mBinding.layoutEditThreshold.etMultiples)
            removeFocusBg(mBinding.layoutEditThreshold.etEquivalentMargin)
            when (newFocus) {
                mBinding.layoutEditThreshold.etMinInvestment -> addFocusBg(mBinding.layoutEditThreshold.etMinInvestment)
                mBinding.layoutEditThreshold.etLots -> addFocusBg(mBinding.layoutEditThreshold.etLots)
                mBinding.layoutEditThreshold.etMultiples -> addFocusBg(mBinding.layoutEditThreshold.etMultiples)
                mBinding.layoutEditThreshold.etEquivalentMargin -> addFocusBg(mBinding.layoutEditThreshold.etEquivalentMargin)
            }
        }
        // 编辑策略的时候 点击修改按钮，跳转最小跟单设置页面
        mBinding.tvEdit.clickNoRepeat {
            editResultLauncher.launch(
                StEditStrategyMinActivity.createIntent(
                    this,
                    minInvestment = mViewModel.strategy.minInvestmentPerCopy,
                    lots = mViewModel.strategy.minLotsPerOrder,
                    multiples = mViewModel.strategy.minLotsMultiplePerOrder,
                    equivalentMargin = mViewModel.strategy.minLotsRatioPerOrder,
                    currency = mViewModel.strategy.loginAccountCurrency
                )
            )
        }
        // 点击保存按钮
        mBinding.tvSave.clickNoRepeat {
            if (dateCheckThreshold(mViewModel.strategy, true)) {
                saveStrategy()
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_SP_CENTER_SAVE_BTN_CLICK)
            }
        }
        // 点击发布按钮
        mBinding.tvPublish.clickNoRepeat {
            if (dataCheck(mViewModel.strategy, true)) {
                mViewModel.createStrategyApi()
                LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_SP_CENTER_PUBLISH_BTN_CLICK)
            }
        }
        // 点击立即更新按钮
        mBinding.tvUpdate.clickNoRepeat {
            updateStrategy()
        }
    }

    private fun showSelectPhotoPopupWindow() {
        BottomSelectListDialog.Builder(this)
            .setTitle(getString(R.string.add_picture_from))
            .setDataList(selectAvatarList)
            .setItemType(1)
            .setOnItemClickListener {
                avatarPopSelect(it)
            }
            .build().showDialog()
    }

    override fun createObserver() {
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    // 获取到了 后台返回的 收益结算周期
                    StCreateAndEditStrategyViewModel.EVENT_PROFIT_CYCLE_TYPE -> {
                        settlementPopupAdapter.setList(it.data as? MutableList<SelectBean>)
                        // 如果不是 settlementFrequency == null 则说明是创建策略或者编辑本地策略的时候没有 选择 settlementFrequency 则需要给设置默认值
                        if (mViewModel.strategy.settlementFrequency == null) {
                            // 如果结算周期有3个的时候 就默认选中第二个，否则默认选中第一个
                            val item = if (settlementPopupAdapter.data.size == 3) {
                                settlementPopupAdapter.data.getOrNull(1)
                            } else {
                                settlementPopupAdapter.data.getOrNull(0)
                            }
                            settlementPopupAdapter.selectTitle = item?.getShowItemValue()
                            mViewModel.strategy.settlementFrequency = item?.id
                        }

                        configSettlementFrequency(mViewModel.strategy)
                    }
                    // 获取账户列表
                    StCreateAndEditStrategyViewModel.EVENT_GET_LIVE_ACCOUNT_LIST -> {
                        val list = it.data as? MutableList<AccountBean>
                        // 获取到账户列表 根据保存的点击状态 判断展示对应弹窗
                        if (!list.isNullOrEmpty()) {
                            sourceAccountPopupAdapter.setList(list)
                            paymentAccountPopupAdapter.setList(list)
                            if (isSourceClick) {
                                isSourceClick = false
                                showPopup(getString(R.string.source_account), sourceAccountPopupAdapter)
                            }
                            if (isPayClick) {
                                isPayClick = false
                                showPopup(getString(R.string.payment_account), paymentAccountPopupAdapter)
                            }
                        } else {
                            // 账户列表为空 弹出提示开同名账户的弹窗 并点击跳转
                            if (isSourceClick || isPayClick) {
                                isSourceClick = false
                                isPayClick = false

                                CenterActionDialog.Builder(this@StCreateAndEditStrategyActivity)
                                    .setContent(getString(R.string.no_available_live_open_live_account)) // 设置内容
                                    .setStartText(getString(R.string.cancel)) // 设置左侧按钮文本
                                    .setEndText(getString(R.string.proceed)) // 设置右侧按钮文本
                                    // 如果展示两个按钮，点击监听使用setOnStartListener和setOnEndListener
                                    .setOnEndListener { textView ->
                                        // 默认关闭
                                        if (SpManager.isV1V2()) {
                                            KycVerifyHelper.showKycDialog(
                                                this@StCreateAndEditStrategyActivity,
                                                mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT)
                                            )
                                        } else {
                                            val intent = Intent(this@StCreateAndEditStrategyActivity, OpenSameNameAccountActivity::class.java)
                                            startActivity(intent)
                                        }
                                    }
                                    .build()
                                    .showDialog()
                            }
                        }
                    }
                    // 头像上传成功
                    StCreateAndEditStrategyViewModel.EVENT_UPLOAD_AVATAR_SUCCESS -> {
                        loadAvatar(it.data.toString())
                    }
                    // 更新策略成功
                    StCreateAndEditStrategyViewModel.EVENT_UPDATE_STRATEGY_SUCCESS -> {
                        CenterActionWithIconDialog.Builder(this@StCreateAndEditStrategyActivity)
                            .setLottieIcon(R.raw.lottie_dialog_ok)
                            .setTitle(getString(R.string.update_successful))
                            .setSingleButton(true)
                            .setSingleButtonText(getString(R.string.ok))
                            .setOnSingleButtonListener {
                                // 通知信号源中心页面 更新成功
                                intent.putExtra("type", 0) // 更新成功
                                setResult(RESULT_OK, intent)
                                finish()
                            }
                            .build().showDialog()
                    }
                    // 创建策略成功
                    StCreateAndEditStrategyViewModel.EVENT_CREATE_STRATEGY_SUCCESS -> {
                        CenterActionWithIconDialog.Builder(this@StCreateAndEditStrategyActivity)
                            .setLottieIcon(R.raw.lottie_dialog_ok)
                            .setTitle(getString(R.string.publish_successful))
                            .setContent(getString(R.string.your_strategy_is_by_others))
                            .setSingleButton(true)
                            .setSingleButtonText(getString(R.string.ok))
                            .setOnSingleButtonListener {
                                // 通知信号源中心页面 创建成功
                                intent.putExtra("type", 1) // 上架成功
                                setResult(RESULT_OK, intent)
                                finish()
                            }
                            .build().showDialog()
                    }

                    StCreateAndEditStrategyViewModel.EVENT_CONFIG_STRATEGY_FAIL_10583 -> {
                        CenterActionDialog.Builder(this@StCreateAndEditStrategyActivity)
                            .setContent(getString(R.string.the_upper_limit_private_you_the_limit))
                            .setSingleButton(true)
                            .setSingleButtonText(getString(R.string.ok))
                            .build().showDialog()
                    }

                    StCreateAndEditStrategyViewModel.EVENT_CONFIG_STRATEGY_FAIL_10585 -> {
                        CenterActionDialog.Builder(this@StCreateAndEditStrategyActivity)
                            .setContent(getString(R.string.you_have_a_strategies_you_as_draft))
                            .setSingleButton(true)
                            .setSingleButtonText(getString(R.string.ok))
                            .build().showDialog()
                    }

                    StCreateAndEditStrategyViewModel.EVENT_REFRESH_STRATEGY_UI -> {
                        updateUI()
                    }

                    StCreateAndEditStrategyViewModel.EVENT_GET_AVATAR_LIST -> {
                        avatarSelectPopup.setData(it.data as MutableList<SelectAllPicBean.Obj>)
                        avatarSelectPopup.setSelectItem(mViewModel.strategy.avatar)
                        loadAvatar(mViewModel.strategy.avatar)
                        if (isAvatarClick) {
                            isAvatarClick = false
                            avatarSelectPopup.showDialog()
                        }
                    }
                }
            }
        }
    }

    /**
     * 根据数据更新页面的ui
     */
    private fun updateUI() {
        mViewModel.strategy.let { data ->
            // 显示头像
            data.avatar?.let { loadAvatar(it) }
            // 显示策略id
            data.strategyNo?.let { mBinding.tvID.text = it }
            // 显示策略名称
            mBinding.tvName.text = data.strategyName
            // 显示策略简介
            data.description?.let { mBinding.tvDescription.text = it }
            // 显示策略 源账户
            data.sourceAccount?.let { mBinding.tvSourceAccount.text = it }
            // 显示策略 交易账户
            data.paymentAccount?.let { mBinding.tvPaymentAccount.text = it }
            // 显示策略 分润比例
            data.profitShareRatio.let { profitShareRatio ->
                profitSharingRatioAdapter.selectTitle = profitSharingRatioAdapter.data.firstOrNull { it.id.toDoubleCatching() == profitShareRatio.toDoubleCatching() }?.title ?: ""
                mBinding.tvProfitSharingRatio.text = profitSharingRatioAdapter.selectTitle
            }

            configSettlementFrequency(data)

            // 显示下一结算周期结算时间
            data.nextSettlementTime?.let {
                mBinding.tvFrequencyTimePrompt.text = getString(R.string.new_frequency_will_take_effect_x, it)
            }
            // 显示是否自动审核的状态
            data.copierReview.let {
                mBinding.mSwitchButton.setState(it)
                mBinding.groupAuto.isVisible = it
            }
            // 根据状态显示自动审核还是自动拒绝
            data.reviewType?.let {
                autoReviewAdapter.selectTitle = if (it == "1") getString(R.string.auto_approve) else getString(R.string.auto_reject)
                mBinding.tvAutoReview.text = autoReviewAdapter.selectTitle
                mBinding.tvAutoReviewPrompt.text = if (it == "1") {
                    getString(R.string.copiers_will_be_approved_of_application)
                } else {
                    getString(R.string.copiers_will_be_rejected_of_application)
                }
            }
            // 显示最小跟单金额
            data.minInvestmentPerCopy?.let {
                if (mViewModel.isEditOpenStrategy()) {
                    mBinding.tvMinInvestment.text = buildString {
                        append(it.toIntCatching().toString())
                        append(" ")
                        append(data.loginAccountCurrency ?: UserDataUtil.currencyType())
                    }
                } else {
                    // 如果是创建策略 或是本地编辑策略 ，则判断一下，如果跟当前输入框的值不一样，则更新， 防止重复赋值
                    if (it != mBinding.layoutEditThreshold.etMinInvestment.text.toString()) {
                        mBinding.layoutEditThreshold.etMinInvestment.setText(it)
                    }
                }
            }
            // 显示最小跟单手数
            data.minLotsPerOrder.let {
                if (mViewModel.isEditOpenStrategy()) {
                    mBinding.tvLots.text = it.toDoubleCatching().toString()
                } else {
                    // 如果跟当前输入框的值不一样，则更新， 防止重复赋值
                    if (it != mBinding.layoutEditThreshold.etLots.text.toString()) {
                        mBinding.layoutEditThreshold.etLots.setText(it)
                    }
                }
            }
            // 显示最小跟单倍数
            data.minLotsMultiplePerOrder.let {
                if (mViewModel.isEditOpenStrategy()) {
                    mBinding.tvMultiples.text = it.toDoubleCatching().toString()
                } else {
                    // 如果跟当前输入框的值不一样，则更新， 防止重复赋值
                    if (it != mBinding.layoutEditThreshold.etMultiples.text.toString()) {
                        mBinding.layoutEditThreshold.etMultiples.setText(it)
                    }
                }
            }
            // 显示等比例占用金模式下最小倍数
            data.minLotsRatioPerOrder.let {
                if (mViewModel.isEditOpenStrategy()) {
                    mBinding.tvEquivalentMargin.text = it.toDoubleCatching().toString()
                } else {
                    // 如果跟当前输入框的值不一样，则更新， 防止重复赋值
                    if (it != mBinding.layoutEditThreshold.etEquivalentMargin.text.toString()) {
                        mBinding.layoutEditThreshold.etEquivalentMargin.setText(it)
                    }
                }
            }
            // 检测数据，刷新提交按钮样式
            updateSubmitButton(data)
        }
    }

    /**
     * 设置结算周期以及 编辑时 的下一结算周期等ui
     */
    private fun configSettlementFrequency(data: StrategyBean) {
        // 如果settlementPopupAdapter里没有数据  说明没有请求到结算周期 ，则不设置结算周期相关的ui
        if (settlementPopupAdapter.data.isEmpty()) return
        if (mViewModel.isEditOpenStrategy()) {
            // 编辑策略的情况下 显示当前结算周期 下一个结算周期
            mBinding.tvCurrentSettlementFrequency.text = mViewModel.createProfitCycleTypeData(data.currentSettlementFrequency ?: StCreateAndEditStrategyViewModel.KEY_WEEKLY).title
            mBinding.tvSettlementFrequency.text = mViewModel.createProfitCycleTypeData(data.currentSettlementFrequency ?: StCreateAndEditStrategyViewModel.KEY_WEEKLY).title
            // 只有下一结算周期的字段不为空的时候 再做逻辑判断
            data.nextSettlementFrequency?.let { nextSettlementFrequency ->
                mBinding.tvNextSettlementFrequency.text = mViewModel.createProfitCycleTypeData(nextSettlementFrequency).title
                // 如果下一结算周期 和 当前结算周期相同，则不显示下一结算周期
                if (mBinding.tvCurrentSettlementFrequency.text == mBinding.tvNextSettlementFrequency.text) {
                    mBinding.groupNewFrequency.isVisible = false
                    mBinding.groupSelectFrequency.isVisible = true
                } else {
                    mBinding.groupNewFrequency.isVisible = true
                    mBinding.groupSelectFrequency.isVisible = false
                }
            }
        } else {
            // 创建策略以及本地编辑策略 显示结算周期
            mBinding.tvSettlementFrequency.text = mViewModel.createProfitCycleTypeData(data.settlementFrequency ?: StCreateAndEditStrategyViewModel.KEY_WEEKLY).title
        }
    }

    /**
     * 加载头像
     */
    private fun loadAvatar(url: String?) {
        ImageLoaderUtil.loadImage(this, url, mBinding.ivAvatar)
    }

    /**
     * 更新策略
     */
    private fun updateStrategy() {
        mViewModel.strategy.let {
            if (dataCheck(it, true)) {
                // 因为后台更改策略的时候 会搜名字看是否重复，去掉以后 就不会搜这个字段
                if (mViewModel.oldStrategy.strategyName == it.strategyName) {
                    it.strategyName = null
                }
                mViewModel.updateStrategyApi(it)
            }
        }
    }

    /**
     * 策略本地保存后 展示成功弹窗 并关闭页面
     * 失败展示失败弹窗
     */
    private fun saveStrategy() {
        if (mViewModel.save()) {
            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.saved_as_draft))
                .setSingleButton(true)
                .setSingleButtonText(getString(R.string.ok))
                .setOnSingleButtonListener {
                    intent.putExtra("type", 2) // 保存草稿成功
                    setResult(RESULT_OK, intent)
                    finish()
                }
                .build().showDialog()
        } else {
            CenterActionDialog.Builder(this)
                .setContent(getString(R.string.the_upper_limit_private_you_the_limit)) // 设置内容
                .setSingleButton(true) // 展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) // 设置单个按钮文本
                .build()
                .showDialog()
        }
    }

    /**
     * 设置edittext 获取焦点后的背景
     */
    private fun addFocusBg(et: AppCompatEditText) {
        // 键盘可见
        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10)
    }

    /**
     * 设置edittext 失去焦点后的背景
     */
    private fun removeFocusBg(et: AppCompatEditText, isClear: Boolean = false) {
        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_c0a1e1e1e_c262930_r10)
        if (isClear) {
            et.clearFocus()
        }
    }

    /**
     * 显示弹窗 传入不同的 title 和 adapter ，显示不同的数据
     */
    private fun showPopup(title: String, adapter: BaseQuickAdapter<*, *>) {
        selectDialog.setTitle(title)
        selectDialog.setAdapter(adapter)
        selectDialog.showDialog()
    }

    /**
     * 刷新 保存和上架 两个提交按钮
     */
    private fun updateSubmitButton(data: StrategyBean) {
        if (dataCheck(data)) {
            mBinding.tvPublish.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            mBinding.tvUpdate.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            mBinding.tvPublish.setTextColor(colorCebffffffC1e1e1e)
            mBinding.tvUpdate.setTextColor(colorCebffffffC1e1e1e)
        } else {
            mBinding.tvPublish.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
            mBinding.tvUpdate.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
            mBinding.tvPublish.setTextColor(colorC731e1e1eC61ffffff)
            mBinding.tvUpdate.setTextColor(colorC731e1e1eC61ffffff)
        }
    }

    /**
     * 检测 数据是否全部填写
     * isShowToast 是否显示toast 默认不显示
     */
    private fun dataCheck(data: StrategyBean, isShowToast: Boolean = false): Boolean {
        if (data.avatar.isNullOrBlank() ||
            data.strategyName.isNullOrBlank() ||
            data.sourceAccount.isNullOrBlank() ||
            data.paymentAccount.isNullOrBlank()
        ) {
            if (isShowToast) {
                ToastUtil.showToast(getString(R.string.please_complete_all_required_information))
            }
            return false
        }
        if (!dateCheckThreshold(data, isShowToast)) {
            return false
        }
        return true
    }

    /**
     * 检测跟单门槛的值是否全部填写（保存草稿时只检查跟单门槛）
     */
    private fun dateCheckThreshold(data: StrategyBean, isShowToast: Boolean = false): Boolean {
        if (data.minInvestmentPerCopy.isNullOrBlank() || !checkNumIsPass(data.minInvestmentPerCopy.ifNull(), min = mViewModel.minInvestedValue().toDouble())) {
            if (isShowToast) {
                ToastUtil.showToast(getString(R.string.the_minimum_investment_x, "${mViewModel.minInvestedValue()} ${UserDataUtil.currencyType()}"))
            }
            return false
        }
        if (data.minLotsPerOrder.isBlank() || !checkNumIsPass(data.minLotsPerOrder.ifNull(), 0.01, 100)) {
            if (isShowToast) {
                ToastUtil.showToast(getString(R.string.min_lots_per_1_100))
            }
            return false
        }
        if (data.minLotsMultiplePerOrder.isBlank() || !checkNumIsPass(data.minLotsMultiplePerOrder.ifNull(), 0.1, 50)) {
            if (isShowToast) {
                ToastUtil.showToast(getString(R.string.min_lots_multiple))
            }
            return false
        }
        if (data.minLotsRatioPerOrder.isBlank() || !checkNumIsPass(data.minLotsRatioPerOrder.ifNull(), 1, 50)) {
            if (isShowToast) {
                ToastUtil.showToast(getString(R.string.min_multiples_for_1_50))
            }
            return false
        }
        return true
    }

    /**
     * 检测 输入数字是否在 限制范围内
     */
    private fun checkNumIsPass(str: String, min: Number, max: Number? = null): Boolean {
        try {
            val value = str.toDoubleCatching()
            return if (max == null) {
                value >= min.toDouble()
            } else {
                value in min.toDouble()..max.toDouble()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return false
    }

    /**
     * 头像选择  拍照 or 相册 or 后台提供的头像
     */
    private fun avatarPopSelect(selectType: Int) {
        when (selectType) {
            0 -> PermissionUtil.checkPermissionWithCallback(this, Constants.PERMISSION_CAMERA) { isGranted ->
                if (isGranted) {
                    openCamera()
                }
            }

            1 -> PermissionUtil.checkPermissionWithCallback(this, *Constants.PERMISSION_STORAGE) { isGranted ->
                if (isGranted) {
                    openGallery()
                }
            }

            else -> {
                if (!avatarSelectPopup.isHasData()) {
                    mViewModel.getAllPicApi(true)
                    isAvatarClick = true
                } else {
                    avatarSelectPopup.setSelectItem(mViewModel.strategy.avatar)
                    avatarSelectPopup.showDialog()
                }
            }
        }
    }

    private fun openCamera() {
        // 打开相机
        pickCamera.launch(cameraUri)
    }

    private fun openGallery() {
        // 打开相册
        pickImage.launchPhoto()
    }

    /**
     * 检测数据有没有变动，判断是否展示挽留弹窗
     */
    private fun checkData() {
        if (mViewModel.isEditStrategy() && mViewModel.oldStrategy != mViewModel.strategy) {
            CenterActionDialog.Builder(this)
                .setTitle(getString(R.string.leave_this_page) + "?")
                .setContent(getString(R.string.do_you_want_to_update_the_strategy))
                .setStartText(getString(R.string.discard))
                .setEndText(getString(R.string.update__now))
                .setOnStartListener {
                    finish()
                }
                .setOnEndListener {
                    if (mViewModel.isEditOpenStrategy()) {
                        updateStrategy()
                    } else {
                        saveStrategy()
                    }
                }
                .build().showDialog()
        } else {
            finish()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    companion object {

        private const val KEY_TYPE = "type"
        private const val KEY_DATA = "data"

        const val TYPE_CREATE = "create"
        const val TYPE_EDIT = "edit"
        const val TYPE_OPEN_EDIT = "open_edit"

        fun createIntent(context: Context?, type: String, strategyBean: StrategyBean? = null) = Intent(context, StCreateAndEditStrategyActivity::class.java).apply {
            putExtra(KEY_TYPE, type)
            putExtra(KEY_DATA, strategyBean)
        }
    }
}