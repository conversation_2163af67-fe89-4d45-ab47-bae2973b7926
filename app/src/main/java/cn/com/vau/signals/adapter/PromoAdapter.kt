package cn.com.vau.signals.adapter

import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.view.system.ScaleImageView
import cn.com.vau.data.discover.PromoEventData
import cn.com.vau.util.*
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2024 11月 02 14:06
 * @updateUser:
 * @updateDate: 2024 11月 02 14:06
 */
class PromoAdapter : BaseQuickAdapter<PromoEventData, BaseViewHolder>(R.layout.item_recycler_home) {

    override fun convert(holder: BaseViewHolder, dataBean: PromoEventData) {
        ImageLoaderUtil.loadImageWithOption(
            context, dataBean.imgUrl, holder.getViewOrNull<ScaleImageView>(R.id.mImageView), RequestOptions()
                .placeholder(R.drawable.shape_placeholder)
                .error(R.drawable.shape_placeholder)
        )

        holder.setText(R.id.tvEventTitle, dataBean.appJumpDefModel?.title)
            .setText(
                R.id.tvEventTime, if ("1" == dataBean.longTerm) {
                    if (Constants.PromoTabTxt) context.getString(R.string.ongoing_promotions) else ""
                } else "${dataBean.startTime.ifNull()} - ${dataBean.endTime.ifNull()}".arabicReverseTextByFlag(" - ")
            )
            .setText(R.id.tvEventState, context.getString(if (0 == dataBean.eventsStatus) R.string.in_progress else R.string.coming_soon))

    }

}