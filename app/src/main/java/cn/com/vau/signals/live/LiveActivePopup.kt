package cn.com.vau.signals.live

import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.utils.*
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.data.discover.PromoEventData
import cn.com.vau.signals.adapter.live.LiveActiveRecyclerAdapter
import cn.com.vau.util.dp2px

/**
 * 直播活动
 * Created by liyang
 */
class LiveActivePopup(val mContext: Context, var dataList: ArrayList<PromoEventData>) :
    PopupWindow() {
    var rcyLiveActive: RecyclerView? = null
    var adapter: LiveActiveRecyclerAdapter? = null
    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(eventId: String)
    }

    init {
        initSteup()
        //initView()
    }

    public fun initView(count : Int) {
        val layoutManager = GridLayoutManager(mContext, count,LinearLayoutManager.VERTICAL, false)
        rcyLiveActive?.layoutManager = layoutManager
        adapter = LiveActiveRecyclerAdapter(mContext, dataList)
        rcyLiveActive?.adapter = adapter

        adapter?.setOnItemClickListener(object : LiveActiveRecyclerAdapter.onItemClickListener {
            override fun onItemClick(position: Int) {
                val selectData = dataList.elementAtOrNull(position)
                LogEventUtil
                    .setLogEvent("livestream_promo_button_click_2", Bundle().apply {
                        putString("promo_name", selectData?.eventsName ?: "")
                    })
                VAUStartUtil.openActivity(
                    mContext, selectData?.appJumpDefModel
                )
                mOnItemClickListener?.onItemClick(selectData?.eventId ?: "")
            }
        })
    }

    fun setData() {
        val layoutParams = rcyLiveActive?.layoutParams
        layoutParams?.height = 100.dp2px()
        adapter?.notifyDataSetChanged()
    }

    private fun initSteup() {
        //设置PopupWindow的View
        val mView = LayoutInflater.from(mContext).inflate(R.layout.lauout_pop_live_active, null)
        rcyLiveActive = mView.findViewById(R.id.rvLiveActive)
        this.contentView = mView
        //设置PopupWindow弹出窗体的宽
        this.width = WindowManager.LayoutParams.MATCH_PARENT
        //设置PopupWindow弹出窗体的高
        this.height = WindowManager.LayoutParams.WRAP_CONTENT
        //设置PopupWindow弹出窗体不可点击
        this.isFocusable = false
        //设置SelectPicPopupWindow弹出窗体动画效果
        this.animationStyle = R.style.popupAnimStyleBottom;
        //实例化一个ColorDrawable颜色为半透明
        //        ColorDrawable dw = new ColorDrawable(0xb0000000);
        //设置SelectPicPopupWindow弹出窗体的背景
        //        this.setBackgroundDrawable(dw);
        this.isOutsideTouchable = true
        this.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        this.inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
        this.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }
}
