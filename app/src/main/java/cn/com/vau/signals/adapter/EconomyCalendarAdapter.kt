package cn.com.vau.signals.adapter

import android.graphics.drawable.Drawable
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.data.discover.CalendarObjFinindex
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.material.imageview.ShapeableImageView

class EconomyCalendarAdapter() : BaseQuickAdapter<CalendarObjFinindex, BaseViewHolder>(R.layout.item_economy_calendar) {

    init {
        addChildClickViewIds(R.id.ivState)
    }

    private val signalsBellInactive: Drawable? by lazy { ContextCompat.getDrawable(context, AttrResourceUtil.getDrawable(context, R.attr.icon2ECInactive)) }
    private val signalsBellActive: Drawable? by lazy { ContextCompat.getDrawable(context, R.drawable.icon2_ec_active) }
    private val signalsBellOut: Drawable? by lazy { ContextCompat.getDrawable(context, AttrResourceUtil.getDrawable(context, R.attr.icon2ECOut)) }
    private val shape_c1fe35728_r100: Drawable? by lazy { ContextCompat.getDrawable(context, R.drawable.shape_c1fe35728_r100) }
    private val shape_ce35728_r100: Drawable? by lazy { ContextCompat.getDrawable(context, R.drawable.shape_ce35728_r100) }
    private val shape_cff8e5c_r100: Drawable? by lazy { ContextCompat.getDrawable(context, R.drawable.shape_cff8e5c_r100) }

    override fun convert(holder: BaseViewHolder, item: CalendarObjFinindex) {
        val countryIcon = holder.getView<ShapeableImageView>(R.id.ivCountry)
        ImageLoaderUtil.loadImage(countryIcon, item.countryImg, countryIcon)

        var importance = ""
        holder.setText(R.id.tvCountry, item.country.ifNull())
            .setText(R.id.tvTitle, item.title.ifNull())
            .setText(R.id.tvPrev, item.previous.ifNull())
            .setText(R.id.tvFcst, item.consensus.ifNull())
            .setText(R.id.tvAct, item.actualVal.ifNull())
            .setText(R.id.tvTime, item.timeShow.ifNull())
            .setBackground(
                R.id.tvImportance, when (item.importance) {
                    "medium" -> {
                        holder.setTextColor(R.id.tvImportance, ContextCompat.getColor(context, R.color.cffffff))
                        importance = context.getString(R.string.medium)
                        shape_cff8e5c_r100
                    }

                    "high" -> {
                        holder.setTextColor(R.id.tvImportance, ContextCompat.getColor(context, R.color.cffffff))
                        importance = context.getString(R.string.high)
                        shape_ce35728_r100
                    }

                    else -> {
                        holder.setTextColor(R.id.tvImportance, ContextCompat.getColor(context, R.color.ce35728))
                        importance = context.getString(R.string.low)
                        shape_c1fe35728_r100
                    }
                }
            )
            .setText(R.id.tvImportance, importance)
            .setImageDrawable(
                R.id.ivState, when (item.isRemind) {
                    0 -> signalsBellInactive
                    1 -> signalsBellActive
                    else -> signalsBellOut
                }
            )
    }

    override fun convert(holder: BaseViewHolder, item: CalendarObjFinindex, payloads: List<Any>) {
        super.convert(holder, item, payloads)
        holder.setImageDrawable(
            R.id.ivState, when (item.isRemind) {
                0 -> signalsBellInactive
                1 -> signalsBellActive
                else -> signalsBellOut
            }
        )
    }

}