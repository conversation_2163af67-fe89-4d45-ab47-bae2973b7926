package cn.com.vau.signals.stsignal.activity

import androidx.core.view.*
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityStProviderToPublicTradeBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.signals.stsignal.viewmodel.StProviderToPublicViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

class StProviderToPublicTradeActivity : BaseMvvmActivity<ActivityStProviderToPublicTradeBinding, StProviderToPublicViewModel>() {

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cebffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    override fun initView() {

    }

    override fun initData() {
        super.initData()
        mViewModel.accountOpenConditionApi(true)
        mViewModel.accountGetDepositAuthorityAmountApi()
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvTask1Commit.clickNoRepeat {
            NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
        }
        mBinding.tvTask2Commit.clickNoRepeat {
            mViewModel.conditionAcceptedApi()
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    StProviderToPublicViewModel.UPDATE_UI -> {
                        rander()
                    }

                    StProviderToPublicViewModel.FINISH_PAGE -> {
                        finish()
                    }

                    StProviderToPublicViewModel.DEPOSIT_AMOUNT -> {
                        mBinding.tvTask1.text = getString(R.string.single_deposit_of_x_trading_account, it.data.toString())
                    }
                }
            }
        }
    }

    private fun rander() {
        mBinding.apply {
            tvTask1Commit.isInvisible = mViewModel.isDepositAccepted
            tvTask2Commit.isInvisible = mViewModel.isAuthorityAccepted
            ivTask1Complete.isVisible = mViewModel.isDepositAccepted
            ivTask2Complete.isVisible = mViewModel.isAuthorityAccepted
            // 是否条件都满足
            val bothOfMeet = mViewModel.isAuthorityAccepted && mViewModel.isDepositAccepted
            tvNext.setBackgroundResource(
                if (bothOfMeet)
                    R.drawable.draw_shape_c1e1e1e_cebffffff_r100
                else
                    R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
            )
            tvNext.setTextColor(if (bothOfMeet) color_cebffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
            tvNext.setOnClickListener {
                if (bothOfMeet) {
                    // 设置公开交易 / 成为信号源
                    mViewModel.userBecomeSignalApi()
                    LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_PROFILE_BECOME_SP_SUBMIT_BTN_CLICK)
                }
            }
        }
    }

}