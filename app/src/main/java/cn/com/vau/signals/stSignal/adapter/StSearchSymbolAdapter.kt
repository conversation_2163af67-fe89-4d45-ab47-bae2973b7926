package cn.com.vau.signals.stsignal.adapter

import android.text.*
import android.text.style.ForegroundColorSpan
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * Filename: SearchSymbolAdapter.kt
 * Author: GG
 * Date: 2024/3/28
 * Description:
 */
class StSearchSymbolAdapter(var searchKey: String? = null) : BaseQuickAdapter<ShareProductData, BaseViewHolder>(R.layout.item_recycler_signal_search_symbol) {

    override fun convert(holder: BaseViewHolder, item: ShareProductData) {
        holder.setText(R.id.tvName, item.symbol)
            .setText(R.id.tvAllName, item.description.ifNull())
        searchKey?.let { key ->
            holder.getViewOrNull<AppCompatTextView>(R.id.tvName)?.let { textView ->
                set(textView, key, ContextCompat.getColor(context, R.color.ce35728))
                textView.typeface = ResourcesCompat.getFont(context, R.font.gilroy_regular)
            }
            holder.getViewOrNull<AppCompatTextView>(R.id.tvAllName)?.let { textView ->
                set(textView, key, ContextCompat.getColor(context, R.color.ce35728))
                textView.typeface = ResourcesCompat.getFont(context, R.font.gilroy_regular)
            }
        }
    }

    private fun set(textView: TextView, span: String, @ColorInt color: Int) {
        val index = textView.text.toString().lowercase().indexOf(span.lowercase())
        val spanString = SpannableString(textView.text)
        if (index != -1) {
            spanString.setSpan(ForegroundColorSpan(color), index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            textView.text = spanString
            textView.setTextColor(AttrResourceUtil.getColor(textView.context, R.attr.color_c1e1e1e_cebffffff))
            textView.highlightColor = ContextCompat.getColor(context, R.color.transparent)
        }
    }
}