package cn.com.vau.signals.stsignal.center.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.content.*
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.strategy.StProfileStrategiesBean
import cn.com.vau.databinding.ActivityStSignalCenterBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.signals.stsignal.activity.*
import cn.com.vau.signals.stsignal.center.fragment.*
import cn.com.vau.signals.stsignal.center.vm.StSignalCenterViewModel
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.*
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2024/04/02
 * desc：信号源中心
 */
class StSignalCenterActivity : BaseMvvmActivity<ActivityStSignalCenterBinding, StSignalCenterViewModel>() {

    private var tabIndex = 0 //当前显示的tab。上个页面传入，不传默认0
    private var strategiesFragTabIndex = 0 //信号源中心显示的tab。上个页面传入，不传默认0

    private var stStrategiesFrag: StStrategiesFragment? = null
    private var stCopierReviewFrag: StCopierReviewFragment? = null

    private var isPublicTrading: Boolean = false //是否公开交易

    override fun initParam(savedInstanceState: Bundle?) {
        tabIndex = intent.getIntExtra("tabIndex", 0)
        strategiesFragTabIndex = intent.getIntExtra("strategiesFragTabIndex", 0)
        isPublicTrading = intent.getBooleanExtra("isPublicTrading", isPublicTrading)
    }

    override fun initView() {
        if (isPublicTrading) { //如果公开交易进来的，弹框
            CenterActionWithIconDialog.Builder(this)
                .setLottieIcon(R.raw.lottie_dialog_ok)
                .setTitle(getString(R.string.congratulations)) //设置则展示标题，否则不展示
                .setContent(getString(R.string.publish_your_strategy_and_start_earning)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                .build()
                .showDialog()
        }

        initTabLayout()
    }

    override fun initData() {
        mViewModel.stProfileSummaryApi(isShowDialog = true) //总结卡片
        mViewModel.stProfileCopyPageTotalsApi() //Copier Review Tab上展示的数量，判断小红点是否显示
    }

    override fun createObserver() {
        //总结卡片summary
        mViewModel.summaryLiveData.observe(this) {
            setData()
        }
        //策略上下架成功后需要刷新头部卡片数据
        mViewModel.refreshStrategyListLiveData.observe(this) {
            mViewModel.stProfileSummaryApi(isShowDialog = true) //总结卡片
        }
        //判断Copier Review Tab小红点是否显示
        mViewModel.copyPageTotalsLiveData.observe(this) {
            //待审核数量大于0就显示红点，反之不显示
            updateCopierReviewTabRedDot(it?.pending.toIntCatching() > 0)
        }
        // 下拉刷新成功
        mViewModel.refreshFinishLiveData.observe(this){
            mBinding.mRefreshLayout.finishRefresh()
        }
    }

    override fun initListener() {
        //标题栏
        mBinding.mHeaderBar.setEndIconClickListener {
            // 跳转到Help Center
            openActivity(HelpCenterActivity::class.java)
        }.setEndIcon1ClickListener {
            // 跳转到信号源详情页
            StSignalDetailsActivity.open(this@StSignalCenterActivity, UserDataUtil.stUserId())
        }
        //跳转到个人Profile页
        mBinding.clPersonal.clickNoRepeat {
            openActivity(PersonalDetailsActivity::class.java)
        }
        //创建策略
        mBinding.tvCreateStrategy.clickNoRepeat {
            val mBean = mViewModel.summaryLiveData.value ?: return@clickNoRepeat
            if (mBean.allowToCreateStrategy == false) { //不是白名单，弹框提示
                CenterActionDialog.Builder(this)
                    .setContent(getString(R.string.the_feature_will_be_available_progressively)) //设置内容
                    .setSingleButton(true) //展示一个按钮，默认两个按钮
                    .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                    .build()
                    .showDialog()
                return@clickNoRepeat
            }
            val list = SpManager.getStrategyListDraft() ?: mutableListOf() //草稿策略
            val bean: StProfileStrategiesBean? = stStrategiesFrag?.getStrategyList() //上下架策略
            val publicCount = bean?.publicStrategies?.size ?: 0
            val delistedCount = bean?.delistedStrategies?.size ?: 0
            val draftCount = list.size
            if ((publicCount + delistedCount + draftCount) >= 100) { //上架策略、下架策略和草稿的总和上限为100
                CenterActionDialog.Builder(this)
                    .setContent(getString(R.string.the_upper_limit_private_you_the_limit)) //设置内容
                    .setSingleButton(true) //展示一个按钮，默认两个按钮
                    .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                    .build()
                    .showDialog()
                return@clickNoRepeat
            }
            //跳转到创建策略页面
            createResultLauncher.launch(
                StCreateAndEditStrategyActivity.createIntent(
                    this, StCreateAndEditStrategyActivity.TYPE_CREATE
                )
            )
            LogEventUtil.setLogEvent(BuryPointConstant.V348.CT_SP_CENTER_CREATE_STRATEGY_BTN_CLICK)
        }
        //下拉刷新
        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.stProfileSummaryApi(isShowDialog = false) //刷新总结卡片
            if (mBinding.viewPager2.currentItem == 0) {
                stStrategiesFrag?.refreshStrategyList() //刷新策略列表
            } else {
                mViewModel.stProfileCopyPageTotalsApi() //刷新Copier Review tab上数量
                stCopierReviewFrag?.refreshData() //刷新Copier Review 对应frag的数据
            }
        }
    }

    /**
     * 初始化tab
     */
    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        stStrategiesFrag = StStrategiesFragment.newInstance(strategiesFragTabIndex)
        fragments.add(stStrategiesFrag!!)
        stCopierReviewFrag = StCopierReviewFragment.newInstance()
        fragments.add(stCopierReviewFrag!!)

        val titleList = arrayListOf<String>()
        titleList.add(getString(R.string.strategies))
        titleList.add(getString(R.string.copier_review))

        mBinding.viewPager2.init(fragments, titleList, supportFragmentManager, this) {
            mBinding.tvCreateStrategy.isVisible = it == 0
        }
        mBinding.viewPager2.offscreenPageLimit = fragments.size
        mBinding.mTabLayout.setVp(mBinding.viewPager2, titleList, TabType.LINE_INDICATOR)
        //默认显示的tab
        mBinding.viewPager2.currentItem = tabIndex
    }

    @SuppressLint("SetTextI18n")
    private fun setData() {
        val mBean = mViewModel.summaryLiveData.value ?: return
        mBean.let {
            //头像
            ImageLoaderUtil.loadImage(this, it.avatar, mBinding.ivAvatar, R.mipmap.ic_launcher)
            //昵称
            mBinding.tvNick.text = it.nickname
            //id
            mBinding.tvProviderId.text = "${getString(R.string.provider_id)}：${it.stUserId}".arabicReverseTextByFlag("：")
            //上架策略数量/允许策略数量
            mBinding.tvStrategies.text = "${it.publicStrategyCount ?: "0"}/${it.maxStrategyCount ?: "0"}"
            //跟单者人数
            mBinding.tvCopiers.text = it.copiers ?: "0"

            EventBus.getDefault().post(DataEvent(NoticeConstants.STStrategy.REFRESH_STRATEGY_COUNT, mBean))
        }
    }

    /**
     * Copier Review Tab是否显示小红点
     */
    private fun updateCopierReviewTabRedDot(isShowDot: Boolean = false) {
        val viewDot = mBinding.mTabLayout.getChildAt(1)?.findViewById<View>(R.id.viewDot)
        viewDot?.isVisible = isShowDot
    }

    /**
     * 创建策略回调
     */
    private val createResultLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val type = result.data?.getIntExtra("type", 0) ?: 0
            when (type) {
                //上架成功，tab切换到上架列表，并刷新策略列表
                1 -> mViewModel.refreshStrategyListLiveData.value = 1
                //保存草稿成功，tab切换到草稿列表，并刷新草稿列表
                2 -> mViewModel.refreshStrategyListLiveData.value = 3
            }
        }
    }

    companion object {
        fun open(
            context: Context?, tabIndex: Int = 0, strategiesFragTabIndex: Int = 0, isPublicTrading: Boolean = false
        ) {
            context?.startActivity(Intent(context, StSignalCenterActivity::class.java).apply {
                putExtra("tabIndex", tabIndex)
                putExtra("strategiesFragTabIndex", strategiesFragTabIndex)
                putExtra("isPublicTrading", isPublicTrading)
            })
        }

        fun createIntent(
            context: Context, tabIndex: Int = 0, strategiesFragTabIndex: Int = 0, isPublicTrading: Boolean = false
        ) =
            Intent(context, StSignalCenterActivity::class.java).apply {
                putExtra("tabIndex", tabIndex)
                putExtra("strategiesFragTabIndex", strategiesFragTabIndex)
                putExtra("isPublicTrading", isPublicTrading) //是否公开交易
            }
    }
}