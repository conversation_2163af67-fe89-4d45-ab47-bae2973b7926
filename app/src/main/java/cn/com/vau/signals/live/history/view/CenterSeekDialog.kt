package cn.com.vau.signals.live.history.view

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import cn.com.vau.databinding.DialogCenterSeekBinding
import cn.com.vau.util.dp2px
import cn.com.vau.util.widget.dialog.base.*

@SuppressLint("ViewConstructor")
class CenterSeekDialog private constructor(activity: Activity) : CenterDialog<DialogCenterSeekBinding>(
    activity, DialogCenterSeekBinding::inflate,
    width = 200.dp2px(), height = 100.dp2px(), maxHeight = 100.dp2px(), maxWidth = 200.dp2px()
) {

    fun showSeekDialog(progress: String?) {

        mContentBinding.progressText.text = progress

        if (!isShowDialog()) {
            showDialog()
        }
    }

    class Builder(activity: Activity) : IBuilder<DialogCenterSeekBinding, Builder>(activity) {

        override fun createDialog(context: Context): IDialog<DialogCenterSeekBinding> {
            return CenterSeekDialog(activity)
        }

        override fun build(): CenterSeekDialog {
            return super.build() as CenterSeekDialog
        }
    }
}
