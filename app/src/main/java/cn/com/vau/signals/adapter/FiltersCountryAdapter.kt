package cn.com.vau.signals.adapter

import cn.com.vau.R
import cn.com.vau.data.discover.FiltersCountryObj
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.setBackground
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 30 20:48
 * @updateUser:
 * @updateDate: 2024 10月 30 20:48
 */
class FiltersCountryAdapter(var areaCode: String? = null) : BaseQuickAdapter<FiltersCountryObj, BaseViewHolder>(R.layout.item_filters_country) {
    override fun convert(holder: BaseViewHolder, item: FiltersCountryObj) {

        if (item.areaCode == areaCode) {
            holder.setTextColor(R.id.tvAll, AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            holder.setBackgroundColor(R.id.llAll, AttrResourceUtil.getColor(context, R.attr.color_c0a1e1e1e_c0affffff))
        } else {
            holder.setTextColor(R.id.tvAll, AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            holder.setBackground(R.id.llAll, null)
        }
        if (getItemPosition(item) != 0) {
            holder.setText(R.id.tvAll, item.areaName)
        } else {
            holder.setText(R.id.tvAll, context.getString(R.string.select_all))
        }
    }
}