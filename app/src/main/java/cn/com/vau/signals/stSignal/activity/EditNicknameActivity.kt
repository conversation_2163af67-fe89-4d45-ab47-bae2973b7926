package cn.com.vau.signals.stsignal.activity

import android.content.Context
import android.content.Intent
import android.text.InputFilter
import android.text.TextUtils
import android.view.MotionEvent
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.view.ClearAndHideEditText
import cn.com.vau.databinding.ActivityEditNicknameBinding
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.RegexUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.toIntCatching

/**
 * Filename: EditNickActivity.kt
 * Author: GG
 * Date: 2024/3/30
 * Description:
 */
class EditNicknameActivity : BaseMvvmBindingActivity<ActivityEditNicknameBinding>() {

    private val title: String by lazy { intent.getStringExtra(KEY_TITLE) ?: getString(R.string.edit_nickname) }
    private val nickname: String? by lazy { intent.getStringExtra(KEY_NICKNAME) }
    private val maxCount: String? by lazy { intent.getStringExtra(KEY_MAX_COUNT).ifNull("100") }

    override fun initView() {
        mBinding.mHeaderBar.setTitleText(title)
        nickname?.let {
            mBinding.etNickname.setText(it)
        }
        mBinding.etNickname.setHint(getString(R.string.enter_4_x_characters, maxCount))
        updateInputLength(nickname.ifNull())
        val maxLength = maxCount.toIntCatching()
        mBinding.etNickname.setFilter(InputFilter.LengthFilter(maxLength))
        mBinding.etNickname.doAfterTextChanged {
            updateInputLength(it.toString())
        }

        mBinding.tvSave.setOnClickListener {
            if (mBinding.etNickname.getText().length < 4) {
                ToastUtil.showToast(getString(R.string.enter_4_x_characters, maxCount))
                return@setOnClickListener
            }
            if (!RegexUtil.isValidText(mBinding.etNickname.getText())) {
                ToastUtil.showToast(getString(R.string.please_enter_letter_number_and_spaces_only))
                return@setOnClickListener
            }
            updateContent()
        }
        KeyboardUtil.registerSoftInputChangedListener(this) {
            if (it == 0) {
                mBinding.root.requestFocus()
                removeFocusBg(mBinding.etNickname, true)
            }
        }
    }

    private fun removeFocusBg(et: ClearAndHideEditText, isClear: Boolean = false) {
//        et.background = AppCompatResources.getDrawable(this, R.drawable.draw_shape_c0a1e1e1e_c262930_r10)
        if (isClear)
            et.clearFocus()
    }

    private fun updateInputLength(it: String?) {
        when {
            it == null || it.toString().length < 4 -> {
                mBinding.tvSave.setBackgroundResource(R.drawable.draw_shape_c1f1e1e1e_c1fffffff_r100)
                mBinding.tvSave.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff))
            }

            else -> {
                mBinding.tvSave.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
                mBinding.tvSave.setTextColor(AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e))
            }
        }
        it?.let {
            mBinding.tvNickLength.text = "${it.length}/$maxCount"
        }
    }

    private fun updateContent() {
        val intent = Intent()
        val tempNickName: String = mBinding.etNickname.getText().trim { it <= ' ' }
        if (!TextUtils.isEmpty(tempNickName)) {
            intent.putExtra(KEY_NICKNAME, mBinding.etNickname.getText().trim { it <= ' ' })
            setResult(Constants.REQUEST_CODE_NICKNAME, intent)
            finish()
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, mBinding.root, event)
        return super.onTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }

    companion object {

        private const val KEY_TITLE = "title"
        private const val KEY_MAX_COUNT = "max_count"
        const val KEY_NICKNAME = "nickname"

        fun createIntent(context: Context, title: String? = null, nickname: String? = null, maxCount: String = "100") =
            Intent(context, EditNicknameActivity::class.java).apply {
                putExtra(KEY_TITLE, title)
                putExtra(KEY_NICKNAME, nickname)
                putExtra(KEY_MAX_COUNT, maxCount)
            }
    }
}