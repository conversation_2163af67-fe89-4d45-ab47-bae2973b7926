package cn.com.vau.common.view

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.*
import cn.com.vau.util.ifNull

class SpanItemDecoration(private val space: Number) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val gridLayoutManager = parent.layoutManager as GridLayoutManager
        val spanCount = gridLayoutManager.spanCount
        val postion = parent.getChildLayoutPosition(view)
        val itemCount = parent.adapter?.itemCount.ifNull()

        val totalSpace: Float = space.toFloat() * (spanCount - 1) // 总共的padding值
        val eachSpace: Float = totalSpace / spanCount // 分配给每个item的padding值
        val column: Int = postion % spanCount // 列数
        val row: Int = postion / spanCount // 行数
        var left = 0f
        var right = 0f
        var top = 0f
        var bottom = 0f

//        val lp = view.layoutParams
//        lp.width = (parent.width - ((spanCount - 1) * space)) / spanCount
//        view.layoutParams = lp
        left = column * eachSpace / (spanCount - 1);
        right = eachSpace - left;
//        if (itemCount / spanCount == row) {
//            bottom = 0;
//        }
        outRect.set(left.toInt(), top.toInt(), right.toInt(), bottom.toInt())
    }
}