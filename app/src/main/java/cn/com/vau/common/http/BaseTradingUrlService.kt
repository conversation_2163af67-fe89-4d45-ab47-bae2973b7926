package cn.com.vau.common.http

import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.*
import cn.com.vau.data.pricealtert.ProduceAlterGroupListData
import cn.com.vau.data.trade.*
import cn.com.vau.history.data.*
import cn.com.vau.trade.bean.kchart.*
import com.google.gson.JsonObject
import okhttp3.RequestBody
import retrofit2.http.*

/**
 * 交易接口
 * Author: GG
 * Date: 2025/2/15
 * Description:
 */
interface BaseTradingUrlService {

    /**
     * 添加 或 修改 价格提醒的接口 ，添加时 不需要id
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("trade/product/addOrUpdatePriceWarn")
    suspend fun tradeProductAddOrUpdatePriceWarn(@Body body: JsonObject?): ApiResponse<Any?>

    /**
     * 获取价格提醒列表
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("trade/product/getPriceWarn")
    suspend fun tradeProductGetPriceWarn(@Body body: JsonObject?): ApiResponse<List<ProduceAlterGroupListData>>

    /**
     * 删除价格提醒
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("trade/product/deletePriceWarn")
    suspend fun tradeProductDeletePriceWarn(@Body body: JsonObject?): ApiResponse<Any?>

    /**
     * 启用或禁用价格提醒
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("trade/product/enableAndDisablePriceWarn")
    suspend fun tradeProductEnableAndDisablePriceWarn(@Body body: JsonObject?): ApiResponse<Any?>

    /**
     * 互抵平仓 -- 多品牌
     */
    @POST("trade/orders/hedgeClose/v2")
    suspend fun tradeOrdersHedgeCloseV2Api(@Body body: RequestBody?): BaseBean

    /**
     * 平仓历史--非跟单
     */
    @POST("trade/orders/list/closeHistory")
    suspend fun tradeOrdersListCloseHistory(@Body body: RequestBody?): TradeClosedHistoryBean?

    /**
     * 交易情绪
     */
    @POST("trade/order/tradeEmotion")
    suspend fun tradeOrderTradeEmotion(@Body body: RequestBody?): ApiResponse<ChartTradeEmotion>

    /**
     * 交易价格变化
     */
    @POST("trade/order/tradePriceChange")
    suspend fun tradeOrderTradePriceChange(@Body body: RequestBody?): ApiResponse<ChartPriceChange>

    /**
     * 获取产品详情属性--非跟单
     */
    @POST("trade/product/attr/v2")
    suspend fun tradeProductAttrV2Api(@Body body: RequestBody?): ApiResponse<ProductInfoBean>

    /**
     * 批量平仓--非跟单
     */
    @POST("trade/orders/batchClose/v2")
    suspend fun tradeOrdersBatchCloseV2Api(@Body body: RequestBody?): ApiResponse<BatchCloseBean>

    /**
     * 获取挂单列表
     */
    @POST("trade/order/pending/list")
    suspend fun tradeOrderPendingListApi(@Body body: RequestBody?): PendingOrdersBean?

    /**
     * 取消挂单
     */
    @POST("trade/orders/cancel")
    suspend fun tradeOrdersCancelApi(@Body body: RequestBody?): BaseBean?

    /**
     * 关闭订单/平仓
     */
    @POST("trade/orders/close")
    suspend fun tradeOrdersCloseApi(@Body body: RequestBody?): BaseBean?

    /**
     * 多品牌 mt4 登录 重新获取 token
     */
    @POST("trade/account/login")
    suspend fun tradeAccountLogin(@Body body: RequestBody?): ApiResponse<TradeAccountLoginBean.Data>

    /**
     * 反向平仓 -- 多品牌
     */
    @POST("trade/orders/reversePosition/v2")
    suspend fun tradeOrdersReversePositionV2Api(@Body body: RequestBody?): ApiResponse<ReverseOpenPositionBean.Data>

    /**
     * 非跟单改单
     */
    @POST("trade/orders/update")
    suspend fun tradeOrdersUpdateApi(@Body body: RequestBody?): BaseBean

    /**
     * 非跟单创建订单
     */
    @POST("trade/orders/open")
    suspend fun tradeOrdersOpenApi(@Body body: RequestBody?): NewOrderBean

    /**
     * 非跟单挂单
     */
    @POST("trade/orders/pending")
    suspend fun tradeOrdersPendingApi(@Body body: RequestBody?): NewOrderBean

    /**
     * 3.14	查询历史定单V2（抹亏券需求）
     */
    @POST("trade/orders/list/v2")
    suspend fun tradeOrdersListV2Api(@Body body: RequestBody): LossOrdersBean

     /**
     * 趋势图用
     */
    @POST("trade/product/trend")
    suspend fun tradeProductTrendApi(@Body body: RequestBody?): TrendBean?

    /**
     * 持仓历史
     */
    @POST("trade/orders/list/orderPage")
    suspend fun tradeOrdersListOrderPageApi(@Body body: RequestParamsWrapper): ApiResponse<HistoryListBaseBean<HistoryItemData>>

    /**
     * 持仓历史详情
     */
    @POST("trade/orders/orderDetails")
    suspend fun tradeOrdersListOrderPageDetailApi(@Body body: RequestParamsWrapper): ApiResponse<PositionDetailWrapper>

    /**
     * 资金历史
     */
    @POST("trade/orders/list/fundPage")
    suspend fun tradeOrdersListFundPageApi(@Body body: RequestParamsWrapper): ApiResponse<HistoryListBaseBean<FundingData>>

    /**
     * 非跟单-获取蜡烛图数据  K线历史数据
     */
    @POST("trade/order/historyMarkets")
    suspend fun klineHistoryMarketsApi(@Body body: RequestBody?): ApiResponse<KChartData>
}