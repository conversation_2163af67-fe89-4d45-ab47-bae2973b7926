package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.annotation.Keep
import androidx.core.view.isVisible
import androidx.viewpager2.widget.ViewPager2
import cn.com.vau.R
import cn.com.vau.databinding.DialogBottomNewVersionGuideBinding
import cn.com.vau.home.adapter.NewVersionGuideAdapter
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import cn.com.vau.util.widget.dialog.base.PopupConfig

@SuppressLint("ViewConstructor")
class BottomNewVersionGuideDialog(
    activity: Activity,
    private val dataList: MutableList<NewVersionGuideBean>?,
    config: PopupConfig<DialogBottomNewVersionGuideBinding>,
    private var callback: (() -> Unit)? = null,
) : BottomDialog<DialogBottomNewVersionGuideBinding>(
    activity,
    DialogBottomNewVersionGuideBinding::inflate,
    onCreateListener = config.onCreateListener,
    onDismissListener = config.onDismissListener
) {

    private val adapter by lazy { NewVersionGuideAdapter() }

    private var itemIndex: Int = 0

    override fun setContentView() {
        super.setContentView()
        mContentBinding.apply {
            mViewPager.adapter = adapter
            dataList?.let {
                adapter.setList(dataList)
                indicator.initIndicatorCount(dataList.size)
                indicator.isVisible = dataList.size > 1
                tvNext.setOnClickListener {
                    if (itemIndex == dataList.size - 1) {
                        callback?.invoke()
                        dismiss()
                    } else {
                        mViewPager.currentItem = itemIndex + 1
                    }
                }
                mViewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                    override fun onPageSelected(position: Int) {
                        itemIndex = position
                        indicator.changeIndicator(position)
                        tvNext.text = if (position == dataList.size - 1) context.getString(R.string.explore_now) else context.getString(R.string.next)
                    }
                })
            }
        }

    }

    class Builder(activity: Activity) : IBuilder<DialogBottomNewVersionGuideBinding, Builder>(activity) {

        private var dataList: MutableList<NewVersionGuideBean>? = null
        private var callback: (() -> Unit)? = null

        fun setDataList(dataList: MutableList<NewVersionGuideBean>?): Builder {
            this.dataList = dataList
            return this
        }

        fun setCallback(callback: (() -> Unit)?): Builder {
            this.callback = callback
            return this
        }

        override fun createDialog(context: Context): IDialog<DialogBottomNewVersionGuideBinding> {
            return BottomNewVersionGuideDialog(activity, dataList, config, callback)
        }

        override fun build(): BottomNewVersionGuideDialog {
            return super.build() as BottomNewVersionGuideDialog
        }
    }
}

@Keep
data class NewVersionGuideBean(
    val description: String? = "",
    val id: Int? = 0,
    val imgUrl: String? = "",
    val name: String? = "",
    val seq: Int? = 0
)