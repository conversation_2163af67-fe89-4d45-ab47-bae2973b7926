package cn.com.vau.common.view.share

import cn.com.vau.common.application.ForegroundCallbacks
import cn.com.vau.util.ScreenCaptureObserver

/**
 * Filename: ScreenCaptureForegroundListener
 * Author: GG
 * Date: 2025/6/12
 * Description:
 */
class ScreenCaptureForegroundListener : ForegroundCallbacks.Listener {
    /**
     * 屏幕截屏监听
     */
    private val screenCaptureObserver by lazy { ScreenCaptureObserver() }

    override fun onBecameForeground() {
        // 添加监听系统截屏
        screenCaptureObserver.stop()
        screenCaptureObserver.setOnScreenCaptureListener {
            ScreenCaptureObserver.isShareScreenCaptureDialogShow = true
            ShareHelper.screenshotShare()
        }
        if (ScreenCaptureObserver.isShareScreenCaptureDialogShow) {
            ShareHelper.sharePopup?.dismissDialog()
            ScreenCaptureObserver.isShareScreenCaptureDialogShow = false
        }
        screenCaptureObserver.start()
    }

    override fun onBecameBackground() {
        screenCaptureObserver.stop()
    }
}