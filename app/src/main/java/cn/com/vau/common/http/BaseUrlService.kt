package cn.com.vau.common.http

import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjBooleanBean
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.DataObjStringBeanNew
import cn.com.vau.data.DataObjStringData
import cn.com.vau.data.account.AccountHomeData
import cn.com.vau.data.account.AccountListBean
import cn.com.vau.data.account.AccountListFirstData
import cn.com.vau.data.account.AccountObjBean
import cn.com.vau.data.account.AsicQuestionData
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.data.account.AuthenticationStatusData
import cn.com.vau.data.account.BindEmailData
import cn.com.vau.data.account.ChangeTFAOtpData
import cn.com.vau.data.account.CheckPassKeyAnd2fa
import cn.com.vau.data.account.DeviceHistoryObj
import cn.com.vau.data.account.EmailBindObj
import cn.com.vau.data.account.KycAuditStatusData
import cn.com.vau.data.account.KycFunctionPermissionData
import cn.com.vau.data.account.KycGuidanceLevelData
import cn.com.vau.data.account.KycVerifyLevelData
import cn.com.vau.data.account.LoginDataBean
import cn.com.vau.data.account.MT4AccountTypeData
import cn.com.vau.data.account.MoreAboutYouData
import cn.com.vau.data.account.PasskeyBean
import cn.com.vau.data.account.PasskeyBeanList
import cn.com.vau.data.account.PersonalInfoData
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.data.account.PlatformTypeTitleDataObj
import cn.com.vau.data.account.RealAccountCacheData
import cn.com.vau.data.account.RegisterJsonData
import cn.com.vau.data.account.ResetAccountData
import cn.com.vau.data.account.SecurityStatusData
import cn.com.vau.data.account.SelectCountryCodeObjBean
import cn.com.vau.data.account.SelectCountryNumberData
import cn.com.vau.data.account.SumSubAccessTokenData
import cn.com.vau.data.account.SumSubJumpBean
import cn.com.vau.data.account.TFADeviceData
import cn.com.vau.data.account.TFAResultData
import cn.com.vau.data.account.TFASettingData
import cn.com.vau.data.account.TFAVerificationData
import cn.com.vau.data.account.UploadFileData
import cn.com.vau.data.account.UserLogsData
import cn.com.vau.data.account.VerificationCodeData
import cn.com.vau.data.account.VerificationResult
import cn.com.vau.data.depositcoupon.CouponExchangeData
import cn.com.vau.data.depositcoupon.CouponManagerData
import cn.com.vau.data.depositcoupon.CouponOutDateData
import cn.com.vau.data.depositcoupon.CurrencyTransformData
import cn.com.vau.data.depositcoupon.FundDetailRetryData
import cn.com.vau.data.depositcoupon.ManageFundsBean
import cn.com.vau.data.depositcoupon.ManageFundsDetailsBean
import cn.com.vau.data.depositcoupon.NeedH5WithdrawData
import cn.com.vau.data.depositcoupon.NeedUploadAddressProofBean
import cn.com.vau.data.depositcoupon.NeedUploadAddressProofData
import cn.com.vau.data.depositcoupon.NeedUploadIdProofData
import cn.com.vau.data.depositcoupon.QueryUserIsProclientData
import cn.com.vau.data.depositcoupon.TransferAccountListData
import cn.com.vau.data.depositcoupon.UserAccountData
import cn.com.vau.data.discover.CalendarData
import cn.com.vau.data.discover.ChartCalendarDataBean
import cn.com.vau.data.discover.EconomicCalendarDataBean
import cn.com.vau.data.discover.EventListData
import cn.com.vau.data.discover.FiltersCountryData
import cn.com.vau.data.discover.FxStreetBaseData
import cn.com.vau.data.discover.FxStreetBeanList
import cn.com.vau.data.discover.LiveHistoryData
import cn.com.vau.data.discover.LiveListData
import cn.com.vau.data.discover.NewsLetterDataBean
import cn.com.vau.data.discover.ProductItemDataBean
import cn.com.vau.data.discover.WebTVData
import cn.com.vau.data.ib.IBUrlData
import cn.com.vau.data.ib.InvitationsData
import cn.com.vau.data.init.AppVersionData
import cn.com.vau.data.init.BasicMyProfileData
import cn.com.vau.data.init.CollectDataData
import cn.com.vau.data.init.ImgAdvertInfoData
import cn.com.vau.data.init.ImgQueryData
import cn.com.vau.data.msg.CSAnswerData
import cn.com.vau.data.msg.CSConsultData
import cn.com.vau.data.msg.CSContactusData
import cn.com.vau.data.msg.CSQuestsData
import cn.com.vau.data.msg.ChannelKeyData
import cn.com.vau.data.msg.MsgInAppTypeData
import cn.com.vau.data.msg.NewNoticeData
import cn.com.vau.data.msg.NoticeSettingData
import cn.com.vau.data.profile.AuthConfigDataBean
import cn.com.vau.data.profile.LanguageDataBean
import cn.com.vau.data.profile.TelegramGetBotIdDataBean
import cn.com.vau.data.profile.UserSettingDataBean
import cn.com.vau.data.strategy.SelectAllPicBean
import cn.com.vau.data.trade.FreeOrdersBean
import cn.com.vau.data.trade.LeverageBean
import cn.com.vau.data.trade.ProductHotBean
import okhttp3.MultipartBody
import retrofit2.http.Body
import retrofit2.http.Field
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.Multipart
import retrofit2.http.POST
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query
import retrofit2.http.QueryMap

/**
 * 非交易接口
 * Author: GG
 * Date: 2025/2/15
 * Description:
 */
interface BaseUrlService {

    // 获取用户账户数据
    @FormUrlEncoded
    @POST("getUserAccountData")
    suspend fun getUserAccountData(@Field("userId") id: String): UserAccountData

    // 抹亏券使用
    @POST("usercoupon/useLossCoupon")
    suspend fun usercouponUseLossCoupon(@QueryMap map: HashMap<String, Any>): BaseBean

    // 用户添加日志
    @POST("userLogs/addLog")
    suspend fun userLogsAddLog(@Body body: MultipartBody?): UserLogsData

    /**
     * 用户系统设置
     */
    @POST("userset/itemset")
    suspend fun usersetItemsetApi(@QueryMap map: HashMap<String, Any>): ApiResponse<Any?>

    /**
     * 通过区号查询国家名称
     */
    @FormUrlEncoded
    @POST("selectCountryByPhoneCode")
    suspend fun selectCountryByPhoneCodeApi(@FieldMap map: HashMap<String, Any>): ApiResponse<SelectCountryCodeObjBean>

    /**
     * 查询可开通的账户类型(获取申请开通mt4账户号类型)
     */
    @POST("crm/getMt4AccountApplyType")
    suspend fun crmGetMt4AccountApplyTypeApi(@Query("token") token: String): ApiResponse<MT4AccountTypeData>

    /**
     * newsletter
     */
    @POST("newsletter/list")
    suspend fun newsletterList(@QueryMap map: HashMap<String, Any>): ApiResponse<NewsLetterDataBean>

    /**
     * newsletter 提交阅读量
     */
    @POST("newsletter/addrecord")
    suspend fun newsletterAddrecord(@QueryMap map: HashMap<String, Any>): BaseBean

    /**
     * 分析师观点列表
     */
    @POST("analyses/list")
    suspend fun analysesList(@QueryMap map: HashMap<String, Any>): ApiResponse<NewsLetterDataBean>

    /**
     * 24/7页面的新闻历史列表接口
     */
    @GET("news/getHistorylist")
    suspend fun newsGetHistorylist(
        @Query("startTime") startTime: String,
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int
    ): ApiResponse<FxStreetBeanList>

    /**
     * 24/7页面的新闻最新列表接口
     */
    @GET("news/getLatestlist")
    suspend fun newsGetLatestlist(): ApiResponse<List<FxStreetBaseData>>

    /**
     * webTV查询视频列表
     */
    @POST("webtv/list")
    suspend fun webtvList(@QueryMap map: HashMap<String, Any>): ApiResponse<WebTVData?>

    /**
     * webTV提交观看记录
     */
    @POST("webtv/addrecord")
    suspend fun webtvAddrecord(@QueryMap map: HashMap<String, String?>): BaseBean

    /**
     * K线页底部 【财经日历】 和 【分析】 vau
     */
    @POST("product/listRelatedItems")
    suspend fun productListRelatedItems(@QueryMap map: HashMap<String, String>): ApiResponse<ProductItemDataBean>

    /**
     * Passkey 列表
     *
     */
    @POST("passKey/list")
    suspend fun passKeyListApi(@QueryMap map: MutableMap<String, String>): ApiResponse<PasskeyBeanList>

    /**
     * Passkey 删除
     *
     */
    @POST("passKey/delete")
    suspend fun passKeyDeleteApi(@QueryMap map: MutableMap<String, String?>): ApiResponse<PasskeyBean>

    /**
     * Passkey 重命名
     *
     */
    @POST("passKey/updateDisplayName")
    suspend fun passKeyUpdateDisplayNameApi(@QueryMap map: MutableMap<String, String?>): ApiResponse<PasskeyBean>

    /**
     * Passkey 检查当前设备是否绑定了Passkey，和 2fa,如果绑定了Passkey 需返回JSONData
     *
     */
    @POST("passKey/hasSet")
    suspend fun passKeyHasSetApi(@QueryMap map: MutableMap<String, String>): ApiResponse<CheckPassKeyAnd2fa>

    /**
     * Passkey 验证Passkey，
     *
     */
    @POST("passKey/validate")
    suspend fun passkeyValidateApi(@QueryMap map: MutableMap<String, String>): ApiResponse<VerificationResult>

    /**
     * Passkey 获取注册Passkey的JSONData
     *
     */
    @POST("passKey/getRegisterData")
    suspend fun passkeyGetRegisterDataApi(@QueryMap map: MutableMap<String, String>?): ApiResponse<RegisterJsonData>

    /**
     * Passkey 回传Passkey创建结果
     *
     */
    @POST("passKey/saveRegister")
    suspend fun passKeySaveRegisterApi(@QueryMap map: MutableMap<String, String>?): ApiResponse<VerificationResult>

    /**
     * Passkey 邮箱验证
     *
     */
    @POST("passKey/validateEmailCode")
    suspend fun passKeyValidateEmailCodeApi(
        @Query("code") authCode: String?,
        @Query("token") token: String?
    ): ApiResponse<Any?>

    /**
     * Passkey 邮箱验证发送验证码
     *
     */
    @POST("passKey/sendEmailCode")
    suspend fun passKeySendEmailCodeApi(@Query("token") token: String?): ApiResponse<Any?>

    /**
     * 自选产品 移动
     */
    @POST("prod/upd")
    suspend fun prodUpd(@QueryMap map: HashMap<String, Any>): ApiResponse<Any>

    /**
     * 验证 2fa 码
     * 新增参数- bizType 业务类型
     * 1: 2fa换绑（2fa换绑校验原验证器code时添加）
     * 2: passkey
     * 3: 出入金
     * 4: 登录（预留，暂时未使用）
     */
    @POST("twoFactor/validateCode")
    suspend fun twoFactorValidateCodeApi(
        @Query("authCode") authCode: String?,
        @Query("token") token: String?,
        @Query("bizType") bizType: String?
    ): ApiResponse<Any>

    /**
     * 财经日历列表
     */
    @POST("finCalendar/list")
    suspend fun finCalendarList(@QueryMap map: HashMap<String, Any?>): ApiResponse<CalendarData>

    /**
     * 财经日历设置提醒
     */
    @GET("finCalendar/remind")
    suspend fun finCalendarRemind(@QueryMap map: HashMap<String, Any?>): BaseBean

    /**
     * 财经日历取消提醒
     */
    @GET("finCalendar/cancelRemind")
    suspend fun finCalendarCancelRemind(@QueryMap map: HashMap<String, Any?>): BaseBean

    /**
     * 财经日历详情
     */
    @GET("finCalendar/detail")
    suspend fun finCalendarDetail(@QueryMap map: HashMap<String, Any?>): ApiResponse<EconomicCalendarDataBean>

    /**
     * 财经日历图表数据
     */
    @GET("finCalendar/chartData")
    suspend fun finCalendarChartData(@QueryMap map: HashMap<String, Any?>): ApiResponse<ChartCalendarDataBean>

    /**
     * 财经日历获取国家地区
     */
    @GET("finCalendar/areas")
    suspend fun finCalendarAreas(): ApiResponse<FiltersCountryData>

    /**
     * 首页 --> 活动
     */
    @POST("events/getList")
    suspend fun eventsGetList(@QueryMap map: HashMap<String, Any>): ApiResponse<EventListData>

    /**
     * 首页弹窗、我的页面的banner、活动页面的banner 以及 直播弹窗 点击后的调用
     */
    @POST("events/addClicksCount")
    suspend fun eventsAddClicksCountApi(
        @Query("eventsId") eventsId: String,
        @Query("token") token: String?
    ): ApiResponse<Unit>

    /**
     * 是否跳转sumsub
     */
    @POST("kyc/sumsub/isJump")
    suspend fun sumsubIsJump(@QueryMap map: HashMap<String, Any?>): ApiResponse<SumSubJumpBean>

    /**
     * 绑定接口 ， 也用做验证码校验
     */
    @POST("thirdparty/bind/v2")
    suspend fun thirdPartyBindApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 获取验证码
     */
    @FormUrlEncoded
    @POST("getTelSms")
    suspend fun getTelSmsApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<ChangeTFAOtpData>

    /**
     * 登录接口 ， 也用做验证码校验
     */
    @FormUrlEncoded
    @POST("loginNew")
    suspend fun loginNewApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 绑定 2fa
     */
    @Headers("apiVer: v2")
    @POST("twoFactor/enable")
    suspend fun twoFactorEnableApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * 获取2fa配置
     */
    @GET("twoFactor/settings")
    suspend fun twoFactorSettingsApi(@Query("token") token: String?): ApiResponse<TFASettingData>

    /**
     * 检查用户密码是否正确
     * https://hytechc.atlassian.net/wiki/x/r4IHBg
     */
    @Headers("apiVer: v2")
    @POST("user/verifyPassword")
    suspend fun userVerifyPasswordApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * 判断用户是否更换设备
     */
    @POST("twoFactor/judgeUserChangeDevice")
    suspend fun twoFactorJudgeUserChangeDeviceApi(
        @Query("token") token: String?,
        @Query("bizType") bizType: Int?
    ): ApiResponse<TFADeviceData>

    /**
     * 2fa验证方式列表
     */
    @POST("twoFactor/getVerificationList")
    suspend fun twoFactorGetVerificationListApi(
        @Query("token") token: String?,
        @Query("bizType") bizType: Int?
    ): ApiResponse<TFAVerificationData>

    /**
     * 获取h5的ib页面链接
     */
    @POST("getIbpUrl")
    suspend fun getIbpUrl(@Query("token") token: String?): ApiResponse<IBUrlData>

    /**
     * 邮件验证码发送
     */
    @POST("email/sendEmailCode")
    suspend fun emailSendEmailCodeApi(
        @Query("token") token: String?,
        @Query("bizType") bizType: String?
    ): ApiResponse<ChangeTFAOtpData>

    /**
     * 校验邮件验证码
     */
    @POST("email/preValidateEmailCode")
    suspend fun emailPreValidateEmailCodeApi(
        @Query("token") token: String?,
        @Query("bizType") bizType: String?,
        @Query("code") code: String?,
        @Query("txId") txId: String?
    ): ApiResponse<ChangeTFAOtpData>

    /**
     * 修改两步验证绑定的接口
     * 该函数允许用户更改其两步验证(2FA)的绑定方式，例如从电子邮件更改为电话号码或反之
     * 它接受用户的身份令牌、新的联系方式（电子邮件交易ID或电话号码）以及当前和新的验证代码
     * 使用suspend标记表明这是一个协程支持的函数，适用于异步操作
     */
    @FormUrlEncoded
    @POST("twoFactor/changeBind")
    suspend fun twoFactorChangeBindApi(
        @FieldMap map: HashMap<String, Any?>
    ): ApiResponse<TFAResultData>

    /**
     * 手机验证码校验
     */
    @POST("sms/validateSmsCode")
    suspend fun smsValidateSmsCodeApi(
        // 验证码
        @Query("validateCode") validateCode: String?,
        // 验证类型
        @Query("code") code: String?,
        // 手机区号
        @Query("nationalCode") nationalCode: String?,
        // 手机号
        @Query("phoneNum") phoneNum: String?
    ): ApiResponse<ChangeTFAOtpData>

    /**
     * 请求跟单交易页面 广告位接口
     */
    @FormUrlEncoded
    @POST("img/advert-info")
    suspend fun imgAdvertInfo(@FieldMap map: HashMap<String, Any>): ApiResponse<ImgAdvertInfoData>

    /**
     * 查询用户设置语言列表
     */
    @GET("userset/language/list")
    suspend fun usersetLanguageListApi(): ApiResponse<LanguageDataBean>

    /**
     * 跟单的我的 和 跟单行情页面 - 用户基础信息
     */
    @POST("accountHome")
    suspend fun accountHomeApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<AccountHomeData.Data>

    /**
     * 非跟单 我的页面  查询用户信息
     */
    @POST("accountHomeBase")
    suspend fun accountHomeBaseApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<AccountHomeData.Data>

    /**
     * 非跟单 我的页面 用户其他信息
     */
    @POST("accountHomeOther")
    suspend fun accountHomeOtherApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<AccountHomeData.Data>

    /**
     * 开户-获取审核状态
     */
    @POST("getAuditStatus")
    suspend fun getAuditStatusApi(@Query("token") token: String): ApiResponse<AuditStatusData.Data>

    /**
     * 安全等级判断 -- profile
     */
    @GET("twoFactor/status")
    suspend fun twoFactorStatusApi(@Query("token") token: String): ApiResponse<SecurityStatusData.Data>

    /**
     * 查询用户是否可以跳转优惠券页面
     */
    @POST("queryUserIsProclient")
    suspend fun queryUserIsProclientApi(@QueryMap map: HashMap<String, Any>): ApiResponse<QueryUserIsProclientData.Data>

    /**
     * 检测用户是否需要更新版本
     */
    @POST("apk/checkVersion")
    suspend fun apkCheckVersionApi(@QueryMap map: HashMap<String, Any>): ApiResponse<AppVersionData>

    /**
     * 同步用户系统设置
     */
    @POST("userset/userSetItems")
    suspend fun usersetUserSetItemsApi(@QueryMap map: HashMap<String, Any>): ApiResponse<UserSettingDataBean>

    /**
     * 用户删除账户后，删除手机号
     */
    @FormUrlEncoded
    @POST("userset/deletePhone")
    suspend fun usersetDeletePhoneApi(@FieldMap map: HashMap<String, Any>): ApiResponse<Any?>

    /**
     * 出金是否需要上传身份地址证明(新) 非 asic 调用
     */
    @POST("addressproof/withrawNeedUploadIdPoaProof")
    suspend fun addressproofWithrawNeedUploadIdPoaProofApi(@Query("token") token: String?): ApiResponse<NeedUploadIdProofData.Data>

    /**
     * 出金查询是否需要上传地址证明 asic 调用
     */
    @POST("addressproof/withrawNeedUploadAddressProof")
    suspend fun addressproofWithrawNeedUploadAddressProofApi(@Query("token") token: String?): ApiResponse<NeedUploadAddressProofData>

    /**
     * 检查是否跳转到H5出金
     */
    @POST("fund/isH5Withdraw")
    suspend fun fundIsH5WithdrawApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<NeedH5WithdrawData>

    /**
     * telegram-获取botId
     */
    @POST("telegram/getBotId")
    suspend fun telegramGetBotIdApi(): ApiResponse<TelegramGetBotIdDataBean>

    /**
     * 三方登录
     */
    @Headers("apiVer: v2")
    @POST("thirdparty/login")
    suspend fun thirdpartyLoginApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 消息列表名称和未读消息数量
     */
    @POST("msg/in-app/type")
    suspend fun msgInAppTypeApi(@Query("token") token: String?): ApiResponse<MsgInAppTypeData>

    /**
     * 消息列表 GET
     */
    @GET("msg/in-app/list")
    suspend fun msgInAppListApi(
        @Query("token") token: String?,
        @Query("type") type: String?,
        @Query("page") page: Int,
        @Query("size") size: Int
    ): ApiResponse<NewNoticeData>

    /**
     * 消息 一键全读
     */
    @POST("msg/in-app/read/all")
    suspend fun msgInAppReadAllApi(@Query("token") token: String?): ApiResponse<NewNoticeData>

    /**
     * 消息 一條消息变成已读
     */
    @POST("msg/in-app/read/{id}")
    suspend fun msgInAppReadApi(
        @Path("id") id: String?,
        @Query("token") token: String?
    ): ApiResponse<NewNoticeData>

    /**
     * 获取消息设置状态
     */
    @GET("msg/in-app/settings/v1")
    suspend fun msgInAppSettingsV1Api(@Query("token") token: String): ApiResponse<NoticeSettingData>

    /**
     * 更新消息设置状态
     */
    @POST("msg/in-app/settings/update/v1")
    suspend fun msgInAppSettingsUpdateV1Api(
        @Query("token") token: String,
        @Query("code") code: String?,
        @Query("value") value: String
    ): ApiResponse<Any>

    /**
     * 资金管理查询
     */
    @POST("fund/index")
    suspend fun fundIndex(@QueryMap map: HashMap<String, Any>): ManageFundsBean?

    /**
     * 出金查询是否需要上传地址证明
     */
    @POST("addressproof/withrawNeedUploadAddressProof")
    suspend fun addressproofWithrawNeedUploadAddressProofApi(@QueryMap map: HashMap<String, Any>): NeedUploadAddressProofBean?

    /**
     * 出金是否需要上传身份地址证明(新)
     */
    @POST("addressproof/withrawNeedUploadIdPoaProof")
    suspend fun addressproofWithrawNeedUploadIdPoaProofApi(@QueryMap map: HashMap<String, Any>): NeedUploadIdProofData?

    /**
     * 获取live账号列表
     */
    @Headers("apiVer: v3")
    @POST("getAccounts")
    suspend fun getAccountsApi(@Query("token") token: String): ApiResponse<AccountListBean>

    /**
     * 获取头像列表 -- 创建新策略 || 编辑策略
     */
    @POST("selectAllPic/v1")
    suspend fun selectAllPicApi(): ApiResponse<SelectAllPicBean.Data>

    /**
     * 文件上传
     */
    @POST("file/fileUpload")
    suspend fun fileFileUploadApi(@Body body: MultipartBody?): ApiResponse<UploadFileData>

    /**
     * 获取交易平台的数据 如 mt4、mt5等
     */
    @Headers("apiVer: v1")
    @POST("getAccountTypeTitle")
    suspend fun getAccountTypeTitleApi(): ApiResponse<PlatformTypeTitleDataObj>

    /**
     * 获取交易平台的 货币类型
     */
    @POST("getPlatFormDemoAccountTypeCurrency")
    suspend fun getPlatFormAccountTypeCurrencyApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<PlatFormAccountData.Data>

    /**
     * 新增/重置 vts demo
     */
    @Headers("apiVer: v1")
    @POST("account/demo/editAccount")
    suspend fun accountDemoEditAccountApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<ResetAccountData>

    /**
     * h5 请求post 接口
     */
    @FormUrlEncoded
    @POST("getRefereeInfo")
    suspend fun getRefereeInfo(@FieldMap map: HashMap<String, Any>): ApiResponse<InvitationsData>

    /**
     * 查询热门搜索产品
     */
    @POST("product/hot")
    suspend fun productHot(@QueryMap map: HashMap<String, Any>): ProductHotBean?

    /**
     * 搜索内容发送服务器
     */
    @POST("product/record")
    suspend fun productRecord(@QueryMap map: HashMap<String, Any>): BaseBean?

    /**
     * 获取客服咨询分类
     */
    @POST("consult/items")
    suspend fun consultItems(@QueryMap map: HashMap<String, Any>): ApiResponse<CSConsultData>

    /**
     * 获取Zendesk初始化用的ChannelKey
     */
    @GET("customerService/getCustomerServiceDetail")
    suspend fun customerServiceGetCustomerServiceDetail(): ApiResponse<ChannelKeyData>

    /**
     * 获取客服问题列表
     */
    @POST("consult/quests")
    suspend fun consultQuests(@QueryMap map: HashMap<String, String>): ApiResponse<CSQuestsData>

    /**
     * 获取客服问题答案
     */
    @POST("consult/answer")
    suspend fun consultAnswer(@QueryMap map: HashMap<String, String>): ApiResponse<CSAnswerData>

    /**
     * 获取SumSub SDK Token
     */
    @POST("kyc/sumsubAccessToken")
    suspend fun kycSumsubAccessToken(@QueryMap map: HashMap<String, Any>): ApiResponse<SumSubAccessTokenData>

    /**
     * 获取下拉列表
     */
    @Headers("apiVer: v1")
    @GET("getAccountSelect")
    suspend fun getAccountSelectApi(@QueryMap map: HashMap<String, String>): ApiResponse<MoreAboutYouData>

    /**
     * 开通真实账户校验邮箱
     */
    @FormUrlEncoded
    @POST("emailIsExist")
    suspend fun emailIsExistApi(@FieldMap map: HashMap<String, Any>): ApiResponse<RealAccountCacheData>

    /**
     * 获取缓存信息
     */
    @Headers("apiVer: v3")
    @FormUrlEncoded
    @POST("getData")
    suspend fun getDataApi(@FieldMap map: HashMap<String, Any>): ApiResponse<RealAccountCacheData>

    /**
     * 保存缓存信息
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("process")
    suspend fun processApi(@FieldMap map: HashMap<String, Any>): ApiResponse<RealAccountCacheData>

    /**
     * ASIC问卷限制次数
     */
    @FormUrlEncoded
    @POST("limitNumberOfAnswers")
    suspend fun limitNumberOfAnswers(@Field("userId") userId: String?): ApiResponse<AsicQuestionData>

    /**
     * 获取账户列表第一步
     */
    @POST("queryAccountList")
    suspend fun queryAccountList(@QueryMap map: HashMap<String, Any>): ApiResponse<AccountListFirstData>

    /**
     * Discover页广告位 点击关闭广告位后调用的
     */
    @FormUrlEncoded
    @POST("img/close")
    suspend fun imgClose(@FieldMap map: HashMap<String, Any>): ApiResponse<DataObjStringBean.Data>

    /**
     * 获取所有国家手机区号
     */
    @POST("selectCountryNumberClassifyScreening")
    suspend fun selectCountryNumberClassifyScreeningApi(): ApiResponse<SelectCountryNumberData>

    /**
     * kyc-用户注册（demo注册）
     */
    @FormUrlEncoded
    @POST("app/user/register")
    suspend fun appUserRegisterApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * kyc-三方登录触发的注册
     */
    @FormUrlEncoded
    @POST("thirdparty/user/register")
    suspend fun thirdPartyUserRegisterApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 获取注册验证码
     */
    @FormUrlEncoded
    @POST("getTelRegisterSms")
    suspend fun getTelRegisterSmsApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<VerificationCodeData.ObjBean>

    /**
     * 校验注册验证码
     */
    @FormUrlEncoded
    @POST("sms/validateSmsRegisterCode")
    suspend fun smsValidateSmsRegisterCodeApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<Any?>

    /**
     * 校验邮箱是否存在
     */
    @FormUrlEncoded
    @POST("isExistEmail")
    suspend fun isExistEmailApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<RealAccountCacheData>

    /**
     * 注册账号
     */
    @FormUrlEncoded
    @POST("registerNew")
    suspend fun registerNewApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 注册时邮箱已存在，进行绑定邮箱
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("emailbindingPhone")
    suspend fun emailBindingPhoneApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 三方登录触发的注册
     */
    @Headers("apiVer: v3")
    @FormUrlEncoded
    @POST("thirdparty/register")
    suspend fun thirdpartyRegister(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * 三方登录触发注册时绑定邮箱
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("thirdparty/bind")
    suspend fun thirdpartyBindApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<LoginDataBean>

    /**
     * kyc认证中心
     */
    @POST("user/myVerificationCenter")
    suspend fun userMyVerificationCenterApi(): ApiResponse<KycAuditStatusData>

    /**
     * 获取kyc认证等级
     */
    @POST("user/query-user-level")
    suspend fun userQueryUserLevelApi(): ApiResponse<KycVerifyLevelData>

    /**
     * 引导客户升级等级接口
     * @param nextLevel 期望等级 如果不传，默认引导达到主流程最高等级
     */
    @POST("user/guidance-level")
    suspend fun userGuidanceLevel(@Query("nextLevel") nextLevel: Int): ApiResponse<KycGuidanceLevelData>

    /**
     * 查询个人信息
     */
    @POST("userform/getUserForum")
    suspend fun userformGetUserForumApi(@Query("token") token: String?): ApiResponse<PersonalInfoData>

    /**
     * 更新个人信息/不包含图片上传
     */
    @FormUrlEncoded
    @POST("userform/update")
    suspend fun userformUpdateApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<PersonalInfoData>

    /**
     * telegram-绑定
     */
    @POST("telegram/binding")
    suspend fun telegramBindingApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * telegram-解绑
     */
    @POST("telegram/unbinding")
    suspend fun telegramUnbindingApi(@Query("token") token: String?): ApiResponse<Any>

    /**
     * 更新个人信息/图片上传
     */
    @Multipart
    @POST("userform/update")
    suspend fun userformUpdateApi(
        @Part file: MultipartBody.Part,
        @QueryMap map: HashMap<String, Any?>
    ): ApiResponse<PersonalInfoData>

    /**
     * 绑定邮箱
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("bindingEmail")
    suspend fun bindEmailUserApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<BindEmailData>

    /**
     * kyc-发送邮箱验证码（公共接口）
     */
    @FormUrlEncoded
    @POST("email/sendEmailCode")
    suspend fun emailSendEmailCodeApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<ChangeTFAOtpData>

    /**
     * kyc-验证邮件验证码（公共接口）
     */
    @FormUrlEncoded
    @POST("email/preValidateEmailCode")
    suspend fun emailPreValidateEmailCodeApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<ChangeTFAOtpData>

    /**
     * 获取用户账户列表用于发送邮件
     */
    @Headers("apiVer: v1")
    @POST("queryAccountListOnSendEmail")
    suspend fun queryAccountListOnSendEmailApi(@Query("token") token: String?): ApiResponse<AccountObjBean>

    /**
     * 判断用户是否设置过资金安全密码
     */
    @POST("userset/hasfundpwd")
    suspend fun usersetHasfundpwdApi(@Query("userToken") token: String?): ApiResponse<DataObjStringBean.Data>

    /**
     * 发送账户邮件
     */
    @POST("sendAccountEmailToCrm")
    suspend fun sendAccountEmailToCrmApi(
        @Query("token") token: String?,
        @Query("account") account: String?,
    ): ApiResponse<AccountObjBean>

    /**
     * 获取用户是否同意收集信息的接口
     */
    @GET("user/collectData/display")
    suspend fun userCollectDataDisplayApi(@Query("userToken") userToken: String): ApiResponse<CollectDataData>

    /**
     * 用户同意开启Firebase数据收集状态变更
     */
    @POST("user/collectData/switch")
    suspend fun userCollectDataSwitchApi(@Query("userToken") userToken: String): ApiResponse<CollectDataData>

    /**
     * 获取用户的基本信息， 邮箱、手机号等以及验证状态
     */
    @POST("basicMyProfileInfo")
    suspend fun basicMyProfileInfoApi(@Query("userToken") userToken: String): ApiResponse<BasicMyProfileData>

    /**
     * 判断用户是否有对应功能权限
     */
    @FormUrlEncoded
    @POST("user/has-function-permission")
    suspend fun userHasFunctionPermission(@FieldMap map: HashMap<String, Any?>): ApiResponse<KycFunctionPermissionData>

    /**
     * 出金限制横幅
     */
    @FormUrlEncoded
    @POST("fund/withdrawRestrictionMessage")
    suspend fun fundWithdrawRestrictionMessageApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<DataObjStringBean.Data>

    /**
     * kyc-获取安全中心配置
     */
    @FormUrlEncoded
    @POST("get-auth-config")
    suspend fun getAuthConfigApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<AuthConfigDataBean>

    /**
     * 修改密码
     */
    @FormUrlEncoded
    @POST("updatePassword")
    suspend fun updatePasswordApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * 我的页面 获取kyc的验证等级 用于显示认证状态
     */
    @POST("user/authenticationstatus")
    suspend fun userAuthenticationstatusApi(): ApiResponse<AuthenticationStatusData>

    /**
     * kyc绑定、认证、修改手机号
     */
    @FormUrlEncoded
    @POST("updatePhone")
    suspend fun updatePhoneApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * kyc绑定、认证、修改邮箱
     */
    @FormUrlEncoded
    @POST("updateUserEmail")
    suspend fun updateUserEmailApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<EmailBindObj>

    /**
     * 新增或修改资金安全密码
     */
    @FormUrlEncoded
    @Headers("apiVer: v2")
    @POST("userset/upfundpwd")
    suspend fun usersetUpfundpwdApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * 忘记资金安全密码
     */
    @FormUrlEncoded
    @Headers("apiVer: v2")
    @POST("userset/forgetSafePwd")
    suspend fun usersetForgetSafePwdApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<Any>

    /**
     * 修改邮箱时验证登录密码
     */
    @FormUrlEncoded
    @POST("user/check-user-password")
    suspend fun userCheckUserPasswordApi(@FieldMap map: HashMap<String, Any?>): ApiResponse<DataObjBooleanBean.Data>

    /**
     * 获取客服联系我们
     */
    @POST("consult/contactus")
    suspend fun consultContactus(@Query("userToken") userToken: String): ApiResponse<CSContactusData>

    /**
     * 用户设备历史
     */
    @Headers("apiVer: v1")
    @POST("userDevice/getHistory")
    suspend fun userDeviceGetHistory(@Query("token") token: String?): ApiResponse<DeviceHistoryObj?>

    /**
     * Splash
     */
    @FormUrlEncoded
    @POST("img/query")
    suspend fun imgQuery(@FieldMap map: HashMap<String, Any>): ApiResponse<ImgQueryData>

    /**
     * 查询用户是否有过期的现金券
     */
    @POST("invite/coupon")
    suspend fun inviteCoupon(@Query("userId") userId: String): ApiResponse<CouponOutDateData>

    /**
     * 用户弹出过期现金券, 点击确定
     */
    @POST("invite/coupon/agree")
    suspend fun inviteCouponAgree(@QueryMap map: HashMap<String, Any>): ApiResponse<*>

    /**
     * 兑换优惠券
     */
    @POST("usercoupon/exchange")
    suspend fun usercouponExchange(@QueryMap map: HashMap<String, Any>): ApiResponse<CouponExchangeData>

    /**
     * 获取已过期/已使用优惠券列表
     */
    @FormUrlEncoded
    @POST("usercoupon/usedOrOut")
    suspend fun usercouponUsedOrOut(@FieldMap map: HashMap<String, Any>): ApiResponse<CouponManagerData>

    /**
     * 获取优惠券列表
     */
    @FormUrlEncoded
    @POST("usercoupon/list")
    suspend fun usercouponList(@FieldMap map: HashMap<String, Any>): ApiResponse<CouponManagerData>

    /**
     * 释放优惠券
     */
    @POST("usercoupon/releaseCoupon")
    suspend fun usercouponReleaseCoupon(@QueryMap map: HashMap<String, Any>): ApiResponse<*>

    /**
     * 激活coupon
     */
    @POST("usercoupon/activateCoupon")
    suspend fun usercouponActivateCoupon(@QueryMap map: HashMap<String, Any>): ApiResponse<*>

    /**
     * 多币种金额转换
     */
    @POST("currencyTransform")
    suspend fun currencyTransform(@QueryMap map: HashMap<String, Any>): ApiResponse<CurrencyTransformData.Data>

    /**
     * 获取转账列表
     */
    @POST("crm/member_mt4AccountId_list")
    suspend fun crmMemberMt4AccountIdList(@QueryMap map: HashMap<String, String>): ApiResponse<TransferAccountListData>

    /**
     * 现金券提现
     */
    @POST("invite/withdraw")
    suspend fun inviteWithdraw(@QueryMap map: HashMap<String, Any>): ApiResponse<*>

    /**
     * 获取最新直播列表
     */
    @GET("getLiveStreamList/v1")
    suspend fun getLiveStreamList(@Query("userId") userId: String): ApiResponse<LiveListData>

    /**
     * 获取历史直播列表
     */
    @POST("getHistoryAwsLiveStreamListPage/v1")
    suspend fun getHistoryAwsLiveStreamListPage(
        @Query("userId") userId: String,
        @Query("curPage") curPage: Int,
        @Query("offset") offset: Int
    ): ApiResponse<LiveHistoryData>

    /**
     * 账户余额检测重置状态
     */
    @POST("accountBalance/checkReset")
    suspend fun accountBalanceCheckReset(@QueryMap map: HashMap<String, Any>): DataObjBooleanBean?

    /**
     * 账户余额重置清零
     */
    @POST("accountBalance/negativeReset")
    suspend fun accountBalanceNegativeReset(@QueryMap map: HashMap<String, Any>): BaseBean?

    /**
     * 查询用户杠杆数据
     */
    @POST("crm/leverage")
    suspend fun crmLeverageApi(@QueryMap map: HashMap<String, Any>): ApiResponse<LeverageBean>

    /**
     * 修改杠杆
     */
    @POST("crm/leveragemodify")
    suspend fun crmLeveragemodifyApi(@QueryMap map: HashMap<String, String>): ApiResponse<*>
    /**
     * 查询是否有免费订单
     */
    @POST("stockActivity/stockListDetail")
    suspend fun stockActivityStockListDetail(@QueryMap map: HashMap<String, Any>): FreeOrdersBean


    /**
     * 直播\待直播\录播总数量
     */
    @GET("selectVideoCount/v1")
    suspend fun selectVideoCountApi(@Query("userId") userId: String): ApiResponse<DataObjStringData>

    /**
     * 重试按钮 -- 入金详情
     */
    @Headers("apiVer: v1")
    @POST("fund/fundDetailsRetry")
    suspend fun fundFundDetailsRetryApi(@QueryMap map: HashMap<String, Any>): ApiResponse<FundDetailRetryData>

    /**
     * 资金管理明细入金详情
     */
    @POST("fund/moneyInDetail")
    suspend fun fundMoneyInDetailApi(@QueryMap map: HashMap<String, Any>): ApiResponse<ManageFundsDetailsBean>

    /**
     * 资金管理明细出金详情
     */
    @POST("fund/withdrawDetail")
    suspend fun fundWithdrawDetailApi(@QueryMap map: HashMap<String, Any>): ApiResponse<ManageFundsDetailsBean>

    /**
     * 撤回出金功能
     */
    @POST("fund/cancelWithdrawalOrder")
    suspend fun fundCancelWithdrawalOrderApi(@QueryMap map: HashMap<String, Any>): ApiResponse<DataObjStringBeanNew>
}