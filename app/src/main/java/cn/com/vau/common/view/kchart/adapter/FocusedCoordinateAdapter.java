package cn.com.vau.common.view.kchart.adapter;

import java.util.List;

import cn.com.vau.common.view.kchart.viewbeans.Coordinates;
import cn.com.vau.common.view.kchart.viewbeans.ViewContainer;
import cn.com.vau.data.init.ShareProductData;
import cn.com.vau.util.ExpandKt;
import cn.com.vau.util.TmpRecordNaNExBean;

public class FocusedCoordinateAdapter extends Coordinates.CoordinateScaleAdapter {

    public FocusedCoordinateAdapter(ShareProductData data){
        this.data = data;
    }

    private int mKeepNums = 3;
    private ShareProductData data;  // 为定位NaN问题后台数据

    @Override
    public String getYLeftScaleString(List dataList, int drawPointIndex, int showPointNums, int scaleIndex, int totalYScaleNum) {

        String scale = "0";
        if (mChartView == null) return scale;

        ViewContainer viewContainer = mChartView.getFocusedView();

        float[] extreme = viewContainer.calculateExtremeY();

        float min = extreme[0];
        float max = extreme[1];

        float decrease = (totalYScaleNum - 1) == 0 ? 0 : (max - min) / (totalYScaleNum - 1);
        scale = (max - (decrease * scaleIndex)) + "";
        TmpRecordNaNExBean tmpBean = new TmpRecordNaNExBean();
        String content = "scale="+scale+",max="+max+",min =" + min + ",decrease="+decrease+",scaleIndex="+scaleIndex;
        tmpBean.setContent(content);
        //return ExpandKt.numFormat(scale, mKeepNums, false);//原来的代码
        return ExpandKt.numFormat_tmp(scale, mKeepNums, false, tmpBean);//临时代码（新增tmpBean参数，目的是超找定位线上问题）
    }

    @Override
    public String getYRightScaleString(List dataList, int drawPointIndex, int showPointNums, int scaleIndex, int totalYScaleNum) {
        String scale = "0";
        if (mChartView == null || mChartView.getFocusedView() == null) return scale;

        ViewContainer viewContainer = mChartView.getFocusedView();
        float[] extreme = viewContainer.calculateExtremeY();

        float min = extreme[0];
        float max = extreme[1];

        float decrease = (totalYScaleNum - 1) == 0 ? 0 : (max - min) / (totalYScaleNum - 1);
        scale = (max - (decrease * scaleIndex)) + "";

        TmpRecordNaNExBean tmpBean = new TmpRecordNaNExBean();
        tmpBean.setData(data);
        String content = "scale="+scale+",max="+max+",min =" + min + ",decrease="+decrease+",scaleIndex="+scaleIndex;
        tmpBean.setContent(content);
        //return ExpandKt.numFormat(scale, mKeepNums, false);//原来的代码
        return ExpandKt.numFormat_tmp(scale, mKeepNums, false, tmpBean);//临时代码（新增tmpBean参数，目的是超找定位线上问题）
    }

    @Override
    public String getXBottomScaleString(List dataList, int drawPointIndex, int showPointNums, int scaleIndex, int totalXScaleNum) {
        return "";
    }

    public void setKeepNums(int keepNums) {
        mKeepNums = keepNums;
    }

}
