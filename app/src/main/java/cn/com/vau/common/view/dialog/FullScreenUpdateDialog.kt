package cn.com.vau.common.view.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.View
import cn.com.vau.databinding.DialogFullScreenUpdateBinding
import cn.com.vau.util.widget.dialog.base.*

/**
 * Created by roy on 2018/3/20 0020.
 */
@SuppressLint("ViewConstructor")
class FullScreenUpdateDialog private constructor(activity: Activity) : FullScreenDialog<DialogFullScreenUpdateBinding>(activity, DialogFullScreenUpdateBinding::inflate) {
    private var confirmListener: (() -> Unit)? = null
    private var cancelListener: (() -> Unit)? = null
    private var updateVersionName: String? = null
    private var updateContent: String? = null
    private var forceFlag = 0

    override fun setContentView() {
        super.setContentView()
        initData()
        initListener()
    }

    private fun initData() {
        mContentBinding.tvVersionName.text = "V $updateVersionName"
        mContentBinding.tvContent.text = updateContent
        mContentBinding.tvCancel.visibility = if (forceFlag == 1) View.GONE else View.VISIBLE
    }

    private fun initListener() {
        mContentBinding.tvCancel.setOnClickListener {
            cancelListener?.invoke()
            dismiss()
        }
        mContentBinding.tvUpdate.setOnClickListener {
            confirmListener?.invoke()
        }
    }

    fun setInfo(updateVersionName: String?, updateContetn: String?, forceFlag: Int): FullScreenUpdateDialog {
        this.updateVersionName = updateVersionName
        this.updateContent = updateContetn
        this.forceFlag = forceFlag
        initData()
        return this
    }

    override fun onBackPressed(): Boolean {
        return true
    }

    fun setConfirmListener(confirm: (() -> Unit)? = null): FullScreenUpdateDialog {
        this.confirmListener = confirm
        return this
    }

    fun setCancelListener(cancel: (() -> Unit)? = null): FullScreenUpdateDialog {
        this.cancelListener = cancel
        return this
    }

    class Builder(activity: Activity) : IBuilder<DialogFullScreenUpdateBinding, Builder>(activity) {

        override fun createDialog(context: Context): IDialog<DialogFullScreenUpdateBinding> {
            return FullScreenUpdateDialog(activity)
        }

        override fun build(): FullScreenUpdateDialog {
            setDismissTouchOutside(false)
            setTouchThrough(false)
            setEnableDrag(false)
            setDismissOnBackPressed(false)
            return super.build() as FullScreenUpdateDialog
        }
    }
}