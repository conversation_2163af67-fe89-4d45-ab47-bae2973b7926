package cn.com.vau.common.view.camera;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Paint.Style;
import android.graphics.PixelFormat;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.Display;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;

public class MaskSurfaceView extends FrameLayout {

    private MSurfaceView surfaceView;
    private MaskView imageView;
    private int width;
    private int height;
    private int maskWidth;
    private int maskHeight;
    private int screenWidth;
    private int screenHeight;

    public MaskSurfaceView(Context context, AttributeSet attrs) {
        super(context, attrs);

        surfaceView = new MSurfaceView(context);
        imageView = new MaskView(context);
        this.addView(surfaceView, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);
        this.addView(imageView, LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT);

        Display display = ((WindowManager) context.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
        screenHeight = display.getHeight();
        screenWidth = display.getWidth();
        CameraHelper.getInstance().setMaskSurfaceView(this);
    }

    public void setMaskSize(Integer width, Integer height) {
        maskHeight = height;
        maskWidth = width;
    }

    public int[] getMaskSize() {
        return new MaskSize().size;
    }

    private class MSurfaceView extends SurfaceView implements SurfaceHolder.Callback {
        private SurfaceHolder holder;

        public MSurfaceView(Context context) {
            super(context);
            this.holder = this.getHolder();
            //translucent半透明 transparent透明
            this.holder.setFormat(PixelFormat.TRANSPARENT);
            this.holder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
            this.holder.addCallback(this);
        }

        @Override
        public void surfaceChanged(SurfaceHolder holder, int format, int w, int h) {
            width = w;
            height = h;
            CameraHelper.getInstance().openCamera(holder, format, width, height, screenWidth, screenHeight);
        }

        @Override
        public void surfaceCreated(SurfaceHolder holder) {

        }

        @Override
        public void surfaceDestroyed(SurfaceHolder holder) {
            CameraHelper.getInstance().releaseCamera();
        }
    }

    private class MaskSize {
        private int[] size;

        private MaskSize() {
            this.size = new int[]{maskWidth, maskHeight, width, height};
        }
    }

    private class MaskView extends View {
        private Paint linePaint;
        private Paint rectPaint;

        public MaskView(Context context) {
            super(context);

            //			绘制中间透明区域矩形边界的Paint
            linePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            linePaint.setColor(Color.WHITE);
            linePaint.setStyle(Style.STROKE);
            linePaint.setStrokeWidth(5f);
            //            linePaint.setAlpha(80);

            //绘制四周矩形阴影区域
            rectPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
            rectPaint.setColor(0xa0000000);
            rectPaint.setStrokeWidth(3);
        }
        private Rect rect;
        @Override
        protected void onDraw(Canvas canvas) {
            if(maskHeight == 0 && maskWidth == 0) {
                return;
            }
            if(maskHeight == height || maskWidth == width) {
                return;
            }

            //            if ((height > width && maskHeight < maskWidth) || (height < width && maskHeight > maskWidth)) {
            //                int temp = maskHeight;
            //                maskHeight = maskWidth;
            //                maskWidth = temp;
            //            }

            int paddingTop = Math.abs((height - maskHeight) / 3);
            int paddingLeft = Math.abs((width - maskWidth) / 2);
            int lineLen = maskWidth / 6;

            //			上
            rect = new Rect(0, 0, screenWidth, paddingTop);
            canvas.drawRect(rect, this.rectPaint);
            //			右
            rect = new Rect(paddingLeft + maskWidth, paddingTop, screenWidth, maskHeight + paddingTop);
            canvas.drawRect(rect, this.rectPaint);
            //			下
            rect = new Rect(0, maskHeight + paddingTop, screenWidth, screenHeight);
            canvas.drawRect(rect, this.rectPaint);
            //			左
            rect = new Rect(0, paddingTop, paddingLeft, paddingTop + maskHeight);
            canvas.drawRect(rect, this.rectPaint);

            canvas.drawLine(paddingLeft, paddingTop, paddingLeft + lineLen, paddingTop, linePaint);
            canvas.drawLine(maskWidth + paddingLeft - lineLen, paddingTop, maskWidth + paddingLeft, paddingTop, linePaint);
            canvas.drawLine(paddingLeft, paddingTop, paddingLeft, paddingTop + lineLen, linePaint);
            canvas.drawLine(maskWidth + paddingLeft, paddingTop, maskWidth + paddingLeft, paddingTop + lineLen, linePaint);
            canvas.drawLine(paddingLeft, maskHeight + paddingTop, paddingLeft + lineLen, maskHeight + paddingTop, linePaint);
            canvas.drawLine(maskWidth + paddingLeft - lineLen, maskHeight + paddingTop, maskWidth + paddingLeft, maskHeight + paddingTop, linePaint);
            canvas.drawLine(paddingLeft, maskHeight + paddingTop - lineLen, paddingLeft, maskHeight + paddingTop, linePaint);
            canvas.drawLine(maskWidth + paddingLeft, maskHeight + paddingTop - lineLen, maskWidth + paddingLeft, maskHeight + paddingTop, linePaint);

            super.onDraw(canvas);
        }
    }
}
