package cn.com.vau.common.base.mvvm

import io.reactivex.*
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers

/**
 *
 * @Description: 数据仓库基类
 */
abstract class BaseRepository {

    /**
     * 子线程切换到主线程
     *
     * @param <T>
     * @return
    </T> */
    open fun <T> threadToMain(): FlowableTransformer<T, T>? {
        return FlowableTransformer { upstream: Flowable<T> ->
            upstream.subscribeOn(Schedulers.io())
                .observeOn(AndroidSchedulers.mainThread())
        }
    }
}