package cn.com.vau.common.view.popup

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Outline
import android.view.*
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AccelerateInterpolator
import androidx.appcompat.app.AppCompatActivity
import androidx.core.animation.doOnEnd
import androidx.core.animation.doOnStart
import androidx.core.text.buildSpannedString
import androidx.core.text.underline
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.adapter.LoginDetailsAccountAdapter
import cn.com.vau.common.view.popup.bean.SelectTitle
import cn.com.vau.databinding.DialogBottomLoginDetailsBinding
import cn.com.vau.signals.live.history.util.VideoUtils.dp2px
import cn.com.vau.util.dp2px
import cn.com.vau.util.setFontG500
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IDialog
import cn.com.vau.util.widget.dialog.buidler.ActionIconBuilder

@SuppressLint("ViewConstructor")
class BottomLoginDetailsDialog private constructor(
    private val activity: AppCompatActivity,
    private val adapter: LoginDetailsAccountAdapter?,
    private val rightClick: ((SelectTitle?) -> Unit)? = null,
    private val getData: (() -> Unit)? = null
) : BottomDialog<DialogBottomLoginDetailsBinding>(activity, DialogBottomLoginDetailsBinding::inflate) {

    override fun setContentView() {
        mContentBinding.run {
            //拼接提示语， 设置邮箱下划线显示
            tvPrompt.text = buildSpannedString {
                append(activity.getString(R.string.send_email_prompt))
                append("  ")
                underline {
                    append(UserDataUtil.email())
                }
            }

            rvAccount.layoutManager = WrapContentLinearLayoutManager(activity)
            rvAccount.adapter = adapter
            measureRv.layoutManager = WrapContentLinearLayoutManager(activity)
            measureRv.adapter = adapter

            clAccount.outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, outline: Outline) {
                    outline.setRoundRect(
                        0,
                        0,
                        view.width,
                        view.height,
                        10f.dp2px()
                    )
                }
            }
            clAccount.clipToOutline = true

            rvAccount.visibility = View.GONE
            tvAccount.text = ""
            adapter?.setOnItemClickListener { _, _, position ->
                adapter.selectAccount = adapter.data.getOrNull(position)
                adapter.notifyDataSetChanged()

                tvAccount.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff, 0)
                hideViewWithAnimation(rvAccount, rootView, listOf(tvAccount, subtitle, title))
                tvAccount.text = adapter.selectAccount?.getTitle()
                if (tvAccount.text.isNotBlank()) {
                    tvAccount.setBackgroundResource(R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_solid_c0a1e1e1e_c262930_r10)
                }
            }

            tvAccount.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff, 0)

            tvCancel.setOnClickListener {
                clearData()
                dismiss()
            }

            tvSendNow.setOnClickListener {
                rightClick?.invoke(adapter?.selectAccount)
            }

            tvAccount.setOnClickListener {

                if (adapter?.data.isNullOrEmpty()) {
                    getData?.invoke()
                    return@setOnClickListener
                }
                if (rvAccount.visibility == View.VISIBLE) {
                    tvAccount.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff, 0)
                    hideViewWithAnimation(rvAccount, rootView, listOf(tvAccount, subtitle, title))
                    setMarginTop(clAccount, 0.dp2px())

                } else {
                    tvAccount.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff, 0)
                    setMarginTop(clAccount, 8.dp2px())
                    showViewWithAnimation(rvAccount, measureRv, rootView, listOf(tvAccount, subtitle, title))
                }
            }

        }
    }

    override fun beforeShow() {
        super.beforeShow()
        if (mContentBinding.tvAccount.text.isNullOrEmpty()) {
            mContentBinding.tvAccount.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c262930_r10)
        }
    }

    /**
     * 设置一个 View 的显示并使用渐变动画，同时让另外一个 View 根据该 View 的高度增大自身高度的动画
     */
    private fun showViewWithAnimation(viewToShow: View, measureView: View, viewToModify: View, viewsToShift: List<View>) {
        val modifyViewHeight = viewToModify.height.toFloat()
        viewToShow.viewTreeObserver.addOnGlobalLayoutListener(object : ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {

                viewToShow.viewTreeObserver.removeOnGlobalLayoutListener(this)
                val animatorSet = AnimatorSet()
                val animatorList = mutableListOf<ValueAnimator>()

                val fadeInAnimator = ValueAnimator.ofFloat(0f, 1f)
                fadeInAnimator.addUpdateListener { valueAnimator ->
                    val alpha = valueAnimator.animatedValue as Float
                    viewToShow.alpha = alpha
                }
                fadeInAnimator.interpolator = AccelerateInterpolator()

                val showViewHeight = measureView.height.toFloat()
                val heightAnimator = ValueAnimator.ofFloat(modifyViewHeight, modifyViewHeight + showViewHeight + 8.dp2px())
                heightAnimator.addUpdateListener { valueAnimator ->
                    val value = valueAnimator.animatedValue as Float
                    viewToModify.layoutParams.height = value.toInt()
                    viewToModify.requestLayout()
                    for (view in viewsToShift) {
                        view.translationY = 0f
                    }
                }

                animatorSet.doOnStart {
                    viewToShow.visibility = View.VISIBLE
                }
                animatorSet.doOnEnd {
                    for (view in viewsToShift) {
                        view.translationY = 0f
                    }
                }
                heightAnimator.interpolator = AccelerateDecelerateInterpolator()
                animatorList.add(heightAnimator)
                animatorList.add(fadeInAnimator)
                animatorSet.duration = 300
                animatorSet.playTogether(animatorList.orEmpty())
                animatorSet.start()
            }
        })
    }

    /**
     * 设置一个 View 的隐藏并使用渐变动画，同时让另外一个 View 根据该 View 的高度减少自身高度的动画
     */
    private fun hideViewWithAnimation(viewToHide: View, viewToModify: View, viewsToShift: List<View>) {
        val animatorSet = AnimatorSet()
        val animatorList = mutableListOf<ValueAnimator>()

        viewToHide.visibility = GONE

        val hideViewHeight = viewToHide.height.toFloat()
        val modifyViewHeight = viewToModify.height.toFloat()

        val heightAnimator = ValueAnimator.ofFloat(modifyViewHeight, modifyViewHeight - hideViewHeight - dp2px(activity, 8))
        heightAnimator.addUpdateListener { valueAnimator ->
            val value = valueAnimator.animatedValue as Float
            viewToModify.layoutParams.height = value.toInt()
            viewToModify.requestLayout()
            for (view in viewsToShift) {
                view.translationY = 0f
            }
        }
        animatorSet.doOnEnd {
            // 重置其他 View 的位移
            for (view in viewsToShift) {
                view.translationY = 0f
            }
        }
        animatorSet.duration = 300
        animatorList.add(heightAnimator)
        animatorSet.playTogether(animatorList.orEmpty())
        animatorSet.start()
    }

    // 动态设置 View 的 marginTop 距离
    private fun setMarginTop(view: View, marginTop: Int) {
        val layoutParams = view.layoutParams as MarginLayoutParams
        layoutParams.topMargin = marginTop
        view.layoutParams = layoutParams
    }

    private fun clearData() {
        mContentBinding.apply {
            if (rvAccount.visibility == VISIBLE) {
                tvAccount.setCompoundDrawablesRelativeWithIntrinsicBounds(0, 0, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff, 0)
                hideViewWithAnimation(rvAccount, rootView, listOf(tvAccount, subtitle, title))
            }
            adapter?.selectAccount = null
            tvAccount.isChecked = false
            tvAccount.text = ""
            adapter?.notifyDataSetChanged()
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        clearData()
    }

    @Suppress("unused")
    class Builder(activity: AppCompatActivity) :
        ActionIconBuilder<DialogBottomLoginDetailsBinding>(activity) {

        private var adapter: LoginDetailsAccountAdapter? = null
        private var rightClick: ((SelectTitle?) -> Unit)? = null
        private var getData: (() -> Unit)? = null

        fun setAdapter(adapter: LoginDetailsAccountAdapter): Builder {
            this.adapter = adapter
            return this
        }

        fun setRightClick(rightClick: ((SelectTitle?) -> Unit)?): Builder {
            this.rightClick = rightClick
            return this
        }

        fun setGetData(getData: (() -> Unit)?): Builder {
            this.getData = getData
            return this
        }

        override fun build(): BottomLoginDetailsDialog {
            setDismissTouchOutside(false)
            setTouchThrough(false)
            setEnableDrag(false)
            setDismissOnBackPressed(false)
            return super.build() as BottomLoginDetailsDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomLoginDetailsBinding> {
            return BottomLoginDetailsDialog(
                context as AppCompatActivity, adapter, rightClick = rightClick, getData = getData
            )
        }
    }

}


