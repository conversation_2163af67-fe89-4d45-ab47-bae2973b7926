package cn.com.vau.common.mvvm.ext

import androidx.lifecycle.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.network.ExceptionHandle
import cn.com.vau.util.LogUtil
import kotlinx.coroutines.*

/**
 * 不过滤请求结果
 * @param block 请求体 必须要用suspend关键字修饰
 * @param onSuccess 成功回调
 * @param onError 失败回调，如网络异常、解析异常等
 * @param isShowDialog 是否显示加载框，也可在VM层或V层showLoading()进行显示
 * @param isAutoDismissDialog 请求成功后是否关闭加载框，根据实际业务逻辑配置，也可在VM层或V层hideLoading()进行关闭
 *
 * 20241213：如果在代码里自己手动调用了 showLoadDialog()，请自行处理关闭弹框逻辑 hideLoadDialog()。把自己业务逻辑弹框和网络框架弹框解耦。
 *
 */
fun <T> BaseViewModel.requestNet(
    block: suspend () -> T,
    onSuccess: (T) -> Unit,
    onError: (Throwable) -> Unit = {},
    isShowDialog: Boolean = false,
    isAutoDismissDialog: Boolean = true
): Job {
    // 如果需要弹窗 通知Activity/fragment弹窗
    if (isShowDialog) loadingChange.dialogLiveData.postValue(true)
    return viewModelScope.launch {
        runCatching {
            // 请求体
            block()
        }.onSuccess {
            if (isAutoDismissDialog) { // 接口请求成功自动关闭loading框
                loadingChange.dialogLiveData.postValue(false)
            }
            /*
            1.接口请求成功，需要开发者自己判断code码是否正确，isSuccess()为成功，其他为失败
            2.如果开发者接口返回的实体不是ApiResponse<T>，则需要自己去处理
             */
            onSuccess(it)
        }.onFailure {
            // 请求异常 关闭弹窗
            loadingChange.dialogLiveData.postValue(false)
            // 打印错误栈信息
            it.printStackTrace()
            // 处理异常，吐司等操作
            val ex = ExceptionHandle.handleException(it)
            LogUtil.e("errMsg -> ${ex.throwable?.message}")
            // 把异常回调给开发者，供开发者做一些业务处理
            onError(it)
        }
    }
}

/**
 * AndroidViewModel级别的网络请求（需要自己处理loading框逻辑）
 */
fun <T> AndroidViewModel.requestNet(
    block: suspend () -> T,
    onSuccess: (T) -> Unit,
    onError: (Throwable) -> Unit = {},
): Job {
    return viewModelScope.launch {
        runCatching {
            // 请求体
            withContext(Dispatchers.IO) {
                block()
            }
        }.onSuccess {
            /*
            1.接口请求成功，需要开发者自己判断code码是否正确，isSuccess()为成功，其他为失败
            2.如果开发者接口返回的实体不是ApiResponse<T>，则需要自己去处理
             */
            onSuccess(it)
        }.onFailure {
            // 打印错误栈信息
            it.printStackTrace()
            // 处理异常，吐司等操作
            val ex = ExceptionHandle.handleException(it)
            LogUtil.e("errMsg -> ${ex.throwable?.message}")
            // 把异常回调给开发者，供开发者做一些业务处理
            onError(it)
        }
    }
}