package cn.com.vau.common.view.popup.adapter

import android.graphics.drawable.Drawable
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.common.view.popup.bean.SelectTitle
import cn.com.vau.util.AttrResourceUtil
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class LoginDetailsAccountAdapter(var selectAccount: SelectTitle? = null) : BaseQuickAdapter<SelectTitle, BaseViewHolder>(R.layout.item_login_details_account) {

    private val drawable: Drawable? by lazy {
        ContextCompat.getDrawable(
            context,
            R.drawable.draw_bitmap_img_source_tick_11x8_c731e1e1e_c61ffffff
        )?.apply {
            setBounds(0, 0, intrinsicWidth, intrinsicHeight)
        }
    }

    override fun convert(holder: BaseViewHolder, item: SelectTitle) {
        val textView = holder.getView<AppCompatTextView>(R.id.tv)
        if (selectAccount != null && selectAccount?.getTitle() == item.getTitle()) {
            textView.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff))
            textView.setBackgroundColor(ContextCompat.getColor(context, R.color.c0e00c79c))

            textView.setCompoundDrawablesRelativeWithIntrinsicBounds(null, null, drawable, null)
            val typeface = ResourcesCompat.getFont(context, R.font.gilroy_semi_bold)
            textView.typeface = typeface
        } else {
            textView.setTextColor(AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff))
            textView.setBackgroundColor(AttrResourceUtil.getColor(context, R.attr.color_cf3f3f3_c262930))
            textView.setCompoundDrawables(null, null, null, null)
            val typeface = ResourcesCompat.getFont(context, R.font.gilroy_medium)
            textView.typeface = typeface
        }
        textView.text = item.getTitle()
    }

}