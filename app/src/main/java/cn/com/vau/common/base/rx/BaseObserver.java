package cn.com.vau.common.base.rx;

import com.google.gson.JsonSyntaxException;

import java.net.SocketTimeoutException;

import cn.com.vau.R;
import cn.com.vau.page.common.InterruptRequestException;
import cn.com.vau.util.*;
import io.reactivex.Observer;
import io.reactivex.disposables.Disposable;

/**
 * Created by Administrator on 2018/8/16.
 */
public abstract class BaseObserver<T> implements Observer<T> {

    private Disposable disposable;

    public Disposable getDisposable() {
        return disposable;
    }

    @Override
    public void onSubscribe(Disposable d) {
        disposable = d;
        onHandleSubscribe(d);
    }

    @Override
    public void onComplete() {
    }

    @Override
    public void onError(Throwable e) {
        // LogUtil.i("okhttp ---- onError message : ",e.getMessage());
        if (e instanceof JsonSyntaxException) {
            // 数据异常，请稍后重试
            ToastUtil.showToast(UtilApp.getApp().getString(R.string.data_exception_please_try_again_later));
            return;
        }
        if (e instanceof InterruptRequestException) return;
        if (e instanceof NullPointerException) return;
        if (e instanceof SocketTimeoutException) return;
        e.printStackTrace();
        // 网络不太顺畅，请检查您的网络设置.
        ToastUtil.showToast(UtilApp.getApp().getString(R.string.slow_or_no_internet_connection));
    }

    protected abstract void onHandleSubscribe(Disposable d);

}
