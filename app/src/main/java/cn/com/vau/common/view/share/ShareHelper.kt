package cn.com.vau.common.view.share

import android.graphics.Bitmap
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseDataBindingActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_KLINE
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_ORDER
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_ORDER_HISTORY
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_RAF
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_SCREENSHOT
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_STRATEGY_DETAIL
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_STRATEGY_ORDER
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_STRATEGY_ORDER_HISTORY
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.util.ActivityManagerUtil
import cn.com.vau.util.BitmapUtil.loadBitmapFromView
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.formatProductPrice
import cn.com.vau.util.ifNull
import cn.com.vau.util.ifNullToDouble
import cn.com.vau.util.ifNullToInt
import cn.com.vau.util.json
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathMul
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.toLongCatching
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * Filename: ShareHelper
 * Author: GG
 * Date: 2025/2/14
 * Description:
 */
object ShareHelper {

    var sharePopup: SharePopup? = null

    /**
     * 截图分享
     */
    fun screenshotShare() {
        val activity = ActivityManagerUtil.getInstance().activityStack.last() as AppCompatActivity
        prepareDataAndShow(activity, shareType = TYPE_SCREENSHOT) { shareData, _ ->
            shareData.screenshotBitmap = loadBitmapFromView(activity.window.decorView)
        }
    }

    /**
     * 策略详情页面分享
     */
    fun strategyShare(
        activity: AppCompatActivity,
        /**
         * 策略 id
         */
        strategyId: String?,
        avatarUrl: String?,
        strategyName: String?,
        strategyNo: String?
    ) {
        prepareDataAndShow(activity, shareType = TYPE_STRATEGY_DETAIL, signalId = strategyId) { shareData, viewModel ->
            showLoading(activity)

            shareData.avatarUrl = avatarUrl
            shareData.strategyName = strategyName
            shareData.strategyNo = strategyNo

            // 获取近3个月收益率 以及当前跟单者数量和累计跟单者数量
            viewModel.strategySharePostertByStrategyIdApi(strategyId)?.apply {
                shareData.returnRate3M = "${if (returnRate.mathCompTo("0") == 1) "+" else ""}${returnRate.mathMul("100").numFormat(2)}%"
                shareData.returnRate3MColor = ContextCompat.getColor(activity, if (returnRate.mathCompTo("0") == -1) R.color.cf44040 else R.color.c00c79c)
                shareData.totalCopiersCount = totalCopiersCount.ifNull("0")
                shareData.currentCopiersCount = currentCopiersCount.ifNull("0")
            }
            // 获取月化收益率 以及最大月化收益率
            viewModel.getMonthlyReturnRate(strategyId)?.apply {
                shareData.returnYTD = "${if (returnYTD.mathCompTo("0") == 1) "+" else ""}${returnYTD.mathMul("100").numFormat(2)}%"
                shareData.returnYTDColor = ContextCompat.getColor(activity, if (returnYTD.mathCompTo("0") == -1) R.color.cf44040 else R.color.c00c79c)
                shareData.maxMonthlyReturn = "${if (maxMonthlyReturn.mathCompTo("0") == 1) "+" else ""}${maxMonthlyReturn.mathMul("100").numFormat(2)}%"
                shareData.maxMonthlyReturnColor = ContextCompat.getColor(activity, if (maxMonthlyReturn.mathCompTo("0") == -1) R.color.cf44040 else R.color.c00c79c)
            }
            // 获取交易次数 以及 交易胜率
            viewModel.getCategoryObj(activity, strategyId)?.apply {
                shareData.totalDealCount = totalTrades.ifNullToInt().toString()

                shareData.winRate = "${profitableTradesPercent.numFormat(2, true)}%"

                shareData.winRateColor = if (profitableTradesPercent.ifNullToDouble(0.0) >= 0.0) {
                    ContextCompat.getColor(activity, R.color.c00c79c)
                } else {
                    ContextCompat.getColor(activity, R.color.ce35728)
                }
            }
            viewModel.getProductList(strategyId)?.apply {
                shareData.dealInfoList = this
                    .asSequence()
                    .take(3)
                    .map { tradeObj ->
                        Pair(tradeObj.product.ifNull(), "${tradeObj.profitableTradesPercent.numFormat(2, true)}%")
                    }
                    .toMutableList()

                shareData.dealInfoList?.let {
                    dealInfoPrepare(it)
                }
            }
            shareData.description = activity.getString(R.string.replicate_the_success_on_x_app, activity.getString(R.string.app_name))
        }
    }

    /**
     * live的 订单 以及 自助交易的订单 列表 以及详情内的分享
     */
    fun orderShare(activity: AppCompatActivity, symbol: String?, entryPrice: String?, currentPrice: String?, profit: String?, cmd: String?, digits: Int?) {
        prepareDataAndShow(activity, shareType = TYPE_ORDER) { shareData, _ ->
            shareData.symbol = symbol
            shareData.entryPrice = entryPrice
            shareData.currentPrice = currentPrice.formatProductPrice(digits.ifNull(), false)
            // todo gold 保证金字段暂时没有，所以收益率暂时无法计算 这里先用 entryPrice 放在这里顶一下， 不能使用entryPrice这个字段
            val profitPercentage = if (profit.mathCompTo("0") == 1 || entryPrice.mathCompTo("0") == 0) {
                0.0
            } else {
                profit.toDoubleCatching().div(entryPrice?.toDoubleOrNull() ?: 0.0).times(100)
            }
            // 盈亏 >0 =0 和 <0 的不同情况 有 文案颜色 以及 前面是否有 + 的区别
            if (profit.mathCompTo("0") == 1) {
                shareData.profit = "+${profit.numCurrencyFormat()}"
                shareData.profitRate = "+${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else if (profit.mathCompTo("0") == 0) {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.cf44040)
            }
            shareData.direction = cmd
        }
    }

    /**
     * live的历史订单 以及 历史订单详情页面 的分享
     */
    fun orderHistoryShare(activity: AppCompatActivity, symbol: String?, entryPrice: String?, currentPrice: String?, profit: String?, cmd: String?, closeTime: String?) {
        prepareDataAndShow(activity, shareType = TYPE_ORDER_HISTORY) { shareData, _ ->
            shareData.symbol = symbol
            shareData.entryPrice = entryPrice
            shareData.currentPrice = currentPrice
            // todo gold 保证金字段暂时没有，所以收益率暂时无法计算 这里先用 entryPrice 放在这里顶一下， 不能使用entryPrice这个字段
            val profitPercentage = if (profit.mathCompTo("0") == 1 || entryPrice.mathCompTo("0") == 0) {
                0.0
            } else {
                profit.toDoubleCatching().div(entryPrice?.toDoubleOrNull() ?: 0.0).times(100)
            }
            // 盈亏 >0 =0 和 <0 的不同情况 有 文案颜色 以及 前面是否有 + 的区别
            if (profit.mathCompTo("0") == 1) {
                shareData.profit = "+${profit.numCurrencyFormat()}"
                shareData.profitRate = "+${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else if (profit.mathCompTo("0") == 0) {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.cf44040)
            }
            shareData.profitTitle = activity.getString(R.string.profit_loss) + " (${UserDataUtil.currencyType()})"
            shareData.direction = cmd
            shareData.bottomTime = activity.getString(R.string.order_was_closed_on_x, TimeUtil.formatDate(closeTime.toLongCatching(), "yyyy/MM/dd"))
        }
    }

    /**
     * 正在跟随的策略订单分享
     */
    fun strategyOrderShare(activity: AppCompatActivity, profilePictureUrl: String?, strategyName: String?, stUserNickname: String?, strategyNo: String?, strategyId: String?, profit: Double, investmentAmount: String?) {
        prepareDataAndShow(activity, shareType = TYPE_STRATEGY_ORDER, signalId = strategyId) { shareData, _ ->
            shareData.avatarUrl = profilePictureUrl
            shareData.strategyName = strategyName
            shareData.userNickname = stUserNickname
            shareData.strategyNo = strategyNo

            val profitPercentage = if (profit == 0.0 || investmentAmount.mathCompTo("0") == 0) {
                0.0
            } else {
                profit.div(investmentAmount?.toDoubleOrNull() ?: 0.0).times(100)
            }
            // 盈亏 >0 =0 和 <0 的不同情况 有 文案颜色 以及 前面是否有 + 的区别
            if (profit > 0) {
                shareData.profit = "+${profit.numCurrencyFormat()}"
                shareData.profitRate = "+${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else if (profit == 0.0) {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.cf44040)
            }
        }
    }

    /**
     * 策略历史订单分享
     */
    fun strategyHistoryOrderShare(activity: AppCompatActivity, profilePictureUrl: String?, strategyName: String?, strategyNo: String?, strategyId: String?, profit: String?, roi: String?, copyDate: String?, stopDate: String?) {
        prepareDataAndShow(activity, shareType = TYPE_STRATEGY_ORDER_HISTORY, signalId = strategyId) { shareData, _ ->
            shareData.avatarUrl = profilePictureUrl
            shareData.strategyName = strategyName
            shareData.strategyNo = strategyNo
            shareData.copyTime = "${activity.getString(R.string.copied)} ${TimeUtil.formatDate(copyDate.toLongCatching(), "dd/MM/yyyy")} - ${TimeUtil.formatDate(stopDate.toLongCatching(), "dd/MM/yyyy")}"

            val profitPercentage = roi.mathMul("100").numFormat(2)

            // 盈亏 >0 =0 和 <0 的不同情况 有 文案颜色 以及 前面是否有 + 的区别
            if (profit.mathCompTo("0") == 1) {
                shareData.profit = "+${profit.numCurrencyFormat()}"
                shareData.profitRate = "+${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else if (profit.mathCompTo("0") == 0) {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.c00c79c)
            } else {
                shareData.profit = profit.numCurrencyFormat()
                shareData.profitRate = "${profitPercentage.numCurrencyFormat()}%"
                shareData.profitColor = ContextCompat.getColor(activity, R.color.cf44040)
            }
        }
    }

    /**
     * h5调用raf分享
     */
    fun rafShare(activity: AppCompatActivity) {
        prepareDataAndShow(activity, shareType = TYPE_RAF)
    }

    /**
     * k线图分享
     * @param kLineBitmap 产品k线图
     * @param symbol 产品名
     * @param dismissCallback 分享弹窗关闭回调
     */
    fun kLineShare(activity: AppCompatActivity, kLineBitmap: Bitmap?, symbol: String?, dismissCallback: (() -> Unit)? = null) {
        prepareDataAndShow(activity, shareType = TYPE_KLINE, dismissCallback = dismissCallback) { shareData, _ ->
            shareData.kLineBitmap = kLineBitmap
            shareData.symbol = symbol
        }
    }

    private fun prepareDataAndShow(
        activity: AppCompatActivity,
        /**
         * 分享弹窗类型
         */
        shareType: Int,
        signalId: String? = "",
        dismissCallback: (() -> Unit)? = null,
        configData: (suspend (ShareData, ShareViewModel) -> Unit)? = null
    ) {
        val viewModel = ViewModelProvider(activity)[ShareViewModel::class.java]
        activity.lifecycleScope.launch(
            CoroutineExceptionHandler { context, exception ->
                hideLoading(activity)
                // 请求异常 关闭弹窗 请求的时候 已经弹出相应toast
                LogUtil.w("分享接口请求异常：${exception.message}")
                context.cancel()
            }
        ) {
            val shareData = ShareData(shareType)

            // 是否使用 ib选择账户 相关按钮
            val isIB: Boolean = UserDataUtil.isIB()
            var accountList: List<AccountTradeBean>? = null
            if (UserDataUtil.isLogin()) {
                accountList = getAccountList(shareType, isIB, activity, shareData, viewModel)
                // ib账户特殊请求
                if (shareType != TYPE_RAF && shareType != TYPE_SCREENSHOT && isIB && !accountList.isNullOrEmpty()) {
                    getRefereeInfoIb(signalId, activity, viewModel, shareType, shareData)
                } else {
                    getRefereeInfo(signalId, activity, viewModel, shareType, shareData)
                }
            } else {
                shareData.qrCodeUrl = UrlConstants.UN_LOGIN_QR_CODE
            }
            configData?.invoke(shareData, viewModel)
            hideLoading(activity)

            sharePopup = SharePopup.show(
                activity = activity,
                shareType = shareType,
                signalId = signalId,
                viewModel = viewModel,
                shareData = shareData,
                dismissCallback = dismissCallback,
                isIB = isIB && !accountList.isNullOrEmpty(),
                accountList = accountList
            )
        }
    }

    /**
     * 获取ib账户的分享信息
     * @param signalId 策略id
     * @param activity 上下文
     * @param viewModel  viewModel
     * @param shareType 分享类型
     * @param shareData 分享数据
     * @return 分享数据
     */
    private suspend fun getRefereeInfoIb(signalId: String?, activity: AppCompatActivity, viewModel: ShareViewModel, shareType: Int, shareData: ShareData) {
        // 如果没有默认选中的账户 就先不请求接口
        if (SpManager.getInvitationLastSelectAccount().isNotBlank()) {
            // 在存有上次选中账户的情况下 先获取缓存数据 ， 如果没有缓存数据 就请求网络， 如果请求成功就保存到缓存中
            if (isCanCache(shareType, signalId) && SpManager.getRefereeInfoIB() != null) {
                SpManager.getRefereeInfoIB()
            } else {
                showLoading(activity)
                viewModel.getRefereeInfoIB(signalId = signalId, shareType = shareType)?.obj
            }?.let {
                // 只有signalId 为空的时候 ，说明不是策略相关分享的时候 才需要缓存接口数据，方便下次使用，策略分享的数据 和 signalId 也有关系，所以不能缓存
                if (isCanCache(shareType, signalId)) {
                    SpManager.putRefereeInfoIB(it.json)
                }
                ImageLoaderUtil.preloadImage(it.qrcodeUrl)
                shareData.qrCodeUrl = it.qrcodeUrl
                // 预加载二维码图片
                shareData.shareCode = it.inviteCode
                shareData.shareUrl = it.refereeUrl
                if (!it.listActivityPic.isNullOrEmpty()) {
                    shareData.topImgUrl = it.listActivityPic?.getOrNull(0)
                }
            }
        }
    }

    /**
     * 获取非ib账号的分享信息
     */
    private suspend fun getRefereeInfo(signalId: String?, activity: AppCompatActivity, viewModel: ShareViewModel, shareType: Int, shareData: ShareData) {
        if (isCanCache(shareType, signalId) && SpManager.getRefereeInfo() != null) {
            SpManager.getRefereeInfo()
        } else {
            showLoading(activity)
            viewModel.getRefereeInfo(signalId = signalId, shareType = shareType)?.obj
        }?.let {
            // 只有signalId 为空的时候 ，说明不是策略相关分享的时候 才需要缓存接口数据，方便下次使用，策略分享的数据 和 signalId 也有关系，所以不能缓存
            if (isCanCache(shareType, signalId)) {
                SpManager.putRefereeInfo(it.json)
            }

            shareData.qrCodeUrl = it.qrcodeUrl
            ImageLoaderUtil.preloadImage(shareData.qrCodeUrl)
            // 预加载二维码图片
            shareData.shareCode = it.inviteCode
            shareData.shareUrl = it.refereeUrl
            if (!it.listActivityPic.isNullOrEmpty()) {
                shareData.topImgUrl = it.listActivityPic?.getOrNull(0)
            }
        }
    }

    private fun isCanCache(shareType: Int, signalId: String?): Boolean = shareType != TYPE_RAF && signalId.isNullOrBlank()

    /**
     * 获取ib账户的可分享账户列表
     */
    private suspend fun getAccountList(shareType: Int, isIB: Boolean, activity: AppCompatActivity, shareData: ShareData, viewModel: ShareViewModel): List<AccountTradeBean>? = if (shareType != TYPE_RAF && shareType != TYPE_SCREENSHOT && isIB) {
        showLoading(activity)
        shareData.shareAccount = SpManager.getInvitationLastSelectAccount()
        viewModel.getAccountList()?.apply {
            if (SpManager.getInvitationLastSelectAccount().isBlank()) {
                val acountCd = firstOrNull()?.acountCd ?: ""
                shareData.shareAccount = acountCd
                SpManager.putInvitationLastSelectAccount(acountCd)
            }
        }
    } else {
        null
    }

    /**
     * 检测接口返回的的数据 少于3个时，递归添加
     */
    private fun dealInfoPrepare(list: MutableList<Pair<String, String>>) {
        if (list.size.ifNull() < 3) {
            list.add(Pair(Constants.NULL_VALUE, Constants.NULL_VALUE))
            dealInfoPrepare(list)
        }
    }

    /**
     * 预加载接口，获取分享 需要的数据
     */
    fun prepareData(activity: AppCompatActivity) {
        val viewModel = ViewModelProvider(activity)[ShareViewModel::class.java]
        activity.lifecycleScope.launch(
            CoroutineExceptionHandler { context, exception ->
                run {
                    hideLoading(activity)
                    LogUtil.w("分享接口请求异常：${exception.message}")
                    context.cancel()
                }
            }
        ) {
            if (SpManager.getRefereeInfo() == null) {
                viewModel.getRefereeInfo()?.obj?.let {
                    SpManager.putRefereeInfo(it.json)
                }
            }
        }
    }

    private fun showLoading(activity: AppCompatActivity) {
        when (activity) {
            is BaseMvvmBindingActivity<*> -> {
                activity.showLoadDialog()
            }

            is BaseDataBindingActivity<*> -> {
                activity.showLoadDialog()
            }

            is BaseActivity -> {
                activity.showNetDialog()
            }
        }
    }

    private fun hideLoading(activity: AppCompatActivity) {
        when (activity) {
            is BaseMvvmBindingActivity<*> -> {
                activity.hideLoadDialog()
            }

            is BaseDataBindingActivity<*> -> {
                activity.hideLoadDialog()
            }

            is BaseActivity -> {
                activity.hideNetDialog()
            }
        }
    }
}