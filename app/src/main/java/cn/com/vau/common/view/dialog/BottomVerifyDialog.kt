package cn.com.vau.common.view.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.view.View
import androidx.annotation.RawRes
import androidx.core.view.isVisible
import cn.com.vau.databinding.DialogBottomVerifyBinding
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.widget.dialog.base.*

/**
 * Filename: BottomVerifyDialog
 * Author: GG
 * Date: 2025/3/10
 * Description:
 */
@SuppressLint("ViewConstructor")
class BottomVerifyDialog private constructor(
    activity: Activity,
    @RawRes val icon: Int?,
    val title: CharSequence?,
    val content: CharSequence?,
    val buttonStr: CharSequence?,
    val linkStr: CharSequence?,
    val buttonClick: (() -> Unit)?,
    val linkClick: (() -> Unit)?,
    val customView: View?
) : BottomDialog<DialogBottomVerifyBinding>(activity, DialogBottomVerifyBinding::inflate) {

    override fun setContentView() {
        super.setContentView()
        mContentBinding.run {
            setIcon(icon)
            setContentTitle(title)
            setContent(content)
            setButtonStr(buttonStr)
            setButtonClick(buttonClick)
            setLinkStr(linkStr)
            setLinkClick(linkClick)
            setCustomView(customView)
        }
    }

    fun setIcon(@RawRes icon: Int?): BottomVerifyDialog {
        if (icon != null) {
            mContentBinding.ivIcon.isVisible = true
            mContentBinding.ivIcon.setAnimation(icon)
            mContentBinding.ivIcon.playAnimation()
        } else {
            mContentBinding.ivIcon.isVisible = false
        }
        return this
    }

    fun setContentTitle(title: CharSequence?): BottomVerifyDialog {
        if (title != null) {
            mContentBinding.tvTitle.text = title
            mContentBinding.tvTitle.isVisible = true
        } else {
            mContentBinding.tvTitle.isVisible = false
        }
        return this
    }

    fun setContent(content: CharSequence?): BottomVerifyDialog {
        if (content != null) {
            mContentBinding.tvContent.text = content
            mContentBinding.tvContent.isVisible = true
        } else {
            mContentBinding.tvContent.isVisible = false
        }
        return this
    }

    fun setButtonClick(buttonClick: (() -> Unit)?): BottomVerifyDialog {
        mContentBinding.tvNext.clickNoRepeat {
            buttonClick?.invoke()
            dismissDialog()
        }
        return this
    }

    fun setLinkClick(linkClick: (() -> Unit)?): BottomVerifyDialog {
        mContentBinding.tvLink.clickNoRepeat {
            linkClick?.invoke()
            dismissDialog()
        }
        return this
    }

    fun setLinkStr(linkStr: CharSequence?): BottomVerifyDialog {
        if (linkStr != null) {
            mContentBinding.tvLink.text = linkStr
            mContentBinding.tvLink.isVisible = true
        } else {
            mContentBinding.tvLink.isVisible = false
        }
        return this
    }

    fun setButtonStr(buttonStr: CharSequence?): BottomVerifyDialog {
        if (buttonStr != null) {
            mContentBinding.tvNext.text = buttonStr
        }
        return this
    }

    fun setCustomView(customView: View?): BottomVerifyDialog {
        if (customView != null) {
            mContentBinding.flCustom.addView(customView)
            mContentBinding.flCustom.isVisible = true
        } else {
            mContentBinding.flCustom.isVisible = false
        }
        return this
    }

    class Builder(activity: Activity) : IBuilder<DialogBottomVerifyBinding, Builder>(activity) {

        /**
         * 图标
         */
        @RawRes
        var icon: Int? = null

        /**
         * 标题
         */
        var title: CharSequence? = null

        /**
         * 内容
         */
        var content: CharSequence? = null

        /**
         * 按钮文字
         */
        var buttonStr: CharSequence? = null

        /**
         * 链接文字
         */
        var linkStr: CharSequence? = null

        /**
         * 按钮点击事件
         */
        var buttonClick: (() -> Unit)? = null

        /**
         * 链接点击事件
         */
        var linkClick: (() -> Unit)? = null

        /**
         *  添加在内容下面的view
         */
        var customView: View? = null

        /**
         * 设置图标
         */
        fun setIcon(@RawRes icon: Int?): Builder {
            this.icon = icon
            return this
        }

        /**
         * 设置标题
         */
        fun setTitle(title: CharSequence?): Builder {
            this.title = title
            return this
        }

        /**
         * 设置内容
         */
        fun setContent(content: CharSequence?): Builder {
            this.content = content
            return this
        }

        /**
         * 设置按钮文字
         */
        fun setButtonStr(buttonStr: CharSequence?): Builder {
            this.buttonStr = buttonStr
            return this
        }

        /**
         * 设置链接文字
         */
        fun setLinkStr(linkStr: CharSequence?): Builder {
            this.linkStr = linkStr
            return this
        }

        /**
         * 设置按钮点击事件
         */
        fun setButtonClick(buttonClick: (() -> Unit)?): Builder {
            this.buttonClick = buttonClick
            return this
        }

        /**
         * 设置链接点击事件
         */
        fun setLinkClick(linkClick: (() -> Unit)?): Builder {
            this.linkClick = linkClick
            return this
        }

        /**
         * 设置自定义添加的view
         */
        fun setCustomView(customView: View?): Builder {
            this.customView = customView
            return this
        }

        override fun createDialog(context: Context): IDialog<DialogBottomVerifyBinding> {
            return BottomVerifyDialog(activity, icon, title, content, buttonStr, linkStr, buttonClick, linkClick, customView)
        }

        override fun build(): BottomVerifyDialog {
            return super.build() as BottomVerifyDialog
        }
    }
}