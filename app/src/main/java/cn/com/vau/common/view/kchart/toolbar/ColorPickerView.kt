package cn.com.vau.common.view.kchart.toolbar

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.example.myapplication.R
import com.example.myapplication.databinding.KlineColorPickerViewBinding
import cn.com.vau.common.view.kchart.toolbar.ToolStyleConstants.colorList

/**
 * 颜色选择器
 */
class ColorPickerView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val binding by lazy { KlineColorPickerViewBinding.inflate(LayoutInflater.from(context), this, true) }

    private var onColorPickListener: ((item: ColorData) -> Unit)? = null
    var mAdapter = ColorGridAdapter()

    init {
        setupRecyclerView()
    }

    private fun setupRecyclerView() {

        binding.rvList.layoutManager = object : GridLayoutManager(context, 3) {
            override fun canScrollVertically(): Boolean = false // 禁用垂直滚动
            override fun canScrollHorizontally(): Boolean = false // 禁用水平滚动
        }
        binding.rvList.itemAnimator = null // 禁用默认动画
        binding.rvList.layoutAnimation = null // 禁用默认动画
        binding.rvList.isNestedScrollingEnabled = false
        binding.rvList.setHasFixedSize(true)

        binding.rvList.adapter = mAdapter

        // 添加分割线
        binding.rvList.addItemDecoration(GridSpacingItemDecoration(3, 10.dp2px(),  10.dp2px()))
        mAdapter.setOnItemClickListener { adapter, view, position ->
            val item = mAdapter.getItem(position) ?: return@setOnItemClickListener
            onColorPickListener?.invoke(item)
        }

        setColorCells(colorList)
    }

    fun setOnColorPickListener(listener: ( item: ColorData) -> Unit) {
        onColorPickListener = listener
    }

    // 批量添加工具项
    private fun setColorCells(items: List<ColorData>) {
        mAdapter.setList(items)
    }
}

class ColorGridAdapter() : BaseQuickAdapter<ColorData, BaseViewHolder>(R.layout.kline_item_color_picker) {
    override fun convert(holder: BaseViewHolder, item: ColorData) {
        holder.setBackgroundColor(R.id.viewColor, item.color)
    }

}
