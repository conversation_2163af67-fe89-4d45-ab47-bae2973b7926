package cn.com.vau.common.view.popup

import android.content.Context
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.PopupBottomListWithTitleBinding
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.screenHeight
import cn.com.vau.util.widget.dialog.base.fixNavigationBarPadding
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.core.BottomPopupView
import com.lxj.xpopup.interfaces.SimpleCallback

/**
 * Filename: BottomSelectPopup
 * Author: GG
 * Date: 2023/9/7 0007 14:29
 * Description:通过传入的adapter控制样式，更灵活
 */
@Deprecated("已弃用")
class BottomSelectPopup(
    context: Context,
    private var titleStr: CharSequence? = null,
    private var adapter: RecyclerView.Adapter<*>? = null,
    private val isShowInterval: Boolean = false,
) : BottomPopupView(context) {

    private var mBinding: PopupBottomListWithTitleBinding? = null

    override fun getImplLayoutId(): Int = R.layout.popup_bottom_list_with_title

    override fun onCreate() {
        super.onCreate()
        mBinding = PopupBottomListWithTitleBinding.bind(popupImplView)

        mBinding?.apply {

            if (titleStr.isNullOrBlank()) {
                title.isVisible = false
            } else {
                title.isVisible = true
                title.text = titleStr
            }
            viewInterval.isVisible = isShowInterval
            rvList.layoutManager = WrapContentLinearLayoutManager(context)
            rvList.adapter = adapter
        }
    }

    fun setAdapter(adapter: RecyclerView.Adapter<*>?) {
        this.adapter = adapter
        mBinding?.rvList?.adapter = adapter
    }

    fun setTitle(title: CharSequence?) {
        titleStr = title
        mBinding?.title?.text = titleStr
    }

    fun closeIconIsShow(isShow: Boolean) {
        mBinding?.ivClose?.isVisible = isShow
        mBinding?.ivClose?.setOnClickListener {
            dismiss()
        }
    }

    /**
     * 设置 间隔的view是否展示
     */
    fun setIntervalViewShow(isShow: Boolean) {
        mBinding?.viewInterval?.isVisible = isShow
    }

    companion object {

        //TODO Felix context 为什么要可空呢
        fun build(
            context: Context?,
            titleStr: CharSequence? = null,
            adapter: RecyclerView.Adapter<*>? = null,
            isShowInterval: Boolean = false,
            dismissCallback: (() -> Unit)? = null
        ): BottomSelectPopup? =
            if (context != null) {
                XPopup.Builder(context)
                    .navigationBarColor(AttrResourceUtil.getColor(context, R.attr.mainLayoutBg))
                    .maxHeight((0.75 * screenHeight).toInt())
                    .setPopupCallback(object : SimpleCallback() {
                        override fun onDismiss(popupView: BasePopupView?) {
                            super.onDismiss(popupView)
                            dismissCallback?.invoke()
                        }
                    })
                    .asCustom(BottomSelectPopup(context, titleStr, adapter, isShowInterval))
                    .fixNavigationBarPadding() as BottomSelectPopup
            } else null

    }
}