package cn.com.vau.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import android.text.TextUtils
import android.util.AttributeSet
import android.view.*
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.LayoutSerialTextBinding
import cn.com.vau.util.AttrResourceUtil

/**
 * 是 SerialLinkTextView 的父类
 * 该组件只做显示，没有超链接功能
 * 显示为：
 * * ·  Bank Statement
 * * ·  Utility Bills (Gas/Electricity/Water/Phone)
 * * ·  Letter issued by a third party company
 */
@SuppressLint("CustomViewStyleable")
open class SerialTextView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null) :
    ConstraintLayout(context, attrs) {

    private var contentMaxLines = -1
    private var serialTxt = "·"
    private var contentTxt: CharSequence = "·"
    private var textColor = -1
    private val parent: LayoutSerialTextBinding by lazy { LayoutSerialTextBinding.inflate(LayoutInflater.from(context), this, true) }

    init {
        val attr = context.obtainStyledAttributes(attrs, R.styleable.view_text_serial)
        contentMaxLines = attr.getInteger(R.styleable.view_text_serial_content_max_lines, -1)
        serialTxt = attr.getString(R.styleable.view_text_serial_serial_text) ?: "·"
        contentTxt = attr.getString(R.styleable.view_text_serial_content_text) ?: ""
        // default color is R.attr.color_c1e1e1e_cebffffff in xml layout
        textColor = attr.getColor(
            R.styleable.view_text_serial_serial_text_color,
            -1
        )
        initView()
        attr.recycle()
    }

    open fun initView() {

        parent?.tvSerialNum?.text = serialTxt
        if (serialTxt == ".") {
            parent?.tvSerialNum?.setTypeface(null, Typeface.BOLD)
        }
        parent?.tvContent?.text = contentTxt
        if (contentMaxLines != -1) {
            parent?.tvContent?.maxLines = contentMaxLines
            parent?.tvContent?.ellipsize = TextUtils.TruncateAt.END
        }
        if (textColor != -1) {
            parent?.tvSerialNum?.setTextColor(textColor)
            parent?.tvContent?.setTextColor(textColor)
        }
    }

    fun setTextColor(textColor: Int) {
        if (textColor != -1) {
            parent?.tvSerialNum?.setTextColor(
                AttrResourceUtil.getColor(context, textColor)
            )
            parent?.tvContent?.setTextColor(
                AttrResourceUtil.getColor(context, textColor)
            )
        }
    }

    fun setContentText(text: CharSequence) {
        contentTxt = text
        parent?.tvContent?.text = contentTxt
    }

    fun getContentText(): String {
        return contentTxt.toString()
    }

    fun text(): String {
        return parent?.tvContent?.text.toString()
    }

    fun textView(): TextView? {
        return parent?.tvContent
    }

    fun updateSerialNum(serialNum: String) {
        parent?.tvSerialNum?.text = serialNum
    }
}
