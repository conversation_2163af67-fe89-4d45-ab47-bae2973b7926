package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import cn.com.vau.R
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.PopupKlineSymbolSearchGuideBinding
import cn.com.vau.util.clickNoRepeat
import com.lxj.xpopup.core.AttachPopupView

@SuppressLint("ViewConstructor")
class AttchKLineSymbolPopup(
    context: Context,
) : AttachPopupView(context) {

    private var binding: PopupKlineSymbolSearchGuideBinding? = null

    override fun getImplLayoutId(): Int = R.layout.popup_kline_symbol_search_guide

    override fun onCreate() {
        super.onCreate()
        binding = PopupKlineSymbolSearchGuideBinding.bind(popupImplView)
        binding?.tvNext?.clickNoRepeat {
            dismiss()
        }
    }

    override fun onDismiss() {
        super.onDismiss()
        SpManager.putKlineSymbolGuide()
    }
}