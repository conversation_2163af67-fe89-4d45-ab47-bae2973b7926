package cn.com.vau.common.base

import androidx.annotation.Keep

@Keep
data class UserInfo(
    /**
     * userId
     */
    var userId: String? = "",
    /**
     * 登录Token
     */
    var loginToken: String? = "",
    /**
     * 用户类型  1:普通账户  0:IB账户
     */
    var userType: Int? = -1,
    /**
     * accountType
     */
    var accountType: String? = "",
    /**
     * 登录所选账户的账户号
     */
    var accountId: String? = "",
    /**
     * 账户货币类型
     */
    var currencyType: String? = "",
    /**
     * 交易Token
     */
    var mt4Token: String? = "",
    /**
     * 交易密码
     */
    var mt4PWD: String? = "",
    /**
     * 当前账号状态 1:审核中,2:真实账户,3:模拟账号,4:返佣账户
     */
    var mt4State: String? = "",
    /**
     * 国家code
     */
    var countryCode: String? = "",
    /**
     * 地区code
     */
    var areaCode: String? = "",
    /**
     * 服务器id  默认未登录是5
     */
    var serverId: String? = "5",
    /**
     * 跟单账号id 后台入参使用跟单账号serverAccountId
     */
    var serverAccountId: String? = "",
    /**
     * 跟单账号返參PortfolioId
     */
    var masterPortfolioId: String? = "",
    /**
     * 跟单账号是否是信号源
     */
    var isSignal: String? = "",
    /**
     * 跟单账号创建日期
     */
    var createdTime: String? = "",

    /**
     * 手机号
     */
    var userTel: String? = "",
    /**
     * 邮箱
     */
    var email: String? = "",
    /**
     * 昵称
     */
    var userNick: String? = "",
    /**
     * 真实姓名
     */
    var realName: String? = "",
    /**
     * 头像地址
     */
    var userPic: String? = "",
    /**
     * mt4 / mt5 / mts
     */
    var platform: String? = "",
    /**
     * 是否快速平仓
     */
    var isFastClose: String? = "",
    /**
     * 是否开通真实账户  0未开通 1已开通
     */
    var isOpenRealAccount: String? = "",
    /**
     * 用户密码 用于Facebook授权后直接登录账户
     */
    var password: String? = "",
    /**
     * 开户类型
     * * 1：没有开下来户【注：没有交易账户或者跟单账户】
     * * 2：已开live账户没有开跟单账户
     * * 3：已开跟单账户没有开live账户
     * * 4：已开live账户和跟单账户
     */
    var openAccountType: Int? = -1,
    /**
     * 是否有跟单账户 || 是否有审核中的跟单账户
     */
    var hasStAccount: Boolean? = false,
) {

    fun isMt5(): Boolean {
        return "5" == platform
    }

    fun isIB(): Boolean {
        return 0 == userType
    }

    fun isStLogin(): Boolean {
        return "5" == mt4State
    }

    fun isLogin(): Boolean {
        return (accountId?.isNotEmpty() == true)
    }
}
