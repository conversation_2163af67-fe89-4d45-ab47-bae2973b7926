package cn.com.vau.common.greendao.dbUtils;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;

@Entity
public class DealLogInfo {

    @Id(autoincrement = true)
    private Long id;
    private String timeStamp;
    private String date;
    private String log;
    private String tel;
    @Generated(hash = 729989070)
    public DealLogInfo(Long id, String timeStamp, String date, String log,
            String tel) {
        this.id = id;
        this.timeStamp = timeStamp;
        this.date = date;
        this.log = log;
        this.tel = tel;
    }
    @Generated(hash = 1161397229)
    public DealLogInfo() {
    }
    public Long getId() {
        return this.id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getTimeStamp() {
        return this.timeStamp;
    }
    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }
    public String getDate() {
        return this.date;
    }
    public void setDate(String date) {
        this.date = date;
    }
    public String getLog() {
        return this.log;
    }
    public void setLog(String log) {
        this.log = log;
    }
    public String getTel() {
        return this.tel;
    }
    public void setTel(String tel) {
        this.tel = tel;
    }

}
