package cn.com.vau.common.view.sudoku;

import android.content.Context;
import android.graphics.Outline;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.ImageView;

import androidx.core.content.ContextCompat;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.util.AppUtil;
import cn.com.vau.util.DistKt;
import cn.com.vau.util.LogUtil;

public class GestureLockView extends ViewGroup {

    private int baseNum = 3;

    private int[] screenDispaly;

    private int d;
    private List<Point> pointList;
    private Context context;
    private Drawl drawl;

    private int pointRadius = 10;

    public GestureLockView(Context context, Drawl.GestureCallBack callBack) {
        super(context);
        screenDispaly = DistKt.getScreenDisplay(context);
        int offset = screenDispaly[0] / 650;
        if (offset < 1) {
            offset = 1;
        }
        d = screenDispaly[0] / offset / 3;
        this.pointList = new ArrayList();
        this.context = context;
        addChild();
        drawl = new Drawl(context, pointList, callBack);
    }

    private void addChild() {

        for (int i = 0; i < 9; i++) {

            ImageView image = new ImageView(context);
            image.setBackgroundColor(ContextCompat.getColor(context, AppUtil.isLightTheme() ? R.color.c1e1e1e : R.color.cebffffff));
            image.setOutlineProvider(new ViewOutlineProvider() {

                @Override
                public void getOutline(View view, Outline outline) {
                    outline.setRoundRect(
                            0,
                            0,
                            view.getWidth(),
                            view.getHeight(),
                            DistKt.dp2px(10)
                    );
                }
            });
            image.setClipToOutline(true);
            this.addView(image);

            int row = i / 3;
            int col = i % 3;

            int leftX = col * d + d / baseNum;
            int topY = row * d + d / baseNum;
            int rightX = col * d + d - d / baseNum;
            int bottomY = row * d + d - d / baseNum;

            Point p = new Point(leftX, rightX, topY, bottomY, image, i + 1);
            LogUtil.w("------" + i);
            this.pointList.add(p);
        }
    }

    public void setParentView(ViewGroup parent) {

        int width = screenDispaly[0];
        LayoutParams layoutParams = new LayoutParams(width, width);

        this.setLayoutParams(layoutParams);
        drawl.setLayoutParams(layoutParams);

        parent.addView(drawl);
        parent.addView(this);

    }

    @Override
    protected void onLayout(boolean changed, int l, int t, int r, int b) {
        for (int i = 0; i < getChildCount(); i++) {
            int row = i / 3;
            int col = i % 3;
            View v = getChildAt(i);
            Point point = pointList.get(i);
            int x = (point.getLeftX() + point.getRightX()) / 2;
            int y = (point.getTopY() + point.getBottomY()) / 2;
            v.layout(x - pointRadius, y - pointRadius, x + pointRadius, y + pointRadius);
        }
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        for (int i = 0; i < getChildCount(); i++) {
            View v = getChildAt(i);
            v.measure(widthMeasureSpec, heightMeasureSpec);
        }
    }

}
