package cn.com.vau.common.performance

import androidx.lifecycle.LifecycleOwner

class PerformManager(val lifecycleOwner: LifecycleOwner) {


    private val performanceList = mutableListOf<AbsPerformance>()

    fun addPerformance(performance: AbsPerformance) {
        //重复添加不生效
        if(performanceList.contains(performance)){
            return
        }
        performanceList.add(performance)
        lifecycleOwner.lifecycle.addObserver(performance)
    }

    fun removePerformance(performance: AbsPerformance) {
        performanceList.remove(performance)
        lifecycleOwner.lifecycle.removeObserver(performance)
    }

    fun unRegister() {
        performanceList.forEach {
            lifecycleOwner.lifecycle.removeObserver(it)
        }
    }

    /**
     * 自定义的生命周期，手动触发
     */
    fun onCustom() {
        performanceList.forEach {
            it.onCustom()
        }
    }

    /**
     * 自定义的生命周期，手动触发
     */
    fun onCallback() {
        performanceList.forEach {
            it.onCallback()
        }
    }


}