package cn.com.vau.common.http

import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.StAccountLoginBean
import cn.com.vau.data.discover.StrategyRecommendAllData
import cn.com.vau.data.ib.CategoryObj
import cn.com.vau.data.ib.MonthlyReturnRate
import cn.com.vau.data.ib.ProductChartData
import cn.com.vau.data.ib.StrategyShareData
import cn.com.vau.data.init.PositionOrdersBean
import cn.com.vau.data.strategy.SearchStrategyBean
import cn.com.vau.data.strategy.SignalDetailsBean
import cn.com.vau.data.strategy.StFansResBean
import cn.com.vau.data.strategy.StFavouriteResBean
import cn.com.vau.data.strategy.StFollowerStrategyPortfolioData
import cn.com.vau.data.strategy.StProfileCopyPageTotalsBean
import cn.com.vau.data.strategy.StProfileCopyResBean
import cn.com.vau.data.strategy.StProfileStrategiesBean
import cn.com.vau.data.strategy.StProfileSummaryBean
import cn.com.vau.data.strategy.StSignalInfoData
import cn.com.vau.data.strategy.StStrategyCopySettingsData
import cn.com.vau.data.strategy.StStrategyFansCountBean
import cn.com.vau.data.strategy.StStrategySignalProviderCenterBean
import cn.com.vau.data.strategy.StTopBean
import cn.com.vau.data.strategy.StrategyBean
import cn.com.vau.data.strategy.StrategyCountBean
import cn.com.vau.data.strategy.StrategyDetailBean
import cn.com.vau.data.strategy.StrategyPositionListBean
import cn.com.vau.data.trade.BatchCloseBean
import cn.com.vau.data.trade.ClosedHistoryBean
import cn.com.vau.data.trade.KChartData
import cn.com.vau.data.trade.OpenConditionData
import cn.com.vau.data.trade.ReverseOpenPositionBean
import cn.com.vau.data.trade.StProductHotBean
import cn.com.vau.data.trade.StTradeHistoryOrdersBean
import cn.com.vau.data.trade.StTradeOrderUpdateBean
import cn.com.vau.data.trade.StTradePositionUpdateBean
import cn.com.vau.data.trade.StTrendBean
import cn.com.vau.data.trade.TradeLossHistoryBean.TradeData
import cn.com.vau.history.data.FundingData
import cn.com.vau.history.data.FundingRequestParams
import cn.com.vau.history.data.HistoryDetailRequestParams
import cn.com.vau.history.data.HistoryItemData
import cn.com.vau.history.data.PositionDetailWrapper
import cn.com.vau.history.data.PositionRequestParams
import cn.com.vau.trade.bean.kchart.ProductInfoBean
import com.google.gson.JsonObject
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.QueryMap

/**
 * 跟单接口
 * Author: GG
 * Date: 2025/2/15
 * Description:
 */
interface BaseStTradingUrlService {
    /**
     * st profile 页面 信号源中心数据 ， 用于获取策略数量 和 活跃跟单者数量
     */
    @POST("strategy/signal/providerCenter")
    suspend fun strategySignalProviderCenterApi(@Body body: RequestBody?): ApiResponse<StStrategySignalProviderCenterBean.Data>

    /**
     * 信号源中心->跟单审核列表
     */
    @GET("strategy-copy/multi-account-page")
    suspend fun strategyCopyMultiAccountPageApi(
        @Query("stUserId") stUserId: String?,
        @Query("applyStatus") applyStatus: String?,
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int,
    ): ApiResponse<StProfileCopyResBean>

    /**
     * 信号源中心->summary、分润卡片summary
     */
    @POST("strategy/signal/multi-account-summary")
    suspend fun strategySignalMultiAccountSummaryApi(@Body body: RequestBody?): ApiResponse<StProfileSummaryBean>

    /**
     * 信号源中心->跟单审核通过
     */
    @POST("strategy-copy/multi-account-approve")
    suspend fun strategyCopyMultiAccountApproveApi(@Body body: RequestBody?): ApiResponse<Any?>

    /**
     * 信号源中心->跟单审核拒绝
     */
    @POST("strategy-copy/multi-account-reject")
    suspend fun strategyCopyMultiAccountRejectApi(@Body body: RequestBody?): ApiResponse<Any?>

    /**
     * 信号源中心->Copier Review Tab上展示的数量
     */
    @GET("strategy-copy/multi-account-page/totals")
    suspend fun strategyCopyMultiAccountPageTotalsApi(
        @Query("stUserId") stUserId: String?,
        @Query("accountId") accountId: String?,
    ): ApiResponse<StProfileCopyPageTotalsBean>

    /**
     * 信号源中心->策略列表
     */
    @GET("strategy/list")
    suspend fun strategyListApi(@Query("stUserId") stUserId: String?): ApiResponse<StProfileStrategiesBean>

    /**
     * 上架策略
     */
    @GET("strategy/publish")
    suspend fun strategyPublishApi(
        @Query("stUserId") stUserId: String?,
        @Query("strategyId") strategyId: String?,
    ): ApiResponse<StrategyBean>

    /**
     * 下架策略
     */
    @POST("strategy/delist")
    suspend fun strategyDelistApi(@Body body: RequestBody?): ApiResponse<Any?>

    /**
     * 创建策略
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy/new-with-account")
    suspend fun strategyNewWithAccountApi(@Body data: JsonObject): ApiResponse<StrategyBean>

    /**
     * st 多策略汇总接口
     */
    @GET("strategy/discover/listAll")
    suspend fun strategyDiscoverListAll(@Query("accountId") accountId: String): ApiResponse<StrategyRecommendAllData>

    /**
     * 策略排行榜
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy/filter-signal")
    suspend fun strategyFilterSignalApi(@Body data: JsonObject): ApiResponse<MutableList<StrategyBean>?>

    /**
     * 修改策略
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy/update")
    suspend fun strategyUpdateApi(@Body data: JsonObject): ApiResponse<StrategyBean>

    /**
     * 获取策略详情
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("strategy/load")
    suspend fun strategyLoadApi(@Body data: JsonObject): ApiResponse<StrategyBean>

    /**
     * 粉丝列表
     */
    @GET("watched-relation/list/fans")
    suspend fun watchedRelationListFansApi(
        @Query("stUserId") stUserId: String?, @Query("strategyId") strategyId: String?,
        @Query("pageNum") pageNum: Int, @Query("pageSize") pageSize: Int
    ): ApiResponse<StFansResBean>

    /**
     * st 关注列表
     */
    @GET("watched-relation/list/watching")
    suspend fun watchedRelationListWatchingApi(
        @Query("stUserId") stUserId: String?,
        @Query("pageNum") pageNum: Int,
        @Query("pageSize") pageSize: Int
    ): ApiResponse<StFavouriteResBean>

    /**
     * st 收藏策略
     */
    @POST("watched-relation/save")
    suspend fun watchedRelationSaveApi(@Body body: RequestBody?): ApiResponse<Any?>

    /**
     * 信号源详情
     */
    @POST("user/signalDetails")
    suspend fun userSignalDetailsApi(@Body body: RequestBody?): ApiResponse<SignalDetailsBean>

    /**
     * 热门搜索 策略
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @GET("strategy/getTopClickStrategies")
    suspend fun strategyGetTopClickStrategies(
        @Query("stUserId") stUserId: String?
    ): ApiResponse<MutableList<StTopBean>>

    /**
     * 热门搜索 信号源
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @GET("user/getTopClickSignals")
    suspend fun userGetTopClickSignals(
        @Query("stUserId") stUserId: String?,
        @Query("currentLoginAccountId") currentLoginAccountId: String?
    ): ApiResponse<MutableList<StTopBean>>

    /**
     * 搜索策略
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("signal/list/search")
    suspend fun signalListSearch(
        @Body data: JsonObject
    ): ApiResponse<MutableList<SearchStrategyBean>>

    /**
     * 按信号源昵称搜索
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("user/search")
    suspend fun userSearch(
        @Body data: JsonObject
    ): ApiResponse<MutableList<SearchStrategyBean>>

    /**
     * 策略详情顶部卡片
     */
    @POST("strategy/detail/top")
    suspend fun strategyDetailTopApi(@Body body: RequestBody?): ApiResponse<StrategyBean>

    /**
     * 策略持仓列表
     */
    @POST("strategy/position/analysis")
    suspend fun strategyPositionAnalysisApi(@Body body: RequestBody?): ApiResponse<StrategyPositionListBean>

    /**
     * st 取消收藏策略
     */
    @POST("watched-relation/remove")
    suspend fun watchedRelationRemoveApi(@Body body: RequestBody?): ApiResponse<Any?>

    /**
     * st 策略粉丝数量
     */
    @GET("strategy/fans/count")
    suspend fun strategyFansCountApi(
        @Query("stUserId") stUserId: String?,
        @Query("strategyId") strategyId: String?
    ): ApiResponse<StStrategyFansCountBean>

    /**
     * 互抵平仓 -- 跟单
     */
    @POST("trade/position/mutual-offset-close-position")
    suspend fun stTradePositionMutualOffsetClosePosition(@Body body: RequestBody?): BaseBean

    /**
     * 平仓历史--跟单
     */
    @GET("trade/list/closeHistory")
    suspend fun tradeListCloseHistory(
        @Query("positionId") positionId: String?,
        @Query("portfolioId") portfolioId: String?
    ): ApiResponse<ClosedHistoryBean?>

    /**
     * 获取产品详情属性--跟单
     */
    @GET("trade/product/detail")
    suspend fun tradeProductDetailApi(
        @Query("symbol") symbol: String, // 产品名
        @Query("login") login: String // 跟单账户stAccountId
    ): ApiResponse<ProductInfoBean>

    /**
     * ST 【跟單】自选产品 移动
     */
    @POST("account/product/my/upd")
    suspend fun stAccountProductMyUpd(@Body body: RequestBody?): ApiResponse<Any>

    /**
     * st 根据产品搜索策略
     */
    @POST("signal/list/monthly-signals-by-product")
    suspend fun signalListMonthlySignalsByProduct(@Body body: RequestBody?): ApiResponse<ArrayList<SearchStrategyBean>?>

    /**
     * 批量平仓--跟单
     */
    @POST("trade/position/batch-close")
    suspend fun tradePositionBatchCloseApi(@Body body: RequestBody?): ApiResponse<BatchCloseBean>

    /**
     * 获取挂单列表 -- 跟单
     */
    @GET("trade/list/order/v2")
    suspend fun stTradeListOrderV2Api(
        @Query("type") type: String,
        @Query("portfolioId") portfolioId: String?
    ): PositionOrdersBean?

    /**
     * 取消挂单 -- 跟单
     */
    @POST("trade/position/cancel")
    suspend fun stTradePositionCancelApi(@Body body: RequestBody?): BaseBean?

    /**
     * 关闭订单/平仓 -- 跟单
     */
    @POST("trade/position/close")
    suspend fun stTradePositionCloseApi(@Body body: RequestBody?): BaseBean?

    /**
     * 反向平仓 -- 跟单
     */
    @POST("trade/position/reverse-open-position")
    suspend fun stTradePositionReverseOpenPositionApi(@Body body: RequestBody?): ApiResponse<ReverseOpenPositionBean.Data>

    /**
     * st 策略订单详情页 - settings
     * Type : Pending、Rejected、Following、Stopped
     * id : follow_request.id / portfolio.id
     */
    @GET("strategy-copy/settings")
    suspend fun strategyCopySettingsApi(@Query("type") type: String?, @Query("id") id: String?): ApiResponse<StStrategyCopySettingsData>

    /**
     * st 跟单策略 增加/减少 资金
     */
    @POST("trade/update/allocation/v2")
    suspend fun tradeUpdateAllocationApi(@Body body: RequestBody?): ApiResponse<Any?>

    /**
     * 跟单-策略订单-历史列表
     */
    @POST("trade/list/deals-history/page")
    suspend fun tradeListDealsHistoryPage(@QueryMap map: HashMap<String, Any>): ApiResponse<StTradeHistoryOrdersBean.Data>

    /**
     * st 修改挂单
     */
    @POST("trade/order/update")
    suspend fun tradeOrderUpdateApi(@Body body: RequestBody?): StTradeOrderUpdateBean

    /**
     * st 下单
     */
    @POST("trade/position/open")
    suspend fun tradePositionOpenApi(@Body body: RequestBody?): ApiResponse<String>

    /**
     * st 挂单
     */
    @POST("trade/order/open")
    suspend fun tradeOrderOpenApi(@Body body: RequestBody?): StTradePositionUpdateBean

    /**
     * 跟单登录
     */
    @POST("account/login-with-account")
    suspend fun accountLoginWithAccountApi(@Body body: RequestBody?): ApiResponse<StAccountLoginBean.Data>

    /**
     * 跟单亏损历史订单列表
     */
    @POST("trade/list/deal-history-loss")
    suspend fun tradeListDealHistoryLossApi(@QueryMap map: HashMap<String, Any?>): ApiResponse<List<TradeData>>

    /**
     * 信号源详情图表 月回报率
     */
    @POST("strategy/detail/top")
    suspend fun shareStrategyDetailTop(@Body body: RequestBody?): StrategyDetailBean?

    /**
     * st 分享 策略 详情图表 月回报率
     */
    @POST("social-trade/return-rate-chart/v2")
    suspend fun socialTradeReturnRateChart(@Body body: RequestBody?): ApiResponse<MonthlyReturnRate>

    /**
     * st 分享 策略 图表 月回报率
     */
    @POST("social-trade/trade/category-chart")
    suspend fun socialTradeTradeCategoryChart(@Query("accountId") accountId: String?): ApiResponse<CategoryObj>?

    /**
     * st 分享 策略 图表 月回报率
     */
    @POST("social-trade/trade/product-chart")
    suspend fun socialTradeTradeProductChart(@Query("accountId") accountId: String?): ApiResponse<List<ProductChartData>>?

    /**
     * st 查询跟单帐号热门产品
     */
    @POST("product/hot")
    suspend fun productHot(): StProductHotBean?

    /**
     * st 走势图 -- 产品搜索 -- 热门产品
     */
    @POST("history/getRunChart")
    suspend fun historyGetRunChart(@Body body: RequestBody?): StTrendBean?

    /**
     * st 获取分润结算周期
     */
    @GET("strategy/get-profit-share-cycle-type")
    suspend fun strategyGetProfitShareCycleTypeApi(): ApiResponse<String>

    /**
     * 成为信号源(3.48.0之后新接口)
     */
    @Headers("Content-Type: application/json", "Accept: application/json")
    @POST("user/become-signal")
    suspend fun userBecomeSignalApi(@Body data: JsonObject): ApiResponse<*>

    /**
     * st 公开交易
     */
    @POST("account/openCondition")
    suspend fun accountOpenConditionApi(@Query("accountId") accountId: String?): ApiResponse<OpenConditionData>

    /**
     * 跟单公开交易页面的 接受条款
     */
    @POST("account/update/authorityAccepted")
    suspend fun accountUpdateAuthorityAcceptedApi(@Query("accountId") accountId: String?): ApiResponse<*>

    /**
     * 信号源公开页面的一次性入金金额
     */
    @GET("account/get-deposit-authority-amount")
    suspend fun accountGetDepositAuthorityAmountApi(@Query("accountId") accountId: String?, @Query("currency") currency: String?): ApiResponse<String>

    /**
     * 持仓历史
     */
    @POST("trade/list/getOrderHistoryByParam")
    suspend fun tradeOrdersListOrderPageApi(@Body body: PositionRequestParams): ApiResponse<ArrayList<HistoryItemData>>

    /**
     * 持仓历史详情
     */
    @POST("trade/getOrderDetailByOrderIdAndLogin")
    suspend fun tradeOrdersListOrderPageDetailApi(@Body body: HistoryDetailRequestParams): ApiResponse<PositionDetailWrapper>

    /**
     * 资金历史
     */
    @POST("trade/list/getFundHistoryByParam")
    suspend fun tradeOrdersListFundPageApi(@Body body: FundingRequestParams): ApiResponse<ArrayList<FundingData>>

    /**
     * st 获取某个信号源信息(Get signal info)
     */
    @POST("signal/get")
    suspend fun signalGetApi(
        @Query("accountId") accountId: String?,
        @Query("signalId") signalId: String?
    ): ApiResponse<StSignalInfoData>

    /**
     * 跟单-k线请求
     */
    @POST("history/getKLine/mts")
    suspend fun klineStHistoryMarketsApi(@Body body: RequestBody?): ApiResponse<KChartData>

    /**
     * st 修改持仓订单
     */
    @POST("trade/position/update")
    suspend fun tradePositionUpdateApi(@Body body: RequestBody?): StTradePositionUpdateBean

    /**
     * st 上架策略数量
     */
    @POST("strategy/count")
    suspend fun strategyCountApi(@Body body: RequestBody?): StrategyCountBean

    /**
     * st 撤单（ 取消挂单 ）
     */
    @POST("trade/position/cancel")
    suspend fun tradePositionCancelApi(@Body body: RequestBody?): ApiResponse<*>

    /**
     * st 平仓
     */
    @POST("trade/position/close")
    suspend fun tradePositionCloseApi(@Body body: RequestBody?): ApiResponse<*>

    /**
     * st 停止跟随 策略/信号源
     */
    @POST("account/remove/follower/v2")
    suspend fun accountRemoveFollowerApi(@Query("portfolioId") portfolioId: String?): ApiResponse<*>

    /**
     * st 恢复跟随 策略/信号源
     */
    @POST("account/resume/following")
    suspend fun accountResumeFollowingApi(@Body body: RequestBody?): ApiResponse<*>

    /**
     * st 暂停跟随 策略/信号源
     */
    @POST("account/pause/following")
    suspend fun accountPauseFollowingApi(@Body body: RequestBody?): ApiResponse<*>

    /**
     * st 策略订单详情页 - 分润 profit-sharing
     */
    @GET("profit-sharing/profile/follower-portfolio")
    suspend fun profitSharingProfileFollowerPortfolioApi(@Query("portfolioId") portfolioId: String?): ApiResponse<StFollowerStrategyPortfolioData>

    /**
     * 根据 strategyId 查询策略3月回报率以及累计和当前跟单者数量
     */
    @POST("strategy/share-postert-by-strategy-id")
    suspend fun strategySharePostertByStrategyIdApi(@Query("strategyId") strategyId: String?): ApiResponse<StrategyShareData?>

    /**
     * 设置策略详情页展示内容
     */
    @POST("strategy/position/setPortfolioShow")
    suspend fun strategyPositionSetPortfolioShowApi(@Body body: RequestBody?): ApiResponse<Any?>
}