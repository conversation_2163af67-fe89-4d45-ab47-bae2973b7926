package cn.com.vau.common.view.share

import androidx.appcompat.app.AppCompatActivity
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.share.SharePopup.Companion.TYPE_RAF
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.data.ib.CategoryObj
import cn.com.vau.data.ib.InvitationsData
import cn.com.vau.data.ib.MonthlyReturnRate
import cn.com.vau.data.ib.ProductChartData
import cn.com.vau.data.ib.StrategyShareData
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import java.time.LocalDate

/**
 * Filename: ShareDateRequestUtils.kt
 * Author: GG
 * Date: 2024 7月 12
 * Description:
 */
class ShareViewModel : BaseViewModel() {

    /**
     * 获取ib账户分享的账号列表
     */
    suspend fun getAccountList(): List<AccountTradeBean>? {
        val params = HashMap<String, Any>()
        params["token"] = UserDataUtil.loginToken()
        val data = baseService.queryAccountList(params)
        return if (data.isSuccess()) {
            data.data?.obj?.listRebateAccount
        } else {
            ToastUtil.showToast(data.getResponseMsg())
            null
        }
    }

    /**
     * 获取分享的邀请信息 url 以及 二维码等
     */
    suspend fun getRefereeInfo(
        signalId: String? = "",
        shareType: Int? = null,
    ): InvitationsData? {
        val params = HashMap<String, Any>()
        params["token"] = UserDataUtil.loginToken()
        params["imgType"] = "5"
        params["fitModel"] = "0"
        params["inviteStatus"] = when (shareType) {
            TYPE_RAF -> "2"
            else -> "1"
        }
        params["activityType"] = when (shareType) {
            TYPE_RAF -> "2"
            else -> "1"
        }
        if (!signalId.isNullOrBlank())
            params["signalId"] = signalId

        val invitationsBean = baseService.getRefereeInfo(params)
        if (invitationsBean.isSuccess()) {
            return invitationsBean.data
        } else {
            ToastUtil.showToast(invitationsBean.getResponseMsg())
            throw Exception(invitationsBean.getResponseMsg())
        }
    }

    /**
     * 获取ib 账户 分享的邀请信息 url 以及 二维码等
     */
    suspend fun getRefereeInfoIB(
        signalId: String? = "",
        shareType: Int? = null,
    ): InvitationsData? {
        val params = HashMap<String, Any>()
        params["token"] = UserDataUtil.loginToken()
        params["imgType"] = "5"
        params["fitModel"] = "0"
        if (shareType == TYPE_RAF) {
            params["activityType"] = "2"
            params["inviteStatus"] = "2"
        } else {
            params["activityType"] = "1"
            params["inviteStatus"] = "1"
        }
        if (!signalId.isNullOrBlank())
            params["signalId"] = signalId
        params["loginUserId"] = UserDataUtil.userId()
        params["mt4AccountId"] = SpManager.getInvitationLastSelectAccount()

        val invitationsBean = baseService.getRefereeInfo(params)
        if (invitationsBean.isSuccess()) {
            return invitationsBean.data
        } else {
            ToastUtil.showToast(invitationsBean.getResponseMsg())
            throw Exception(invitationsBean.getResponseMsg())
        }
    }

    /**
     * 获取月化收益率，部分分享页面只有策略id，所以这种情况需要特殊请求一下
     */
    suspend fun getMonthlyReturnRate(
        /**
         * 策略 id
         */
        strategyId: String? = null,
    ): MonthlyReturnRate? {
        val jsonObject = JsonObject()
        jsonObject.addProperty("accountId", strategyId)
        jsonObject.addProperty("startYear", "2023")
        jsonObject.addProperty("endYear", "${LocalDate.now().year}")
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val sTSignalDataReturnRateItemBean = stTradingService.socialTradeReturnRateChart(requestBody)

        if (sTSignalDataReturnRateItemBean.isSuccess()) {
            return sTSignalDataReturnRateItemBean.data
        } else {
            ToastUtil.showToast(sTSignalDataReturnRateItemBean.getResponseMsg())
            throw Exception(sTSignalDataReturnRateItemBean.getResponseMsg())
        }
    }

    /**
     * 获取交易分类，部分分享页面只有策略id，所以这种情况需要特殊请求一下
     */
    suspend fun getCategoryObj(activity: AppCompatActivity, strategyId: String?): CategoryObj? {
        val sTSignalDataCategoryItemBean = stTradingService.socialTradeTradeCategoryChart(strategyId)
        if (sTSignalDataCategoryItemBean?.isSuccess() == true) {
            // 该策略已下架
            if (sTSignalDataCategoryItemBean.data?.offLine == true) {
                CenterActionDialog.Builder(activity)
                    .setContent(activity.getString(R.string.this_strategy_has_signal_provider))
                    .setSingleButton(true)
                    .setSingleButtonText(activity.getString(R.string.ok))
                    .build()
                    .showDialog()
                throw Exception(activity.getString(R.string.this_strategy_has_signal_provider))
            }
            return sTSignalDataCategoryItemBean.data
        } else {
            ToastUtil.showToast(sTSignalDataCategoryItemBean?.getResponseMsg())
            throw Exception(sTSignalDataCategoryItemBean?.getResponseMsg())
        }
    }

    /**
     * 获取交易产品
     */
    suspend fun getProductList(strategyId: String?): List<ProductChartData>? {
        val data = stTradingService.socialTradeTradeProductChart(strategyId)
        if (data?.isSuccess() == true) {
            return data.data
        } else {
            ToastUtil.showToast(data?.getResponseMsg())
            throw Exception(data?.getResponseMsg())
        }
    }

    /**
     * 根据 strategyId 查询策略3月回报率以及累计和当前跟单者数量
     */
    suspend fun strategySharePostertByStrategyIdApi(strategyId: String?): StrategyShareData? {
        val data = stTradingService.strategySharePostertByStrategyIdApi(strategyId)
        if (data.isSuccess()) {
            return data.data
        } else {
            ToastUtil.showToast(data.getResponseMsg())
            throw Exception(data.getResponseMsg())
        }
    }

    /**
     * 获取IB分享的邀请信息 url 以及 二维码等 这个请求需要传入loginUserId以及mt4AccountId
     */
    suspend fun getIBRefereeInfo(accountId: String?, signalId: String? = null): InvitationsData? {
        val params = HashMap<String, Any>()
        params["token"] = UserDataUtil.loginToken()
        params["imgType"] = "5"
        params["fitModel"] = "0"
        params["loginUserId"] = UserDataUtil.userId()
        params["mt4AccountId"] = accountId ?: SpManager.getInvitationLastSelectAccount()
        params["activityType"] = "1"
        params["inviteStatus"] = "1"
        if (!signalId.isNullOrEmpty())
            params["signalId"] = signalId

        val invitationsBean = baseService.getRefereeInfo(params)
        if (invitationsBean.isSuccess()) {
            return invitationsBean.data
        } else {
            ToastUtil.showToast(invitationsBean.getResponseMsg())
            throw Exception(invitationsBean.getResponseMsg())
        }
    }

}