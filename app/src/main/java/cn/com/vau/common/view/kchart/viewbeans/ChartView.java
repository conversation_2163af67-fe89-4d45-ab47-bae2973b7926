package cn.com.vau.common.view.kchart.viewbeans;

import android.view.MotionEvent;

import java.util.List;

import cn.com.vau.common.view.kchart.views.ChartViewImp;

/**
 * 描述：绘图基础组件接口
 */
public interface ChartView {

    void addChild(ViewContainer vc);

    void removeAllChildren();

    void removeChild(ViewContainer vc);

    ViewContainer getFocusedView();

    void requestFocusChild(ViewContainer vc);

    void requestSyncDataWithFocused();

    boolean isFocused(ViewContainer vc);

    List<ViewContainer<Object>> getChildren();

    void setYMax(float YMax);

    void setYMin(float YMin);

    void snapshotSwitch(boolean switcher);

    boolean isSnapshotOpen();

    boolean onTouchEvent(MotionEvent event);

    void followTouch(ChartViewImp view);

    void loseFollow(ChartViewImp view);

}
