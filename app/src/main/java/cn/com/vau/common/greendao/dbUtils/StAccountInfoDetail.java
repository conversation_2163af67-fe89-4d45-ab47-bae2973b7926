package cn.com.vau.common.greendao.dbUtils;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;

/**
 * 跟单账户数据库 Detail
 * 升级数据库操作步骤：
 * 1.表增加列，映射类增加字段，对应Dao类增加配置
 * 2.@Generated(hash = xxxxx)哈希值需要更新，更新方法：删掉(hash = xxxxx)后编译自动生成
 * 3.gradle中修改升级库版本
 * 4.检查MySQLiteOpenHelper类中是否添加相应Dao类，否则不会迁移数据
 */
@Entity
public class StAccountInfoDetail {
    @Id(autoincrement = true)
    private Long id;                                                                    // ----Not Used
    private String accountId;//跟單用户id                                                 DBUtil stAccountId ?
    private String stToken;//跟單帳號token                                                DBUtil token
    private String serverAccountId;//跟單帳號id後台入參使用跟單帳號serverAccountId           DBUtil accountCd   ?
    private String masterPortfolioId;//跟單帳號返參PortfolioId                            DBUtil stMasterPortfolioId  ?
    private String followPortfolioId;//跟單帳號返參Master PortfolioId                     // ----Not Used
    private String nickName;//跟單帳號暱稱                                                保存在 UserInfoDetail userNick  ----Not Used
    private String serverId; //跟單帳號serverId                                          DBUtil serverId
    private String currencyType = "USD"; //跟單帳號貨幣 跟單帳號目前只有默認USD (2020.04)    DBUtil currencyType
    private String isSignal; //跟單帳號isSignal 公开交易                                  DBUtil isStPublicTrade
    private String createdTime; //跟單帳號创建日期                                        // ----Not Used
    private Boolean isStarSignal;                                                       // ----Not Used
    private String stUserId;//跟單多策略用户id                                            DBUtil stUserId

    @Generated(hash = **********)
    public StAccountInfoDetail(Long id, String accountId, String stToken, String serverAccountId, String masterPortfolioId, String followPortfolioId,
            String nickName, String serverId, String currencyType, String isSignal, String createdTime, Boolean isStarSignal, String stUserId) {
        this.id = id;
        this.accountId = accountId;
        this.stToken = stToken;
        this.serverAccountId = serverAccountId;
        this.masterPortfolioId = masterPortfolioId;
        this.followPortfolioId = followPortfolioId;
        this.nickName = nickName;
        this.serverId = serverId;
        this.currencyType = currencyType;
        this.isSignal = isSignal;
        this.createdTime = createdTime;
        this.isStarSignal = isStarSignal;
        this.stUserId = stUserId;
    }

    @Generated(hash = *********)
    public StAccountInfoDetail() {
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAccountId() {
        return this.accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getStToken() {
        return this.stToken;
    }

    public void setStToken(String stToken) {
        this.stToken = stToken;
    }

    public String getServerAccountId() {
        return this.serverAccountId;
    }

    public void setServerAccountId(String serverAccountId) {
        this.serverAccountId = serverAccountId;
    }

    public String getMasterPortfolioId() {
        return this.masterPortfolioId;
    }

    public void setMasterPortfolioId(String masterPortfolioId) {
        this.masterPortfolioId = masterPortfolioId;
    }

    public String getNickName() {
        return this.nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getServerId() {
        return this.serverId;
    }

    public void setServerId(String serverId) {
        this.serverId = serverId;
    }

    public String getCurrencyType() {
        return this.currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public String getIsSignal() {
        return isSignal;
    }

    public void setIsSignal(String isSignal) {
        this.isSignal = isSignal;
    }

    public String getFollowPortfolioId() {
        return followPortfolioId;
    }

    public void setFollowPortfolioId(String followPortfolioId) {
        this.followPortfolioId = followPortfolioId;
    }

    public String getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(String createdTime) {
        this.createdTime = createdTime;
    }

    public Boolean getIsStarSignal() {
        return isStarSignal;
    }

    public void setIsStarSignal(Boolean isStarSignal) {
        this.isStarSignal = isStarSignal;
    }

    public String getStUserId() {
        return stUserId;
    }

    public void setStUserId(String stUserId) {
        this.stUserId = stUserId;
    }
}
