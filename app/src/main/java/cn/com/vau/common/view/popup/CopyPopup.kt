package cn.com.vau.common.view.popup

import android.content.Context
import cn.com.vau.R
import cn.com.vau.databinding.PopupCopyBinding
import com.lxj.xpopup.core.AttachPopupView

/**
 * Filename: CopyPopup.kt
 * Author: GG
 * Date: 2024/2/20
 * Description:
 */
class CopyPopup(context: Context) : AttachPopupView(context) {

    private var mBinding: PopupCopyBinding? = null

    private var click: (() -> Unit)? = null

    override fun getImplLayoutId(): Int = R.layout.popup_copy

    override fun onCreate() {
        super.onCreate()
        mBinding = PopupCopyBinding.bind(popupImplView)

        mBinding?.root?.setOnClickListener {
            click?.invoke()
            dismiss()
        }
    }

    fun setClick(click: (() -> Unit)?): CopyPopup {
        this.click = click
        return this
    }

}