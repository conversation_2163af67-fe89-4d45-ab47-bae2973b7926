package cn.com.vau.common.greendao.common;

import android.database.Cursor;
import android.database.sqlite.SQLiteStatement;

import org.greenrobot.greendao.AbstractDao;
import org.greenrobot.greendao.Property;
import org.greenrobot.greendao.internal.DaoConfig;
import org.greenrobot.greendao.database.Database;
import org.greenrobot.greendao.database.DatabaseStatement;

import cn.com.vau.common.greendao.dbUtils.UserInfoDetail;

// THIS CODE IS GENERATED BY greenDAO, DO NOT EDIT.
/** 
 * DAO for table "USER_INFO_DETAIL".
*/
public class UserInfoDetailDao extends AbstractDao<UserInfoDetail, Long> {

    public static final String TABLENAME = "USER_INFO_DETAIL";

    /**
     * Properties of entity UserInfoDetail.<br/>
     * Can be used for QueryBuilder and for referencing column names.
     */
    public static class Properties {
        public final static Property Id = new Property(0, Long.class, "id", true, "_id");
        public final static Property UserId = new Property(1, String.class, "userId", false, "USER_ID");
        public final static Property LoginToken = new Property(2, String.class, "loginToken", false, "LOGIN_TOKEN");
        public final static Property UserType = new Property(3, int.class, "userType", false, "USER_TYPE");
        public final static Property UserTel = new Property(4, String.class, "userTel", false, "USER_TEL");
        public final static Property AccountCd = new Property(5, String.class, "accountCd", false, "ACCOUNT_CD");
        public final static Property Mt4Token = new Property(6, String.class, "mt4Token", false, "MT4_TOKEN");
        public final static Property Mt4PWD = new Property(7, String.class, "mt4PWD", false, "MT4_PWD");
        public final static Property Mt4State = new Property(8, String.class, "mt4State", false, "MT4_STATE");
        public final static Property Mt4NickName = new Property(9, String.class, "mt4NickName", false, "MT4_NICK_NAME");
        public final static Property CountryCode = new Property(10, String.class, "countryCode", false, "COUNTRY_CODE");
        public final static Property AreaCode = new Property(11, String.class, "areaCode", false, "AREA_CODE");
        public final static Property CreditAmount = new Property(12, String.class, "creditAmount", false, "CREDIT_AMOUNT");
        public final static Property ServerId = new Property(13, String.class, "serverId", false, "SERVER_ID");
        public final static Property AccountType = new Property(14, String.class, "accountType", false, "ACCOUNT_TYPE");
        public final static Property Email = new Property(15, String.class, "email", false, "EMAIL");
        public final static Property UserNick = new Property(16, String.class, "userNick", false, "USER_NICK");
        public final static Property UserPic = new Property(17, String.class, "userPic", false, "USER_PIC");
        public final static Property Platform = new Property(18, String.class, "platform", false, "PLATFORM");
        public final static Property CurrencyType = new Property(19, String.class, "currencyType", false, "CURRENCY_TYPE");
        public final static Property IsFastClose = new Property(20, String.class, "isFastClose", false, "IS_FAST_CLOSE");
        public final static Property IsOpenRealAccount = new Property(21, String.class, "isOpenRealAccount", false, "IS_OPEN_REAL_ACCOUNT");
        public final static Property Password = new Property(22, String.class, "password", false, "PASSWORD");
        public final static Property TrueName = new Property(23, String.class, "trueName", false, "TRUE_NAME");
        public final static Property ReadyOnlyAccount = new Property(24, Boolean.class, "readyOnlyAccount", false, "READY_ONLY_ACCOUNT");
        public final static Property IsMt5 = new Property(25, Boolean.class, "isMt5", false, "IS_MT5");
        public final static Property OpenAccountType = new Property(26, int.class, "openAccountType", false, "OPEN_ACCOUNT_TYPE");
        public final static Property LoginAccountType = new Property(27, int.class, "loginAccountType", false, "LOGIN_ACCOUNT_TYPE");
        public final static Property HasStAccount = new Property(28, Boolean.class, "hasStAccount", false, "HAS_ST_ACCOUNT");
    }


    public UserInfoDetailDao(DaoConfig config) {
        super(config);
    }
    
    public UserInfoDetailDao(DaoConfig config, DaoSession daoSession) {
        super(config, daoSession);
    }

    /** Creates the underlying database table. */
    public static void createTable(Database db, boolean ifNotExists) {
        String constraint = ifNotExists? "IF NOT EXISTS ": "";
        db.execSQL("CREATE TABLE " + constraint + "\"USER_INFO_DETAIL\" (" + //
                "\"_id\" INTEGER PRIMARY KEY AUTOINCREMENT ," + // 0: id
                "\"USER_ID\" TEXT," + // 1: userId
                "\"LOGIN_TOKEN\" TEXT," + // 2: loginToken
                "\"USER_TYPE\" INTEGER NOT NULL ," + // 3: userType
                "\"USER_TEL\" TEXT," + // 4: userTel
                "\"ACCOUNT_CD\" TEXT," + // 5: accountCd
                "\"MT4_TOKEN\" TEXT," + // 6: mt4Token
                "\"MT4_PWD\" TEXT," + // 7: mt4PWD
                "\"MT4_STATE\" TEXT," + // 8: mt4State
                "\"MT4_NICK_NAME\" TEXT," + // 9: mt4NickName
                "\"COUNTRY_CODE\" TEXT," + // 10: countryCode
                "\"AREA_CODE\" TEXT," + // 11: areaCode
                "\"CREDIT_AMOUNT\" TEXT," + // 12: creditAmount
                "\"SERVER_ID\" TEXT," + // 13: serverId
                "\"ACCOUNT_TYPE\" TEXT," + // 14: accountType
                "\"EMAIL\" TEXT," + // 15: email
                "\"USER_NICK\" TEXT," + // 16: userNick
                "\"USER_PIC\" TEXT," + // 17: userPic
                "\"PLATFORM\" TEXT," + // 18: platform
                "\"CURRENCY_TYPE\" TEXT," + // 19: currencyType
                "\"IS_FAST_CLOSE\" TEXT," + // 20: isFastClose
                "\"IS_OPEN_REAL_ACCOUNT\" TEXT," + // 21: isOpenRealAccount
                "\"PASSWORD\" TEXT," + // 22: password
                "\"TRUE_NAME\" TEXT," + // 23: trueName
                "\"READY_ONLY_ACCOUNT\" INTEGER," + // 24: readyOnlyAccount
                "\"IS_MT5\" INTEGER," + // 25: isMt5
                "\"OPEN_ACCOUNT_TYPE\" INTEGER NOT NULL ," + // 26: openAccountType
                "\"LOGIN_ACCOUNT_TYPE\" INTEGER NOT NULL ," + // 27: loginAccountType
                "\"HAS_ST_ACCOUNT\" INTEGER);"); // 28: hasStAccount
    }

    /** Drops the underlying database table. */
    public static void dropTable(Database db, boolean ifExists) {
        String sql = "DROP TABLE " + (ifExists ? "IF EXISTS " : "") + "\"USER_INFO_DETAIL\"";
        db.execSQL(sql);
    }

    @Override
    protected final void bindValues(DatabaseStatement stmt, UserInfoDetail entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String userId = entity.getUserId();
        if (userId != null) {
            stmt.bindString(2, userId);
        }
 
        String loginToken = entity.getLoginToken();
        if (loginToken != null) {
            stmt.bindString(3, loginToken);
        }
        stmt.bindLong(4, entity.getUserType());
 
        String userTel = entity.getUserTel();
        if (userTel != null) {
            stmt.bindString(5, userTel);
        }
 
        String accountCd = entity.getAccountCd();
        if (accountCd != null) {
            stmt.bindString(6, accountCd);
        }
 
        String mt4Token = entity.getMt4Token();
        if (mt4Token != null) {
            stmt.bindString(7, mt4Token);
        }
 
        String mt4PWD = entity.getMt4PWD();
        if (mt4PWD != null) {
            stmt.bindString(8, mt4PWD);
        }
 
        String mt4State = entity.getMt4State();
        if (mt4State != null) {
            stmt.bindString(9, mt4State);
        }
 
        String mt4NickName = entity.getMt4NickName();
        if (mt4NickName != null) {
            stmt.bindString(10, mt4NickName);
        }
 
        String countryCode = entity.getCountryCode();
        if (countryCode != null) {
            stmt.bindString(11, countryCode);
        }
 
        String areaCode = entity.getAreaCode();
        if (areaCode != null) {
            stmt.bindString(12, areaCode);
        }
 
        String creditAmount = entity.getCreditAmount();
        if (creditAmount != null) {
            stmt.bindString(13, creditAmount);
        }
 
        String serverId = entity.getServerId();
        if (serverId != null) {
            stmt.bindString(14, serverId);
        }
 
        String accountType = entity.getAccountType();
        if (accountType != null) {
            stmt.bindString(15, accountType);
        }
 
        String email = entity.getEmail();
        if (email != null) {
            stmt.bindString(16, email);
        }
 
        String userNick = entity.getUserNick();
        if (userNick != null) {
            stmt.bindString(17, userNick);
        }
 
        String userPic = entity.getUserPic();
        if (userPic != null) {
            stmt.bindString(18, userPic);
        }
 
        String platform = entity.getPlatform();
        if (platform != null) {
            stmt.bindString(19, platform);
        }
 
        String currencyType = entity.getCurrencyType();
        if (currencyType != null) {
            stmt.bindString(20, currencyType);
        }
 
        String isFastClose = entity.getIsFastClose();
        if (isFastClose != null) {
            stmt.bindString(21, isFastClose);
        }
 
        String isOpenRealAccount = entity.getIsOpenRealAccount();
        if (isOpenRealAccount != null) {
            stmt.bindString(22, isOpenRealAccount);
        }
 
        String password = entity.getPassword();
        if (password != null) {
            stmt.bindString(23, password);
        }
 
        String trueName = entity.getTrueName();
        if (trueName != null) {
            stmt.bindString(24, trueName);
        }
 
        Boolean readyOnlyAccount = entity.getReadyOnlyAccount();
        if (readyOnlyAccount != null) {
            stmt.bindLong(25, readyOnlyAccount ? 1L: 0L);
        }
 
        Boolean isMt5 = entity.getIsMt5();
        if (isMt5 != null) {
            stmt.bindLong(26, isMt5 ? 1L: 0L);
        }
        stmt.bindLong(27, entity.getOpenAccountType());
        stmt.bindLong(28, entity.getLoginAccountType());
 
        Boolean hasStAccount = entity.getHasStAccount();
        if (hasStAccount != null) {
            stmt.bindLong(29, hasStAccount ? 1L: 0L);
        }
    }

    @Override
    protected final void bindValues(SQLiteStatement stmt, UserInfoDetail entity) {
        stmt.clearBindings();
 
        Long id = entity.getId();
        if (id != null) {
            stmt.bindLong(1, id);
        }
 
        String userId = entity.getUserId();
        if (userId != null) {
            stmt.bindString(2, userId);
        }
 
        String loginToken = entity.getLoginToken();
        if (loginToken != null) {
            stmt.bindString(3, loginToken);
        }
        stmt.bindLong(4, entity.getUserType());
 
        String userTel = entity.getUserTel();
        if (userTel != null) {
            stmt.bindString(5, userTel);
        }
 
        String accountCd = entity.getAccountCd();
        if (accountCd != null) {
            stmt.bindString(6, accountCd);
        }
 
        String mt4Token = entity.getMt4Token();
        if (mt4Token != null) {
            stmt.bindString(7, mt4Token);
        }
 
        String mt4PWD = entity.getMt4PWD();
        if (mt4PWD != null) {
            stmt.bindString(8, mt4PWD);
        }
 
        String mt4State = entity.getMt4State();
        if (mt4State != null) {
            stmt.bindString(9, mt4State);
        }
 
        String mt4NickName = entity.getMt4NickName();
        if (mt4NickName != null) {
            stmt.bindString(10, mt4NickName);
        }
 
        String countryCode = entity.getCountryCode();
        if (countryCode != null) {
            stmt.bindString(11, countryCode);
        }
 
        String areaCode = entity.getAreaCode();
        if (areaCode != null) {
            stmt.bindString(12, areaCode);
        }
 
        String creditAmount = entity.getCreditAmount();
        if (creditAmount != null) {
            stmt.bindString(13, creditAmount);
        }
 
        String serverId = entity.getServerId();
        if (serverId != null) {
            stmt.bindString(14, serverId);
        }
 
        String accountType = entity.getAccountType();
        if (accountType != null) {
            stmt.bindString(15, accountType);
        }
 
        String email = entity.getEmail();
        if (email != null) {
            stmt.bindString(16, email);
        }
 
        String userNick = entity.getUserNick();
        if (userNick != null) {
            stmt.bindString(17, userNick);
        }
 
        String userPic = entity.getUserPic();
        if (userPic != null) {
            stmt.bindString(18, userPic);
        }
 
        String platform = entity.getPlatform();
        if (platform != null) {
            stmt.bindString(19, platform);
        }
 
        String currencyType = entity.getCurrencyType();
        if (currencyType != null) {
            stmt.bindString(20, currencyType);
        }
 
        String isFastClose = entity.getIsFastClose();
        if (isFastClose != null) {
            stmt.bindString(21, isFastClose);
        }
 
        String isOpenRealAccount = entity.getIsOpenRealAccount();
        if (isOpenRealAccount != null) {
            stmt.bindString(22, isOpenRealAccount);
        }
 
        String password = entity.getPassword();
        if (password != null) {
            stmt.bindString(23, password);
        }
 
        String trueName = entity.getTrueName();
        if (trueName != null) {
            stmt.bindString(24, trueName);
        }
 
        Boolean readyOnlyAccount = entity.getReadyOnlyAccount();
        if (readyOnlyAccount != null) {
            stmt.bindLong(25, readyOnlyAccount ? 1L: 0L);
        }
 
        Boolean isMt5 = entity.getIsMt5();
        if (isMt5 != null) {
            stmt.bindLong(26, isMt5 ? 1L: 0L);
        }
        stmt.bindLong(27, entity.getOpenAccountType());
        stmt.bindLong(28, entity.getLoginAccountType());
 
        Boolean hasStAccount = entity.getHasStAccount();
        if (hasStAccount != null) {
            stmt.bindLong(29, hasStAccount ? 1L: 0L);
        }
    }

    @Override
    public Long readKey(Cursor cursor, int offset) {
        return cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0);
    }    

    @Override
    public UserInfoDetail readEntity(Cursor cursor, int offset) {
        UserInfoDetail entity = new UserInfoDetail( //
            cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0), // id
            cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1), // userId
            cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2), // loginToken
            cursor.getInt(offset + 3), // userType
            cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4), // userTel
            cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5), // accountCd
            cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6), // mt4Token
            cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7), // mt4PWD
            cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8), // mt4State
            cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9), // mt4NickName
            cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10), // countryCode
            cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11), // areaCode
            cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12), // creditAmount
            cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13), // serverId
            cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14), // accountType
            cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15), // email
            cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16), // userNick
            cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17), // userPic
            cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18), // platform
            cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19), // currencyType
            cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20), // isFastClose
            cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21), // isOpenRealAccount
            cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22), // password
            cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23), // trueName
            cursor.isNull(offset + 24) ? null : cursor.getShort(offset + 24) != 0, // readyOnlyAccount
            cursor.isNull(offset + 25) ? null : cursor.getShort(offset + 25) != 0, // isMt5
            cursor.getInt(offset + 26), // openAccountType
            cursor.getInt(offset + 27), // loginAccountType
            cursor.isNull(offset + 28) ? null : cursor.getShort(offset + 28) != 0 // hasStAccount
        );
        return entity;
    }
     
    @Override
    public void readEntity(Cursor cursor, UserInfoDetail entity, int offset) {
        entity.setId(cursor.isNull(offset + 0) ? null : cursor.getLong(offset + 0));
        entity.setUserId(cursor.isNull(offset + 1) ? null : cursor.getString(offset + 1));
        entity.setLoginToken(cursor.isNull(offset + 2) ? null : cursor.getString(offset + 2));
        entity.setUserType(cursor.getInt(offset + 3));
        entity.setUserTel(cursor.isNull(offset + 4) ? null : cursor.getString(offset + 4));
        entity.setAccountCd(cursor.isNull(offset + 5) ? null : cursor.getString(offset + 5));
        entity.setMt4Token(cursor.isNull(offset + 6) ? null : cursor.getString(offset + 6));
        entity.setMt4PWD(cursor.isNull(offset + 7) ? null : cursor.getString(offset + 7));
        entity.setMt4State(cursor.isNull(offset + 8) ? null : cursor.getString(offset + 8));
        entity.setMt4NickName(cursor.isNull(offset + 9) ? null : cursor.getString(offset + 9));
        entity.setCountryCode(cursor.isNull(offset + 10) ? null : cursor.getString(offset + 10));
        entity.setAreaCode(cursor.isNull(offset + 11) ? null : cursor.getString(offset + 11));
        entity.setCreditAmount(cursor.isNull(offset + 12) ? null : cursor.getString(offset + 12));
        entity.setServerId(cursor.isNull(offset + 13) ? null : cursor.getString(offset + 13));
        entity.setAccountType(cursor.isNull(offset + 14) ? null : cursor.getString(offset + 14));
        entity.setEmail(cursor.isNull(offset + 15) ? null : cursor.getString(offset + 15));
        entity.setUserNick(cursor.isNull(offset + 16) ? null : cursor.getString(offset + 16));
        entity.setUserPic(cursor.isNull(offset + 17) ? null : cursor.getString(offset + 17));
        entity.setPlatform(cursor.isNull(offset + 18) ? null : cursor.getString(offset + 18));
        entity.setCurrencyType(cursor.isNull(offset + 19) ? null : cursor.getString(offset + 19));
        entity.setIsFastClose(cursor.isNull(offset + 20) ? null : cursor.getString(offset + 20));
        entity.setIsOpenRealAccount(cursor.isNull(offset + 21) ? null : cursor.getString(offset + 21));
        entity.setPassword(cursor.isNull(offset + 22) ? null : cursor.getString(offset + 22));
        entity.setTrueName(cursor.isNull(offset + 23) ? null : cursor.getString(offset + 23));
        entity.setReadyOnlyAccount(cursor.isNull(offset + 24) ? null : cursor.getShort(offset + 24) != 0);
        entity.setIsMt5(cursor.isNull(offset + 25) ? null : cursor.getShort(offset + 25) != 0);
        entity.setOpenAccountType(cursor.getInt(offset + 26));
        entity.setLoginAccountType(cursor.getInt(offset + 27));
        entity.setHasStAccount(cursor.isNull(offset + 28) ? null : cursor.getShort(offset + 28) != 0);
     }
    
    @Override
    protected final Long updateKeyAfterInsert(UserInfoDetail entity, long rowId) {
        entity.setId(rowId);
        return rowId;
    }
    
    @Override
    public Long getKey(UserInfoDetail entity) {
        if(entity != null) {
            return entity.getId();
        } else {
            return null;
        }
    }

    @Override
    public boolean hasKey(UserInfoDetail entity) {
        return entity.getId() != null;
    }

    @Override
    protected final boolean isEntityUpdateable() {
        return true;
    }
    
}
