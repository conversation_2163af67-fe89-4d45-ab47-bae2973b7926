package cn.com.vau.common.http

import com.google.firebase.ktx.Firebase
import com.google.firebase.perf.ktx.performance
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Response

/**
 * 上报Firebase
 */
class NetPerformanceInterceptor : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        val request = chain.request()

        val httpUrl: HttpUrl = request.url
//        val metric = Firebase.performance.newHttpMetric(
//            httpUrl.toUrl(),
//            if("GET" == request.method) FirebasePerformance.HttpMethod.GET else FirebasePerformance.HttpMethod.POST,
//        )
//        metric.start();

        //后面路径
        val urlPath = httpUrl.pathSegments.joinToString("/")
        val mTrace = Firebase.performance.newTrace("Url--$urlPath")
        mTrace.start()

        val response = chain.proceed(request)

        mTrace.putAttribute("code", response.code.toString())

        mTrace.stop()

//        metric.setHttpResponseCode(response.code)
//        //后面路径
//        val urlPath = httpUrl.pathSegments.joinToString("/")
//        metric.putAttribute("url_path", urlPath)
//        metric.stop()
        return response
    }
}