package cn.com.vau.common.utils

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.webkit.WebView
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseDataBindingActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.page.UploadBean
import cn.com.vau.page.user.openAccoGuide.lv2.UploadInfo
import cn.com.vau.page.user.openAccoGuide.lv2.vm.OpenLv2ViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomSelectListDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import java.io.IOException

@SuppressLint("StaticFieldLeak")
class OpenUploadToH5Util(val activity: AppCompatActivity) {

    private var viewModel: OpenLv2ViewModel? = null
    private var uploadBean: UploadBean? = null
    private var webView: WebView? = null

    private val pickImage = activity.createPhotoRequestForUri { uri ->
        selectAndUpload(uri)
    }

    private val cameraUri by lazy { activity.createTempImageUri() }

    private val pickCamera = activity.createCameraRequestForUri { isSuccess ->
        if (isSuccess) {
            cameraUri?.let { uri -> selectAndUpload(uri) }
        }
    }

    val launcher: ActivityResultLauncher<Intent>? = activity.registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Activity.RESULT_OK) {
            it.data.let { data ->
                val uri = data?.data
                if (uri != null) {
                    selectAndUpload(uri)
                }
            }
        }
    }

    /**
     * 构造这个类的页面 必须传入一个webview 不然上传图片以后不会给webview回调
     */
    fun setWebView(webView: WebView) {
        this.webView = webView
    }

    fun showUploadBottomDialog() {
        PermissionUtil.checkPermissionWithCallback(activity, *Constants.PERMISSION_STORAGE, Constants.PERMISSION_CAMERA) {
            if (it) {
                if (viewModel == null) {
                    viewModel = ViewModelProvider(activity)[OpenLv2ViewModel::class.java]
                    registerLiveData(activity)
                }
                BottomSelectListDialog.Builder(activity)
                    .setTitle(activity.getString(R.string.add_picture_from))
                    .setDataList(
                        listOf(
                            activity.getString(R.string.camera),
                            activity.getString(R.string.photo_library),
                            activity.getString(R.string.file)
                        )
                    )
                    .setItemType(1)
                    .setOnItemClickListener { position ->
                        when (position) {
                            0 -> { // camera
                                onSelectMethod(0)
                            }

                            1 -> { // photo library
                                onSelectMethod(1)
                            }

                            2 -> { // system files
                                onSelectMethod(2)
                            }
                        }
                    }
                    .build()
                    .showDialog()
            }
        }
    }

    private fun registerLiveData(activity: FragmentActivity) {
        activity.runOnUiThread {
            viewModel?.uploadPhotoLiveData?.observe(activity) {
                when (activity) {
                    is BaseActivity -> {
                        activity.hideNetDialog()
                    }

                    is BaseDataBindingActivity<*> -> {
                        activity.hideLoadDialog()
                    }

                    is BaseMvvmBindingActivity<*> -> {
                        activity.hideLoadDialog()
                    }
                }

                if ("V00000" == it.resultCode) {
                    val bean = it.data?.obj
                    val path = bean?.imgFile.ifNull()
                    if (path.isNotEmpty()) {
//                        LogUtil.d("wj", "add: $path")
                        val uploadInfo = UploadInfo(bean?.imgFile.ifNull(), bean?.imgFileoos.ifNull(), uploadBean?.type.ifNull())
//                        LogUtil.d("wj", "uploadInfo: $uploadInfo")
                        val json = GsonUtil.toJson(uploadInfo)
                        webView?.loadUrl("javascript:retrieveUploadData(${json})")
                        uploadBean = null
                    }
                } else {
                    uploadBean = null
                    ToastUtil.showToast(it.msgInfo)
                }
            }
        }
    }

    private fun onSelectMethod(selectType: Int) {
        when (selectType) {
            0 -> openCamera()
            1 -> openGallery()
            else -> openFile()
        }
    }

    private fun openCamera() {
        // 打开相机
        pickCamera.launch(cameraUri)
    }

    private fun openGallery() {
        // 打开相册
        pickImage.launchPhoto()
    }

    private fun openFile() {
        // 打开系统文件夹  (从【近期的文件】列表中选取的文件有可能是Uri格式有问题，会导致转成的文件类型无法判断触发文件类型弹窗)
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
        intent.type = "*/*"
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        try {
            launcher?.launch(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun selectAndUpload(uri: Uri) {
        var pass = false
        var fileType: String? = ""
        val exts = arrayOf("png", "jpg", "jpeg", "bmp", "pdf", "doc", "docx")
        try {
            fileType = UriUtil.checkFileType(uri)
            if (exts.contains(fileType)) {
                pass = true
            }
        } catch (e: IOException) {
            e.printStackTrace()
            // 无效路径弹窗 有可能是因为选择了【近期的文件】列表中的文件
            CenterActionDialog.Builder(activity)
                .setTitle(activity.getString(R.string.upload_failed))//设置则展示标题，否则不展示
                .setContent(activity.getString(R.string.unfortunately_the_files_again)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(activity.getString(R.string.ok)) //设置单个按钮文本
                .build()
                .showDialog()
        }

        // 类型不符合 弹窗提示
        if (!pass) {
            CenterActionDialog.Builder(activity)
                .setTitle(activity.getString(R.string.file_type_restriction))//设置则展示标题，否则不展示
                .setContent(activity.getString(R.string.please_choose_a_format)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(activity.getString(R.string.ok)) //设置单个按钮文本
                .build()
                .showDialog()
            return
        }
        val size = UriUtil.getFileSize(uri)
        // 超过15MB 弹窗提示
        if (size > (1024 * 1024 * 15)) {
            CenterActionDialog.Builder(activity)
                .setTitle(activity.getString(R.string.file_size_restriction))//设置则展示标题，否则不展示
                .setContent(activity.getString(R.string.the_selected_file_exceeds)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(activity.getString(R.string.ok).ifNull()) //设置单个按钮文本
                .build()
                .showDialog()
            return
        }
        upload(UploadBean(uri = uri, type = fileType))
    }

    private fun upload(photo: UploadBean) {
        when (activity) {
            is BaseActivity -> {
                activity.showNetDialog()
            }

            is BaseDataBindingActivity<*> -> {
                activity.showLoadDialog()
            }

            is BaseMvvmBindingActivity<*> -> {
                activity.showLoadDialog()
            }
        }
        uploadBean = photo
        viewModel?.eachUpload(photo)
    }
}

