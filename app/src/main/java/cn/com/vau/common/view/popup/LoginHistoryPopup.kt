package cn.com.vau.common.view.popup

import android.content.Context
import android.graphics.*
import android.graphics.drawable.ColorDrawable
import android.view.*
import android.widget.PopupWindow
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.*
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.bean.SelectTitle
import cn.com.vau.page.user.loginPwd.LoginHistoryAdapter
import cn.com.vau.signals.live.history.util.VideoUtils
import cn.com.vau.util.dp2px

/**
 * 登陆历史纪录
 * Created by zhy on 2018/10/30.
 */
class LoginHistoryPopup(private val mContext: Context, private var realWidth: Int, val listener: (String) -> Unit) : PopupWindow() {

    private var rcyLoginHistory: RecyclerView? = null
    private val adapter: LoginHistoryAdapter by lazy { LoginHistoryAdapter() }

    init {
        initSteup()
        initView()
    }

    private fun initView() {
        val layoutManager = WrapContentLinearLayoutManager(mContext, LinearLayoutManager.VERTICAL, false)
        rcyLoginHistory?.layoutManager = layoutManager
        rcyLoginHistory?.adapter = adapter
        adapter.setOnItemClickListener { _, _, position ->
            listener.invoke(adapter.data.getOrNull(position)?.getTitle() ?: "")
        }
    }

    fun setData(dataList: List<SelectTitle>) {
        adapter.setList(dataList)
        changeHeight(dataList.size > 4)
    }

    private fun changeHeight(isFixed: Boolean) {
        val layoutParams = rcyLoginHistory?.layoutParams
        layoutParams?.height = if (isFixed)
            166.dp2px()
        else
            ConstraintLayout.LayoutParams.WRAP_CONTENT
        rcyLoginHistory?.layoutParams = layoutParams
    }

    private fun initSteup() {
        //设置PopupWindow的View
        val mView = LayoutInflater.from(mContext).inflate(R.layout.lauout_pop_login_history, null)
        rcyLoginHistory = mView.findViewById(R.id.rcyLoginHistory)
        this.contentView = mView
        //设置PopupWindow弹出窗体的宽
        this.width = realWidth
        //设置PopupWindow弹出窗体的高
        this.height = WindowManager.LayoutParams.WRAP_CONTENT
        //设置PopupWindow弹出窗体不可点击
        this.isFocusable = false
        this.isOutsideTouchable = true
        this.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        this.inputMethodMode = PopupWindow.INPUT_METHOD_NEEDED
        this.softInputMode = WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN

        rcyLoginHistory?.outlineProvider = object : ViewOutlineProvider() {
            override fun getOutline(view: View, outline: Outline) {
                outline.setRoundRect(
                    0,
                    0,
                    view.width,
                    view.height,
                    VideoUtils.dp2px(mContext, 10).toFloat()
                )
            }
        }
        rcyLoginHistory?.clipToOutline = true
    }

    fun updateInput(text: String?) {
        adapter.updateSelect(text)
    }
}
