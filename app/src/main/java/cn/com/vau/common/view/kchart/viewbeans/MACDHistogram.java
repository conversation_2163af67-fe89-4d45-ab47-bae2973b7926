package cn.com.vau.common.view.kchart.viewbeans;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.PointF;
import android.graphics.RectF;

import java.util.ArrayList;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.view.kchart.views.ChartViewImp;
import cn.com.vau.trade.kchart.KLineDataUtils;

/**
 * 描述：MACD柱状图
 */
public class MACDHistogram extends ZoomMoveViewContainer<MACDHistogram.MACDBean> {
    // 实心画笔
    private Paint mFillPaint = null;
    // 是否填充
    private boolean isFill = true;
    // 涨时颜色
    private int mUpColor = mContext.getResources().getColor(R.color.c00c79c);
    //跌时颜色
    private int mDownColor = mContext.getResources().getColor(R.color.ce35728);
    //不涨不跌颜色
    private int mEvenColor = Color.parseColor("#656565");
    //柱之间间隙
    private float mSpace = 0;
    //是否计算极值(有时元素极值并不是元素本身数据的极值,有可能是其他数据极值)
    private boolean isCalculateDataExtraNum = true;
    //每个点的宽度
    private float mPointWidth = 0;

    private final RectF rectF = new RectF();

    public MACDHistogram(Context context) {
        super(context);
        //初始化线画笔
        init();
    }

    private void init() {
        //初始化线画笔
        this.mFillPaint = new Paint();
        mFillPaint.setAntiAlias(true);
        mFillPaint.setStyle(Paint.Style.FILL);
//        mFillPaint.setStrokeWidth(getPixelDp(1));
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        try {
            if (isShow) {
                checkParameter();
                mPointWidth = (mCoordinateWidth - mCoordinateMarginLeft - mCoordinateMarginRight) / mShownPointNums;
                mSpace = mPointWidth / 7;
//                PointF rightBottomPoint;
//                PointF leftTopPoint;
                for (int i = 0; i < mShownPointNums && i < mDataList.size(); i++) {
                    MACDBean bean = mDataList.get(i + mDrawPointIndex);
//                    leftTopPoint = getLeftTopPoint(i, bean);
//                    rightBottomPoint = getRightBottomPoint(i, bean);

                    if (bean.getMacd() >= 0) {
                        mFillPaint.setColor(mUpColor);
                    } else if (bean.getMacd() < 0) {
                        mFillPaint.setColor(mDownColor);
                    } else {
                        mFillPaint.setColor(mEvenColor);
                    }

                    // 左上
                    float left = i * mPointWidth + mSpace + mCoordinateMarginLeft;
                    float leftTopZeroY = (1f - (0 - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
                    float top = leftTopZeroY;
                    if (bean.getMacd() > 0) {
                        top = (1f - (bean.getMacd() - 0) / (mYMax - 0)) * leftTopZeroY;
                    }

                    // 右下
                    float right = (i + 1) * mPointWidth - mSpace + mCoordinateMarginLeft;
                    float rightBottomZeroY = (1f - (0 - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
                    float bottom = rightBottomZeroY;
                    if (bean.getMacd() < 0) {
                        bottom = (bean.getMacd() / mYMin) * (mCoordinateHeight - rightBottomZeroY) + rightBottomZeroY;
                    }

                    rectF.set(left,top,right,bottom);

                    //画实心
//                    if (isFill) {
//                        RectF rectF = new RectF();
//                        rectF.left = leftTopPoint.x;
//                        rectF.top = leftTopPoint.y;
//                        rectF.right = rightBottomPoint.x;
//                        rectF.bottom = rightBottomPoint.y;
                        canvas.drawRect(rectF, mFillPaint);
//                    } else {
//                        float width = leftTopPoint.x - rightBottomPoint.x;
//                        canvas.drawLine(leftTopPoint.x - width / 2f, leftTopPoint.y, rightBottomPoint.x + width / 2f, rightBottomPoint.y, mFillPaint);
//                    }
                }
                //改变坐标轴显示
                notifyCoordinateChange();
            }
        } catch (Exception ignored) {
        }
    }

    private PointF getLeftTopPoint(int index, MACDBean bean) {
        PointF pointF = new PointF();
//        mSpace = mPointWidth / 7;

        float x = index * mPointWidth + mSpace + mCoordinateMarginLeft;
        float zeroY = (1f - (0 - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        float y = zeroY;

        if (bean.getMacd() > 0) {
            y = (1f - (bean.getMacd() - 0) / (mYMax - 0)) * zeroY;
        }
        pointF.set(x, y);
        return pointF;
    }

    private PointF getRightBottomPoint(int index, MACDBean bean) {
        PointF pointF = new PointF();
//        mPointWidth = (mCoordinateWidth - mCoordinateMarginLeft - mCoordinateMarginRight) / mShownPointNums;
//        mSpace = mPointWidth / 7;

        float x = (index + 1) * mPointWidth - mSpace + mCoordinateMarginLeft;
        float zeroY = (1f - (0 - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        float y = zeroY;

        if (bean.getMacd() < 0) {
            y = (bean.getMacd() / mYMin) * (mCoordinateHeight - zeroY) + zeroY;
        }
        pointF.set(x, y);
        return pointF;
    }

    private void checkParameter() {
        if (this.mCoordinateHeight <= 0) {
            throw new IllegalArgumentException("mCoordinateHeight can't be zero or smaller than zero");
        }
        if (this.mCoordinateWidth <= 0) {
            throw new IllegalArgumentException("mCoordinateWidth can't be zero or smaller than zero");
        }
    }

    public static class MACDBean {
        private float macd = 0f;

        public MACDBean(float macd) {
            this.macd = macd;
        }

        public float getMacd() {
            return macd;
        }

        public void setMacd(float macd) {
            this.macd = macd;
        }
    }

    public void setColor(int upColor, int evenColor, int downColor) {
        this.mUpColor = upColor;
        this.mDownColor = downColor;
        this.mEvenColor = evenColor;
    }

    public void setUpColor(int upColor) {
        this.mUpColor = upColor;
    }

    public void setEvenColor(int evenColor) {
        this.mEvenColor = evenColor;
    }

    public void setDownColor(int downColor) {
        this.mDownColor = downColor;
    }

    public boolean isFill() {
        return isFill;
    }

    public void setFill(boolean isFill) {
        this.isFill = isFill;
        if (isFill) {
            mFillPaint.setStyle(Paint.Style.FILL);
        } else {
            mFillPaint.setStyle(Paint.Style.STROKE);
        }
    }

    public boolean isCalculateDataExtraNum() {
        return isCalculateDataExtraNum;
    }

    public void setCalculateDataExtraNum(boolean isCalculateDataExtremum) {
        this.isCalculateDataExtraNum = isCalculateDataExtremum;
    }

    @Override
    public float getSingleDataWidth() {
        return mPointWidth;
    }

    @Override
    public float[] calculateExtremeY() {
        if (mExtremeCalculatorInterface != null) {
            return mExtremeCalculatorInterface.onCalculateExtreme(mDrawPointIndex, mShownPointNums, 108);
        } else if (mDataList != null && mDataList.size() > mDrawPointIndex) {
            ChartViewImp chartView = getChartView();
            List<String> dataList = new ArrayList<>();
            for (int i = mDrawPointIndex + 1; i < mDrawPointIndex + mShownPointNums && i < mDataList.size(); i++) {
                MACDBean bean = mDataList.get(i);
                dataList.add(bean.getMacd() + "");
            }
            float[] result = KLineDataUtils.getExtremeNumber(dataList, chartView.getIsHaveYPadding(), chartView.getPaddingPercent(), 8);
            return result;

        }
        return new float[]{0, 0};
    }


    @Override
    protected float transDataToCrossDataFromDataList(int crossPointIndexInScreen, int dataInListIndex) {
        if (dataInListIndex >= mDataList.size()) {
            return super.transDataToCrossDataFromDataList(crossPointIndexInScreen, dataInListIndex);
        }

        MACDBean bean = mDataList.get(dataInListIndex);
        return bean.getMacd();
    }
}
