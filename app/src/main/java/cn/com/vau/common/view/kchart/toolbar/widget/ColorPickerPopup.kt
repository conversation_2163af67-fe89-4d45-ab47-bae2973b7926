package cn.com.vau.common.view.kchart.toolbar.widget

import android.content.Context
import cn.com.vau.R
import cn.com.vau.databinding.KlineDialogColorPickerBinding
import com.lxj.xpopup.core.AttachPopupView
import cn.com.vau.common.view.kchart.toolbar.ColorData

/**
 * 颜色选择器弹窗
 */
class ColorPickerPopup(context: Context) : AttachPopupView(context) {

    private val mBinding: KlineDialogColorPickerBinding by lazy {
        KlineDialogColorPickerBinding.bind(popupImplView)
    }

    private var onColorPickListener: ((item: ColorData) -> Unit)? = null

    override fun getImplLayoutId(): Int = R.layout.kline_dialog_color_picker

    override fun onCreate() {
        super.onCreate()
        mBinding.rvGrid.setOnColorPickListener { colorItem ->
            onColorPickListener?.invoke(colorItem)
            dismiss()
        }
    }

    fun setOnColorPickListener(listener: (ColorData) -> Unit): ColorPickerPopup {
        onColorPickListener = listener
        return this
    }

}