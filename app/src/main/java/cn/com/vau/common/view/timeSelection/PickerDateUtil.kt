package cn.com.vau.common.view.timeSelection

import android.annotation.SuppressLint
import cn.com.vau.util.*
import java.text.*
import java.util.*

@SuppressLint("SimpleDateFormat")
object PickerDateUtil {
    @JvmStatic
    fun currentTimeMillisToString(pattern: String?): String {
        val sdf = SimpleDateFormat(pattern, Locale.ENGLISH)
        sdf.timeZone = TimeZone.getTimeZone("GMT")
        return sdf.format(Date(System.currentTimeMillis() + AppUtil.getTimeZoneRawOffsetToHour() * 60 * 60 * 1000))
    }

    @Throws(ParseException::class)
    fun dateStrToLong(timeStr: String): String {
        val res: String
        val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
        val date = simpleDateFormat.parse(timeStr)
        val ts = date?.time
        res = ts.toString()
        return res
    }

    /**
     * 时间戳转字符串
     */
    fun longTimeToString(timeStamp: Long?, pattern: String?): String {
        val sdf = SimpleDateFormat(pattern, Locale.ENGLISH)
        sdf.timeZone = TimeZone.getTimeZone("GMT")
        return sdf.format(Date(timeStamp.ifNull()))
    }

    @JvmStatic
    fun longTimeToString(timeStamp: String?, pattern: String?): String {
        val sdf = SimpleDateFormat(pattern, Locale.ENGLISH)
        sdf.timeZone = TimeZone.getTimeZone("GMT")
        return sdf.format(Date(timeStamp.toLongCatching()))
    }

    fun formatHhMm(dateL: Long): String {
        val sdf = SimpleDateFormat("HH:mm", Locale.ENGLISH)
        return sdf.format(Date(dateL))
    }

    fun getWeek(data: Long): Int {
        val sdr = SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒", Locale.ENGLISH)
        val times = sdr.format(Date(data))
        val date: Date?
        var mydate = 0
        try {
            date = sdr.parse(times)
            val cd = Calendar.getInstance()
            if (date != null) {
                cd.time = date
            }
            mydate = cd[Calendar.DAY_OF_WEEK]
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        val week = when (mydate) {
            1 -> 6
            in 2..7 -> mydate - 2
            else -> -1
        }
        return week
    }

    fun dateToTime(time: String, pattern: String?): Long {
        val sdf = SimpleDateFormat(pattern, Locale.ENGLISH)
        sdf.timeZone = TimeZone.getTimeZone("GMT")
        var date = Date()
        try {
            date = sdf.parse(time) ?: Date()
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return date.time
    }

    private var dateMarketClose: Date? = null

    fun dateToTimeMarketClose(time: String?): Long {
        time ?: return 0
        try {
            dateMarketClose = sdfMarketClose.parse(time)
        } catch (e: ParseException) {
            e.printStackTrace()
        } catch (e: NumberFormatException) {
            e.printStackTrace()
        }
        return dateMarketClose?.time ?: 0
    }

    private val sdfMarketClose by lazy {
        SimpleDateFormat("HH:mm", Locale.ENGLISH).apply {
            timeZone = TimeZone.getTimeZone("GMT")
        }
    }

}