package cn.com.vau.common.application

import android.os.Handler
import android.os.Looper
import java.util.Collections
import java.util.HashSet

/**
 * 司初始之务，掌信通接续之态。
 */
object LinkStateManager {

    private val mHandler = Handler(Looper.getMainLooper())

    //观察者集合
    private val callbackSet = Collections.synchronizedSet(HashSet<StateChangeCallback>())

    //产品列表返回成功则为true
    @Volatile
    private var isProductListSuccess = false

    //websocket链接成功则为true
    @Volatile
    private var isWebSocketConnected = false

    //状态判断，产品列表成功且websocket连接成功，二者皆满足则返回true
    fun isStateLinkSuccess(): Boolean {
        return isWebSocketConnected && isProductListSuccess
    }

    fun registerCallback(callback: StateChangeCallback) {
        callbackSet.add(callback)
    }

    fun unregisterCallback(callback: StateChangeCallback) {
        if (callbackSet.contains(callback)) {
            callbackSet.remove(callback)
        }
    }

    fun dispatchStartInit() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleStartInit()
        } else {
            mHandler.post {
                handleStartInit()
            }
        }
    }

    private fun handleStartInit() {
        isProductListSuccess = false
        isWebSocketConnected = false
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            iterator.next().onStartInit()
        }
    }

    fun dispatchProductListError() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleDispatchProductListError()
        } else {
            mHandler.post {
                handleDispatchProductListError()
            }
        }
    }

    private fun handleDispatchProductListError() {
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            iterator.next().onProductListError()
        }
    }

    fun dispatchProductListSuccess() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleDispatchProductListSuccess()
        } else {
            mHandler.post {
                handleDispatchProductListSuccess()
            }
        }
    }

    private fun handleDispatchProductListSuccess() {
        isProductListSuccess = true
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            with(iterator.next()) {
                onProductListSuccess()
                if (isStateLinkSuccess()) {
                    onProductAndWebsocketSuccess()
                }
            }
        }
    }

    fun dispatchWebSocketConnected() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleDispatchWebSocketConnected()
        } else {
            mHandler.post {
                handleDispatchWebSocketConnected()
            }
        }

    }

    private fun handleDispatchWebSocketConnected() {
        isWebSocketConnected = true
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            with(iterator.next()) {
                //onWebSocketConnected()
                if (isStateLinkSuccess()) {
                    onProductAndWebsocketSuccess()
                }
            }
        }
    }

    fun dispatchNetSlow() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleDispatchNetSlow()
        } else {
            mHandler.post {
                handleDispatchNetSlow()
            }
        }
    }

    private fun handleDispatchNetSlow() {
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            iterator.next().onNetSlow()
        }
    }

    fun dispatchHeartbeatNormal() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleHeartbeatNormal()
        } else {
            mHandler.post {
                handleHeartbeatNormal()
            }
        }
    }

    private fun handleHeartbeatNormal() {
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            iterator.next().onHeartbeatNormal()
        }
    }

    fun dispatchDisconnected() {
        if (Looper.myLooper() == Looper.getMainLooper()) {
            handleDisconnected()
        } else {
            mHandler.post {
                handleDisconnected()
            }
        }
    }

    private fun handleDisconnected() {
        isWebSocketConnected = false
        val iterator = callbackSet.iterator()
        while (iterator.hasNext()) {
            iterator.next().onWebsocketDisconnected()
        }
    }


    //供公数据初始化步骤，状态改变监听，回调都在主线程
    open class StateChangeCallback {
        //开始初始化操作
        open fun onStartInit() {}

        //产品列表加载完成
        open fun onProductListSuccess() {}

        //产品列表加载失败
        open fun onProductListError() {}

        //websocket连接成功
        //        open fun onWebSocketConnected() {}
        //产品列表和WebSocket连接成功，同时满足，则会回调此方法
        open fun onProductAndWebsocketSuccess() {}

        //WebSocket连接慢
        open fun onNetSlow() {}

        //心跳恢复正常状态
        open fun onHeartbeatNormal() {}

        //WebSocket已经断开连接
        open fun onWebsocketDisconnected() {}
    }

}