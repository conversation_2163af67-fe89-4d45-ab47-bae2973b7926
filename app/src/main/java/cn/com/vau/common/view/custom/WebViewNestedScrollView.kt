package cn.com.vau.common.view.custom

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.*
import android.webkit.WebView
import androidx.core.widget.NestedScrollView
import kotlin.math.abs

/**
 * author：lvy
 * date：2025/03/24
 * desc：解决 NestedScrollView 嵌套 WebView 导致 WebView 内容横向滑动冲突
 */
class WebViewNestedScrollView(context: Context, attrs: AttributeSet?) : NestedScrollView(context, attrs) {

    private var isWebViewScrolling = false // 标记 WebView 是否正在滚动
    private var initialX = 0f
    private var initialY = 0f
    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop // 滑动阈值

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        val webView = findWebView()
        if (webView != null && isTouchInView(ev, webView)) {
            when (ev.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = ev.x
                    initialY = ev.y
                    isWebViewScrolling = false
                }

                MotionEvent.ACTION_MOVE -> {
                    val dx = abs(ev.x - initialX)
                    val dy = abs(ev.y - initialY)
                    if (dx > touchSlop && dx > dy) {
                        // 如果是水平滑动，不拦截事件，让 WebView 处理
                        isWebViewScrolling = true
                        return false
                    } else if (dy > touchSlop) {
                        // 如果是垂直滑动，拦截事件，让 NestedScrollView 处理
                        return true
                    }
                }
            }
        }
        return super.onInterceptTouchEvent(ev)
    }

    override fun onTouchEvent(ev: MotionEvent): Boolean {
        val webView = findWebView()
        if (webView != null && isTouchInView(ev, webView) && isWebViewScrolling) {
            // 如果 WebView 正在滚动，不处理事件
            return false
        }
        return super.onTouchEvent(ev)
    }

    /**
     * 查找布局中的 WebView
     */
    private fun findWebView(): WebView? {
        for (i in 0 until childCount) {
            val child = getChildAt(i)
            if (child is WebView) {
                return child
            }
        }
        return null
    }

    /**
     * 判断触摸事件是否在指定 View 的区域内
     */
    private fun isTouchInView(ev: MotionEvent, view: View): Boolean {
        val rect = Rect()
        view.getGlobalVisibleRect(rect)
        return rect.contains(ev.rawX.toInt(), ev.rawY.toInt())
    }
}