package cn.com.vau.common.view.system

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Typeface
import android.text.*
import android.text.method.LinkMovementMethod
import android.text.style.ClickableSpan
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.core.content.ContextCompat
import cn.com.vau.R
import kotlin.text.indexOf
import kotlin.text.isNotEmpty

@SuppressLint("AppCompatCustomView")
class LinkSpanTextView(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int
) : TextView(context, attrs, defStyleAttr) {

    constructor(context: Context) : this(context, null) {}
    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0) {}

    private var wholeText: String = ""
    private var spanString: SpannableString? = null

    fun set(span: String, color: Int = -1, isShowUnderLine: Boolean = true, isBold: Boolean = false, click: (() -> Unit)? = null): LinkSpanTextView {
        try {
            wholeText = this.text.toString()
            if (wholeText.isNotEmpty() && span.isNotEmpty()) {
                spanString = getSpannableString()
                val index = wholeText.indexOf(span)
                if (index != -1 && (index + span.length) <= wholeText.length) {
                    spanString?.setSpan(object : ClickableSpan() {
                        override fun onClick(widget: View) {
                            click?.invoke()
                        }

                        override fun updateDrawState(ds: TextPaint) {
                            ds.isUnderlineText = isShowUnderLine
                            if (color != -1) {
                                ds.color = color
                            }
                            if (isBold) {
                                ds.typeface = Typeface.create(ds.typeface, Typeface.BOLD)
                            }
                        }
                    }, index, (index + span.length), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
                    this.text = spanString
                    this.movementMethod = LinkMovementMethod.getInstance()
                    this.highlightColor = ContextCompat.getColor(context, R.color.transparent)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return this
    }

    private fun getSpannableString(): SpannableString {
        return this.spanString ?: SpannableString(wholeText)
    }
}