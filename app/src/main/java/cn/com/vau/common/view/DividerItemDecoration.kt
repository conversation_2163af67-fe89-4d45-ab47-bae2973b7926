package cn.com.vau.common.view

import android.graphics.*
import android.view.View
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.util.language.LanguageHelper

/**
 * RecyclerView 分割线
 */
class DividerItemDecoration(
    /**
     * 默认间距
     */
    private val dividerSize: Number,
    /**
     * 最后一个item的底部间距
     */
    private val lastDividerSize: Int = 0,
    /**
     * 间距颜色
     */
    private val dividerColor: Int? = null, // Divider color parameter
    /**
     * 从第一个item开始显示间距
     */
    private val firstShowCount: Int = 0,
    /**
     * 第一个item的顶部的间距
     */
    private val firstDividerSize: Int = 0,
    @RecyclerView.Orientation
    private val orientation: Int = RecyclerView.VERTICAL,
) : RecyclerView.ItemDecoration() {

    private var paint: Paint? = null

    init {
        if (dividerColor != null) {
            paint = Paint().apply {
                color = dividerColor
                style = Paint.Style.FILL
            }

        }
    }

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        super.getItemOffsets(outRect, view, parent, state)

        val position = parent.getChildAdapterPosition(view)
        val itemCount = parent.adapter?.itemCount ?: 0

        if (orientation == RecyclerView.HORIZONTAL) {

            if (position == 0) {
                if (LanguageHelper.isRtlLanguage())
                    outRect.right = firstDividerSize
                else
                    outRect.left = firstDividerSize
            }
            if (position == itemCount - 1 && lastDividerSize != 0) {
                if (LanguageHelper.isRtlLanguage())
                    outRect.left = lastDividerSize
                else
                    outRect.right = lastDividerSize
            } else {
                outRect.right = dividerSize.toInt()
            }
        } else {
            if (position == 0) {
                outRect.top = firstDividerSize
            }
            if (position == itemCount - 1 && lastDividerSize != 0) {
                outRect.bottom = lastDividerSize
            } else {
                outRect.bottom = dividerSize.toInt()
            }
        }
    }

    override fun onDrawOver(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        super.onDrawOver(c, parent, state)
        if (paint == null)
            return
        val childCount = parent.childCount

        for (i in firstShowCount until childCount) {
            val child = parent.getChildAt(i)
            val params = child.layoutParams as RecyclerView.LayoutParams

            if (orientation == RecyclerView.HORIZONTAL) {
                val left = child.right + params.rightMargin
                val right = left + dividerSize.toInt()
                val top = child.top
                val bottom = child.bottom
                paint?.let {
                    c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), it)
                }
            } else {
                val top = child.bottom + params.bottomMargin
                val bottom = top + dividerSize.toInt()
                val left = child.left
                val right = child.right
                paint?.let {
                    c.drawRect(left.toFloat(), top.toFloat(), right.toFloat(), bottom.toFloat(), it)
                }
            }
        }
    }
}