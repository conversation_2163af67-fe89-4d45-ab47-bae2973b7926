package cn.com.vau.common.view

import android.content.Context
import android.util.AttributeSet
import android.view.*
import android.widget.*
import kotlin.math.abs

class RefreshSlideLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var downX: Float = 0f
    private var downY: Float = 0f
    private var isDragged = false
    private val touchSlop = ViewConfiguration.get(context).scaledTouchSlop
    private val HORIZONTAL = LinearLayout.HORIZONTAL
    private val VERTICAL = LinearLayout.VERTICAL
    private var orientation = VERTICAL

    override fun onInterceptTouchEvent(ev: MotionEvent): Boolean {
        try {
            when (ev.action) {
                MotionEvent.ACTION_DOWN -> {
                    downX = ev.x
                    downY = ev.y
                    isDragged = false
                }

                MotionEvent.ACTION_MOVE -> {
                    if (!isDragged) {
                        val dx = abs(ev.x - downX)
                        val dy = abs(ev.y - downY)
                        if (orientation == HORIZONTAL) {
                            isDragged = dx > touchSlop && dx > dy
                        } else if (orientation == VERTICAL) {
                            isDragged = dy > touchSlop && dy > dx
                        }
                    }
                    parent.requestDisallowInterceptTouchEvent(isDragged)
                }

                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    isDragged = false
                    //阻止父层的view获取touch事件
                    parent.requestDisallowInterceptTouchEvent(false)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return super.onInterceptTouchEvent(ev)
    }
}
