package cn.com.vau.common.http

import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjBooleanBean
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.StringBean
import cn.com.vau.data.account.AccountAuditBean
import cn.com.vau.data.account.AccountHomeData
import cn.com.vau.data.account.AccountListFirstBean
import cn.com.vau.data.account.AccountOpeningGuideBean
import cn.com.vau.data.account.AccountsEquityData
import cn.com.vau.data.account.AloneAccountInfoData
import cn.com.vau.data.account.AuditQuestionData
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.data.account.BindEmailBean
import cn.com.vau.data.account.ChangeUserInfoSuccessBean
import cn.com.vau.data.account.CheckVirtualAccountBean
import cn.com.vau.data.account.DemoAccountBean
import cn.com.vau.data.account.DemoAccountListData
import cn.com.vau.data.account.EmailBindBean
import cn.com.vau.data.account.EmploymentBean
import cn.com.vau.data.account.EventsTicketData
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean
import cn.com.vau.data.account.KycVerifyLevelDataBean
import cn.com.vau.data.account.LoginBean
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.account.MoreAboutYouBean
import cn.com.vau.data.account.NDBStatusBean
import cn.com.vau.data.account.OpenWalletBean
import cn.com.vau.data.account.PasskeyLoginData
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.data.account.RealAccountCacheBean
import cn.com.vau.data.account.ResidenceBean
import cn.com.vau.data.account.SelectCountryNumberBean
import cn.com.vau.data.account.SelectNationalityBean
import cn.com.vau.data.account.StAccMarginBean
import cn.com.vau.data.account.StAccountLoginBean
import cn.com.vau.data.account.UploadImageBean
import cn.com.vau.data.account.UserLogsData
import cn.com.vau.data.account.VerificationCodeData
import cn.com.vau.data.depositcoupon.FundHistoryBean
import cn.com.vau.data.depositcoupon.ManageFundsBean
import cn.com.vau.data.depositcoupon.NeedH5WithdrawBean
import cn.com.vau.data.depositcoupon.NeedUploadAddressProofBean
import cn.com.vau.data.depositcoupon.NeedUploadIdProofData
import cn.com.vau.data.depositcoupon.QueryUserIsProclientData
import cn.com.vau.data.discover.AddAWSData
import cn.com.vau.data.discover.FilterChartData
import cn.com.vau.data.discover.HistoryMessageData
import cn.com.vau.data.discover.LiveInfoData
import cn.com.vau.data.discover.LiveLikes
import cn.com.vau.data.discover.LivePromoData
import cn.com.vau.data.discover.ProductItemInfoData
import cn.com.vau.data.discover.StrategyRecommendAllBean
import cn.com.vau.data.discover.WbpStatusData
import cn.com.vau.data.init.AccountInfoBean
import cn.com.vau.data.init.AppVersionBean
import cn.com.vau.data.init.CheckDepositStatusBean
import cn.com.vau.data.init.CollectDataBean
import cn.com.vau.data.init.ImgAdvertInfoBean
import cn.com.vau.data.init.InAppBean
import cn.com.vau.data.init.MaintenanceBean
import cn.com.vau.data.init.PopWindowBean
import cn.com.vau.data.init.PositionOrdersBean
import cn.com.vau.data.init.PromoTabBean
import cn.com.vau.data.init.SeasonBean
import cn.com.vau.data.init.ServerBaseUrlBean
import cn.com.vau.data.init.ServerTimeBean
import cn.com.vau.data.init.StAccMarginSimplifiedBean
import cn.com.vau.data.init.StFollowStrategyBean
import cn.com.vau.data.init.StIsShowBean
import cn.com.vau.data.init.TradeAccountLoginBean
import cn.com.vau.data.msg.AppsFlyerPointBean
import cn.com.vau.data.profile.TelegramGetBotBean
import cn.com.vau.data.strategy.StProfileSharingFollowerData
import cn.com.vau.data.strategy.StSignalInfoBean
import cn.com.vau.data.strategy.StrategyHistoryBean
import cn.com.vau.data.strategy.StrategyOtherData
import cn.com.vau.data.trade.FreeOrdersBean
import cn.com.vau.data.trade.OptionalBean
import cn.com.vau.data.trade.StOptionalBean
import cn.com.vau.data.trade.StTradePositionUpdateBean
import cn.com.vau.data.trade.StTrendBean
import cn.com.vau.data.trade.SymbolsChartData
import cn.com.vau.data.trade.TrendBean
import io.reactivex.Observable
import okhttp3.MultipartBody
import okhttp3.RequestBody
import retrofit2.http.Body
import retrofit2.http.FieldMap
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Headers
import retrofit2.http.POST
import retrofit2.http.Query
import retrofit2.http.QueryMap

/**
 * Created by ZhengJiao on 2017/5/26.
 */
interface HttpService {

    /**
     * 获取时区时令
     */
    @POST("trade/season/get")
    fun tradeSeasonGet(@QueryMap map: HashMap<String, Any>): Observable<SeasonBean>

    /**
     * mt4登陆
     */
    @POST("trade/account/login")
    fun tradeAccountLogin(@Body body: RequestBody?): Observable<TradeAccountLoginBean>

    /**
     * 改单
     */
    @POST("trade/orders/update")
    fun tradeOrdersUpdate(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * main --> 获取产品列表
     */
    @POST("trade/product/list/v2/zip")
    fun tradeProductListV2Zip(@Body body: RequestBody?): Observable<StringBean>

    /**
     * 持仓订单查询
     */
    @POST("trade/order/position/list/v3")
    fun tradeOrderPositionListV3(@Body body: RequestBody?): Observable<PositionOrdersBean>

    /**
     * 账号信息
     */
    @POST("trade/account/info/v2")
    fun tradeAccountInfoV2(@Body body: RequestBody?): Observable<AccountInfoBean>

    /**
     * 平仓
     */
    @POST("trade/orders/close")
    fun tradeOrdersClose(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * 批量平仓
     */
    @POST("trade/orders/batchClose/v2")
    fun tradeOrdersBatchCloseV2(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * 批量平仓 - 跟单
     */
    @POST("trade/position/batch-close")
    fun tradePositionBatchClose(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * 获取蜡烛图数据  K线历史数据
     */
    @POST("trade/order/historyMarkets")
    fun tradeOrderHistoryMarketsApi(@Body body: RequestBody?): Observable<SymbolsChartData>

    /**
     * 趨勢圖用
     */
    @POST("trade/product/trend")
    fun tradeProductTrend(@Body body: RequestBody?): Observable<TrendBean>

    /**
     * 首页 --> 弹窗接口
     */
    @Headers("apiVer: v2")
    @POST("popWindow")
    fun popWindowApi(@QueryMap map: HashMap<String, Any?>): Observable<PopWindowBean>

    /**
     * 获取新用户引导礼包的信息
     */
    @POST("newerGiftActivity/getWBPStatus")
    fun newerGiftActivityGetWBPStatusApi(@QueryMap map: HashMap<String, Any>): Observable<WbpStatusData>

    /**
     * 行情首页广告位接口
     */
    @FormUrlEncoded
    @POST("img/advert-info")
    fun imgAdvertInfoApi(@FieldMap map: HashMap<String, Any>): Observable<ImgAdvertInfoBean>

    /**
     * 行情首页广告位 点击关闭广告位后调用的
     */
    @FormUrlEncoded
    @POST("img/close")
    fun imgCloseApi(@FieldMap map: HashMap<String, Any>): Observable<DataObjStringBean>

    /**
     * 首页多策略汇总接口
     */
    @GET("strategy/discover/listAll")
    fun strategyDiscoverListAllApi(@Query("accountId") accountId: String): Observable<StrategyRecommendAllBean>

    /**
     * 用户是否入过金
     */
    @POST("fund/checkDepositStatus")
    fun fundCheckDepositStatusApi(@QueryMap map: HashMap<String, Any>): Observable<CheckDepositStatusBean>

    /**
     * 出金查询是否需要上传地址证明
     */
    @POST("addressproof/withrawNeedUploadAddressProof")
    fun addressproofWithrawNeedUploadAddressProofApi(@QueryMap map: HashMap<String, Any>): Observable<NeedUploadAddressProofBean>

    /**
     * 出金是否需要上传身份地址证明(新)
     */
    @POST("addressproof/withrawNeedUploadIdPoaProof")
    fun addressproofWithrawNeedUploadIdPoaProofApi(@QueryMap map: HashMap<String, Any>): Observable<NeedUploadIdProofData>

    /**
     * 检查是否跳转到H5出金
     */
    @POST("fund/isH5Withdraw")
    fun fundIsH5WithdrawApi(@QueryMap map: HashMap<String, Any>): Observable<NeedH5WithdrawBean>

    /**
     * 用户同意开启Firebase数据收集状态变更
     */
    @POST("user/collectData/switch")
    fun userCollectDataSwitch(@Query("userToken") userToken: String): Observable<CollectDataBean>

    /**
     * 非跟单 我的页面 用户其他信息
     */
    @POST("accountHomeOther")
    fun accountHomeOther(@QueryMap map: HashMap<String, Any>): Observable<AccountHomeData>

    /**
     * 用户系统设置
     */
    @POST("userset/itemset")
    fun usersetItemsetApi(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * st 交易 --> 跟單帳號自选产品
     */
    @GET("account/product/my")
    fun accountProductMyApi(@QueryMap map: HashMap<String, Any>): Observable<StOptionalBean>

    /**
     * 交易 --> 查询自选产品
     */
    @POST("prod/list")
    fun prodListApi(@QueryMap map: HashMap<String, Any>): Observable<OptionalBean>

    /**
     * 登陆
     */
    @POST("loginNew")
    fun loginNewApi(@QueryMap map: HashMap<String, Any?>): Observable<LoginBean>

    /**
     * 三方登录
     */
    @Headers("apiVer: v2")
    @POST("thirdparty/login")
    fun thirdpartyLoginApi(@QueryMap map: HashMap<String, Any?>): Observable<LoginBean>

    /**
     * 自选产品 移动
     */
    @POST("prod/upd")
    fun prodUpdApi(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * ST 【跟單】自选产品 移动
     */
    @POST("account/product/my/upd")
    fun stAccountProductMyUpdApi(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * K线页底部 【财经日历】 和 【分析】 vau
     */
    @POST("product/listRelatedItems")
    fun productListRelatedItems(@QueryMap map: HashMap<String, String>): Observable<ProductItemInfoData>

    /**
     * 获取忘记密码的验证码
     */
    @POST("forgetpwd/getVerificationCode")
    fun forgetpwdGetVerificationCodeApi(@QueryMap map: HashMap<String, Any?>): Observable<ForgetPwdVerificationCodeBean>

    /**
     * 获取国家列表
     */
    @get:GET("queryNationalitys")
    val queryNationalitys: Observable<SelectNationalityBean>

    // 查询国家/省份/城市
    @POST("queryAddress")
    fun queryAddress(@QueryMap map: HashMap<String, Any>): Observable<ResidenceBean>

    /**
     * 查询国家/省份/城市
     */
    @POST("queryPlaceOfBirth")
    fun queryPlaceOfBirth(@QueryMap map: HashMap<String, Any>): Observable<ResidenceBean>

    /**
     * 修改密码
     */
    @POST("forgetpwd/forgetUpPwd")
    fun forgetpwdForgetUpPwdApi(@QueryMap map: HashMap<String, Any?>): Observable<ChangeUserInfoSuccessBean>

    /**
     * 获取账户列表第一步
     */
    @POST("queryAccountList")
    fun queryAccountList(@QueryMap map: HashMap<String, Any>): Observable<AccountListFirstBean>

    /**
     * 账户净值
     */
    @POST("queryAccountEquitList/v1")
    fun queryAccountEquitList(@Query("token") token: String): Observable<AccountsEquityData>

    /**
     * 单个账户信息
     */
    @POST("queryAccountInfo/v1")
    fun queryAccountInfo(@QueryMap map: HashMap<String, Any>): Observable<AloneAccountInfoData>

    /**
     * 获取跟单默认图片
     */
    @POST("getCopyTradingDefaultImg")
    fun getCopyTradingDefaultImg(@Query("token") token: String): Observable<DataObjStringBean>

    /**
     * 修改昵称
     */
    @POST("updateAccountNickName")
    fun updateAccountNickName(@QueryMap map: HashMap<String, String>): Observable<BaseBean>

    /**
     * 修改跟單昵称
     */
    @POST("updateStAccountNickName")
    fun updateStAccountNickName(@QueryMap map: HashMap<String, String>): Observable<BaseBean>

    /**
     * 查询可开通的账户类型(获取申请开通mt4账户号类型)
     */
    @POST("crm/getMt4AccountApplyType")
    fun crmGetMt4AccountApplyType(@QueryMap map: HashMap<String, String>): Observable<MT4AccountTypeBean>

    /**
     * 注册获取验证码
     */
    @POST("getTelRegisterSms")
    fun getTelRegisterSmsApi(@QueryMap map: HashMap<String, Any>): Observable<VerificationCodeData>

    /**
     * 校验注册验证码
     */
    @POST("sms/validateSmsRegisterCode")
    fun smsValidateSmsRegisterCodeApi(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 注册账号
     */
    @FormUrlEncoded
    @POST("registerNew")
    fun registerNewApi(@FieldMap map: HashMap<String, Any?>): Observable<LoginBean>

    /**
     * 三方注册
     */
    @Headers("apiVer: v3")
    @FormUrlEncoded
    @POST("thirdparty/register")
    fun thirdpartyRegisterApi(@FieldMap map: HashMap<String, Any?>): Observable<LoginBean>

    /**
     * 资金管理查询
     */
    @POST("fund/index")
    fun fundIndex(@QueryMap map: HashMap<String, Any>): Observable<ManageFundsBean>

    /**
     * 出金
     */
    @POST("fund/withDraw")
    fun fundWithDraw(@QueryMap map: HashMap<String, Any>): Observable<DataObjStringBean>

    /**
     * 开通真实账户
     */
    @FormUrlEncoded
    @POST("rgstRealMt4Account")
    fun rgstRealMt4Account(@FieldMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 保存缓存信息
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("process")
    fun process(@FieldMap map: HashMap<String, Any>): Observable<RealAccountCacheBean>

    /**
     * 开通真实账户校验邮箱
     * todo 此接口没有地方调用，可删除
     */
    @FormUrlEncoded
    @POST("emailIsExist")
    fun emailIsExist(@FieldMap map: HashMap<String, Any>): Observable<RealAccountCacheBean>

    /**
     * 注册校验邮箱
     */
    @FormUrlEncoded
    @POST("isExistEmail")
    fun isExistEmail(@FieldMap map: HashMap<String, Any>): Observable<RealAccountCacheBean>

    /**
     * 获取缓存信息
     */
    @Headers("apiVer: v3")
    @FormUrlEncoded
    @POST("getData")
    fun getData(@FieldMap map: HashMap<String, Any>): Observable<RealAccountCacheBean>

    /**
     * 就业及财务资料（澳洲接口数据
     */
    @GET("employmentFinance")
    fun employmentFinance(
        @QueryMap map: HashMap<String, String>
    ): Observable<EmploymentBean>

    /**
     * 贸易知识和经验（澳洲接口数据）
     * todo 此接口没有地方调用，可删除
     */
    @GET("trading")
    fun trading(@QueryMap map: HashMap<String, String>): Observable<EmploymentBean>

    /**
     * 上传图片
     */
    @POST("file/fileUpload")
    fun fileFileUpload(@Body body: MultipartBody?): Observable<UploadImageBean>

    /**
     * 获取下拉列表
     */
    @Headers("apiVer: v1")
    @GET("getAccountSelect")
    fun getAccountSelect(@QueryMap map: HashMap<String, String>): Observable<MoreAboutYouBean>

    /**
     *  获取验证码（api=SongPeng）
     *  0:用户注册;1:修改密码;2:绑定用户;3:修改手机号验证原手机号;4:修改手机号,新手机号验证;
     *  5:新增资金安全密码;6:忘记资金安全密码;7:绑定银行卡;
     */
    @POST("getTelSms")
    fun getTelSMSApi(@QueryMap map: HashMap<String, Any?>): Observable<ForgetPwdVerificationCodeBean>

    /**
     * 修改手机号
     */
    @Headers("apiVer: v2")
    @POST("userset/upTel")
    fun usersetUpTelApi(@QueryMap map: HashMap<String, Any?>): Observable<ChangeUserInfoSuccessBean>

    /**
     * 新增或修改资金安全密码
     */
    @Headers("apiVer: v2")
    @POST("userset/upfundpwd")
    fun usersetUpfundpwdApi(@QueryMap map: HashMap<String, Any?>): Observable<BaseBean>

    /**
     * 忘记资金安全密码
     */
    @Headers("apiVer: v2")
    @POST("userset/forgetSafePwd")
    fun usersetForgetSafePwdApi(@QueryMap map: HashMap<String, Any?>): Observable<BaseBean>

    /**
     * 开户引导
     */
    @POST("accountOpeningGuide")
    fun accountOpeningGuide(@QueryMap map: HashMap<String, Any>): Observable<AccountOpeningGuideBean>

    /**
     * 获取KYC等级信息
     */
    @FormUrlEncoded
    @POST("user/query-user-level")
    fun userQueryUserLevel(@FieldMap map: HashMap<String, Any>): Observable<KycVerifyLevelDataBean>

    /**
     * 查询虚拟MT5账户信息
     */
    @POST("queryVirtualAccount")
    fun queryVirtualAccount(): Observable<CheckVirtualAccountBean>

    /**
     * 查询主交易账户审核状态
     */
    @POST("queryFirstAccountAuditStatus")
    fun queryFirstAccountAuditStatus(): Observable<AccountAuditBean>

    /**
     * Demo清单列表
     */
    @POST("queryDemoAccountList")
    fun queryDemoAccountList(@QueryMap map: HashMap<String, Any>): Observable<DemoAccountListData>

    /**
     * 获取同名账户的相关信息
     */
    @POST("getPlatFormAccountTypeCurrency")
    fun getPlatFormAccountTypeCurrency(@QueryMap map: HashMap<String, String>): Observable<PlatFormAccountData>

    /**
     * 开通同名账户
     */
    @POST("applyTradeAccount")
    fun applyTradeAccount(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 检查是否有NDB活动
     */
    @FormUrlEncoded
    @POST("newCredit/isParticipating/v1")
    fun newCreditIsParticipatingApi(@FieldMap map: HashMap<String, Any?>): Observable<NDBStatusBean>

    /**
     * 退出NDB活动
     */
    @FormUrlEncoded
    @POST("newCredit/manualExitActivity/v1")
    fun newCreditManualExitActivityApi(@FieldMap map: HashMap<String, Any?>): Observable<BaseBean>

    /**
     * 获取问卷列表
     */
    @POST("appraisalList")
    fun appraisalList(@QueryMap map: HashMap<String, Any>): Observable<AuditQuestionData>

    /**
     * 提交问卷答案
     */
    @POST("auditRightAnswer")
    fun auditRightAnswer(@QueryMap map: HashMap<String, Any>): Observable<AuditQuestionData>

    /**
     * 校验手机号是否可以使用
     */
    @POST("phoneIsUsed")
    fun phoneIsUsedApi(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 获取邮箱登陆绑定验证码
     */
    @POST("getTelBindingSms")
    fun getTelBindingSmsApi(@QueryMap map: HashMap<String, Any>): Observable<VerificationCodeData>

    /**
     * 邮箱绑定
     */
    @Headers("apiVer: v2")
    @POST("userFirstLoginNew")
    fun userFirstLoginNewApi(@QueryMap map: HashMap<String, Any?>): Observable<EmailBindBean>

    /**
     * 检测用户是否需要更新版本
     */
    @POST("apk/checkVersion")
    fun apkCheckVersion(@QueryMap map: HashMap<String, Any>): Observable<AppVersionBean>

    /**
     * 获取所有国家手机区号
     */
    @POST("selectCountryNumberClassifyScreening")
    fun selectCountryNumberClassifyScreeningApi(): Observable<SelectCountryNumberBean>

    /**
     * 告诉后端登陆了...
     */
    @POST("updateAccountLogin")
    fun updateAccountLogin(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 告诉后端登陆跟單帳號了
     */
    @POST("updateStAccountLogin")
    fun updateStAccountLogin(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 绑定邮箱
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("bindingEmail")
    fun bindEmailUserApi(@FieldMap map: HashMap<String, Any?>): Observable<BindEmailBean>

    /**
     * 注册邮箱绑定
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("emailbindingPhone")
    fun emailBindingPhoneApi(@FieldMap map: HashMap<String, Any?>): Observable<LoginBean>

    /**
     * 查询模拟账户的信息
     */
    @FormUrlEncoded
    @POST("queryDemoAccount")
    fun queryDemoAccount(
        @FieldMap map: HashMap<String, Any>
    ): Observable<DemoAccountBean>

    /**
     * 同步demo
     */
    @FormUrlEncoded
    @POST("synchroDemo")
    fun synchroDemo(@FieldMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 获取埋点 param
     */
    @POST("appsFlyerStatistic/event-values/v1")
    fun appsFlyerStatisticEventValue(@Header("userToken") userToken: String): Observable<AppsFlyerPointBean>

    /**
     * 三方登录后的绑定用户
     */
    @Headers("apiVer: v2")
    @POST("thirdparty/bind")
    fun thirdpartyBindApi(@QueryMap map: HashMap<String, Any?>): Observable<LoginBean>

    /**
     * 三方登录绑定手机
     */
    @Headers("apiVer: v2")
    @FormUrlEncoded
    @POST("thirdparty/bindPhone")
    fun thirdpartyBindPhoneApi(@FieldMap map: HashMap<String, Any?>): Observable<EmailBindBean>

    /**
     * 获取服务器时间戳
     */
    @POST("data/server/time")
    fun dataServerTime(@QueryMap map: HashMap<String, Any>): Observable<ServerTimeBean>

    /**
     * FCM设备token绑定(新)
     */
    @POST("push/deviceToken/bind/v1")
    fun pushDeviceTokenBind(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * FCM设备token解绑(新)
     */
    @POST("push/deviceToken/unbind/v1")
    fun pushDeviceTokenUnbind(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * 删除旧的服务端推送设备号(新)
     */
    @POST("push/deviceToken/delete/v1")
    fun pushDeviceTokenDelete(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * 获取活动和新手券 -- 开户成功
     */
    @POST("getEventsAndRookieTicket")
    fun getEventsAndRookieTicket(@QueryMap map: HashMap<String, Any>): Observable<EventsTicketData>

    /**
     * 记录用户登录数据
     */
    @POST("record/add")
    fun recordAdd(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 是否显示 promoTab
     */
    @POST("promoTab")
    fun promoTab(@QueryMap map: HashMap<String, Any>): Observable<PromoTabBean>

    /**
     * 查询是否有新活动
     */
    @POST("userAction/hasChanges")
    fun userActionHasChanges(@QueryMap map: HashMap<String, Any>): Observable<DataObjBooleanBean>

    /**
     * 更新用户查看PromoTab时间
     */
    @POST("userAction/updateLastActionTime")
    fun userActionUpdateLastActionTime(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 查询是否有免费订单
     */
    @POST("stockActivity/stockListDetail")
    fun stockActivityStockListDetail(@QueryMap map: HashMap<String, Any>): Observable<FreeOrdersBean>

    /**
     * 关闭免费订单
     */
    @POST("stockActivity/closeStock/v1")
    fun stockActivityCloseStock(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 查询用户是否可以跳转优惠券页面
     */
    @POST("queryUserIsProclient")
    fun queryUserIsProclient(@QueryMap map: HashMap<String, Any>): Observable<QueryUserIsProclientData>

    /**
     * st 获取用户 token -- 账户列表
     */
    @POST("account/login-with-account")
    fun accountLoginWithAccount(@Body body: RequestBody?): Observable<StAccountLoginBean>

    /**
     * 产品列表 -- 跟单 (压缩)
     */
    @GET("trade/compress-products/simplified")
    fun tradeCompressProductsSimplified(@Query("login") accountId: String?): Observable<StringBean>

    /**
     * st 获取用户跟单 & 自主交易信息
     */
    @POST("account/acc-margin/simplified")
    fun accountAccMarginSimplified(@Header("token") token: String?): Observable<StAccMarginSimplifiedBean>

    /**
     * st 获取用户跟单 & 自主交易信息
     */
    @POST("account/acc-margin")
    fun accountAccMargin(@Header("token") token: String?): Observable<StAccMarginBean>

    /**
     * st 获取开仓订单/挂单 v2
     */
    @GET("trade/list/order/v2")
    fun tradeListOrderV2(
        @Query("type") type: String, @Query("portfolioId") portfolioId: String?
    ): Observable<PositionOrdersBean>

    /**
     * st 跟随策略列表  accountId：账号ID
     */
    @GET("order/follower-detail/list/v2")
    fun orderFollowerDetailListV2(@Query("accountId") accountId: String?): Observable<StFollowStrategyBean>

    /**
     * st 策略 待审核/已拒绝
     */
    @POST("strategy-copy/request-list")
    fun strategyCopyRequestList(@Body body: RequestBody?): Observable<StrategyOtherData>

    /**
     * 历史跟随策略列表
     */
    @GET("strategy/history-follower-detail/list")
    fun strategyHistoryFollowerDetailList(@Query("accountId") accountId: String?): Observable<StrategyHistoryBean>

    /**
     * st 停止跟随 策略/信号源
     */
    @POST("account/remove/follower/v2")
    fun accountRemoveFollower(@Query("portfolioId") portfolioId: String?): Observable<BaseBean>

    /**
     * st 恢复跟随 策略/信号源
     */
    @POST("account/resume/following")
    fun accountResumeFollowing(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * st 暂停跟随 策略/信号源
     */
    @POST("account/pause/following")
    fun accountPauseFollowing(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * st 平仓
     */
    @POST("trade/position/close")
    fun tradePositionClose(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * st 修改持仓订单
     */
    @POST("trade/position/update")
    fun tradePositionUpdate(@Body body: RequestBody?): Observable<StTradePositionUpdateBean>

    /**
     * st 获取某个信号源信息(Get signal info)
     */
    @POST("signal/get")
    fun signalGet(
        @Query("accountId") accountId: String?,
        @Query("signalId") signalId: String?
    ): Observable<StSignalInfoBean>

    /**
     * 跟单的k线请求
     */
    @POST("history/getKLine/mts")
    fun historyGetKLineMts(@Body body: RequestBody?): Observable<SymbolsChartData>

    /**
     * 首页弹窗、我的页面的banner、活动页面的banner 以及 直播弹窗 点击后的调用
     */
    @POST("events/addClicksCount")
    fun eventsAddClicksCount(@Query("eventsId") eventsId: String, @Query("token") token: String?): Observable<BaseBean>

    /**
     * 校验忘记密码验证码
     */
    @POST("sms/validateSmsForgetPwdCode")
    fun smsValidateSmsForgetPwdCodeApi(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 校验邮箱发送的忘记密码验证码
     */
    @POST("forgetpwd/otpVerification")
    fun forgetpwdOtpVerificationApi(@QueryMap map: HashMap<String, Any>): Observable<BaseBean>

    /**
     * 获取交易资金变动历史
     */
    @POST("trade/list/fund-history/v2")
    fun tradeListFundHistory(@QueryMap map: HashMap<String, Any>): Observable<FundHistoryBean>

    /**
     * 推送点击后调用
     */
    @POST("notificationMarketing/clickCnt/update")
    fun notificationMarketingClickCntUpdate(@Body body: RequestBody?): Observable<BaseBean>

    /**
     * st 走势图 -- 产品搜索 -- 热门产品
     */
    @POST("history/getRunChart")
    fun historyGetRunChart(@Body body: RequestBody?): Observable<StTrendBean>

    /**
     * 进入直播
     */
    @POST("addAwsWatchUser/v1")
    fun addAwsWatchUser(
        @Query("userId") userId: String,
        @Query("roomArn") roomArn: String,
        @Query("channelId") channelId: Long
    ): Observable<AddAWSData>

    /**
     * 退出直播
     */
    @POST("awsWatchUserSignOutLiveStream/v1")
    fun awsWatchUserSignOutLiveStream(
        @Query("userId") userId: String,
        @Query("channelId") channelId: Long
    ): Observable<BaseBean>

    /**
     * 获取观看人数等信息
     */
    @GET("getChannelById/v1")
    fun getChannelById(@Query("id") id: Long): Observable<LiveInfoData>

    /**
     * 过滤聊天内容
     */
    @POST("filter/v1")
    fun filter(
        @Query("userId") userId: String,
        @Query("brand") brand: String,
        @Query("roomId") roomId: Long,
        @Query("ChatContent") chatContent: String
    ): Observable<FilterChartData>

    /**
     * 点赞
     */
    @POST("addAndCancelLike/v1")
    fun addAndCancelLike(
        @Query("isLike") isLike: Int,
        @Query("id") channelId: Long
    ): Observable<LiveLikes>

    /**
     * 获取聊天内容
     */
    @POST("getChatRecord/v1")
    fun getChatRecord(@Query("roomId") roomId: Long): Observable<HistoryMessageData>

    /**
     * 获取聊天token
     */
    @POST("getToken/v1")
    fun getToken(
        @Query("userId") userId: String,
        @Query("roomArn") roomArn: String
    ): Observable<DataObjStringBean>

    /**
     * 获取双域名配置
     */
    @GET("common/baseUrl")
    fun commonBaseUrl(): Observable<ServerBaseUrlBean>

    /**
     * 直播里面的活动
     */
    @GET("events/getListByListStream/v1")
    fun eventsGetListByListStream(): Observable<LivePromoData>

    /**
     * 活动更新v2
     */
    @POST("maintenance/v2")
    fun maintenanceV2(@Query("imgType") imgType: Int): Observable<MaintenanceBean>

    /**
     * 日志拆分接口
     */
    @POST("consult/logContent/v1")
    suspend fun consultLogContent(
        @Query("token") token: String,
        @Query("logContent") logContent: String
    ): UserLogsData

    /**
     * 开户-获取审核状态
     */
    @POST("getAuditStatus")
    fun getAuditStatus(@Query("token") token: String): Observable<AuditStatusData>

    /**
     * 是否显示跟单入口接口定义
     */
    @POST("isShowSt")
    fun isShowSt(@Query("userId") userId: String?): Observable<StIsShowBean>

    /**
     * st 跟单者的结算页面的入口
     */
    @GET("profit-sharing/profile/follower")
    fun profitSharingProfileFollower(@Query("accountId") accountId: String?): Observable<StProfileSharingFollowerData>

    /**
     * inapp 点击后的接口请求
     */
    @Headers("apiVer: v1")
    @GET("inAppInfo/recordClick")
    fun inAppInfoRecordClick(
        @Query("token") token: String?,
        @Query("imgType") imgType: String?,
        @Query("userId") userId: String?,
        @Query("eventId") eventId: String?,
        @Query("accountId") accountId: String?,
    ): Observable<BaseBean>

    /**
     * 公告查询
     */
    @Headers("apiVer: v1")
    @POST("inAppInfo/list")
    fun inAppInfoList(@QueryMap map: HashMap<String, Any>): Observable<InAppBean>

    /**
     * 出金限制横幅
     */
    @POST("fund/withdrawRestrictionMessage")
    fun fundWithdrawRestrictionMessageApi(@QueryMap map: HashMap<String, Any>): Observable<DataObjStringBean>

    /**
     * 升级账户类型
     */
    @POST("account/upgradeGroup")
    fun accountUpgradeGroup(@QueryMap map: HashMap<String, Any>): Observable<DataObjBooleanBean>

    /**
     * Passkey登录
     */
    @POST("passKey/loginData")
    fun passKeyLoginDataApi(@QueryMap map: HashMap<String, String?>): Observable<PasskeyLoginData>

    /**
     * 发送邮箱验证码
     */
    @POST("email/sendEmailCode")
    fun emailSendEmailCodeApi(@QueryMap map: HashMap<String, Any?>): Observable<ForgetPwdVerificationCodeBean>

    /**
     * 校验邮箱验证码
     */
    @POST("email/preValidateEmailCode")
    fun emailPreValidateEmailCodeApi(@QueryMap map: HashMap<String, Any?>): Observable<BaseBean>

    /**
     * telegram-获取botId
     */
    @POST("telegram/getBotId")
    fun telegramGetBotIdApi(): Observable<TelegramGetBotBean>

    /**
     * 是否可以开通/显示钱包
     */
    @POST("account/open-account-validate")
    fun accountOpenAccountValidate(): Observable<OpenWalletBean>
}