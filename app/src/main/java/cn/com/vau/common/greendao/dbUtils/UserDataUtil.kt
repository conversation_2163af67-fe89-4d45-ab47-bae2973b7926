package cn.com.vau.common.greendao.dbUtils

import android.text.TextUtils
import cn.com.vau.common.greendao.dbUtils.UserDataUtil.isCopyTradingAccount
import cn.com.vau.common.greendao.dbUtils.UserDataUtil.isDemoAccount
import cn.com.vau.common.greendao.dbUtils.UserDataUtil.isLiveAccount
import cn.com.vau.common.greendao.dbUtils.UserDataUtil.isLiveVirtualAccount
import cn.com.vau.common.greendao.dbUtils.UserDataUtil.isRebateAccount
import cn.com.vau.common.storage.*

/**
 * loginToken()     账号 token
 * tradeToken()     交易 token
 * stToken()        跟单 token
 * userId()         账号 userId
 * nickname()       账号 昵称
 * userPic()        账号 头像
 * userTel()        账号 手机号
 * countryCode()    账号 国家简称 【CN】
 * areaCode()       账号 区号 【86】
 * email()          账号 email 邮箱
 * fastCloseState() 账号 快速平仓状态
 *
 * mt4State()       账户类型状态
 * userPwd()        账户登录密码
 * serverId()       账户 serverId
 * accountCd()      账户编号
 * currencyType()   账户货币
 */
object UserDataUtil {

    /**
     * 账号 是否登录
     */
    @JvmStatic
    fun isLogin(): Boolean {
        return isLiveVirtualAccount() || !TextUtils.isEmpty(accountCd()) || !TextUtils.isEmpty(stAccountId())
    }

    /**
     * 是否跟单登录
     */
    @JvmStatic
    fun isStLogin(): Boolean {
        return "5" == accountDealType()
    }

    /**
     * 账号 token
     */
    @JvmStatic
    fun loginToken(): String {
        return UserStorage.getLoginToken()
    }
    fun setLoginToken(token: String?) {
        UserStorage.setLoginToken(token)
    }

    /**
     * 账号 token(三端统一,后期会替换为使用这个token)
     */
    @JvmStatic
    fun xToken(): String {
        return UserStorage.getXToken()
    }
    fun setXToken(token: String?) {
        UserStorage.setXToken(token)
    }

    /**
     * 交易 token
     */
    @JvmStatic
    fun tradeToken(): String {
        return UserStorage.getTradeToken()
    }
    @JvmStatic
    fun setTradeToken(token: String?) {
        UserStorage.setTradeToken(token)
    }

    /**
     * 跟单 token
     */
    @JvmStatic
    fun stToken(): String {
        return UserStorage.getStToken()
    }
    fun setStToken(token: String?) {
        UserStorage.setStToken(token)
    }

    /**
     * 账号 userId
     */
    @JvmStatic
    fun userId(): String {
        return UserStorage.getUserId()
    }
    fun setUserId(userId: String?) {
        UserStorage.setUserId(userId)
    }

    /**
     * 跟单账户 公开交易
     */
    fun isStPublicTrade(): Boolean {
        return UserStorage.getIsStPublicTrade()
    }
    fun setIsStPublicTrade(isPublicTrade: Boolean?) {
        UserStorage.setIsStPublicTrade(isPublicTrade)
    }

    /**
     * 账号 昵称
     */
    @JvmStatic
    fun nickname(): String {
        return UserStorage.getUserNick()
    }
    fun setUserNickName(nickname: String?) {
        UserStorage.setUserNick(nickname)
    }

    /**
     * 账号 头像
     */
    @JvmStatic
    fun userPic(): String {
        return UserStorage.getUserPic()
    }
    fun setUserPic(userPic: String?) {
        UserStorage.setUserPic(userPic)
    }

    /**
     * 账号 手机号
     */
    @JvmStatic
    fun userTel(): String {
        return UserStorage.getUserTel()
    }
    fun setUserTel(userTel: String?) {
        UserStorage.setUserTel(userTel)
    }

    /**
     * 账号 国家简称 【CN】
     */
    fun countryCode(): String {
        return UserStorage.getCountryCode()
    }
    fun setCountryCode(countryCode: String?) {
        UserStorage.setCountryCode(countryCode)
    }

    /**
     * 账号 区号 【86】
     */
    @JvmStatic
    fun areaCode(): String {
        return UserStorage.getAreaCode()
    }
    fun setAreaCode(areaCode: String?) {
        UserStorage.setAreaCode(areaCode)
    }

    /**
     * 账号 email 邮箱
     */
    @JvmStatic
    fun email(): String {
        return UserStorage.getEmail()
    }
    fun setEmail(email: String?) {
        UserStorage.setEmail(email)
    }

    /**
     * 当前账号 快速平仓状态
     */
    fun fastCloseState(): String {
        /**
         * 后端接口反参决定
         * 0 开启平仓确认弹框
         * 1 关闭平仓确认弹窗
         */
        return UserStorage.getFastCloseState()
    }
    fun setFastCloseState(state: String?) {
        UserStorage.setFastCloseState(state)
    }

    /**
     * 当前账户状态 1:审核中,2:真实账户,3:模拟账号,4:返佣账户,5:跟单账户
     *
     * 可参考使用 [isLiveVirtualAccount] [isDemoAccount] [isLiveAccount] [isCopyTradingAccount] [isRebateAccount] 等方法来判断账户类型
     */
    @JvmStatic
    @Deprecated("请使用accountDealType字段来判断账户类型，但请注意原mt4State字段值并不完全等同于accountDealType字段值", replaceWith = ReplaceWith("accountDealType()"))
    fun mt4State(): String {
        return UserStorage.getMt4State()
    }
    fun setMt4State(mt4State: String?) {
        UserStorage.setMt4State(mt4State)
    }

    /**
     * 账户登录密码 【用户手动输入密码 明文密码 需优化】
     */
    @JvmStatic
    fun userPwd(): String {
        return UserStorage.getUserPassword()
    }
    fun setUserPassword(userPassword: String?) {
        UserStorage.setUserPassword(userPassword)
    }

    /**
     * 账户 serverId
     */
    @JvmStatic
    fun serverId(): String {
        return UserStorage.getServerId()
    }
    fun setServerId(serverId: String?) {
        UserStorage.setServerId(serverId)
    }

    /**
     * 账户编号
     */
    @JvmStatic
    fun accountCd(): String {
        return UserStorage.getAccountCd()
    }
    fun setAccountCd(accountCd: String?) {
        UserStorage.setAccountCd(accountCd)
    }

    /**
     * 账户货币
     */
    @JvmStatic
    fun currencyType(): String {
        return UserStorage.getCurrencyType()
    }
    fun setCurrencyType(currencyType: String?) {
        UserStorage.setCurrencyType(currencyType)
    }

    /**
     * 跟单账户 masterPortfolioId -- ???
     */
    @JvmStatic
    fun stMasterPortfolioId(): String {
        return UserStorage.getMasterPortfolioId()
    }
    fun setMasterPortfolioId(masterPortfolioId: String?) {
        UserStorage.setMasterPortfolioId(masterPortfolioId)
    }

    /**
     * 跟单账户 accountId -- ???
     */
    @JvmStatic
    fun stAccountId(): String {
        return UserStorage.getStAccountId()
    }
    fun setStAccountId(accountId: String?) {
        UserStorage.setStAccountId(accountId)
    }

    /**
     * 跟单账户 userId ( 和账号 userId 两码事 )
     */
    fun stUserId(): String {
        return UserStorage.getStUserId()
    }
    fun setStUserId(userId: String?) {
        UserStorage.setStUserId(userId)
    }

    /**
     * 跟单账户地区: 'GS'或'APAC'  GS区域账户跟随指定策略时获取额外收益或亏损兜底
     */
    fun isGSAccount(): Boolean {
        return UserStorage.getStUserArea() == "GS"
    }
    fun setStUserArea(userArea: String?) {
        UserStorage.setStUserArea(userArea)
    }

    /**
     * 账号 是否已开 live 账户
     */
    fun isOpenLiveAccount(): Boolean {
        return "1" == UserStorage.getOpenLiveAccountState()
    }
    fun setOpenLiveAccountState(state: String?) {
        UserStorage.setOpenLiveAccountState(state)
    }

    /**
     * 账号 是否已开 跟单 账户
     */
    fun isOpenStAccount(): Boolean {
        val openAccountType = UserStorage.getOpenAccountType()
        return 3 == openAccountType || 4 == openAccountType
    }
    fun setOpenAccountType(type: Int?) {
        UserStorage.setOpenAccountType(type)
    }

    /**
     * 当前账户 是否是IB账户
     */
    @JvmStatic
    fun isIB(): Boolean {
        // 0:IB账户 1:普通账户
        return 0 == UserStorage.getUserType()
    }
    fun setUserType(type: Int?) {
        UserStorage.setUserType(type)
    }

    /**
     * 当前账户 是否是MT5平台
     */
    @JvmStatic
    fun isMT5(): Boolean {
        return "5" == platform()
    }

    /**
     * 当前账户 是否是VTS平台
     */
    @JvmStatic
    fun isVts(): Boolean {
        return "7" == platform()
    }

    /**
     * 当前账户 所属平台
     */
    fun platform(): String {
        return UserStorage.getPlatForm()
    }
    fun setPlatForm(platform: String?) {
        UserStorage.setPlatForm(platform)
    }

    /**
     * 当前账户 类型 【 可优化 去除 】
     * 账户类型（1：Standard STP，2：Raw ECN，6：Islamic STP，13：Hedge STP，14：Hedge ECN）
     */
    fun accountType(): String {
        return UserStorage.getAccountType()
    }
    fun setAccountType(accountType: String?) {
        UserStorage.setAccountType(accountType)
    }

    /**
     * 账户类型
     * * 0：申请中账户
     * * 1：交易账户
     * * 2：返佣账户(暂不显示)
     * * 3：模拟账户
     * * 4：Pending账户 (审核状态之一:拒绝/通过/pending)
     * * 5：跟单账户
     * * 6：虚拟MT5的假Live账户
     */
    fun accountDealType(): String {
        if (!isLogin()) return ""

        val accountDealType = UserStorage.getAccountDealType()
        if (accountDealType.isNotEmpty()) return accountDealType
        // 防止线上留存用户没有这个字段时的处理方案
        val convert = when (mt4State()) {
            "1" -> "0"  // 审核中
            "2" -> "1"  // 真实账户
            "3" -> "3"  // 模拟账户
            "4" -> "2"  // 返佣账户
            "5" -> "5"  // 跟单账户
            else -> "3" // 其他按模拟账户
        }
        setAccountDealType(convert)
        return convert
    }
    fun setAccountDealType(accountDealType: String?) {
        UserStorage.setAccountDealType(accountDealType)
    }

    /**
     * 是否是虚拟MT5的假Live账户
     */
    fun isLiveVirtualAccount(): Boolean {
        return "6" == UserStorage.getAccountDealType()
    }
    /**
     * 是否是模拟账户
     */
    @JvmStatic
    fun isDemoAccount(): Boolean {
        return "3" == accountDealType()
    }
    /**
     * 是否是真实账户
     */
    fun isLiveAccount(): Boolean {
        return "1" == accountDealType()
    }
    /**
     * 是否是跟单账户
     */
    fun isCopyTradingAccount(): Boolean {
        return "5" == accountDealType()
    }
    /**
     * 是否是返佣账户
     */
    @JvmStatic
    fun isRebateAccount(): Boolean {
        return "2" == accountDealType()
    }

    /**
     * 用户已完成的KYC等级
     */
    fun kycLevel(): String {
        return UserStorage.getKycLevel()
    }
    fun setKycLevel(value: String?) {
        UserStorage.setKycLevel(value)
    }

    /**
     * mt4PWD 具体不清楚后端干啥用 【 可优化 】
     */
    @JvmStatic
    fun mt4PWD(): String {
        return UserStorage.getMt4PWD()
    }
    fun setMt4PWD(mt4PWD: String?) {
        UserStorage.setMt4PWD(mt4PWD)
    }

    /**
     * 账号真实姓名 【 可优化 去除 只有客服使用】
     */
    fun userRealName(): String {
        return UserStorage.getUserRealName()
    }
    fun setUserRealName(userRealName: String?) {
        UserStorage.setUserRealName(userRealName)
    }

    /**
     * 账号是否是只读账户
     */
    @JvmStatic
    fun isReadOnly(): Boolean {
        return UserStorage.getReadOnlyState()
    }
    fun setReadOnly(state: Boolean?) {
        UserStorage.setReadOnlyState(state)
    }

    /**
     * 当前账号 快速停止跟单状态
     */
    fun fastStopCopyState(): String {
        /**
         * 后端接口反参决定
         * 2 接口不返回表示用户从来未设置过，用户点击停止跟单弹出首次停止跟单的提示
         * 0 接口返回未设置快速停止跟单，用户点击停止跟单弹出是否停止跟单提示
         * 1 接口返回已设置快速停止跟单，用户点击停止跟单直接同步后端执行停止跟单
         */
        return UserStorage.getFastStopCopyState()
    }
    fun setFastStopCopyState(state: String?) {
        UserStorage.setFastStopCopyState(state)
    }

    /**
     * 订单确认状态
     */
    fun orderConfirmState(): String {
        return UserStorage.getOrderConfirmationState()
    }
    fun setOrderConfirmState(state: String?) {
        UserStorage.setOrderConfirmationState(state)
    }

    /**
     * 退出虚拟MT5账户 (变为存储了账号信息的未登录状态)
     */
    fun exitVirtualAccount() {
        setAccountDealType("")
        setAccountCd("")
        setServerId("5") // 按默认未登录状态
    }

    /**
     * 清除用户数据
     */
    fun clearUserData() {
        StorageUtil.clearUserData()
    }

}