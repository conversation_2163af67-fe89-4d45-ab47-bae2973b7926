package cn.com.vau.common.view.kchart.viewbeans;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.DashPathEffect;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.Log;

import androidx.core.content.res.ResourcesCompat;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.trade.kchart.ChartUIParamUtil;
import cn.com.vau.util.ExpandKt;
import cn.com.vau.util.widget.FirebaseManager;


/**
 * 描述：蜡烛线
 */
public class CandleLine extends ZoomMoveViewContainer<CandleLine.CandleLineBean> {
    //上下文
    private Context mContext;
    //极值指示线长度
    private static final int EXTREME_INDICATOR_LINE_WIDTH = 60;
    //蜡烛画笔
    private Paint mCandlePaint = null;
    //周期间隔线画笔
    private Paint mCycleIntervalPaint = null;
    //是否填充
    private boolean isFill = true;
    //涨时颜色
    private int mUpColor = Color.parseColor("#ff322e");
    //跌时颜色
    private int mDownColor = Color.parseColor("#2eff2e");
    //不涨不跌颜色
    private int mEvenColor = Color.parseColor("#656565");
    //周期间隔线颜色
    private int mCycleColor = Color.parseColor("#646C70");
    //柱之间间隙
    private float mSpace = 0f;
    //蜡烛宽度
    private float mCandleWidth = 0;
    //画最大最小值的画笔
    private Paint mTxtPaint;
    //画最大最小值前虚线的画笔
    private Paint mDashPaint;
    //是否显示了最大值
    private boolean isAlreadyShowMax = false;
    //是否显示了最小值
    private boolean isAlreadyShowMin = false;
    //是否显示最大值
    private boolean isNeedShowMaxPrice = true;
    //是否显示最小值
    private boolean isNeedShowMinPrice = true;
    private Typeface textTypeface;

    public CandleLine(Context context) {
        super(context);
        this.mContext = context;
        init();
    }

    //初始化画笔
    private void init() {
        textTypeface = ResourcesCompat.getFont(mContext, R.font.gilroy_medium);

        mCandlePaint = new Paint();
        mCandlePaint.setAntiAlias(true);
        mCandlePaint.setStrokeWidth(1.5f);
        mCandlePaint.setColor(Color.BLACK);
        mCandlePaint.setStyle(Paint.Style.STROKE);

        mCycleIntervalPaint = new Paint();
        mCycleIntervalPaint.setAntiAlias(true);
        mCycleIntervalPaint.setStrokeWidth(1.5f);
        mCycleIntervalPaint.setColor(mCycleColor);
        mCycleIntervalPaint.setStyle(Paint.Style.STROKE);
        mCycleIntervalPaint.setPathEffect(new DashPathEffect(new float[]{9, 9}, 0));

        mTxtPaint = new Paint();
        mTxtPaint.setAntiAlias(true);
        mTxtPaint.setStrokeWidth(1.5f);
        mTxtPaint.setColor(ChartUIParamUtil.INSTANCE.getCandleMaxMinTextColor());
        mTxtPaint.setTextSize(getPixelSp(9));
        mTxtPaint.setTypeface(textTypeface);
        mTxtPaint.setTextAlign(Paint.Align.LEFT);

        mDashPaint = new Paint();
        mDashPaint.setStyle(Paint.Style.STROKE);
        mDashPaint.setColor(ChartUIParamUtil.INSTANCE.getCandleMaxMinTextColor());
        mDashPaint.setStrokeWidth(1.5f);
//        mDashPaint.setPathEffect(ChartUIParamUtil.INSTANCE.getCommonDashEffect());

        mMinShownPointNums = 1;
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);
        try {
            if (isShow) {
                isAlreadyShowMax = false;
                isAlreadyShowMin = false;
                checkParamter();
                List<CandleLineBean> list = null;
                list = new ArrayList<>();
                // 计算最大最小值
                float max = 0, min = 0;
                for (int i = 0; i < mShownPointNums && i < mDataList.size(); i++) {
                    CandleLineBean bean = mDataList.get(i + mDrawPointIndex);
                    if(i == 0) {
                        max = bean.getHeightPrice();
                        min = bean.getLowPrice();
                    }else {
                        if (bean.getHeightPrice() > max) {
                            max = bean.getHeightPrice();
//                            LogUtil.i("wj", "max="+max+",    index="+i);
                        }
                        if (bean.getLowPrice() < min) {
                            min = bean.getLowPrice();
                        }
                    }
                    list.add(bean);
                }

//                LogUtil.i("wj", min+", "+max);
                for (int i = 0; i < list.size(); i++) {
                    CandleLineBean bean = list.get(i);
                    drawCandle(bean, i, canvas, min, max);
//                    if (checkInterval(bean, curPos, i)) {
//                        drawCycleInterval(bean, i, canvas);
//                    }
                }
                //改变坐标轴显示
                notifyCoordinateChange();
            }
        } catch (Exception ignored) {
            Log.d("CandleLine", "CandleLine on draw() has exception");
        }
    }

    // 绘制蜡烛一根
    private void drawCandle(CandleLineBean candleLineBean, int i, Canvas canvas, float min, float max) {
        //从收盘价与开盘价之间比较出最大值
        float maxPrice = candleLineBean.closePrice >= candleLineBean.openPrice ? candleLineBean.closePrice : candleLineBean.openPrice;
        //从收盘价与开盘价之间比较出最小值
        float minPrice = candleLineBean.closePrice <= candleLineBean.openPrice ? candleLineBean.closePrice : candleLineBean.openPrice;
        //计算出蜡烛顶端尖尖的Y轴坐标
        float y1 = (1f - (candleLineBean.heightPrice - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        //计算出蜡烛顶端横线的Y轴坐标
        float y2 = (1f - (maxPrice - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        //计算出蜡烛底端横线的Y轴坐标
        float y3 = (1f - (minPrice - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        //计算出蜡烛底端尖尖的Y轴坐标
        float y4 = (1f - (candleLineBean.lowPrice - mYMin) / (mYMax - mYMin)) * mCoordinateHeight;
        //每根蜡烛的宽度
        mCandleWidth = (mCoordinateWidth - mCoordinateMarginLeft - mCoordinateMarginRight) / mShownPointNums;
        //计算间隙
        mSpace = mCandleWidth / 7;

        // 非红即绿 绿涨红跌
        if (candleLineBean.openPrice < candleLineBean.closePrice) {//红
            mCandlePaint.setColor(mUpColor);
        } else {//绿
            mCandlePaint.setColor(mDownColor);
        }
        // 没有不涨不跌颜色 与iOS保持一致 也没有与昨收比较的逻辑了(没有调用了)
//        else {//黑
//            //当收盘价等于开盘价时，就拿收盘价和昨收比较，如果涨就红，如果跌就绿
//            if (candleLineBean.closePrice > candleLineBean.yesterdayPrice) {
//                mCandlePaint.setColor(mUpColor);
//            } else if (candleLineBean.closePrice < candleLineBean.yesterdayPrice) {
//                mCandlePaint.setColor(mDownColor);
//            } else {
//                mCandlePaint.setColor(mEvenColor);
//            }
//        }
        //进行绘画
        if (y2 != y3 && Math.abs(y2 - y3) > 1) {//非停牌且今开和收盘价差高于1块
            RectF rect = new RectF();
            rect.set(i * mCandleWidth + mSpace + mCoordinateMarginLeft,
                    y2,
                    i * mCandleWidth + mCandleWidth + mCoordinateMarginLeft,
                    y3);
            //画蜡烛的方块主干
            canvas.drawRect(rect, mCandlePaint);
        } else {//停牌,今开等于收盘
            //画蜡烛的方块主干,因为y2和y3相等或者差1,因此我们默认使用y2
            canvas.drawLine(i * mCandleWidth + mSpace + mCoordinateMarginLeft, y2,
                    i * mCandleWidth + mCandleWidth - mSpace + mCoordinateMarginLeft, y2,
                    mCandlePaint);
        }

        //TODO 有些计算可以放在外面，只算一次
        float needleX = ((i * mCandleWidth) + mSpace + (mCandleWidth - mSpace) / 2 + mCoordinateMarginLeft) - (mCandlePaint.getStrokeWidth() / 2);

        //画蜡烛的上尖尖
        canvas.drawLine(needleX, y1, needleX, y2, mCandlePaint);
        //画蜡烛的下尖尖
        canvas.drawLine(needleX, y3, needleX, y4, mCandlePaint);
        Path path = new Path();
        if (isNeedShowMaxPrice && candleLineBean.heightPrice == max && !isAlreadyShowMax) {
            //判断这个数据是在屏幕右边还是左边
            boolean right = i > mShownPointNums / 2;
            //获取要画的蜡烛的中心点
            String txt = ExpandKt.numFormat(candleLineBean.heightPrice, candleLineBean.digits, false);
            float width = mTxtPaint.measureText(txt);
            Paint.FontMetrics fm = new Paint.FontMetrics();
            mTxtPaint.getFontMetrics(fm);
            float height = Math.abs(fm.ascent);//文字高度
            if (right) {
                //如果在屏幕右边就往左边画
//                canvas.drawLine(needleX, y1, needleX - EXTREME_INDICATOR_LINE_WIDTH, y1, mDashPaint);
                path.moveTo(needleX, y1 - height/2);
                path.lineTo(needleX - EXTREME_INDICATOR_LINE_WIDTH, y1 - height/2);
                canvas.drawPath(path, mDashPaint);
                canvas.drawText(txt, needleX - EXTREME_INDICATOR_LINE_WIDTH - width-5, y1, mTxtPaint);
            } else {
                //如果在屏幕左边就往右边画
//                canvas.drawLine(needleX, y1, needleX + EXTREME_INDICATOR_LINE_WIDTH, y1, mDashPaint);
                path.moveTo(needleX, y1 - height/2);
                path.lineTo(needleX + EXTREME_INDICATOR_LINE_WIDTH, y1 - height/2);
                canvas.drawPath(path, mDashPaint);
                canvas.drawText(txt, needleX + EXTREME_INDICATOR_LINE_WIDTH+5, y1, mTxtPaint);
            }
            isAlreadyShowMax = true;
        }

        if (isNeedShowMinPrice && candleLineBean.lowPrice == min && !isAlreadyShowMin) {
            //判断这个数据是在屏幕右边还是左边
            boolean right = i > mShownPointNums / 2;
            //获取要画的蜡烛的中心点
            String txt = ExpandKt.numFormat(candleLineBean.lowPrice, candleLineBean.digits, false);
            float width = mTxtPaint.measureText(txt);
            Paint.FontMetrics fm = new Paint.FontMetrics();
            mTxtPaint.getFontMetrics(fm);
            float height = Math.abs(fm.ascent);//文字高度
            if (right) {
                //如果在屏幕右边就往左边画
//                canvas.drawLine(needleX, y4, needleX - EXTREME_INDICATOR_LINE_WIDTH, y4, mDashPaint);
                path.moveTo(needleX, y4 + height/2);
                path.lineTo(needleX - EXTREME_INDICATOR_LINE_WIDTH, y4 + height/2);
                canvas.drawPath(path, mDashPaint);
                canvas.drawText(txt, needleX - EXTREME_INDICATOR_LINE_WIDTH - width-5, y4 + height, mTxtPaint);
            } else {
                //如果在屏幕左边就往右边画
//                canvas.drawLine(needleX, y4, needleX + EXTREME_INDICATOR_LINE_WIDTH, y4, mDashPaint);
                path.moveTo(needleX, y4 + height/2);
                path.lineTo(needleX + EXTREME_INDICATOR_LINE_WIDTH, y4 + height/2);
                canvas.drawPath(path, mDashPaint);
                canvas.drawText(txt, needleX + EXTREME_INDICATOR_LINE_WIDTH+5, y4 + height, mTxtPaint);
            }
            isAlreadyShowMin = true;
        }
    }

    //绘制周期间隔线
    private void drawCycleInterval(CandleLineBean candleLineBean, int i, Canvas canvas) {
        float needleX = ((i * mCandleWidth) + mCandleWidth / 2 + mCoordinateMarginLeft) - (mCycleIntervalPaint.getStrokeWidth() / 2);
        Path path = new Path();
        path.moveTo(needleX, 0);
        path.lineTo(needleX, mCoordinateHeight);
        canvas.drawPath(path, mCycleIntervalPaint);
    }

    private void checkParamter() {
        if (this.mCoordinateHeight <= 0) {
            throw new IllegalArgumentException("mCoordinateHeight can't be zero or smaller than zero");
        }
        if (this.mCoordinateWidth <= 0) {
            throw new IllegalArgumentException("mCoordinateWidth can't be zero or smaller than zero");
        }
    }

    private boolean checkInterval(CandleLineBean candleLineBean, int curPos, int i) {
        Calendar calender = Calendar.getInstance();
        calender.setFirstDayOfWeek(Calendar.MONDAY);
        calender.setTimeInMillis(candleLineBean.mt4TimeMills);
//        LogUtil.d("wj", sdf.format(calender.getTime()));
//        LogUtil.d("wj", "week="+calender.get(Calendar.DAY_OF_WEEK)+", day="+calender.get(Calendar.DATE)+", hour="+calender.get(Calendar.HOUR_OF_DAY)+", min="+calender.get(Calendar.MINUTE)+", sec="+calender.get(Calendar.SECOND));

        CandleLineBean before = null;
        Calendar calender1 = null;
        if (i > 0) {
            int beforeI = i - 1;
            before = mDataList.get(beforeI + mDrawPointIndex);
            calender1 = Calendar.getInstance();
            calender1.setFirstDayOfWeek(Calendar.MONDAY);
            calender1.setTimeInMillis(before.mt4TimeMills);
        }
        switch (curPos) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
                //每日零点 或 零点的后一根
                if (calender.get(Calendar.HOUR_OF_DAY) == 0 && calender.get(Calendar.MINUTE) == 0) {
                    return true;
                } else {
                    if (calender1 != null) {
                        if (calender1.get(Calendar.HOUR_OF_DAY) == 0 && calender1.get(Calendar.MINUTE) == 0) {
                            return false;
                        } else {
                            if (Math.abs(calender.get(Calendar.DATE) - calender1.get(Calendar.DATE)) > 0) {
                                return true;
                            } else {
                                return false;
                            }
                        }
                    }
                }
                break;
            case 6:
            case 7:
                //每周一零点 或 零点的后一根
                if (calender.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY &&
                        calender.get(Calendar.HOUR_OF_DAY) == 0 && calender.get(Calendar.MINUTE) == 0) {
                    return true;
                } else {
                    if (calender1 != null) {
                        if (calender1.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY &&
                                calender1.get(Calendar.HOUR_OF_DAY) == 0 && calender1.get(Calendar.MINUTE) == 0) {
                            return false;
                        } else {
                            if (isSameWeek(calender, calender1)) {
                                return false;
                            } else {
                                return true;
                            }
                        }
                    }
                }
                break;
            case 8:
                //每月第一个周 或 后一根
                if (calender.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY && calender.get(Calendar.DAY_OF_WEEK_IN_MONTH) == 1) {
                    return true;
                } else {
                    if (calender1 != null) {
                        if (calender1.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY && calender1.get(Calendar.DAY_OF_WEEK_IN_MONTH) == 1) {
                            return false;
                        } else {
                            if (isSameMonth(calender, calender1)) {
                                return false;
                            } else {
                                return true;
                            }
                        }
                    }
                }
                break;
            case 9:
                //每年1月 或 后一根
                if (calender.get(Calendar.MONTH) == 0) {
                    return true;
                } else {
                    if (calender1 != null) {
                        if (calender1.get(Calendar.MONTH) == 0) {
                            return false;
                        } else {
                            if (calender.get(Calendar.YEAR) - calender1.get(Calendar.YEAR) == 0) {
                                return false;
                            } else {
                                return true;
                            }
                        }
                    }
                }
                break;
            default:
                break;
        }
        return false;
    }

    private boolean isSameWeek(Calendar cal1, Calendar cal2) {
        if (cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR) == 0) {
            return cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR);
        } else {
            int diffDay = 0;
            switch (cal1.get(Calendar.DAY_OF_WEEK)) {
                case Calendar.MONDAY:
                    break;
                case Calendar.TUESDAY:
                    diffDay = -1;
                    break;
                case Calendar.WEDNESDAY:
                    diffDay = -2;
                    break;
                case Calendar.THURSDAY:
                    diffDay = -3;
                    break;
                case Calendar.FRIDAY:
                    diffDay = -4;
                    break;
                case Calendar.SATURDAY:
                    diffDay = -5;
                    break;
                case Calendar.SUNDAY:
                    diffDay = -6;
                    break;
            }
            Calendar node = Calendar.getInstance();
            node.setFirstDayOfWeek(Calendar.MONDAY);
            node.setTime(cal1.getTime());
            node.set(Calendar.HOUR_OF_DAY, 0);
            node.set(Calendar.MINUTE, 0);
            node.set(Calendar.SECOND, 0);
            if (diffDay < 0) {
                node.add(Calendar.DATE, diffDay);
            }
            return cal2.compareTo(node) >= 0;
        }
    }

    private boolean isSameMonth(Calendar cal1, Calendar cal2) {
        return cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR) == 0 && cal1.get(Calendar.MONTH) - cal2.get(Calendar.MONTH) == 0;
    }

    public void setFill(boolean isFill) {
        this.isFill = isFill;
        if (this.isFill) {
            mCandlePaint.setStyle(Paint.Style.FILL);
        } else {
            mCandlePaint.setStyle(Paint.Style.STROKE);
        }
    }

    public void setColor(int upColor, int evenColor, int downColor) {
        this.mUpColor = upColor;
        this.mDownColor = downColor;
        this.mEvenColor = evenColor;
    }

    @Override
    public void setCoordinate(Coordinates coordinates) {
        this.mCoordinates = coordinates;
    }

    public Coordinates getCoordinates() {
        return mCoordinates;
    }

    public void setUpColor(int upColor) {
        this.mUpColor = upColor;
    }

    public void setEvenColor(int evenColor) {
        this.mEvenColor = evenColor;
    }

    public void setDownColor(int downColor) {
        this.mDownColor = downColor;
    }

    @Override
    public float getSingleDataWidth() {
        return mCandleWidth;
    }

    public void setShowMaxPrice(boolean needShowMaxPrice) {
        isNeedShowMaxPrice = needShowMaxPrice;
    }

    public void setShowMinPrice(boolean needShowMinPrice) {
        isNeedShowMinPrice = needShowMinPrice;
    }

    @Override
    public float[] calculateExtremeY() {
        if (mExtremeCalculatorInterface != null) {
            return mExtremeCalculatorInterface.onCalculateExtreme(mDrawPointIndex, mShownPointNums, 1111);
        } else if (mDataList != null && mDataList.size() > mDrawPointIndex) {
            float min = mDataList.get(mDrawPointIndex).getLowPrice();
            float max = mDataList.get(mDrawPointIndex).getHeightPrice();
            for (int i = mDrawPointIndex + 1; i < mDrawPointIndex + mShownPointNums && i < mDataList.size(); i++) {
                CandleLineBean entity = mDataList.get(i);
                min = entity.getLowPrice() < min && entity.getLowPrice() > 0 ? entity.getLowPrice() : min;
                max = max > entity.getHeightPrice() ? max : entity.getHeightPrice();
            }
            //-------临时代码，为了查找问题-----
            if (Float.isNaN(min) || Float.isNaN(max)){
                //上报为不严重类型
                FirebaseManager.INSTANCE.recordException(new Exception("CandleLine#calculateExtremeY(), min = "+min + ", max = "+max ));
            }
            //-------临时代码，结束-----
            return new float[]{min, max};
        }
        return new float[]{0, 0};
    }

    @Override
    protected float transDataToCrossDataFromDataList(int crossPointIndexInScreen, int dataInListIndex) {
        if (dataInListIndex >= mDataList.size()) {
            return super.transDataToCrossDataFromDataList(crossPointIndexInScreen, dataInListIndex);
        }

        CandleLineBean bean = mDataList.get(dataInListIndex);
        return bean.getClosePrice();
    }

    /**
     * 蜡烛数据信息
     */
    public static class CandleLineBean {
        public CandleLineBean() {

        }

        public CandleLineBean(int index, float heightPrice, float lowPrice, float openPrice, float closePrice, int digits) {
            this.index = index;
            this.heightPrice = heightPrice;
            this.lowPrice = lowPrice;
            this.openPrice = openPrice;
            this.closePrice = closePrice;
            this.digits = digits;
        }

        //下标
        private int index = -1;
        //最高价
        private float heightPrice = 0.0f;
        //最低价
        private float lowPrice = 0.0f;
        //开盘价
        private float openPrice = 0.0f;
        //收盘价
        private float closePrice = 0.0f;
        //原始买价（未加点）
        private float originalAsk = 0.0f;
        //原始卖价（未加点）
        private float originalBid = 0.0f;
        //毫秒数
        private long timeMills = 0;
        //mt4毫秒数
        private long mt4TimeMills = 0;
        //资产类型，用于格式化价格用
        private int type = 0;
        //昨日收盘价
        private float yesterdayPrice = 0.0f;
        //保留小数位
        private int digits = 2;

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public float getHeightPrice() {
            return heightPrice;
        }

        public void setHeightPrice(float heightPrice) {
            this.heightPrice = heightPrice;
        }

        public float getLowPrice() {
            return lowPrice;
        }

        public void setLowPrice(float lowPrice) {
            this.lowPrice = lowPrice;
        }

        public float getOpenPrice() {
            return openPrice;
        }

        public void setOpenPrice(float openPrice) {
            this.openPrice = openPrice;
        }

        public float getClosePrice() {
            return closePrice;
        }

        public void setClosePrice(float closePrice) {
            this.closePrice = closePrice;
        }

        public float getOriginalAsk() {
            return originalAsk;
        }

        public void setOriginalAsk(float askPrice) {
            this.originalAsk = askPrice;
        }

        public float getOriginalBid() {
            return originalBid;
        }

        public void setOriginalBid(float bidPrice) {
            this.originalBid = bidPrice;
        }

        public long getTimeMills() {
            return timeMills;
        }

        public void setTimeMills(long timeMills) {
            this.timeMills = timeMills;
        }

        public long getMt4TimeMills() {
            return mt4TimeMills;
        }

        public void setMt4TimeMills(long timeMills) {
            this.mt4TimeMills = timeMills;
        }

        public float getYesterdayPrice() {
            return yesterdayPrice;
        }

        public void setYesterdayPrice(float yesterdayPrice) {
            this.yesterdayPrice = yesterdayPrice;
        }

        public int getType() {
            return type;
        }

        public void setType(int type) {
            this.type = type;
        }

        public int getDigits() {
            return digits;
        }
    }

}
