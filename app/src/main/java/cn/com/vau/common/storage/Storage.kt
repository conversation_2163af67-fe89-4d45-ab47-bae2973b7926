package cn.com.vau.common.storage

import android.os.Parcelable
import cn.com.vau.util.ifNull
import com.tencent.mmkv.MMKV

class Storage(private var mmkv: MMKV?) {

    // ----------------------------- encode ---------------------------------
    fun encode(key: String, value: Any?): Boolean {
        return when (value) {
            null -> false
            is String -> mmkv?.encode(key, value) == true
            is Float -> mmkv?.encode(key, value.ifNull()) == true
            is Boolean -> mmkv?.encode(key, value.ifNull()) == true
            is Int -> mmkv?.encode(key, value.ifNull()) == true
            is Long -> mmkv?.encode(key, value.ifNull()) == true
            is Double -> mmkv?.encode(key, value.ifNull()) == true
            is ByteArray -> mmkv?.encode(key, value) == true
            is Parcelable -> mmkv?.encode(key, value) == true
            else -> false
        }
    }

    // ----------------------------- decode ---------------------------------

    fun decodeString(key: String, defaultValue: String = ""): String {
        return mmkv?.decodeString(key, defaultValue) ?: defaultValue
    }

    fun decodeInt(key: String, defaultValue: Int = 0): Int {
        return mmkv?.decodeInt(key, defaultValue) ?: defaultValue
    }

    fun decodeDouble(key: String, defaultValue: Double = 0.0): Double {
        return mmkv?.decodeDouble(key, defaultValue) ?: defaultValue
    }

    fun decodeLong(key: String, defaultValue: Long = 0L): Long {
        return mmkv?.decodeLong(key, defaultValue) ?: defaultValue
    }

    fun decodeBoolean(key: String, defaultValue: Boolean = false): Boolean {
        return mmkv?.decodeBool(key, defaultValue) ?: defaultValue
    }

    fun decodeFloat(key: String, defaultValue: Float = 0f): Float {
        return mmkv?.decodeFloat(key, defaultValue) ?: defaultValue
    }

    fun decodeByteArray(key: String): ByteArray? {
        return mmkv?.decodeBytes(key)
    }

    fun <T : Parcelable> decodeParcelable(key: String, tClass: Class<T>): T? {
        return mmkv?.decodeParcelable(key, tClass)
    }

    /**
     * 根据key移除字段，部分业务需求需要使用
     */
    fun removeKey(key: String) {
        mmkv?.remove(key)
    }

    fun clearAll() {
        mmkv?.clearAll()
    }
}