package cn.com.vau.common.http;

import java.util.HashMap;
import java.util.Map;

import retrofit2.Retrofit;

public class ServiceManager {

    /**
     * ServiceManager单例对象
     */
    private volatile static ServiceManager serviceManager;
    /**
     * API服务缓存
     */
    private final Map<String, Object> cacheServices = new HashMap<>();
    /**
     * Retrofit缓存
     */
    private final Map<String, Retrofit> cacheRetrofits = new HashMap<>();

    private ServiceManager() {
    }

    /**
     * 获取ServiceManager对象
     *
     * @return
     */
    public static ServiceManager getInstance() {
        if (serviceManager == null) {
            synchronized (ServiceManager.class) {
                if (serviceManager == null) {
                    serviceManager = new ServiceManager();
                }
            }
        }
        return serviceManager;
    }

    /**
     * 创建服务
     *
     * @param service
     * @param baseUrl
     * @param <T>
     * @return
     */
    public <T> T create(Class<T> service, String baseUrl) {
        if (service == null) {
            throw new RuntimeException("Api service is null!");
        }
        String key = service.getName() + baseUrl;
        Object cache = cacheServices.get(key);
        if (cache == null) {
            Retrofit retrofit = cacheRetrofits.get(baseUrl);
            if (retrofit == null) {
                retrofit = RetrofitUtils.getInstance()
                        .baseUrl(baseUrl)
                        .build();
                cacheRetrofits.put(baseUrl, retrofit);
            }
            T t = retrofit.create(service);
            cacheServices.put(key, t);
            return t;
        } else {
            return (T) cache;
        }
    }

    public FlowableService getBaseUrlService() {
        return create(FlowableService.class, HttpUrl.INSTANCE.getBaseUrl());
    }

    public FlowableService getBaseTradingUrlService() {
        return create(FlowableService.class, HttpUrl.INSTANCE.getBaseTradingUrl());
    }

    public FlowableService getBaseStTradingUrlService() {
        return create(FlowableService.class, HttpUrl.INSTANCE.getBaseStTradingUrl());
    }
}
