package cn.com.vau.common.view

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.LayoutEditTextOpenAccountBinding

class OpenAccountForActivityResult constructor(context: Context, attrs: AttributeSet? = null) : ConstraintLayout(context, attrs), VerifyComponent {
    private var parent: LayoutEditTextOpenAccountBinding
    private var targetIntent: Intent? = null
    private var mustFill = false
    private var hintTxt: CharSequence = ""
    private var titleTxt: CharSequence = ""
    private var showArrow = false
    private var launcher: ActivityResultLauncher<Intent>? = null

    init {
        parent = LayoutEditTextOpenAccountBinding.inflate(LayoutInflater.from(context), this, true)
        val attr = context.obtainStyledAttributes(attrs, R.styleable.OpenAccount_Option_Text)
        mustFill = attr.getBoolean(R.styleable.OpenAccount_Option_Text_must_fill, false)
        hintTxt = attr.getString(R.styleable.OpenAccount_Option_Text_hint_text) ?: ""
        showArrow = attr.getBoolean(R.styleable.OpenAccount_Option_Text_show_arrow, false)
        initView()
        initListener()
        attr.recycle()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        parent.tvHint.text = "${titleTxt.ifEmpty { hintTxt }}${if (mustFill) "*" else ""}"
        parent.mEditText.hint = hintTxt
        parent.mEditText.isFocusable = false
        parent.mEditText.isClickable = false
        parent.mEditText.isFocusableInTouchMode = false
        parent.mIfvArrow.visibility = if (showArrow) View.VISIBLE else View.GONE
    }

    private fun initListener() {
        parent.mEditText.keyListener = null
        parent.mEditText.setOnClickListener {
            if (targetIntent == null) {
                throw RuntimeException("Please set Intent in this component first!")
            } else {
                launcher?.launch(targetIntent)
            }
        }
    }

    fun setTitle(title: CharSequence?) {
        titleTxt = title ?: ""
        parent.tvHint.text = "${titleTxt.ifEmpty { hintTxt }}${if (mustFill) "*" else ""}"
    }

    fun setTarget(launcher: ActivityResultLauncher<Intent>, intent: Intent) {
        this.launcher = launcher
        this.targetIntent = intent
    }

    fun text(): String {
        return parent.mEditText.text.toString()
    }

    fun setText(text: String) {
        parent.mEditText.setText(text)
        callback?.verify(text.isNotEmpty())
    }

    @SuppressLint("SetTextI18n")
    fun initMustFill(mustFill: Boolean) {
        if (this.mustFill == mustFill) return
        this.mustFill = mustFill
        parent.tvHint.text = "$hintTxt${if (mustFill) "*" else ""}"
    }

    override var callback: VerifyCallBack? = null
    override fun getVerify(): Boolean = parent.mEditText.text.toString().isNotEmpty()
}