package cn.com.vau.common.view.popup.bean

import androidx.annotation.Keep
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.util.ifNull
import com.google.gson.Gson
import java.io.Serializable

@Keep
data class TradingViewSettingData(
    var main: MainIndicator? = null,
    var sub: SubIndicator? = null,
    var line: ExtraLine? = null,
    var mainChartName: String = "MA",
    var subChartName: String = "MACD",
    var mainSelectedIndex: Int = 0,
    var subSelectedIndex: Int = 0,
) : Serializable {
    companion object {
        fun init(): TradingViewSettingData {
            return TradingViewSettingData().apply {
                main = MainIndicator().apply {
                    ma = ListCount3().apply {
                        child1 = ListItem("MA1", 5, 0)
                        child2 = ListItem("MA2", 10, 0)
                        child3 = ListItem("MA3", 30, 0)
                    }
                    ema = ListCount3().apply {
                        child1 = ListItem("EMA1", 5, 0)
                        child2 = ListItem("EMA2", 10, 0)
                        child3 = ListItem("EMA3", 30, 0)
                    }
                    boll = ListCount3().apply {
                        child1 = ListItem("N", 20, 1)
                        child2 = ListItem("P", 2, 1)
                    }
                    mike = ListCount3().apply {
                        child1 = ListItem("N", 12, 1, 100)
                    }
                }
                sub = SubIndicator().apply {
                    macd = ListCount3().apply {
                        child1 = ListItem("S", 12, 1)
                        child2 = ListItem("L", 26, 1)
                        child3 = ListItem("M", 9, 1)
                    }
                    kdj = ListCount3().apply {
                        child1 = ListItem("N", 14, 1)
                        child2 = ListItem("M-1", 1, 1)
                        child3 = ListItem("M-2", 3, 1)
                    }
                    rsi = ListCount3().apply {
                        child1 = ListItem("RSI", 14, 1)
                    }
                    wr = ListCount3().apply {
                        child1 = ListItem("WR", 14, 1)
                    }
                    cci = ListCount3().apply {
                        child1 = ListItem("CCI", 14, 2, 100)
                    }
                    kd = ListCount3().apply {
                        child1 = ListItem("K", 14, 1, 100)
                        child2 = ListItem("D", 3, 1, 100)
                        child3 = ListItem("Smooth", 3, 1, 100)
                    }
                    dmi = ListCount3().apply {
                        child1 = ListItem("DI", 14, 2, 1000)
                        child2 = ListItem("ADX", 6, 2, 1000)
                    }
                }
                line = ExtraLine().apply {
                    ask = LineItem().apply {
                        type = "ask"
                        typeName = "Ask"
                    }
                    bid = LineItem().apply {
                        type = "bid"
                        typeName = "Bid"
                    }
                    tp = LineItem().apply {
                        type = "profit"
                        status = 1
                    }
                    sl = LineItem().apply {
                        type = "loss"
                        status = 1
                    }
                    position = LineItem().apply {
                        type = "open"
                        status = 1
                    }
                }
            }
        }

        fun getHistoryData(): TradingViewSettingData {
            if (UserDataUtil.isLogin()) {
                val userid = UserDataUtil.userId()
                val userData = SpManager.getTradingViewSettingData("${userid}_")
                if (userData.isNotEmpty()) {
//                    LogUtil.d("wj", "getHistoryData: 获取到userid: ${userid}的数据")
                    return Gson().fromJson(userData, TradingViewSettingData::class.java)
                } else {
                    val defaultData = getNotLoginData()
                    defaultData.save()
                    return defaultData
                }
            } else {
                return getNotLoginData()
            }
        }

        private fun getNotLoginData(): TradingViewSettingData {
            val defaultData = SpManager.getTradingViewSettingData("default_")
            return if (defaultData.isNotEmpty()) {
//                LogUtil.d("wj", "getNotLoginData: 获取到未登录数据")
                Gson().fromJson(defaultData, TradingViewSettingData::class.java)
            } else {
//                LogUtil.d("wj", "getNotLoginData: 什么数据都没获取到，返回初始化数据")
                init()
            }
        }
    }

    fun save() {
        val ext = if (UserDataUtil.isLogin()) UserDataUtil.userId() else "default"
        SpManager.putTradingViewSettingData("${ext}_",Gson().toJson(this))
//        LogUtil.d("wj", "save: 保存数据为 ${ext}_${StorageConstants.TRADING_VIEW_SETTING_DATA}")
    }

    fun outputIndicators(): Map<String, List<Int>> {
        val map: Map<String, List<Int>> = hashMapOf<String, List<Int>>().apply {
            put(
                "MA", listOf(
                    main?.ma?.child1?.num.ifNull(5),
                    main?.ma?.child2?.num.ifNull(10),
                    main?.ma?.child3?.num.ifNull(30),
                )
            )
            put(
                "EMA", listOf(
                    main?.ema?.child1?.num.ifNull(5),
                    main?.ema?.child2?.num.ifNull(10),
                    main?.ema?.child3?.num.ifNull(30),
                )
            )
            put(
                "BOLL", listOf(
                    main?.boll?.child1?.num.ifNull(20),
                    main?.boll?.child2?.num.ifNull(2),
                )
            )
            put(
                "MIKE", listOf(
                    main?.mike?.child1?.num.ifNull(12),
                )
            )
            put(
                "MACD", listOf(
                    sub?.macd?.child1?.num.ifNull(12),
                    sub?.macd?.child2?.num.ifNull(26),
                    sub?.macd?.child3?.num.ifNull(9),
                )
            )
            put(
                "KDJ", listOf(
                    sub?.kdj?.child1?.num.ifNull(14),
                    sub?.kdj?.child2?.num.ifNull(1),
                    sub?.kdj?.child3?.num.ifNull(3),
                )
            )
            put(
                "RSI", listOf(
                    sub?.rsi?.child1?.num.ifNull(14),
                )
            )
            put(
                "WR", listOf(
                    sub?.wr?.child1?.num.ifNull(14),
                )
            )
            put(
                "CCI", listOf(
                    sub?.cci?.child1?.num.ifNull(14),
                )
            )
            put(
                "KD", listOf(
                    sub?.kd?.child1?.num.ifNull(14),
                    sub?.kd?.child2?.num.ifNull(3),
                    sub?.kd?.child3?.num.ifNull(3),
                )
            )
            put(
                "DMI", listOf(
                    sub?.dmi?.child1?.num.ifNull(14),
                    sub?.dmi?.child2?.num.ifNull(6),
                )
            )
        }

//        return Gson().toJson(json)
        return map
    }

    fun outputLines(): List<LineItem> {
        return arrayListOf(
            line?.ask ?: LineItem(),
            line?.bid ?: LineItem(),
//            line?.tp ?: LineItem(),
//            line?.sl ?: LineItem(),
//            line?.position ?: LineItem()
        )
    }
}

@Keep
data class MainIndicator(
    var ma: ListCount3? = null,
    var ema: ListCount3? = null,
    var boll: ListCount3? = null,
    var mike: ListCount3? = null,
    var bbi: ListCount3? = null,
    var sar: ListCount3? = null
)

@Keep
data class SubIndicator(
    var macd: ListCount3? = null,
    var kdj: ListCount3? = null,
    var rsi: ListCount3? = null,
    var wr: ListCount3? = null,
    var cci: ListCount3? = null,
    var kd: ListCount3? = null,
    var dmi: ListCount3? = null
)

@Keep
data class ListCount3(
    var child1: ListItem? = null,
    var child2: ListItem? = null,
    var child3: ListItem? = null
)

@Keep
data class ListItem(
    var title: String? = null,
    val default: Int = 0,
    var minNum: Int = 0,
    var maxNum: Int = 999,
    var num: Int = default,
)

@Keep
data class ExtraLine(
    var ask: LineItem? = null,
    var bid: LineItem? = null,
    var tp: LineItem? = null,
    var sl: LineItem? = null,
    var position: LineItem? = null
)

@Keep
data class LineItem(
    var ordernumber: String = "",
    var price: Double = 0.00,
    var volume: Double = 0.00,
    var tradetype: String = "",
    var type: String? = null,
    var typeName: String = "",
    var status: Int = 0
)
