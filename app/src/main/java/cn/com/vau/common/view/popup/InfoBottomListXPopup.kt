package cn.com.vau.common.view.popup

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Paint
import android.text.TextUtils
import android.view.View
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.adapter.XPopupInfoBottomRecyclerAdapter
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.XpopupBottomInfoListBinding
import cn.com.vau.util.screenHeight
import com.lxj.xpopup.core.BottomPopupView

@SuppressLint("ViewConstructor")
@Deprecated("已弃用")
class InfoBottomListXPopup(
    context: Context, val dataList: ArrayList<HintLocalData>
) : BottomPopupView(context) {

    private var title = ""
    private var linkStr = ""

    private var isHideTitle = false

    // 单组内容 && 无标题
    constructor(
        context: Context,
        contentTitle: String,
        content: String
    ) : this(context, arrayListOf(HintLocalData(contentTitle, content))) {
        isHideTitle = true
    }

    // 标题 && 多组内容
    constructor(
        context: Context,
        title: String,
        dataList: ArrayList<HintLocalData>
    ) : this(context, dataList) {
        this.title = title
    }

    // 默认标题 && 多组内容 && 底部超链接
    constructor(
        context: Context,
        dataList: ArrayList<HintLocalData>,
        linkStr: String
    ) : this(context, dataList) {
        this.linkStr = linkStr
    }

    // 标题 && 多组内容 && 底部超链接
    constructor(
        context: Context,
        title: String,
        dataList: ArrayList<HintLocalData>,
        linkStr: String
    ) : this(context, dataList, linkStr) {
        this.title = title
    }

    private var closeClick: (() -> Unit)? = null
    private var linkClick: (() -> Unit)? = null

    override fun getImplLayoutId(): Int = R.layout.xpopup_bottom_info_list

    fun closeClick(click: () -> Unit): InfoBottomListXPopup {
        this.closeClick = click
        return this
    }

    fun linkClick(click: () -> Unit): InfoBottomListXPopup {
        this.linkClick = click
        return this
    }

    override fun getMaxHeight(): Int = (screenHeight * 0.75).toInt()

    override fun onCreate() {
        super.onCreate()

        val mBinding = XpopupBottomInfoListBinding.bind(popupImplView)

        if (!TextUtils.isEmpty(title)) {
            mBinding.tvTitle.text = title
        }

        if (isHideTitle) {
            mBinding.tvTitle.visibility = View.GONE
        }

        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(context)
        mBinding.mRecyclerView.adapter = XPopupInfoBottomRecyclerAdapter(context, dataList)

        mBinding.tvLink.isVisible = (!TextUtils.isEmpty(linkStr))
        if (!TextUtils.isEmpty(linkStr)) {
            mBinding.tvLink.text = linkStr
            mBinding.tvLink.paintFlags = Paint.UNDERLINE_TEXT_FLAG
            mBinding.tvLink.paint.isAntiAlias = true
        }

//        mBinding.ivClose.setOnClickListener {
//            closeClick?.invoke()
////            closeClick()
//            dismiss()
//        }

        mBinding.tvLink.setOnClickListener {
            linkClick?.invoke()
        }

    }

}