package cn.com.vau.trade.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.Editable
import android.text.TextUtils
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.ws.StWsManager
import cn.com.vau.common.http.ws.WsManager
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.ActivityModifyOrderBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.trade.viewmodel.ModifyOrderViewModel
import cn.com.vau.trade.viewmodel.OrderViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomActionWithIconDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.neovisionaries.ws.client.WebSocketState
import org.greenrobot.eventbus.EventBus
import kotlin.math.abs
import kotlin.math.pow

class NewModifyOrderActivity : BaseMvvmActivity<ActivityModifyOrderBinding, ModifyOrderViewModel>(), SDKIntervalCallback {
    var isInit = false
    private val shape_c00c79c_r10 by lazy { R.drawable.shape_c00c79c_r10 }
    private val shape_cf44040_r10 by lazy { R.drawable.shape_cf44040_r10 }
    private val draw_shape_c0a1e1e1e_c262930_r10 by lazy { R.drawable.draw_shape_c0a1e1e1e_c262930_r10 }
    private val color_ca61e1e1e_c99ffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_ca61e1e1e_c99ffffff) }
    private val cebffffff by lazy { R.color.cebffffff }

    private val atPriceWatcher by lazy {
        object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                val temp = edt.toString()
                if (temp.contains(".")) {
                    val posDot = temp.indexOf(".")
                    //删除前进行数值校验，看是否在length内
                    if (temp.length - posDot - 1 > mViewModel.digits) {
                        val endIndex = posDot + 2 + mViewModel.digits
                        if (endIndex <= edt.length) {
                            edt.delete(posDot + mViewModel.digits + 1, endIndex)
                        }
                    }
                    if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length) {
                        edt.delete(posDot - 1, posDot)
                    }
                } else {
                    if (temp.length > 9)
                        edt.delete(temp.length - 1, temp.length)
                }
                checkAtPrice()
                checkStopLimitPrice()
//                checkTvNext()
            }
        }
    }

    private val takeProfitWatcher by lazy {
        object : CustomTextWatcher() {
            override fun afterTextChanged(edt: Editable) {
                val temp = edt.toString()
                if (temp.contains(".")) {
                    val posDot = temp.indexOf(".")
                    if (temp.length - posDot - 1 > mViewModel.digits) {
                        edt.delete(posDot + mViewModel.digits + 1, posDot + 2 + mViewModel.digits)
                    }
                    if (posDot > 9 && posDot - 1 in 0..edt.length && posDot in 0..edt.length)
                        edt.delete(posDot - 1, posDot)
                } else {
                    if (temp.length > 9)
                        edt.delete(temp.length - 1, temp.length)
                }
                checkTakeProfitAndStopLoss()
//                checkTvNext()
            }
        }
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        if (intent?.extras?.containsKey(Constants.PARAM_ORDER_DATA) == true)
            mViewModel.orderData = intent?.extras?.getSerializable(Constants.PARAM_ORDER_DATA) as ShareOrderData
    }

    override fun useEventBus(): Boolean {
        return true
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.updatePendingOrderSuccessLiveData.observe(this){
            showDealSuccessDialog()
        }

        mViewModel.hintDataDialogLiveData.observe(this) {
            CenterActionDialog.Builder(this).setContent(it).setSingleButton().build().showDialog()
        }

        mViewModel.tokenErrorLiveData.observe(this) {
            showTokenErrorDialog(it)
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        SDKIntervalUtil.instance.addCallBack(this)
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        //展示订单信息
        showOrderInfo()
        //展示产品信息
        showProductData()
        // 进来时判断网络连接状态
        if (UserDataUtil.isStLogin()) {
            if (StWsManager.getInstance().getWsState() != WebSocketState.OPEN) {
                mViewModel.isConnected = false
                mBinding.tvNetworkStatus.text = getString(R.string.network_disconnected_please_again_later)
                mBinding.tvNetworkStatus.isVisible = true
                mBinding.tvGoProduct.setPadding(0,16.dp2px(),0,8.dp2px())
            }
        } else {
            if (WsManager.getInstance().getWsState() != WebSocketState.OPEN) {
                mViewModel.isConnected = false
                mBinding.tvNetworkStatus.text = getString(R.string.network_disconnected_please_again_later)
                mBinding.tvNetworkStatus.isVisible = true
                mBinding.tvGoProduct.setPadding(0,16.dp2px(),0,8.dp2px())
            }
        }
        //检查限价价格是否合法
        checkStopLimitPrice()
        //监听键盘是否弹起
        KeyboardUtil.registerSoftInputChangedListener(this){
            if (it == 0){
                clearFocus()
            }
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mBinding.ivKline.clickNoRepeat {
            openActivity(KLineActivity::class.java, Bundle().apply {
                putString(Constants.PARAM_PRODUCT_NAME, mViewModel.orderData?.symbol)
            })
        }

        mBinding.etAtPrice.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clAtPrice.isSelected = hasFocus
            formatAtPrice()
        }

        mBinding.etAtPrice.addTextChangedListener(atPriceWatcher)

        mBinding.ivAtPriceAdd.setOnClickListener {
            addAtPrice()
        }
        mBinding.ivAtPriceSub.setOnClickListener {
            subAtPrice()
        }

        mBinding.etStopLimitPrice.addTextChangedListener(atPriceWatcher)

        mBinding.ivStopLimitPriceAdd.setOnClickListener {
            addStopLimitPrice()
        }

        mBinding.ivStopLimitPriceSub.setOnClickListener {
            subStopLimitPrice()
        }

        mBinding.etStopLimitPrice.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clStopLimitPrice.isSelected = hasFocus
            formatStopLimitPrice()
        }

        mBinding.ivTakeProfitChecked.clickNoRepeat {
            selectedTakeProfile(mViewModel.takeProfitCb.not())
        }

        mBinding.tvTakeProfitTitle.clickNoRepeat {
            selectedTakeProfile(mViewModel.takeProfitCb.not())
        }

        mBinding.etTakeProfit.addTextChangedListener(takeProfitWatcher)

        mBinding.ivTakeProfitAdd.setOnClickListener {
            addTakeProfit()
        }

        mBinding.ivTakeProfitSub.setOnClickListener {
            subTakeProfit()
        }

        mBinding.etTakeProfit.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clTakeProfit.isSelected = hasFocus
            formatTakeProfit()
        }

        mBinding.ivStopLessChecked.clickNoRepeat {
            selectedStopLoss(mViewModel.stopLossCb.not())
        }

        mBinding.tvStopLessTitle.clickNoRepeat {
            selectedStopLoss(mViewModel.stopLossCb.not())
        }

        mBinding.etStopLoss.addTextChangedListener(takeProfitWatcher)

        mBinding.ivStopLossAdd.setOnClickListener {
            addStopLoss()
        }

        mBinding.ivStopLossSub.setOnClickListener {
            subStopLoss()
        }

        mBinding.etStopLoss.setOnFocusChangeListener { _, hasFocus ->
            mBinding.clStopLoss.isSelected = hasFocus
            formatStopLoss()
        }

        mBinding.mTimeChartView.setExpandStateListener { state ->
            mBinding.clContent.setPadding(0,0,0,if (state) 370.dp2px() else 193.dp2px())
        }

        mBinding.tvNext.clickNoRepeat {
           tradeOrdersUpdate()
        }
    }

    /**
     * 订单信息
     */
    @SuppressLint("SetTextI18n")
    private fun showOrderInfo() {
        val orderData = mViewModel.orderData
        mViewModel.digits = orderData?.digits ?: 0
        mBinding.tvGoProduct.text = orderData?.symbol // 商品名称
        mBinding.tvOrderNumber.text = "#${orderData?.order}"
        mBinding.tvVolume.text = "${orderData?.volume.ifNull("0.00")} ${getString(R.string.lots)}"
        mBinding.tvType.text = OrderUtil.getOrderTypeName(orderData?.cmd)
        mBinding.etAtPrice.setText(orderData?.openPrice)
        // 跟单没有
        mBinding.etStopLimitPrice.setText(orderData?.stopLimitPrice)
        mViewModel.setPendingTypeStr()
        val isSelectedTakeProfit = orderData?.takeProfit.toDoubleCatching() > 0
        selectedTakeProfile(isSelectedTakeProfit)
        val isSelectedStopLoss = orderData?.stopLoss.toDoubleCatching() > 0
        selectedStopLoss(isSelectedStopLoss)
        // 跟单没有
        mBinding.gpStopLimitPrice.isVisible = (mViewModel.orderData?.cmd == "6" || mViewModel.orderData?.cmd == "7")
    }

    /**
     * 格式化挂单价格小数位
     */
    private fun formatAtPrice() {
        val atPrice = mBinding.etAtPrice.text.toString()
        if (atPrice.isEmpty()){
            return
        }
        mBinding.etAtPrice.setText(atPrice.numFormat(mViewModel.digits, false))
    }

    /**
     * 点击减号，挂单价格计算
     */
    private fun subAtPrice() {
        val currentCount = mBinding.etAtPrice.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etAtPrice.setText(mViewModel.orderData?.openPrice)
            mBinding.etAtPrice.setSelection(mBinding.etAtPrice.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etAtPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etAtPrice.setSelection(mBinding.etAtPrice.text.toString().length)
    }

    /**
     * 点击加号，挂单价格计算
     */
    private fun addAtPrice() {
        val currentCount = mBinding.etAtPrice.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etAtPrice.setText(mViewModel.orderData?.openPrice)
            mBinding.etAtPrice.setSelection(mBinding.etAtPrice.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etAtPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etAtPrice.setSelection(mBinding.etAtPrice.text.toString().length)
    }

    /**
     * 检查挂单价格合法性
     */
    private fun checkAtPrice() {
        val atRange = mBinding.tvAtPriceRange.text.toString().replace(">=", "").replace("<=", "")
        val atCompareResults = if (mViewModel.pendingTypeStr == "<") 1 else -1
        val atPrice = mBinding.etAtPrice.text.toString()
        if (atPrice.mathCompTo(atRange) == atCompareResults) {
            val tipStr = if (atCompareResults == 1) {
                "${getString(R.string.max_value)}:$atRange"
            } else {
                "${getString(R.string.min_value)}:$atRange"
            }
            mBinding.tvAtPriceTip.text = tipStr
            mBinding.tvAtPriceTip.isVisible = true
            mBinding.tvDistanceTitle.isVisible = false
            mBinding.tvDistance.isVisible = false
        } else {
            mBinding.tvAtPriceTip.isVisible = false
            mBinding.tvDistanceTitle.isVisible = true
            mBinding.tvDistance.isVisible = true
        }
    }

    /**
     * 挂单价格和市价点差
     */
    @SuppressLint("SetTextI18n")
    private fun showDistance() {
        mViewModel.productData?.let {
            // distance = 挂单价- 买价/卖价
            var atPriceStr = mBinding.etAtPrice.text.toString()
            if (atPriceStr.isEmpty()) {
                atPriceStr = "0"
            }
            val atPriceFloat = atPriceStr.toFloatCatching()
            val abs = if (OrderUtil.isBuyOfOrder(mViewModel.orderData?.cmd).not()) {
                abs(atPriceFloat - it.bid)
            } else {
                abs(atPriceFloat - it.ask)
            }
            val distance: Float = abs * 10.0.pow((mViewModel.digits).toDouble()).toFloat()
            mBinding.tvDistance.text = "${distance.numFormat(0, true)} ${getString(R.string.points)}"
        }
    }

    /**
     * 限价价格小数位格式化
     */
    private fun formatStopLimitPrice() {
        val stopLimitPrice = mBinding.etStopLimitPrice.text.toString()
        if (stopLimitPrice.isEmpty()) {
            return
        }
        mBinding.etStopLimitPrice.setText(stopLimitPrice.numFormat(mViewModel.digits, false))
    }

    /**
     * 点击加号，限价价格计算
     */
    private fun addStopLimitPrice() {
        val currentCount = mBinding.etStopLimitPrice.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etStopLimitPrice.setText(mViewModel.orderData?.stopLimitPrice)
            mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etStopLimitPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
    }

    /**
     * 点击减号，限价价格计算
     */
    private fun subStopLimitPrice() {
        val currentCount = mBinding.etStopLimitPrice.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etStopLimitPrice.setText(mViewModel.orderData?.stopLimitPrice)
            mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etStopLimitPrice.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLimitPrice.setSelection(mBinding.etStopLimitPrice.text.toString().length)
    }

    /**
     * 点击减号，止盈价格计算
     */
    private fun subTakeProfit() {
        val currentCount = mBinding.etTakeProfit.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etTakeProfit.setText(mViewModel.takeProfitRange)
            mBinding.etTakeProfit.setSelection(mBinding.etStopLoss.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etTakeProfit.setText(
            editCount.numFormat(mViewModel.digits)
        )
        mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
    }

    /**
     * 点击加号，止盈价格计算
     */
    private fun addTakeProfit() {
        val currentCount = mBinding.etTakeProfit.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etTakeProfit.setText(mViewModel.takeProfitRange)
            mBinding.etTakeProfit.setSelection(mBinding.etStopLoss.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etTakeProfit.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
    }

    /**
     * 止盈价格小数位格式化
     */
    private fun formatTakeProfit() {
        val takeProfit = mBinding.etTakeProfit.text.toString()
        if (takeProfit.isEmpty()) {
            return
        }
        mBinding.etTakeProfit.setText(takeProfit.numFormat(mViewModel.digits, false))
    }

    /**
     * 点击减号，止损价格计算
     */
    private fun subStopLoss() {
        val currentCount = mBinding.etStopLoss.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etStopLoss.setText(mViewModel.stopLossRange)
            mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
            return
        }
        if (currentCount.mathCompTo("0") != 1) return
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        mBinding.etStopLoss.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
    }

    /**
     * 点击加号，止损价格计算
     */
    private fun addStopLoss() {
        val currentCount = mBinding.etStopLoss.text.toString()
        if (currentCount.isEmpty()){
            mBinding.etStopLoss.setText(mViewModel.stopLossRange)
            mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
            return
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        mBinding.etStopLoss.setText(editCount.numFormat(mViewModel.digits, false))
        mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
    }

    /**
     * 止损价格小数位格式化
     */
    private fun formatStopLoss() {
        val currentCount = mBinding.etStopLoss.text.toString()
        if (currentCount.isEmpty()) {
            return
        }
        mBinding.etStopLoss.setText(currentCount.numFormat(mViewModel.digits, false))
    }

    /**
     * 产品信息
     */
    @SuppressLint("SetTextI18n")
    private fun showProductData() {
        val data = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == mViewModel.orderData?.symbol
        } ?: return
        mViewModel.digits = data.digits
        mBinding.mTimeChartView.setData(data)
        OrderUtil.editextTop(
            mBinding.tvSellPrice,
            data.bid.formatProductPrice(data.digits, false)
        )

        OrderUtil.editextTop(
            mBinding.tvBuyPrice,
            data.ask.formatProductPrice(data.digits, false)
        )
        mBinding.tvSpread.text = data.spreadUI
        selectedOrderType(if (OrderUtil.isBuyOfOrder(mViewModel.orderData?.cmd)) OrderViewModel.TRADE_BUY else OrderViewModel.TRADE_SELL )
        var inputPrice = mBinding.etAtPrice.text.toString().trim()
        val isStopLimitPrice = (mViewModel.orderData?.cmd == "6" || mViewModel.orderData?.cmd == "7")
        if (isStopLimitPrice) {
            inputPrice = mBinding.etStopLimitPrice.text.toString()
        }
        val allMoney = OrderUtil.getRequiredMargin(
            data, mViewModel.orderData?.volume ?: "0",
            if (mViewModel.orderData?.cmd == "7" || mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "3") "bid" else "ask",
            inputPrice
        )
        mBinding.tvMargin.text = "${allMoney.numCurrencyFormat()} ${UserDataUtil.currencyType()}/"
        mBinding.tvFreeMargin.setTextDiff("${mViewModel.getFreeMargin()} ${UserDataUtil.currencyType()}")
    }

    /**
     * 持仓方向
     */
    private fun selectedOrderType(tradeType: String) {
//        mViewModel.tradeType = tradeType
        if (OrderViewModel.TRADE_SELL == tradeType) {
            mBinding.llSell.setBackgroundResource(shape_cf44040_r10)
            mBinding.llBuy.setBackgroundResource(draw_shape_c0a1e1e1e_c262930_r10)
            mBinding.tvSell.setTextColor(getColor(cebffffff))
            mBinding.tvSellPrice.setTextColor(getColor(cebffffff))
            mBinding.tvBuy.setTextColor(color_ca61e1e1e_c99ffffff)
            mBinding.tvBuyPrice.setTextColor(color_ca61e1e1e_c99ffffff)

        } else {
            mBinding.llSell.setBackgroundResource(draw_shape_c0a1e1e1e_c262930_r10)
            mBinding.llBuy.setBackgroundResource(shape_c00c79c_r10)
            mBinding.tvSell.setTextColor(color_ca61e1e1e_c99ffffff)
            mBinding.tvSellPrice.setTextColor(color_ca61e1e1e_c99ffffff)
            mBinding.tvBuy.setTextColor(getColor(cebffffff))
            mBinding.tvBuyPrice.setTextColor(getColor(cebffffff))
        }
//        mViewModel.initTradeTypeList(this)
//        initTradeTypeView()
    }

    /**
     * 是否开启止盈
     * isSelected: true 开启
     */
    private fun selectedTakeProfile(isSelected: Boolean) {
        mViewModel.takeProfitCb = isSelected
        mBinding.gpTakeProfit.isVisible = isSelected
        mBinding.tvTakeProfitTip.isVisible = false
        mBinding.ivTakeProfitChecked.setImageResource(if (isSelected) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
        setTakeProfit(isSelected)
        if (isSelected) {
            checkTakeProfitAndStopLoss()
//            checkTvNext()
        }
    }

    /**
     * 挂单原订单是否设置了止盈
     * isChecked: true 开启
     */
    private fun setTakeProfit(isChecked: Boolean) {
        //显示止盈和止损
        mBinding.etTakeProfit.setText(
            if (isChecked){
                if (TextUtils.isEmpty(mBinding.etTakeProfit.text.toString().trim()))
                    mViewModel.takeProfitRange
                else
                    mBinding.etTakeProfit.text.toString()
            }
            else null
        )
        mBinding.etTakeProfit.setSelection(mBinding.etTakeProfit.text.toString().length)
    }

    /**
     * 是否开启止损
     * isSelected: true 开启
     */
    private fun selectedStopLoss(isSelected: Boolean) {
        mViewModel.stopLossCb = isSelected
        mBinding.gpStopLoss.isVisible = isSelected
        mBinding.ivStopLessChecked.setImageResource(if (isSelected) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
        setStopLoss(isSelected)
        if (isSelected) {
            checkTakeProfitAndStopLoss()
//            checkTvNext()
        }
    }

    /**
     * 挂单原订单是否设置了止损
     * isChecked: true 开启
     */
    private fun setStopLoss(isChecked: Boolean) {
        //显示止盈和止损
        mBinding.etStopLoss.setText(
            if (isChecked){
                if (TextUtils.isEmpty(mBinding.etStopLoss.text.toString().trim()))
                    mViewModel.stopLossRange
                else mBinding.etStopLoss.text.toString()
            }
            else null
        )
        mBinding.etStopLoss.setSelection(mBinding.etStopLoss.text.toString().length)
    }

    /**
     * 检查止盈止损是否合法
     */
    private fun checkTakeProfitAndStopLoss() {
        val tpCompareResults = if (mViewModel.orderData?.cmd == "7" || mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "3") 1 else -1
        checkTakeProfit(tpCompareResults)
        checkStopLoss(-tpCompareResults)
    }

    /**
     * 检查止盈是否合法
     */
    private fun checkTakeProfit(tpCompareResults: Int) {
        val takeProfit = mBinding.etTakeProfit.text.toString()
        if (takeProfit.mathCompTo(mViewModel.takeProfitRange) == tpCompareResults) {
            val tipStr = if (tpCompareResults == 1) {
                "${getString(R.string.max_value)}:${mViewModel.takeProfitRange}"
            } else {
                "${getString(R.string.min_value)}:${mViewModel.takeProfitRange}"
            }
            mBinding.tvTakeProfitTip.text = tipStr
            mBinding.tvTakeProfitTip.isVisible = mViewModel.takeProfitCb
            mBinding.tvEstimatedProfitTitle.isVisible = false
            mBinding.tvEstimatedProfit.isVisible = false
        } else {
            mBinding.tvTakeProfitTip.isVisible = false
            mBinding.tvEstimatedProfitTitle.isVisible = mViewModel.takeProfitCb
            mBinding.tvEstimatedProfit.isVisible = mViewModel.takeProfitCb
        }
    }

    /**
     * 检查止损是否合法
     */
    private fun checkStopLoss(slCompareResults: Int) {
        val stopLoss = mBinding.etStopLoss.text.toString()
        if (stopLoss.mathCompTo(mViewModel.stopLossRange) == slCompareResults) {
            val tipStr = if (slCompareResults == 1) {
                "${getString(R.string.max_value)}:${mViewModel.stopLossRange}"
            } else {
                "${getString(R.string.min_value)}:${mViewModel.stopLossRange}"
            }
            mBinding.tvStopLossTip.text = tipStr
            mBinding.tvStopLossTip.isVisible = mViewModel.stopLossCb
            mBinding.tvEstimatedLossTitle.isVisible = false
            mBinding.tvEstimatedLoss.isVisible = false
        } else {
            mBinding.tvStopLossTip.isVisible = false
            mBinding.tvEstimatedLossTitle.isVisible = mViewModel.stopLossCb
            mBinding.tvEstimatedLoss.isVisible = mViewModel.stopLossCb
        }
    }

    /**
     * 检查限价价格是否合法
     */
    private fun checkStopLimitPrice() {
        if (mViewModel.isStopLimit().not()) {
            return
        }
        val stopLimitPriceRange = mBinding.tvStopLimitPriceRange.text.toString().replace(">=", "").replace("<=", "")
        val atCompareResults = if (OrderUtil.isBuyOfOrder(mViewModel.orderData?.cmd)) {
            1
        } else {
            -1
        }
        val stopLimitPrice = mBinding.etStopLimitPrice.text.toString()
        if (stopLimitPrice.mathCompTo(stopLimitPriceRange) == atCompareResults) {
            val tipStr = if (atCompareResults == 1) {
                "${getString(R.string.max_value)}:$stopLimitPriceRange"
            } else {
                "${getString(R.string.min_value)}:$stopLimitPriceRange"
            }
            mBinding.tvStopLimitTip.text = tipStr
            mBinding.tvStopLimitTip.isVisible = true
        } else {
            mBinding.tvStopLimitTip.isVisible = false
        }
    }

    /**
     * 根据行情刷新订单信息
     */
    @SuppressLint("SetTextI18n")
    override fun onCallback() {
        val data = VAUSdkUtil.symbolList().firstOrNull {
            it.symbol == mViewModel.orderData?.symbol
        } ?: return

        mViewModel.productData = data
        OrderUtil.editextTop(
            mBinding.tvSellPrice,
            data.bid.formatProductPrice(data.digits, false)
        )
        OrderUtil.editextTop(
            mBinding.tvBuyPrice,
            data.ask.formatProductPrice(data.digits, false)
        )
        mBinding.tvSpread.text = data.spreadUI
        // 下单页挂单价格 = 市加或减止损水平，这里没加或减，这里应该也没啥用，下方有重新计算的重复逻辑
//        mBinding.tvAtPriceRange.setTextDiff(
//            if (OrderUtil.isBuyOfOrder(mViewModel.orderData?.cmd).not()) {
//                "${mViewModel.pendingTypeStr}=${data.bid}"
//            } else {
//                "${mViewModel.pendingTypeStr}=${data.ask}"
//            }.arabicText().ifNull()
//        )

        //TODO Felix 不需要重复计算
        val stopLossLevel = data.stopslevel.mathDiv("${10.0.pow(data.digits)}", data.digits + 1)
//        val atPriceValue = mBinding.etAtPrice.text.toString().trim()

        mBinding.tvFreeMargin.setTextDiff("${mViewModel.getFreeMargin()} ${UserDataUtil.currencyType()}")
        //刷新挂单价格范围，止盈止损范围
        refreshAtPriceRangeTakeProfitRangeStopLossRange(data,stopLossLevel)
        if (isInit.not()) {
            //没有初始化时要初始止盈止损范围
            isInit = true
            // Felix 不用重复计算
            mViewModel.minProfit = "${1 / 10.0.pow(mViewModel.digits.toDouble())}"
            if (mViewModel.orderData?.takeProfit.toDoubleCatching() > 0 || mViewModel.orderData?.stopLoss.toDoubleCatching() > 0) {
                // 订单已设置止盈则直接展示
                mBinding.etTakeProfit.setText(if (mViewModel.takeProfitCb) mViewModel.orderData?.takeProfit else null)
                mBinding.etStopLoss.setText(if (mViewModel.stopLossCb) mViewModel.orderData?.stopLoss else null)
            } else {
                // 订单没有设置止盈止损则初始止盈止损
                initTakeProfitStopLoss(data,stopLossLevel)
            }
        }
        //计算盈利
        showEstimated(data)

        var inputPrice = mBinding.etAtPrice.text.toString().trim()
        val isStopLimitPrice = (mViewModel.orderData?.cmd == "6" || mViewModel.orderData?.cmd == "7")
        if (isStopLimitPrice) {
            inputPrice = mBinding.etStopLimitPrice.text.toString()
        }

        val allMoney = OrderUtil.getRequiredMargin(
            data, mViewModel.orderData?.volume ?: "0",
            if (mViewModel.orderData?.cmd == "7" || mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "3") "bid" else "ask",
            inputPrice
        )

        mBinding.tvMargin.text = "${allMoney.numCurrencyFormat()} ${UserDataUtil.currencyType()}/"
        mBinding.tvFreeMargin.setTextDiff("${mViewModel.getFreeMargin()} ${UserDataUtil.currencyType()}")
        //检查限价价格是否合法
        checkStopLimitPrice()
        //检查止盈止损是否合法
        checkTakeProfitAndStopLoss()
        //挂单价格和市价点差
        showDistance()
        //检查挂单价格合法性
        checkAtPrice()
        //检查网络状态和闭市状态
        checkNetWorkStatus(data)
        //分时图更新
        mBinding.mTimeChartView.updateQuotation()
    }


    /**
     * 刷新挂单价格范围，止盈止损范围
     */
    private fun refreshAtPriceRangeTakeProfitRangeStopLossRange(data: ShareProductData,stopLossLevel:String) {
        val atPriceValue = mBinding.etAtPrice.text.toString().trim()
        if (mViewModel.orderData?.cmd == "7" || mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "3") {
            // Sell
            //如果是跟单这个值不用关注
            val stopLimitValue = mBinding.etStopLimitPrice.text.toString()

            if (mViewModel.orderData?.cmd == "7") {
                mViewModel.stopLossRange = (stopLimitValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.takeProfitRange = (stopLimitValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
            } else {
                mViewModel.stopLossRange = (atPriceValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.takeProfitRange = (atPriceValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
            }
            //mViewModel.orderData?.cmd == "4" 一直是false 删除了
            val atPrice = (if (mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "7")
                "${data.bid}".mathSub(stopLossLevel).numFormat(
                    mViewModel.digits,
                    false
                )
            else
                "${data.bid}".mathAdd(stopLossLevel)).numFormat(
                mViewModel.digits,
                false
            )
            mBinding.tvAtPriceRange.setTextDiff("${mViewModel.pendingTypeStr}=$atPrice")

            mBinding.tvStopLimitPriceRange.setTextDiff(
                ">=${
                    (mBinding.etAtPrice.text.toString().mathAdd(stopLossLevel)).numFormat(
                        mViewModel.digits,
                        false
                    )
                }".arabicText().ifNull()
            )
        } else {
            //Buy
            // 跟单不用关注
            val stopLimitValue = mBinding.etStopLimitPrice.text.toString()

            if (mViewModel.orderData?.cmd == "6") {
                mViewModel.stopLossRange = (stopLimitValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.takeProfitRange = (stopLimitValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
            } else {
                mViewModel.stopLossRange = (atPriceValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.takeProfitRange = (atPriceValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
            }
            //  mViewModel.orderData?.cmd == "5" 一直是false 删除了
            val atPrice = (if (mViewModel.orderData?.cmd == "4" || mViewModel.orderData?.cmd == "6")
                "${data.ask}".mathAdd(stopLossLevel).numFormat(
                    mViewModel.digits,
                    false
                )
            else
                "${data.ask}".mathSub(stopLossLevel)).numFormat(
                mViewModel.digits,
                false
            )
            mBinding.tvAtPriceRange.setTextDiff("${mViewModel.pendingTypeStr}=$atPrice")

            mBinding.tvStopLimitPriceRange.setTextDiff(
                "<=${
                    (mBinding.etAtPrice.text.toString().mathSub(stopLossLevel)).numFormat(
                        mViewModel.digits,
                        false
                    )
                }"
            )
        }
    }

    /**
     * 初始显示止盈止损价格
     */
    @SuppressLint("SetTextI18n")
    private fun initTakeProfitStopLoss(data: ShareProductData, stopLossLevel:String) {
        val atPriceValue = mBinding.etAtPrice.text.toString().trim()
        if (mViewModel.orderData?.cmd == "7" || mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "3") { // sell
            // mViewModel.orderData?.cmd == "4"  不能成立 已删除
            val atPrice = (if (mViewModel.orderData?.cmd == "5" || mViewModel.orderData?.cmd == "7")
                "${data.bid}".mathSub(stopLossLevel)
            else
                "${data.bid}".mathAdd(stopLossLevel)).numFormat(
                mViewModel.digits,
                false
            )

            mBinding.tvAtPriceRange.setTextDiff("${mViewModel.pendingTypeStr}=$atPrice")

            val stopLimitValue = mBinding.etAtPrice.text.toString()

            if (mViewModel.orderData?.cmd == "7") {
                mViewModel.takeProfitRange = (stopLimitValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.stopLossRange = (stopLimitValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
            } else {
                mViewModel.takeProfitRange = (atPriceValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.stopLossRange = (atPriceValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
            }

            mBinding.tvStopLimitPriceRange.setTextDiff(">=${(mBinding.etAtPrice.text.toString().mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)}")

            mBinding.etTakeProfit.setText(if (mViewModel.takeProfitCb) mViewModel.takeProfitRange else null)
            mBinding.etStopLoss.setText(if (mViewModel.stopLossCb) mViewModel.stopLossRange else null)

        }else{
            // buy
            // mViewModel.orderData?.cmd == "5" 不可能成立 已删除

            val atPrice = (if (mViewModel.orderData?.cmd == "4" || mViewModel.orderData?.cmd == "6")
                "${data.ask}".mathAdd(stopLossLevel)// 原来是减（原来是错的）
            else
                "${data.ask}".mathSub(stopLossLevel)).numFormat( // 原来是加（原来是错的）
                mViewModel.digits,
                false
            )

            mBinding.tvAtPriceRange.text = "${mViewModel.pendingTypeStr}=$atPrice"

            val stopLimitValue = mBinding.etAtPrice.text.toString()

            if (mViewModel.orderData?.cmd == "6") {
                mViewModel.stopLossRange = (stopLimitValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.takeProfitRange = (stopLimitValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
            } else {
                mViewModel.stopLossRange = (atPriceValue.mathSub(stopLossLevel)).numFormat(mViewModel.digits, false)
                mViewModel.takeProfitRange = (atPriceValue.mathAdd(stopLossLevel)).numFormat(mViewModel.digits, false)
            }

            mBinding.tvStopLimitPriceRange.setTextDiff(
                "<=${
                    (mBinding.etAtPrice.text.toString().mathSub(stopLossLevel)).numFormat(
                        mViewModel.digits,
                        false
                    )
                }"
            )

            mBinding.etTakeProfit.setText(if (mViewModel.takeProfitCb) mViewModel.takeProfitRange else null)
            mBinding.etStopLoss.setText(if (mViewModel.stopLossCb) mViewModel.stopLossRange else null)
        }
    }

    /**
     * 计算盈利
     */
    private fun showEstimated(data: ShareProductData) {
        mBinding.tvEstimatedProfit.text =
            if (mViewModel.takeProfitCb) {
                buildString {
                    append(
                        (VAUSdkUtil.getProfitLoss(
                            data,
                            mBinding.etAtPrice.text.toString(),
                            mViewModel.orderData?.volume?:"0",
                            mViewModel.orderData?.cmd.ifNull(),
                            mBinding.etTakeProfit.text.toString()
                        ).toString().numCurrencyFormat() + " " + mViewModel.currencyType).arabicReverseTextByFlag(" ")
                    )
                }.arabicReverseTextByFlag(": ")
            } else {
                "-- ${mViewModel.currencyType}"
            }

        mBinding.tvEstimatedLoss.text =
            if (mViewModel.stopLossCb) {
                buildString {
                    append(
                        (VAUSdkUtil.getProfitLoss(
                            data,
                            mBinding.etAtPrice.text.toString(),
                            mViewModel.orderData?.volume?:"0",
                            mViewModel.orderData?.cmd.ifNull(),
                            mBinding.etStopLoss.text.toString()
                        ).toString().numCurrencyFormat() + " " + mViewModel.currencyType).arabicReverseTextByFlag(" ")
                    )
                }.arabicReverseTextByFlag(": ")
            } else {
                "-- ${mViewModel.currencyType}"
            }
    }

    /**
     * 检查网络状态和闭市状态
     */
    private fun checkNetWorkStatus(data: ShareProductData) {
        if (mViewModel.isConnected.not()) {
            mBinding.tvNetworkStatus.text = getString(R.string.network_disconnected_please_again_later)
            mBinding.tvNetworkStatus.isVisible = true
            mBinding.tvGoProduct.setPadding(0,16.dp2px(),0,8.dp2px())
        } else if (data.marketClose) {
            mBinding.tvNetworkStatus.text = getString(R.string.market_is_closed)
            mBinding.tvNetworkStatus.isVisible = true
            mBinding.tvGoProduct.setPadding(0,16.dp2px(),0,8.dp2px())
        } else {
            mBinding.tvNetworkStatus.isVisible = false
            mBinding.tvGoProduct.setPadding(0,8.dp2px(),0,8.dp2px())
        }
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(this)
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnDismissListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    override fun onMsgEvent(eventTag: String) {
        super.onMsgEvent(eventTag)
        when (eventTag) {
            // 断开连接
            NoticeConstants.WS.SOCKET_DISCONNECTED -> {
                mViewModel.isConnected = false
            }
            // 已连接
            NoticeConstants.Init.WS_SUCCESS_CONNECT -> {
                mViewModel.isConnected = true
            }
        }
    }
    /**
     * 更新订单
     */
    private fun tradeOrdersUpdate() {
        mViewModel.pendingPriceParam = mBinding.etAtPrice.text.toString()
        mViewModel.takeProfitParam = mBinding.etTakeProfit.text.toString()
        mViewModel.stopLossParam = mBinding.etStopLoss.text.toString()
        mViewModel.stopLimitPrice = mBinding.etStopLimitPrice.text.toString()
        val pendingPriceNum = mViewModel.pendingPriceParam.toDoubleCatching()
        if (pendingPriceNum <= 0.0) {
            // 设置的开仓价格无效
            ToastUtil.showToast(getString(R.string.the_set_open_is_invalid))
            return
        }
        if (mBinding.tvAtPriceTip.isVisible) {
            ToastUtil.showToast(getString(R.string.the_set_open_is_invalid))
            return
        }
        if (mBinding.tvTakeProfitTip.isVisible) {
            ToastUtil.showToast(getString(R.string.the_set_take_is_invalid))
            return
        }

        if (mBinding.tvStopLossTip.isVisible) {
            ToastUtil.showToast(getString(R.string.the_set_stop_is_invalid))
            return
        }

        if (mBinding.tvStopLimitTip.isVisible) {
            ToastUtil.showToast(getString(R.string.the_set_stop_limit_is_invalid))
            return
        }

        if (UserDataUtil.isStLogin()) {
           mViewModel.tradeOrderUpdate()
        } else {
            mViewModel.tradeOrdersUpdate()
        }
    }

    private fun clearFocus() {
        mBinding.etAtPrice.clearFocus()
        mBinding.etStopLimitPrice.clearFocus()
        mBinding.etTakeProfit.clearFocus()
        mBinding.etStopLoss.clearFocus()
    }

    /**
     * 改单成功弹窗
     */
    private fun showDealSuccessDialog() {
        val dialogTitle = getString(R.string.order_modified_successfully)
        val typeStr = when (mViewModel.orderData?.cmd) {
            "2" -> "Buy Limit"
            "3" -> "Sell Limit"
            "4" -> "Buy Stop"
            "6" -> "Buy Stop Limit"
            "7" -> "Sell Stop Limit"
            else -> "Sell Stop"
        }
        val dialogContent =
            "${mViewModel.orderData?.symbol} ${mViewModel.orderData?.volume} ${getString(R.string.lot)}\n$typeStr\n${
                getString(R.string.order_number)
            } #${mViewModel.orderData?.order}"

        BottomActionWithIconDialog.Builder(this)
            .setTitle(dialogTitle)
            .setContent(dialogContent)
            .setOnSingleButtonListener {
                finish()
            }
            .build()
            .showDialog()
    }

    override fun onDestroy() {
        super.onDestroy()
        SDKIntervalUtil.instance.removeCallBack(this)
        mBinding.etAtPrice.removeTextChangedListener(atPriceWatcher)
        mBinding.etStopLimitPrice.removeTextChangedListener(atPriceWatcher)
        mBinding.etTakeProfit.removeTextChangedListener(takeProfitWatcher)
        mBinding.etStopLoss.removeTextChangedListener(takeProfitWatcher)
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
    }
}