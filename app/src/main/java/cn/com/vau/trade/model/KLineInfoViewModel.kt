package cn.com.vau.trade.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.trade.bean.kchart.ProductInfoBean
import cn.com.vau.util.ToastUtil
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * author：lvy
 * date：2024/10/19
 * desc：产品属性详情
 */
class KLineInfoViewModel : BaseViewModel() {

    val productInfoLiveData = MutableLiveData<ProductInfoBean?>()

    /**
     * 获取产品属性详情
     */
    fun tradeProductDetailApi(symbol: String) {
        requestNet({
            if (UserDataUtil.isStLogin()) {
                stTradingService.tradeProductDetailApi(symbol, UserDataUtil.stAccountId())
            } else {
                val jsonObject = JsonObject()
                jsonObject.addProperty("login", UserDataUtil.accountCd()) // MT4/MT5账户号
                jsonObject.addProperty("serverId", UserDataUtil.serverId()) // 服务器ID
                jsonObject.addProperty("symbol", symbol)  //产品名
                val dataObject = JsonObject()
                dataObject.addProperty("data", jsonObject.toString())
                val requestBody =
                    dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
                tradingService.tradeProductAttrV2Api(requestBody)
            }
        }, {
            if (it.isSuccess()) {
                productInfoLiveData.value = it.getResponseData()
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        })
    }
}