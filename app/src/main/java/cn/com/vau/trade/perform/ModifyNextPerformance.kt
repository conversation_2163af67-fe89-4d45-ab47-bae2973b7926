package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import android.view.View
import androidx.core.content.ContentProviderCompat.requireContext
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.utils.VAUStartUtil
import cn.com.vau.common.view.dialog.GenericDialog
import cn.com.vau.databinding.DialogBottomDialogModifyOrderBinding
import cn.com.vau.trade.dialog.BottomModifyOrderDialog
import cn.com.vau.trade.viewmodel.BottomModifyOrderViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.ifNull
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.widget.dialog.BottomActionWithIconDialog
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.google.gson.JsonObject
import org.greenrobot.eventbus.EventBus

/**
 * 处理提交逻辑
 */
class ModifyNextPerformance(
    val activity: FragmentActivity,
    val dialog: BottomModifyOrderDialog,
    val mBinding: DialogBottomDialogModifyOrderBinding,
    val mViewModel: BottomModifyOrderViewModel,
    var atPriceSetEnable: Boolean = true,
    var limitPriceSetEnable: Boolean = true,
    var tpSetEnable: Boolean = true,
    var slSetEnable: Boolean = true,
    private val onNextClick: ((params: JsonObject) -> Unit)? = null,
) : AbsPerformance() {

    private val color_cebffffff_c1e1e1e by lazy { AttrResourceUtil.getColor(activity, R.attr.color_cebffffff_c1e1e1e) }
    private val color_c731e1e1e_c61ffffff by lazy { AttrResourceUtil.getColor(activity, R.attr.color_c731e1e1e_c61ffffff) }
    private val draw_shape_c1e1e1e_cebffffff_r100 by lazy { ContextCompat.getDrawable(activity, R.drawable.draw_shape_c1e1e1e_cebffffff_r100) }
    private val draw_shape_c0a1e1e1e_c0affffff_r100 by lazy { ContextCompat.getDrawable(activity, R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100) }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initView()
        createObserver()
    }

    @SuppressLint("SetTextI18n")
    fun initView() {
        mBinding.tvNext.clickNoRepeat {
            tradeOrdersUpdate()
            mViewModel.hideSoftInput()
        }
    }

    fun createObserver() {
        mViewModel.updatePendingOrderSuccessLiveData.observe(dialog) {
            dialog.dismiss()
            showDealSuccessDialog()
        }

        mViewModel.hintDataDialogLiveData.observe(dialog) {
            CenterActionDialog.Builder(activity)
                .setContent(it)
                .setSingleButton()
                .build()
                .showDialog()
        }

        mViewModel.tokenErrorLiveData.observe(dialog) {
            showTokenErrorDialog(it)
        }

    }

    /**
     * 改单成功弹窗
     */
    private fun showDealSuccessDialog() {
        val dialogTitle = activity.getString(R.string.order_modified_successfully)
        val typeStr = when (mViewModel.orderBean?.cmd) {
            "2" -> "Buy Limit"
            "3" -> "Sell Limit"
            "4" -> "Buy Stop"
            "6" -> "Buy Stop Limit"
            "7" -> "Sell Stop Limit"
            else -> "Sell Stop"
        }
        val dialogContent =
            "${mViewModel.orderBean?.symbol} ${mViewModel.orderBean?.volume} ${activity.getString(R.string.lot)}\n$typeStr\n${
                activity.getString(R.string.order_number)
            } #${mViewModel.orderBean?.order}"

        CenterActionDialog.Builder(activity)
            .setTitle(dialogTitle)
            .setContent(dialogContent)
            .setSingleButton()
            .setOnDismissListener {
                dialog.dismiss()
            }
            .build()
            .showDialog()
    }

    private fun showTokenErrorDialog(msg: String?) {
        CenterActionDialog.Builder(activity)
            .setContent(msg.ifNull())
            .setSingleButton(true)
            .setOnDismissListener {
                // 退出登录
                EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
            }.build()
            .showDialog()
    }

    fun updateNextStyle() {

        mBinding.tvNext.apply {
            val isEnable = atPriceSetEnable && limitPriceSetEnable && tpSetEnable && slSetEnable
            setTextColorDiff(if (isEnable) color_cebffffff_c1e1e1e else color_c731e1e1e_c61ffffff)
            background = if (isEnable) draw_shape_c1e1e1e_cebffffff_r100 else draw_shape_c0a1e1e1e_c0affffff_r100
        }
    }

    /**
     * 更新订单
     */
    private fun tradeOrdersUpdate() {
        val atPrice = mBinding.viewAtPrice.getPriceText()
        val limitPrice = mBinding.viewLimitPrice.getPriceText()
        val takeProfitPrice = mBinding.viewTakeProfit.getPriceEditText()
        val stopLossPrice = mBinding.viewStopLoss.getPriceEditText()
        val stopPriceNum = atPrice.toDoubleCatching()
        if (stopPriceNum <= 0.0) {
            // 设置的开仓价格无效
            ToastUtil.showToast(activity.getString(R.string.the_set_open_is_invalid))
            return
        }
        if (atPriceSetEnable.not()) {
            ToastUtil.showToast(activity.getString(R.string.the_set_open_is_invalid))
            return
        }

        if (limitPriceSetEnable.not()) {
            ToastUtil.showToast(activity.getString(R.string.the_set_stop_limit_is_invalid))
            return
        }
        if (tpSetEnable.not()) {
            ToastUtil.showToast(activity.getString(R.string.the_set_take_is_invalid))
            return
        }

        if (slSetEnable.not()) {
            ToastUtil.showToast(activity.getString(R.string.the_set_stop_is_invalid))
            return
        }

        if (dialog.useInnerRequest) {
            if (UserDataUtil.isStLogin()) {
                val jsonObject = mViewModel.tradeOrderUpdate(atPrice, takeProfitPrice, stopLossPrice)
                mViewModel.stTradeOrdersUpdateRequest(jsonObject)
            } else {
                val jsonObject = mViewModel.tradeOrdersUpdate(atPrice, limitPrice, takeProfitPrice, stopLossPrice)
                mViewModel.tradeOrdersUpdateRequest(atPrice, limitPrice, takeProfitPrice, stopLossPrice, jsonObject)
            }
        } else {
            if (UserDataUtil.isStLogin()) {
                val jsonObject = mViewModel.tradeOrderUpdate(atPrice, takeProfitPrice, stopLossPrice)
                onNextClick?.invoke(jsonObject)
            } else {
                val jsonObject = mViewModel.tradeOrdersUpdate(atPrice, limitPrice, takeProfitPrice, stopLossPrice)
                onNextClick?.invoke(jsonObject)
            }
        }

    }

}