package cn.com.vau.trade.data

class AccountInfoDiffUtil {
    /**
     * 字段变化检测
     */
    fun calculateDiff(old: AccountInfoCardBean?, new: AccountInfoCardBean): MutableSet<AccountInfoType> {
        if (old == null) return AccountInfoType.allTypes
        return mutableSetOf<AccountInfoType>().apply {
            if (old.equity != new.equity) add(AccountInfoType.Equity)
            if (old.profit != new.profit) add(AccountInfoType.FloatingPnL)
            if (old.marginLevel != new.marginLevel ||
                old.marginCall != new.marginCall ||
                old.marginStopOut != new.marginStopOut
            ) {
                add(AccountInfoType.MarginLevel)
            }
            if (old.credit != new.credit) add(AccountInfoType.Credit)
            if (old.margin != new.margin || old.freeMargin != new.freeMargin) {
                add(AccountInfoType.MarginAndFreeMargin)
            }
            if (old.balance != new.balance) add(AccountInfoType.Balance)
        }
    }

}
