package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.R
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.databinding.DialogBottomDialogModifyOrderBinding
import cn.com.vau.trade.dialog.BottomModifyOrderDialog
import cn.com.vau.trade.ext.EditState
import cn.com.vau.trade.ext.TpSlUtil
import cn.com.vau.trade.ext.computeLimitPriceRange
import cn.com.vau.trade.ext.isStopLimitOrder
import cn.com.vau.trade.ext.stopLossLevel
import cn.com.vau.trade.viewmodel.BottomModifyOrderViewModel
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathSub
import cn.com.vau.util.numFormat

/**
 * Created by array on 2025/5/27 17:23
 * Desc:限价价格
 */
class ModifyLimitPricePerformance(
    val activity: FragmentActivity,
    val dialog: BottomModifyOrderDialog,
    val mBinding: DialogBottomDialogModifyOrderBinding,
    val mViewModel: BottomModifyOrderViewModel,
    val callback: ((enable: Boolean) -> Unit)? = null
) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initData()
        initListener()
    }

    @SuppressLint("SetTextI18n")
    fun initData() {
        val orderBean = mViewModel.orderBean ?: return
        val productData = mViewModel.productBean ?: return
        mBinding.viewLimitPrice.setData(digits = productData.digits)
        mBinding.viewLimitPrice.setPrice(orderBean.stopLimitPrice)
        mBinding.viewLimitPrice.setPriceTitleText(activity.getString(R.string.limit_price))
        mBinding.viewLimitPrice.setPriceHint(activity.getString(R.string.limit_price))
        mBinding.viewLimitPrice.isVisible = orderBean.isStopLimitOrder()
    }

    fun initListener() {
        /*停损输入框监听*/
        mBinding.viewLimitPrice.setOnPriceTextChangedListener { s ->
            checkLimitPriceRange()
            mViewModel.notifyLimitPriceTextChange(s)
        }

        /*点击加号，停损价格计算*/
        mBinding.viewLimitPrice.setOnAddClickListener { s ->
            mBinding.viewLimitPrice.setPrice(addLimitPrice())
            mViewModel.hideSoftInput()
        }

        /*点击减号，停损价格计算*/
        mBinding.viewLimitPrice.setOnSubClickListener { s ->
            mBinding.viewLimitPrice.setPrice(subLimitPrice())
            mViewModel.hideSoftInput()
        }

    }

    override fun onCallback() {
        val orderBean = mViewModel.orderBean
        if (orderBean?.isStopLimitOrder() == true) {
            checkLimitPriceRange()
        }
    }

    /**
     * 计算市价的停损范围
     */
    private fun getLimitPriceRange(): String {
        val orderBean = mViewModel.orderBean ?: return ""
        val productBean = mViewModel.productBean ?: return ""
        val atPrice = mBinding.viewAtPrice.getPriceText()
        val limitPriceRange = productBean.computeLimitPriceRange(atPrice, orderBean.cmd, productBean.stopLossLevel())
        return limitPriceRange
    }

    /**
     * 检查停损价格范围
     */
    private fun checkLimitPriceRange() {
        val orderBean = mViewModel.orderBean ?: return
        val limitPrice = mBinding.viewLimitPrice.getPriceText()
        val limitPriceRange = getLimitPriceRange()

        TpSlUtil.checkLimitPriceRange(activity, limitPrice, limitPriceRange, orderBean.cmd,
            emptyBlock = { tipStr ->
                mBinding.viewLimitPrice.showTips(true, tipStr)
                mBinding.viewLimitPrice.setTipsTextColor(ContextCompat.getColor(activity, R.color.cf44040))
                mBinding.viewLimitPrice.setPriceEditState(EditState.ERROR)
                callback?.invoke(false)
            },
            validBlock = {
                mBinding.viewLimitPrice.showTips(false)
                mBinding.viewLimitPrice.checkPriceEditFocus()
                callback?.invoke(true)
            },
            invalidBlock = { tipStr ->
                mBinding.viewLimitPrice.showTips(true, tipStr)
                mBinding.viewLimitPrice.setTipsTextColor(ContextCompat.getColor(activity, R.color.cf44040))
                mBinding.viewLimitPrice.setPriceEditState(EditState.ERROR)
                callback?.invoke(false)
            })
    }

    /**
     * 点击加号，停损价格计算
     */
    private fun addLimitPrice(): String {
        val orderBean = mViewModel.orderBean ?: return ""
        val currentCount = mBinding.viewLimitPrice.getPriceText()
        if (currentCount.isEmpty()) {
            return orderBean.stopLimitPrice.ifNull()
        }
        val editCount = currentCount.mathAdd(mViewModel.minProfit)
        return editCount.numFormat(mViewModel.digits, false)
    }

    /**
     * 点击减号，停损价格计算
     */
    private fun subLimitPrice(): String {
        val orderBean = mViewModel.orderBean ?: return ""
        val currentCount = mBinding.viewLimitPrice.getPriceText()
        if (currentCount.isEmpty()) {
            return orderBean.stopLimitPrice.ifNull()
        }
        if (currentCount.mathCompTo("0") != 1) return ""//小于等于0的话，返回“”，点击减号输入框不做处理
        val editCount = currentCount.mathSub(mViewModel.minProfit)
        return editCount.numFormat(mViewModel.digits, false)
    }

}