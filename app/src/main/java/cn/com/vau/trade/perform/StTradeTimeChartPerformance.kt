package cn.com.vau.trade.perform

import androidx.fragment.app.Fragment
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.databinding.FragmentStManualTradingBinding
import cn.com.vau.trade.viewmodel.OrderViewModel

/**
 * Created by array on 2025/5/12 14:31
 * Desc:
 */
class StTradeTimeChartPerformance(
    val fragment: Fragment,
    private val mBinding: FragmentStManualTradingBinding,
    private val orderViewMode: OrderViewModel?,
) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initListener()
    }

    fun initListener() {
        /**
         * LiveData: 切换产品
         */
        orderViewMode?.productDataChangeLieData?.observe(fragment) {
            mBinding.mTimeChartView.setData(it,true)
        }
    }

    override fun onCallback() {
        super.onCallback()
        mBinding.mTimeChartView.updateQuotation()
    }
}