package cn.com.vau.trade.fragment.deal

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewStub
import android.view.animation.AccelerateInterpolator
import androidx.core.view.isVisible
import androidx.recyclerview.widget.OrientationHelper
import androidx.recyclerview.widget.SimpleItemAnimator
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.FootRecyclerDealOptionalBinding
import cn.com.vau.databinding.FragmentDealItemOptionalBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.activity.ManageSymbolsActivity
import cn.com.vau.trade.adapter.DealItemRecyclerAdapter
import cn.com.vau.trade.model.DealItemOptionalModel
import cn.com.vau.trade.presenter.DealItemOptionalPresenter
import cn.com.vau.trade.presenter.DealOptionalContract
import cn.com.vau.util.AdapterRefreshNotifyItemController
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.TradeSortUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject

/**
 * 交易模块
 */
class DealItemOptionalFragment :
    BaseFrameFragment<DealItemOptionalPresenter, DealItemOptionalModel>(),
    DealOptionalContract.View, SDKIntervalCallback {

    private val mBinding: FragmentDealItemOptionalBinding by lazy { FragmentDealItemOptionalBinding.inflate(layoutInflater) }
    private var mAdapter: DealItemRecyclerAdapter? = null
    private val mFootView: FootRecyclerDealOptionalBinding by lazy { FootRecyclerDealOptionalBinding.inflate(layoutInflater) }

    private var layoutManager: WrapContentLinearLayoutManager? = null
    private var refreshController: AdapterRefreshNotifyItemController? = null

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    override fun onCallback() {
        refreshAdapter(false)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mPresenter.isViewCreated = true
        lazyInitData()
        return mBinding.root
    }

    override fun initParam() {
        super.initParam()
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
    }

    @SuppressLint("WrongConstant", "SetTextI18n")
    override fun initView() {
        super.initView()
        //初始化开关

        val switchMode = SpManager.getTradeSwitchMode()
        layoutManager = WrapContentLinearLayoutManager(context)
        layoutManager?.orientation = OrientationHelper.VERTICAL
        mBinding.mRecyclerView.layoutManager = layoutManager
        mAdapter = DealItemRecyclerAdapter(requireContext(), mPresenter.dataList, switchMode)
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.addFooterView(mFootView.root)

        addPerformance()

        controlShowNoDataScroll(VAUSdkUtil.collectSymbolList.isEmpty())
        recyclerViewOpt()
        startAlphaAnimator()
    }

    private fun startAlphaAnimator() {
        val ofFloat = ObjectAnimator.ofFloat(mBinding.mRecyclerView, "alpha", 0f, 1f)
        ofFloat.duration = 350
        ofFloat.interpolator = AccelerateInterpolator()
        ofFloat.start()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    //recyclerView优化
    private fun recyclerViewOpt() {
        mBinding.mRecyclerView.recycledViewPool.setMaxRecycledViews(0, 20)
        mBinding.mRecyclerView.setHasFixedSize(true)
//        mBinding.mRecyclerView.setRecycledViewPool(TradesFragment.sRecycledViewPool)
        layoutManager?.recycleChildrenOnDetach = true
        (mBinding.mRecyclerView.itemAnimator as SimpleItemAnimator).supportsChangeAnimations = false
        refreshController = AdapterRefreshNotifyItemController(mBinding.mRecyclerView, mAdapter)
    }

    private fun lazyInitData() {
        if (!mPresenter.isViewCreated || !mPresenter.isUIVisible) return
        // 初始化自选产品
        mPresenter.getOptionalProdListApi()
        mPresenter.isViewCreated = false
        mPresenter.isUIVisible = false
    }

    override fun initListener() {
        super.initListener()

        mFootView.tvAddSymbolFoot.setOnClickListener {
            if (!UserDataUtil.isLogin()) {
                openActivity(LoginActivity::class.java)
                return@setOnClickListener
            }
            if (VAUSdkUtil.shareGoodList().isEmpty()) return@setOnClickListener

            tradePermissionPerformance.run {
                if (handleTradeBlockType { openActivity(ManageSymbolsActivity::class.java) }) return@setOnClickListener
            }

            openActivity(ManageSymbolsActivity::class.java)

            // 神策自定义埋点(v3500)
            sensorsTrack("", 0, "Add Symbol")
        }

        mAdapter?.setOnItemClickListener(object : DealItemRecyclerAdapter.OnItemClickListener {
            override fun onItemClick(position: Int) {
                val symbolName =
                    mPresenter.dataList.elementAtOrElse(position) { ShareProductData() }.symbol
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(Constants.PARAM_PRODUCT_NAME, symbolName)
                }, true)

                // 神策自定义埋点(v3500)
                sensorsTrack(symbolName, position, "")
            }
        })
    }

    override fun initAdapterData(state: Boolean) {
        mPresenter.dataList.clear()
        mPresenter.dataList.addAll(VAUSdkUtil.collectSymbolList)
        TradeSortUtil.sort(mPresenter.dataList, null)
        refreshAdapter(state)
        controlShowNoDataScroll(VAUSdkUtil.collectSymbolList.isEmpty())
    }

    private fun controlShowNoDataScroll(isControlShowNoDataScroll: Boolean) {
        // 互斥显示
        showNoDataScroll(isControlShowNoDataScroll)
        mFootView.tvAddSymbolFoot.isVisible = !isControlShowNoDataScroll
    }

    private fun showNoDataScroll(isShowVisible: Boolean) {
        if (mBinding.mVsNoDataScroll.parent != null) {
            mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
                override fun onInflate(stub: ViewStub?, inflated: View) {
                    val mNoDataScrollBinding = VsLayoutNoDataScrollBinding.bind(inflated)
                    mNoDataScrollBinding.mNoDataScrollView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
                    mNoDataScrollBinding.mNoDataScrollView.setHintMessage(getString(R.string.no_symbols))
                    mNoDataScrollBinding.mNoDataScrollView.setBottomBtnText(getString(R.string.add_symbol))
                    mNoDataScrollBinding.mNoDataScrollView.setBottomBtnViewClickListener {
                        if (!UserDataUtil.isLogin()) {
                            openActivity(LoginActivity::class.java)
                            return@setBottomBtnViewClickListener
                        }
                        if (VAUSdkUtil.shareGoodList().isEmpty()) return@setBottomBtnViewClickListener

                        tradePermissionPerformance.run {
                            if (handleTradeBlockType { openActivity(ManageSymbolsActivity::class.java) }) return@setBottomBtnViewClickListener
                        }
                        openActivity(ManageSymbolsActivity::class.java)
                    }
                }
            })
        }
        mBinding.mVsNoDataScroll.isVisible = isShowVisible
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun refreshAdapter(state: Boolean) {
        if (state) {
            mAdapter?.notifyDataSetChanged()
        } else {
            refreshController?.refresh(recordRefreshPositionList())
        }
    }

    private fun recordRefreshPositionList(): MutableList<Int> {
        val refreshPositionList = mutableListOf<Int>()
        for ((index, dataBean) in VAUSdkUtil.collectSymbolList.withIndex()) {
            if (dataBean.refresh) {
                //收集刷新的位置
                refreshPositionList.add(index)
                dataBean.refresh = false
            }
        }
        return refreshPositionList
    }

    override fun onVisibleToUserChanged(isVisibleToUser: Boolean, invokeInResumeOrPause: Boolean) {
        super.onVisibleToUserChanged(isVisibleToUser, invokeInResumeOrPause)
        if (isVisibleToUser) {
            mPresenter.isUIVisible = true
            lazyInitData()
            initAdapterData(true)
            SDKIntervalUtil.instance.removeCallBack(this)
            SDKIntervalUtil.instance.addCallBack(this)
            controlShowNoDataScroll(VAUSdkUtil.collectSymbolList.isEmpty())
        } else {
            SDKIntervalUtil.instance.removeCallBack(this)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    @Subscribe(threadMode = ThreadMode.MAIN) //在ui线程执行
    fun onMsgEvent(tag: String) {
        when (tag) {
            NoticeConstants.Init.APPLICATION_START -> {
                refreshAdapter(true)
            }

            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                mPresenter.requestNum = 1
                mPresenter.getOptionalProdListApi()
            }

            NoticeConstants.WS.LOGIN_ERROR_CHANGE_OF_GROUP -> {
                mPresenter.requestNum = 1
                mPresenter.getOptionalProdListApi()
            }

            // 切换排序
            NoticeConstants.Quotes.TRADE_SORT_CHANGE_UP,
            NoticeConstants.Quotes.TRADE_SORT_CHANGE_DOWN -> {
                if (mAdapter != null && mPresenter.dataList.isNotEmpty()) {
                    TradeSortUtil.sort(mPresenter.dataList, tag) {
                        mAdapter?.notifyDataSetChanged()
                    }
                }
            }

            NoticeConstants.Quotes.TRADE_SORT_CHANGE_NONE -> {
                if (mAdapter != null && mPresenter.dataList.isNotEmpty()) {
                    mPresenter.dataList.clear()
                    mPresenter.dataList.addAll(VAUSdkUtil.collectSymbolList)
                    mAdapter?.notifyDataSetChanged()
                }
            }

            // 切换Buy/Sell模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_BUYSELL,
                // 切换Classic模式
            NoticeConstants.Quotes.TRADE_SWITCH_MODE_CLASSIC -> {
                mAdapter?.setMode(SpManager.getTradeSwitchMode())
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 神策自定义埋点(v3500)
     * App_交易页产品点击 -> 点击app交易页产品时触发
     */
    private fun sensorsTrack(symbolName: String, position: Int, buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.SYMBOL_ID, symbolName) // symbolId
        properties.put(SensorsConstant.Key.SYMBOL_NAME, symbolName) // symbol名称
        properties.put(SensorsConstant.Key.SYMBOL_RANK, position + 1) // symbol排序
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.APP_TRADES_PRODUCT_CLICK, properties)
    }
}
