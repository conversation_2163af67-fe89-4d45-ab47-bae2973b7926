package cn.com.vau.trade.st.fragment.strategy

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.databinding.FragmentOpenTradesOrderBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.adapter.StStrategyPendingCloseRecyclerAdapter
import cn.com.vau.trade.st.model.StStrategyOrdersViewModel

/**
 * 策略订单详情页 -- 待平仓 pending close
 */
class StStrategyOrdersPendingCloseFragment : BaseMvvmBindingFragment<FragmentOpenTradesOrderBinding>() {

    private var mAdapter: StStrategyPendingCloseRecyclerAdapter? = null

    var shareOrderList: ArrayList<ShareOrderData>? = null

    private val mViewModel by activityViewModels<StStrategyOrdersViewModel>()

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        shareOrderList = mViewModel.shareStrategyData?.pendingClose
    }

    @SuppressLint("WrongConstant")
    override fun initView() {
        mBinding.mVsNoDataScroll.setOnInflateListener { stub, inflated ->
            val vs = VsLayoutNoDataScrollBinding.bind(inflated)
            vs.mNoDataScrollView.setHintMessage(getString(R.string.no_positions))
        }

        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)

        mAdapter = StStrategyPendingCloseRecyclerAdapter(
            requireContext(), shareOrderList ?: ArrayList()
        )
        mBinding.mRecyclerView.adapter = mAdapter
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoDataScroll)

    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initData() {
        super.initData()
        shareOrderList?.forEachIndexed { _, orderData ->
            VAUSdkUtil.symbolList().firstOrNull {
                it.symbol == orderData.symbol
            }?.let {
                orderData.closePrice = "${if (OrderUtil.isBuyOfOrder(orderData.cmd)) it.bid else it.ask}"
                orderData.profit = VAUSdkUtil.getProfitLoss(
                    it,
                    "${orderData.openPrice}",
                    "${orderData.volume}",
                    "${orderData.cmd}",
                    "${orderData.closePrice}"
                ).toDouble()
            }
        }
        mAdapter?.notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun initListener() {
        super.initListener()
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            mAdapter?.notifyDataSetChanged()
            mBinding.mSmartRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
        }

        mAdapter?.setOnItemClickListener(object : StStrategyPendingCloseRecyclerAdapter.OnItemClickListener {
            override fun onStartKLine(position: Int) {
                openActivity(KLineActivity::class.java, Bundle().apply {
                    putString(
                        Constants.PARAM_PRODUCT_NAME,
                        shareOrderList?.getOrNull(position)?.symbol ?: ""
                    )
                })
            }
        })
    }
}