package cn.com.vau.trade.view

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.databinding.ViewTradeAccountInfoBinding
import cn.com.vau.trade.data.AccountInfoItem
import cn.com.vau.trade.data.AccountInfoType
import cn.com.vau.trade.data.MarginRiskLevel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.setFontG500
import cn.com.vau.util.setFontG600
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff

/**
 * Created by array on 2025/5/9 15:20
 * Desc: 交易标题
 */
@SuppressLint("ViewConstructor")
class TradeAccountInfoView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding by lazy { ViewTradeAccountInfoBinding.inflate(LayoutInflater.from(context), this) }
    private val accountInfoItems = mutableListOf<AccountInfoItem>()
    private var onSelectListener: ((AccountInfoType) -> Unit)? = null
    private var onTipClickListener: ((AccountInfoType) -> Unit)? = null
    private var mSelectedType: AccountInfoType = AccountInfoType.Equity
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff) }
    private val color_c731e1e1e_c61ffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c731e1e1e_c61ffffff) }

    init {
        initView()
        initListener()
    }

    @SuppressLint("SetTextI18n")
    private fun initView() {
        mBinding.tvMarginAndFreeMarginTitle.text = "${context.getString(R.string.margin)}/${context.getString(R.string.free_margin)}"
        accountInfoItems.apply {
            add(AccountInfoItem(mBinding.clEquity, mBinding.tvEquityTitle, AccountInfoType.Equity))
            add(AccountInfoItem(mBinding.clFloatingPnL, mBinding.tvFloatingPnLTitle, AccountInfoType.FloatingPnL))
            add(AccountInfoItem(mBinding.clMarginLevel, mBinding.tvMarginLevelTitle, AccountInfoType.MarginLevel))
            add(AccountInfoItem(mBinding.clCredit, mBinding.tvCreditTitle, AccountInfoType.Credit))
            add(AccountInfoItem(mBinding.clMarginAndFreeMargin, mBinding.tvMarginAndFreeMarginTitle, AccountInfoType.MarginAndFreeMargin))
            add(AccountInfoItem(mBinding.clBalance, mBinding.tvBalanceTitle, AccountInfoType.Balance))
        }
        selectItem(mSelectedType)
    }

    fun initListener() {
        mBinding.apply {
            /** 点击选中: 净值 */
            clEquity.clickNoRepeat {
                onSelectListener?.invoke(AccountInfoType.Equity)
                selectItem(AccountInfoType.Equity)
            }
            /** 点击选中: 浮动盈亏 */
            clFloatingPnL.clickNoRepeat {
                onSelectListener?.invoke(AccountInfoType.FloatingPnL)
                selectItem(AccountInfoType.FloatingPnL)
            }
            /** 点击选中: 保证金水平 */
            clMarginLevel.clickNoRepeat {
                onSelectListener?.invoke(AccountInfoType.MarginLevel)
                selectItem(AccountInfoType.MarginLevel)
            }
            /** 点击选中: 信用额度 */
            clCredit.clickNoRepeat {
                onSelectListener?.invoke(AccountInfoType.Credit)
                selectItem(AccountInfoType.Credit)
            }
            /** 点击选中: 保证金和可用保证金 */
            clMarginAndFreeMargin.clickNoRepeat {
                onSelectListener?.invoke(AccountInfoType.MarginAndFreeMargin)
                selectItem(AccountInfoType.MarginAndFreeMargin)
            }
            /** 点击选中: 余额 */
            clBalance.clickNoRepeat {
                onSelectListener?.invoke(AccountInfoType.Balance)
                selectItem(AccountInfoType.Balance)
            }
        }

        mBinding.apply {
            /** 点击提示: 净值 */
            tvEquityTitle.clickNoRepeat {
                onTipClickListener?.invoke(AccountInfoType.Equity)
            }
            /** 点击提示: 浮动盈亏 */
            tvFloatingPnLTitle.clickNoRepeat {
                onTipClickListener?.invoke(AccountInfoType.FloatingPnL)
            }
            /** 点击提示: 保证金水平 */
            tvMarginLevelTitle.clickNoRepeat {
                onTipClickListener?.invoke(AccountInfoType.MarginLevel)
            }
            /** 点击提示: 信用额度 */
            tvCreditTitle.clickNoRepeat {
                onTipClickListener?.invoke(AccountInfoType.Credit)
            }
            /** 点击提示: 保证金和可用保证金 */
            tvMarginAndFreeMarginTitle.clickNoRepeat {
                onTipClickListener?.invoke(AccountInfoType.MarginAndFreeMargin)
            }
            /** 点击提示: 余额 */
            tvBalanceTitle.clickNoRepeat {
                onTipClickListener?.invoke(AccountInfoType.Balance)
            }

        }

    }

    fun getResetEntry(): ImageView {
        return mBinding.ivReset
    }

    fun getSelectedType(): AccountInfoType {
        return mSelectedType
    }

    private fun selectItem(type: AccountInfoType) {
        this.mSelectedType = type
        accountInfoItems.forEach { item ->
            if (item.type == type) {
                item.clItemBg.setBackgroundResource(item.selectedBg)
                item.tvItemTitle.setFontG600()
                item.tvItemTitle.setTextColorDiff(color_c1e1e1e_cebffffff)
            } else {
                item.clItemBg.setBackgroundResource(item.defaultBg)
                item.tvItemTitle.setFontG500()
                item.tvItemTitle.setTextColorDiff(color_c731e1e1e_c61ffffff)
            }
        }
    }

    /**
     * 设置选中监听
     * @param onSelectListener (AccountInfoType) -> Unit
     */
    fun setOnSelectListener(onSelectListener: (AccountInfoType) -> Unit) {
        this.onSelectListener = onSelectListener
    }

    /**
     * 设置提示点击监听
     * @param onTipClickListener (AccountInfoType) -> Unit
     */
    fun setOnTipClickListener(onTipClickListener: (AccountInfoType) -> Unit) {
        this.onTipClickListener = onTipClickListener
    }

    fun setAccountInfoOriginalState() {
        setEquityText("...")
        setFloatingPnLText("...")
        setFloatingPnlColor(color_c1e1e1e_cebffffff)
        setMarginLevelText("...")
        setMarginLevelColor(color_c1e1e1e_cebffffff)
        setMarginRiskLevel(MarginRiskLevel.Low)
        setCreditText("...")
        setMarginAndFreeMarginText("...")
        setBalanceText("...")
    }

    fun setEquityTitle(title: String) {
        mBinding.tvEquityTitle.text = title
    }

    fun getEquityText(): String {
        return mBinding.tvEquity.text.toString()
    }

    fun setEquityText(text: String) {
        mBinding.tvEquity.setTextDiff(text)
    }

    fun setFloatingPnLTitle(title: String) {
        mBinding.tvFloatingPnLTitle.text = title
    }

    fun getFloatingPnLText(): String {
        return mBinding.tvFloatingPnL.text.toString()
    }

    fun setFloatingPnLText(text: String) {
        mBinding.tvFloatingPnL.setTextDiff(text)
    }

    fun setFloatingPnlColor(color: Int) {
        mBinding.tvFloatingPnL.setTextColorDiff(color)
    }

    fun setMarginLevelTitle(title: String) {
        mBinding.tvMarginLevelTitle.text = title
    }

    fun getMarginLevelText(): String {
        return mBinding.tvMarginLevel.text.toString()
    }

    fun setMarginLevelText(text: String) {
        mBinding.tvMarginLevel.setTextDiff(text)
    }

    fun setMarginLevelColor(color: Int) {
        mBinding.tvMarginLevel.setTextColorDiff(color)
    }

    fun setCreditTitle(title: String) {
        mBinding.tvCreditTitle.text = title
    }

    fun getCreditText(): String {
        return mBinding.tvCredit.text.toString()
    }

    fun setCreditText(text: String) {
        mBinding.tvCredit.setTextDiff(text)
    }

    fun setMarginAndFreeMarginTitle(title: String) {
        mBinding.tvMarginAndFreeMarginTitle.text = title
    }

    fun getMarginAndFreeMarginText(): String {
        return mBinding.tvMarginAndFreeMargin.text.toString()
    }

    fun setMarginAndFreeMarginText(text: String) {
        mBinding.tvMarginAndFreeMargin.setTextDiff(text)
    }

    fun setBalanceTitle(title: String) {
        mBinding.tvBalanceTitle.text = title
    }

    fun getBalanceText(): String {
        return mBinding.tvBalance.text.toString()
    }

    fun setBalanceText(text: String) {
        mBinding.tvBalance.setTextDiff(text)
    }

    fun setBalanceColor(color: Int) {
        mBinding.tvBalance.setTextColorDiff(color)
    }

    fun setMarginRiskLevel(risk: MarginRiskLevel) {
        when (risk) {
            MarginRiskLevel.Low -> {
                mBinding.ivRisk.setImageResource(R.drawable.img_low_risk)
            }

            MarginRiskLevel.Medium -> {
                mBinding.ivRisk.setImageResource(R.drawable.img_medium_risk)
            }

            MarginRiskLevel.High -> {
                mBinding.ivRisk.setImageResource(R.drawable.img_high_risk)
            }
        }
    }

}




