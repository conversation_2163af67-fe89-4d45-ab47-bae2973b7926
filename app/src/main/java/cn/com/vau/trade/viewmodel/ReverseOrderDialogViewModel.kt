package cn.com.vau.trade.viewmodel

import androidx.lifecycle.viewModelScope
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.livedata.UnPeekLiveData
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.trade.ReverseOpenPositionBean
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.json
import cn.com.vau.util.mathMul
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import com.google.gson.JsonObject
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject

/**
 * 反向开仓
 */
class ReverseOrderDialogViewModel : BaseViewModel() {

    // 主订单
    var mainOrderId: String? = null
    var mainOrderData: ShareOrderData? = null

    // 当点击 next 时不再接收通知
    var isReceiptNotification = true
    val tradePositionReverseOpenPositionLiveData = UnPeekLiveData<ReverseOpenPositionBean.Data?>()
    val dismissDialogLiveData = UnPeekLiveData<Long>()

    override fun onCleared() {
        super.onCleared()
    }

    fun tradePositionReverseOpenPosition() {
        if (UserDataUtil.isStLogin()) {
            stTradePositionReverseOpenPosition()
        } else {
            tradeOrdersReversePositionV2()
        }

        SensorsDataUtil.track(
            SensorsConstant.V3510.REVERSE_PAGE_CLOSE_AND_REVERSE_BTN_CLICK,
            JSONObject().apply { put(SensorsConstant.Key.SYMBOL_NAME, mainOrderData?.symbol.ifNull()) }
        )

    }

    private fun tradeOrdersReversePositionV2() {

        val isMt5 = UserDataUtil.isMT5()

        var handleCount = mainOrderData?.volume.mathMul(if (isMt5) "10000" else "100")
        if (handleCount.contains("."))
            handleCount = handleCount.split(".").getOrNull(0) ?: ""

        val map = hashMapOf<String, Any?>(
            "login" to UserDataUtil.accountCd(),
            "serverId" to UserDataUtil.serverId(),
            "token" to UserDataUtil.tradeToken(),
            "symbol" to mainOrderData?.symbol.ifNull(),
            "cmd" to mainOrderData?.cmd.ifNull(),
            "order" to mainOrderId,
            "price" to if ("0" == mainOrderData?.cmd) mainOrderData?.ask else mainOrderData?.bid,
            "volume" to handleCount,
        )
        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", map.json)
        val requestBody = jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet(
            { tradingService.tradeOrdersReversePositionV2Api(requestBody) },
            onSuccess = {
                // 已闭市，停留 2S 关闭页面
                if ("********" == it.getResponseCode()){
                    ToastUtil.showToast(it.getResponseMsg())
                    viewModelScope.launch {
                        delay(2000)
                        dismissDialogLiveData.value = System.currentTimeMillis()
                    }
                    return@requestNet
                }
                if (!it.isSuccess()) {
                    ToastUtil.showToast(it.getResponseMsg())
                    dismissDialogLiveData.value = System.currentTimeMillis()
                    return@requestNet
                }
                if (false == it.data?.closeOrderStatus){
                    ToastUtil.showToast(it.getResponseMsg().ifNull())
                    return@requestNet
                }

                tradePositionReverseOpenPositionLiveData.value = it.data
            },
            onError = {
                isReceiptNotification = true
            },
            true
        )
    }

    fun stTradePositionReverseOpenPosition() {

        val map = hashMapOf<String, Any?>(
            "orderId" to mainOrderData?.stOrder.ifNull(),
            "accountId" to UserDataUtil.stAccountId()
        )
        val requestBody = map.json.toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { stTradingService.stTradePositionReverseOpenPositionApi(requestBody) },
            onSuccess = {
                // 已闭市，停留 2S 关闭页面
                if ("10527" == it.getResponseCode()){
                    ToastUtil.showToast(it.getResponseMsg())
                    viewModelScope.launch {
                        delay(2000)
                        dismissDialogLiveData.value = System.currentTimeMillis()
                    }
                    return@requestNet
                }
                if (!it.isSuccess()) {
                    ToastUtil.showToast(it.getResponseMsg())
                    dismissDialogLiveData.value = System.currentTimeMillis()
                    return@requestNet
                }
                if (false == it.data?.closeOrderStatus){
                    ToastUtil.showToast(it.getResponseMsg().ifNull())
                    return@requestNet
                }

                tradePositionReverseOpenPositionLiveData.value = it.data
            },
            onError = {
                isReceiptNotification = true
            },
            isShowDialog = true
        )

    }

}