package cn.com.vau.trade.perform

import android.annotation.SuppressLint
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.utils.inApp.InAppDataUtil.data
import cn.com.vau.databinding.DialogTakeProfitStopLossBinding
import cn.com.vau.trade.dialog.BottomTakeProfitStopLossDialog
import cn.com.vau.trade.viewmodel.TakeProfitStopLossViewModel
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import cn.com.vau.util.setTextDiff

class TpSlOrderInfoPerformance(val activity: FragmentActivity, val dialog: BottomTakeProfitStopLossDialog, val mBinding: DialogTakeProfitStopLossBinding, val mViewModel: TakeProfitStopLossViewModel) : AbsPerformance() {

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initView()
    }

    override fun onCallback() {
        val orderData = VAUSdkUtil.shareOrderList().firstOrNull {
            it.order == mViewModel.orderBean?.order
        } ?: return

        mBinding.tvOpenPrice.setTextDiff(orderData.openPrice.ifNull())
        mBinding.tvCurrentPrice.setTextDiff(
            if ("-" == orderData.closePrice) "-" else orderData.currentPriceUI.ifNull()
        )
    }

    @SuppressLint("SetTextI18n")
    fun initView() {
        val orderBean = mViewModel.orderBean ?: return
        /*买卖类型*/
        if (OrderUtil.isBuyOfOrder(orderBean.cmd)) {
            mBinding.tvOrderType.text = "B"
            mBinding.tvOrderType.setTextColor(ContextCompat.getColor(activity, R.color.cebffffff))
            mBinding.tvOrderType.background = ContextCompat.getDrawable(activity, R.drawable.shape_c00c79c_r4)
        } else {
            mBinding.tvOrderType.text = "S"
            mBinding.tvOrderType.setTextColor(ContextCompat.getColor(activity, R.color.cebffffff))
            mBinding.tvOrderType.background = ContextCompat.getDrawable(activity, R.drawable.shape_cf44040_r4)
        }

        /*产品名称*/
        mBinding.tvProdName.text = orderBean.symbol

        /*开仓价*/
        mBinding.tvOpenPriceTitle.text = "${activity.getString(R.string.entry_price).arabicReverseTextByFlag(" ")} (${orderBean.priceCurrency.ifNull()})".arabicReverseTextByFlag(" ").ifNull()
        mBinding.tvOpenPrice.setTextDiff(orderBean.openPrice.ifNull())

        /*最新价*/
        mBinding.tvCurrentPriceTitle.text = "${activity.getString(R.string.current_price).arabicReverseTextByFlag(" ")} (${orderBean.priceCurrency.ifNull()})".arabicReverseTextByFlag(" ").ifNull()
        mBinding.tvCurrentPrice.setTextDiff(
            if ("-" == orderBean.closePrice) "-" else orderBean.currentPriceUI.ifNull()
        )

    }

}