package cn.com.vau.trade.adapter

import android.os.Bundle
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.databinding.ItemSymbolSearchBinding
import cn.com.vau.trade.adapter.PayloadKeys.KEY_ONLY_NAME_CHANGE
import cn.com.vau.trade.adapter.PayloadKeys.KEY_ONLY_PRICE_CHANGE
import cn.com.vau.trade.adapter.PayloadKeys.KEY_ONLY_SELECT_CHANGE
import cn.com.vau.trade.bean.SymbolItemBean
import cn.com.vau.util.LogUtil
import cn.com.vau.util.getParcelableCompat

/**
 * Created by array on 2025/4/24 15:23
 * Desc: 搜索页-产品列表adapter
 */
class SymbolSearchListAdapter(
    private var onFollowClick: ((itemBean: SymbolItemBean) -> Unit)? = null,
    private var onItemClick: ((position: Int, itemBean: SymbolItemBean) -> Unit)? = null
) : ListAdapter<SymbolItemBean, SymbolSearchListAdapter.ItemViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
        return ItemViewHolder(ItemSymbolSearchBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        holder.binding.apply {
            val item = getItem(holder.bindingAdapterPosition)
            //绑定数据
            viewSymbolItem.bindData(item)
            //item点击事件
            viewSymbolItem.setOnItemClick {
                onItemClick?.invoke(holder.bindingAdapterPosition, item)
            }
            //添加自选
            viewSymbolItem.setOnFollowClick {
                onFollowClick?.invoke(item)
            }
        }
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty()) {
            val payload = payloads[0] as Bundle
            if (payload.containsKey(KEY_ONLY_PRICE_CHANGE)) {
                val newItem = payload.getParcelableCompat<SymbolItemBean>(KEY_ONLY_PRICE_CHANGE) ?: return
                holder.binding.viewSymbolItem.bindDataOnlyPriceChange(newItem)
                return
            }
            if (payload.containsKey(KEY_ONLY_SELECT_CHANGE)) {
                val newItem = payload.getParcelableCompat<SymbolItemBean>(KEY_ONLY_SELECT_CHANGE) ?: return
                holder.binding.viewSymbolItem.bindDataOnlySelectChange(newItem)
                //item点击事件
                holder.binding.viewSymbolItem.setOnItemClick {
                    onItemClick?.invoke(holder.bindingAdapterPosition, newItem)
                }
                //添加自选
                holder.binding.viewSymbolItem.setOnFollowClick {
                    onFollowClick?.invoke(newItem)
                }
                return
            }
            if (payload.containsKey(KEY_ONLY_NAME_CHANGE)) {
                val newItem = payload.getParcelableCompat<SymbolItemBean>(KEY_ONLY_NAME_CHANGE) ?: return
                holder.binding.viewSymbolItem.bindDataOnlyNameChange(newItem)
                return
            }
        }
        super.onBindViewHolder(holder, position, payloads)
    }

    fun setOnFollowClick(onFollowClick: ((itemBean: SymbolItemBean) -> Unit)?) {
        this.onFollowClick = onFollowClick
    }

    fun setOnItemClick(onItemClick: ((position: Int, itemBean: SymbolItemBean) -> Unit)?) {
        this.onItemClick = onItemClick
    }

    inner class ItemViewHolder(val binding: ItemSymbolSearchBinding) : RecyclerView.ViewHolder(binding.root) {}

    class DiffCallback : DiffUtil.ItemCallback<SymbolItemBean>() {
        override fun areItemsTheSame(oldItem: SymbolItemBean, newItem: SymbolItemBean): Boolean {
            val areItemsTheSame = oldItem.product.symbol == newItem.product.symbol
            return areItemsTheSame
        }

        override fun areContentsTheSame(oldItem: SymbolItemBean, newItem: SymbolItemBean): Boolean {
            val areContentsTheSame = oldItem.product.symbol == newItem.product.symbol
                    && oldItem.isSelected == newItem.isSelected
                    && oldItem.searchKey == newItem.searchKey
                    && oldItem.product.description == newItem.product.description
                    && oldItem.product.closePrice == newItem.product.closePrice
                    && oldItem.product.bid == newItem.product.bid
                    && oldItem.product.rose == newItem.product.rose
            return areContentsTheSame
        }

        override fun getChangePayload(oldItem: SymbolItemBean, newItem: SymbolItemBean): Any? {
            val payload = Bundle()
            if (isChangedSelected(oldItem, newItem).not() && isChangedSearchKey(oldItem, newItem).not() && isChangedPrice(oldItem, newItem)) {
                payload.putParcelable(KEY_ONLY_PRICE_CHANGE, newItem)
            } else if (isChangedSelected(oldItem, newItem) && isChangedSearchKey(oldItem, newItem).not() && isChangedPrice(oldItem, newItem).not()) {
                payload.putParcelable(KEY_ONLY_SELECT_CHANGE, newItem)
            } else if (isChangedSelected(oldItem, newItem).not() && isChangedSearchKey(oldItem, newItem) && isChangedPrice(oldItem, newItem).not()) {
                payload.putParcelable(KEY_ONLY_NAME_CHANGE, newItem)
            }
            return if (payload.isEmpty) null else payload
        }

        /**
         * 选中状态变化
         * @param oldItem
         * @param newItem
         * @return Boolean true 选中状态变化 false 选中状态不变
         */
        private fun isChangedSelected(oldItem: SymbolItemBean, newItem: SymbolItemBean): Boolean {
            return oldItem.isSelected != newItem.isSelected
        }

        /**
         * 搜索关键字变化
         * @param oldItem
         * @param newItem
         * @return Boolean true 搜索关键字变化 false 搜索关键字不变
         */
        private fun isChangedSearchKey(oldItem: SymbolItemBean, newItem: SymbolItemBean): Boolean {
            return oldItem.searchKey != newItem.searchKey
        }

        /**
         * 价格变化
         * @param oldItem
         * @param newItem
         * @return Boolean true 价格变化 false 价格不变
         */
        private fun isChangedPrice(oldItem: SymbolItemBean, newItem: SymbolItemBean): Boolean {
            return oldItem.product.closePrice != newItem.product.closePrice
                    || oldItem.product.bid != newItem.product.bid
                    || oldItem.product.rose != newItem.product.rose
        }
    }
}

object PayloadKeys {
    const val KEY_ONLY_SELECT_CHANGE = "KEY_ONLY_SELECT_CHANGE"
    const val KEY_ONLY_NAME_CHANGE = "KEY_ONLY_NAME_CHANGE"
    const val KEY_ONLY_PRICE_CHANGE = "KEY_ONLY_PRICE_CHANGE"
}