package cn.com.vau.trade.st.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.trade.StTradeHistoryOrdersBean.Data.PortfolioDealsData
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull

class StStrategyOrderHistoryViewModel : BaseViewModel() {

    var uiListLiveData = MutableLiveData<ListUIState<List<PortfolioDealsData>?>>()
    var page = 1

    fun refreshHistoryListData(portfolioId: String?, isShowDialog: Boolean) {
        page = 1
        stTradeListDealHistoryByCollectData(portfolioId, isShowDialog)
    }

    fun loadMoreHistoryListData(portfolioId: String?) {
        page++
        stTradeListDealHistoryByCollectData(portfolioId, false)
    }

    private fun stTradeListDealHistoryByCollectData(portfolioId: String?, isShowDialog: Boolean) {
        requestNet({
            stTradingService.tradeListDealsHistoryPage(
                hashMapOf<String, Any>(
                    "accountId" to UserDataUtil.stAccountId(),
                    "portfolioId" to portfolioId.ifNull(),
                    "page" to page,
                    "rowsPerPage" to 50
                )
            )
        }, { dataBean ->
            hideLoading()
            if (!dataBean.isSuccess()) {
                ToastUtil.showToast(dataBean.getResponseMsg())
                uiListLiveData.value = ListUIState.Error(dataBean.getResponseMsg())
                return@requestNet
            }
            val list = dataBean.data?.listOfPortfolioDeals
            if (page <= 1) {        // Refresh
                if (list.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.Empty
                } else {
                    uiListLiveData.value = ListUIState.RefreshSuccess(list)
                }
            } else {                // LoadMore
                if (list.isNullOrEmpty()) {
                    uiListLiveData.value = ListUIState.LoadEnd(list)
                } else {
                    uiListLiveData.value = ListUIState.LoadMoreSuccess(list)
                }
            }
        }, {
            uiListLiveData.value = ListUIState.Error()
        }, isShowDialog = isShowDialog)
    }
}