package cn.com.vau.trade.st.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.trade.st.contract.StCopyTradingPositionsOpenContract
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

class StCopyTradingPositionsOpenModel:StCopyTradingPositionsOpenContract.Model {

    override fun stAccountPauseFollowing(body: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().accountPauseFollowing(body), baseObserver)
        return baseObserver.disposable
    }

    override fun stAccountResumeFollowing(body: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().accountResumeFollowing(body), baseObserver)
        return baseObserver.disposable
    }

    override fun stAccountRemoveFollower(portfolioId: String, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().accountRemoveFollower(portfolioId), baseObserver)
        return baseObserver.disposable
    }

    override fun userSetItemset(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().usersetItemsetApi(map), baseObserver)
        return baseObserver.disposable
    }
}