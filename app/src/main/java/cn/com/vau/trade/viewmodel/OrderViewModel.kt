package cn.com.vau.trade.viewmodel

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.OrderVolumeUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.page.StickyEvent
import cn.com.vau.profile.adapter.SelectBean
import cn.com.vau.trade.data.AccountInfoItemBean
import cn.com.vau.trade.data.ProductState
import cn.com.vau.util.DealLogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.addComma
import cn.com.vau.util.ifNull
import cn.com.vau.util.mathAdd
import cn.com.vau.util.mathCompTo
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.mathSub
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.substringCatching
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.tracking.SensorsHelper
import cn.com.vau.util.widget.dialog.CenterActionDialog
import com.google.gson.JsonObject
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import kotlin.math.pow

class OrderViewModel : BaseViewModel() {

    /**
     * 交易类型变化时
     */
    val tradeTypeChangeLiveData by lazy {
        MutableLiveData<Int>()
    }

    val maxOpenChangeLiveData by lazy {
        MutableLiveData<String>()
    }

    val volumeInvalidChangeLiveData by lazy {
        MutableLiveData<Pair<Boolean, String>>()
    }

    val requestMarginChangeLiveData by lazy {
        MutableLiveData<String>()
    }

    val productDataChangeLieData by lazy {
        MutableLiveData<ShareProductData>()
    }

    val atPriceChangeLiveData by lazy {
        MutableLiveData<String>()
    }

    val stopLimitPriceChangeLiveData by lazy {
        MutableLiveData<String>()
    }

    // TODO: 只在老下单用
    val currencyType: String by lazy {
        UserDataUtil.currencyType()
    }

    private var requestMargin = "0"
        set(value) {
            field = value
            requestMarginChangeLiveData.postValue(value)
        }

    var productData: ShareProductData? = null

    /**
     * 挂单价格：stop、 limit
     */
    var atPriceInput: String = ""
        set(value) {
            field = value
            atPriceChangeLiveData.value = value
        }

    /**
     * 挂单价格：stopLimit
     */
    var stopLimitPriceInput: String = ""
        set(value) {
            field = value
            stopLimitPriceChangeLiveData.value = value
        }

    val inputPrice: String
        get() {
            if (tradeTypeIndex == INDEX_MARKET) {
                return if (tradeType == TRADE_BUY) "${productData?.ask}" else "${productData?.bid}"
            }
            if (tradeTypeIndex == INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
                return stopLimitPriceInput.ifEmpty { "0" }
            }
            return atPriceInput.ifEmpty { "0" }
        }

    val submitOrderSuccessLiveData by lazy {
        MutableLiveData<String>()
    }

    val fundLackLiveData by lazy {
        MutableLiveData<String>()
    }
    val checkDelayLiveData by lazy {
        MutableLiveData<String>()
    }
    val hintDataDialogLiveData by lazy {
        MutableLiveData<String>()
    }
    val tokenErrorLiveData by lazy {
        MutableLiveData<String>()
    }

    val buyOrSellChangeLiveData by lazy {
        MutableLiveData<String>()
    }

    private val _accountInfoItemLiveData = MutableLiveData<AccountInfoItemBean>()
    val accountInfoItemLiveData = _accountInfoItemLiveData.distinctUntilChanged()

    private val _productStateLiveData = MutableLiveData<ProductState>()
    val productStateLiveData = _productStateLiveData.distinctUntilChanged()

    var isConnected = true

    var digits = 0

    /**
     *  1 sell bid / 0 buy ask
     */
    var tradeType = TRADE_SELL
        set(value) {
            field = value
            buyOrSellChangeLiveData.value = value
        }

    var defaultLot = ""
    var productName = ""

    var minProfit = "0.0"
    var stopLossLevel = "0.0"

    var minVolume = "0.01"
    var maxVolume = "0.0"
    var stepVolume = "0.01"
        set(value) {
            if (isSameDigits(value).not()) {
                field = minVolume
                return
            }
            field = value
        }

    /**
     * 接口请求参数
     */
    var volumeParam = ""
    var tpParam = ""
    var slParam = ""
    var atPriceParam = ""
    var stopLimitPriceParam = ""

    var takeProfitCb = false
    var stopLossCb = false

    var tpSlIsCheckEd = false
    var tpInputValue = ""
    var slInputValue = ""

    var takeProfitRange = ""
    var stopLossRange = ""

    /**
     * 刷新行情的
     */
    val refreshProductDataLiveData by lazy {
        MutableLiveData<ShareProductData>()
    }

    val takeProfitCheckLiveData: MutableLiveData<String> by lazy {
        MutableLiveData<String>()
    }
    val stopLessCheckLiveData: MutableLiveData<String> by lazy {
        MutableLiveData<String>()
    }

    var tradeTypeList: MutableList<SelectBean> = mutableListOf()
    var tradeTypeIndex = 0
        set(value) {
            field = value
            tradeTypeChangeLiveData.value = value
        }

    var isInitAtPrice = false

    var unitTypeList: MutableList<SelectBean> = mutableListOf()

    /**
     * 交易单位，1 手数，2 金额
     */
    var unit = UNIT_LOTS
    var maxOpenVolume = maxVolume
    var maxOpenAmount = "0"

    var isFirstSync = true
    var isInputVolumeFromKeyBoard = false

    var isSwitchUnit = false

    var inputVolume = ""
    var inputAmount = ""

    var pendingTypeStr: String? = null

    init {
        unit = SpManager.getOpenPositionUnit(UNIT_LOTS)
    }

    /**
     * 设置产品信息
     */
    fun setProduceData(shareProductData: ShareProductData?) {
        if (shareProductData == null) return
        productName = shareProductData.symbol
        digits = shareProductData.digits.ifNull()
        minVolume = shareProductData.minvolume ?: "0.01"
        maxVolume = shareProductData.maxvolume
        stepVolume = shareProductData.stepvolume
        minProfit = "${1 / 10.toDouble().pow(digits.ifNull(0).toDouble())}"
        stopLossLevel = shareProductData.stopslevel.mathDiv("${10.0.pow(shareProductData.digits)}", 8)
        productData = shareProductData
        getMaxOpenVolume(inputPrice)
        getMaxOpenAmount(inputPrice)
        productDataChangeLieData.value = shareProductData
    }

    fun checkProduceCanTrade(): Boolean {
        if (productData == null) {
            return false
        }
        return !(productData?.enable == "0" || productData?.enable == "1" || productData?.marketClose == true)
    }

    /**
     * 刷新航企
     */
    fun refreshProduce() {
        if (productData == null) {
            return
        }
        viewModelScope.launch {
            withContext(Dispatchers.Default) {
                getMargin(inputVolume, inputPrice)
                initTpSlRange()
            }
            refreshProductDataLiveData.value = productData
        }
    }

    fun initTpSlRange() {
        if (tradeTypeIndex == INDEX_MARKET) {
            computeMarketTakeProfitAndStopLoss()
        } else {
            initAtPriceTakeProfitStopLoss(atPriceInput, stopLimitPriceInput)
        }
    }

    /**
     * 初始化手数
     * 手数规则：没有传入默认手数时使用产品存在本地的手数，需要检查本地的手是否能被手数步长整除，能整除则用本地的手，否则使用：手数步长+（(本地的手/手数步长）取整）* 手数步长
     *
     * 如果有默认手数，则将最小手数替换为默认手数计算。
     */
    fun initVolume(): Double {
        if (TextUtils.isEmpty(defaultLot)) {
            val localVolume = readStorageProductLots()
            if (localVolume == "0") {
                return 0.0
            }
            // 如果可以整除
            if (OrderVolumeUtil.isDivideExactly(localVolume.toDoubleCatching(), stepVolume.toDoubleCatching())) {
                return localVolume.toDoubleCatching()
            }
            return stepVolume.toDoubleCatching() + (localVolume.toDoubleCatching() / stepVolume.toDoubleCatching()).toInt() * stepVolume.toDoubleCatching()
        }

        // 如果可以整除
        if (OrderVolumeUtil.isDivideExactly(defaultLot.toDoubleCatching(), stepVolume.toDoubleCatching())) {
            return defaultLot.toDoubleCatching()
        }
        return stepVolume.toDoubleCatching() + (defaultLot.toDoubleCatching() / stepVolume.toDoubleCatching()).toInt() * stepVolume.toDoubleCatching()

    }

    /**
     * 检查手数是否合法
     */
    @SuppressLint("SetTextI18n")
    fun checkVolume() {
        var tipStr = ""
        //手数下单
        if (unit == UNIT_LOTS) {
            if (inputVolume.mathCompTo(minVolume) == -1) {
                tipStr = "${getString(R.string.min_value)}:${minVolume} ${getString(R.string.lots)}"
                volumeInvalidChangeLiveData.value = Pair(true, tipStr)
                return
            }
            if (inputVolume.mathCompTo(maxOpenVolume) == 1) {
                tipStr = "${getString(R.string.max_value)}:${maxOpenVolume} ${getString(R.string.lots)}"
                volumeInvalidChangeLiveData.value = Pair(true, tipStr)
                return
            }
            volumeInvalidChangeLiveData.value = Pair(false, tipStr)
            return
        }
        //金额下单
        if (inputAmount.mathCompTo(getMinAmount(inputPrice)) == -1) {
            tipStr = "${getString(R.string.min_value)}:${getMinAmount(inputPrice)} ${UserDataUtil.currencyType()}"
            volumeInvalidChangeLiveData.value = Pair(true, tipStr)
            return
        }
        if (inputAmount.mathCompTo(maxOpenAmount) == 1) {
            tipStr = "${getString(R.string.max_value)}:${maxOpenAmount.numCurrencyFormat()} ${UserDataUtil.currencyType()}"
            volumeInvalidChangeLiveData.value = Pair(true, tipStr)
            return
        }
        volumeInvalidChangeLiveData.value = Pair(false, tipStr)
        return
    }

    /**
     * 获取最大可开手数
     */
    fun getMaxOpenVolume(price: String): String {
        var maxOpenVolume = OrderUtil.getMaxOpenVolume(tradeType, productData, price)
        val valueDiv = maxOpenVolume.mathDiv(stepVolume, 1)
        val valueDivInt = valueDiv.substringCatching(0, valueDiv.indexOf("."), valueDiv)
        maxOpenVolume = valueDivInt.mathMul(stepVolume).numFormat(2)
        if (maxOpenVolume.mathCompTo(maxVolume) == 1) {
            maxOpenVolume = maxVolume
        }
        this.maxOpenVolume = maxOpenVolume
        refreshMaxOpen()
        return maxOpenVolume
    }

    /**
     * 获取最大可开手数对应的金额
     */
    fun getMaxOpenAmount(price: String): String {
        var maxOpenVolume = getMaxOpenVolume(price)
        if (maxOpenVolume.mathCompTo(maxVolume) == 1) {
            maxOpenVolume = maxVolume
        }
        val productMaxOpen = OrderUtil.getAmountFromVolume(productData, maxOpenVolume, tradeType, price)
        this.maxOpenAmount = productMaxOpen
        refreshMaxOpen()
        return productMaxOpen
    }

    private fun refreshMaxOpen() {
        if (unit == UNIT_LOTS) {
            maxOpenChangeLiveData.value = maxOpenVolume
            return
        }
        maxOpenChangeLiveData.value = maxOpenAmount
    }

    /**
     * 点击加号，手数计算
     */
    fun addVolume(currentCount: String, inputPrice: String): String {
        if (maxOpenVolume.mathCompTo("0") != 1){
            return "0.00"
        }
        //手数加
        if (unit == UNIT_LOTS) {
            getMaxOpenVolume(inputPrice)
            if (currentCount.isEmpty()) {
                val initValue = initVolume()
                return initValue.numFormat(2, false)
            }
            if (currentCount.mathCompTo(maxOpenVolume) != -1) {
                return maxOpenVolume.numFormat(2, false)
            }
            val editCount = currentCount.mathAdd(stepVolume)
            //手数
            return editCount.numFormat(2, false)
        }
        // 金额加
        getMaxOpenAmount(inputPrice)
        if (currentCount.mathCompTo(getMinAmount(inputPrice)) == -1) {
            return getMinAmount(inputPrice)
        }
        if (currentCount.mathCompTo(maxOpenAmount) == 1) {
            return maxOpenAmount
        }
        if (currentCount.mathCompTo(maxOpenAmount) != -1) {
            return currentCount
        }
        val editCount = currentCount.mathAdd(getMinStepAmount(inputPrice))
        val div = editCount.mathDiv(getMinStepAmount(inputPrice), 0)
        var finalValue = div.mathMul(getMinStepAmount(inputPrice))
        if (finalValue.mathCompTo(maxOpenAmount) == 1) {
            finalValue = maxOpenAmount
        }
        return finalValue.numCurrencyFormat()
    }

    /**
     * 点击减号，手数计算
     */
    fun subVolume(currentCount: String, inputPrice: String): String {
        if (maxOpenVolume.mathCompTo("0") != 1){
            return "0.00"
        }
        if (unit == UNIT_LOTS) {
            getMaxOpenVolume(inputPrice)
            if (currentCount.isEmpty()) {
                val initValue = initVolume()
                return initValue.numFormat(2, false)
            }
            val editCount = currentCount.mathSub(stepVolume)
            if (currentCount.mathCompTo(minVolume) != 1) {
                return minVolume.numFormat(2, false)
            }
            return editCount.numFormat(2, false)
        }
        //金额的减少
        getMaxOpenAmount(inputPrice)
        if (currentCount.mathCompTo(getMinAmount(inputPrice)) == -1) {
            return getMinAmount(inputPrice)
        }
        if (currentCount.mathCompTo(maxOpenAmount) == 1) {
            return maxOpenAmount
        }
        if (currentCount.mathCompTo(getMinAmount(inputPrice)) != 1) {
            return currentCount
        }

        val editCount = currentCount.mathSub(getMinStepAmount(inputPrice))
        val div = editCount.mathDiv(getMinStepAmount(inputPrice), 0)
        var finalValue = div.mathMul(getMinStepAmount(inputPrice))
        if (finalValue.mathCompTo(getMinStepAmount(inputPrice)) == -1) {
            finalValue = getMinAmount(inputPrice)
        }
        return finalValue.numCurrencyFormat()
    }

    /**
     * 手数格式化，固定两位小数
     */
    fun formatVolume(etVolumeStr: String): String {
        if (etVolumeStr.isEmpty()) {
            return etVolumeStr
        }
        if (unit == UNIT_LOTS) {
            return etVolumeStr.numFormat(2, false)
        }
        return etVolumeStr.numCurrencyFormat()
    }

    /**
     * 计算市价的止盈止损
     * 买入：止损 低于等于（卖出价-止损水平）；止盈：大于等于（卖出价+止损水平）
     * 卖出：止损 大于等于（买入价+止损水平）；止盈：低于等于（买入价-止损水平）
     */
    private fun computeMarketTakeProfitAndStopLoss() {
        if (productData == null) return
        if (tradeType == TRADE_SELL) {
            val stopLossLevelYES = "${productData?.ask}".mathSub(stopLossLevel)
            takeProfitRange = stopLossLevelYES.numFormat(digits)
            val stopLossLevelNO = "${productData?.ask}".mathAdd(stopLossLevel)
            stopLossRange = stopLossLevelNO.numFormat(digits)
        } else {
            // buy
            // 止盈范围（大于等于（卖出价+止损水平））
            val stopLossLevelYES = "${productData?.bid}".mathAdd(stopLossLevel)
            takeProfitRange = stopLossLevelYES.numFormat(digits)
            // 止损范围（低于等于（卖出价-止损水平））
            val stopLossLevelNO = "${productData?.bid}".mathSub(stopLossLevel)
            stopLossRange = stopLossLevelNO.numFormat(digits)
        }
    }

    private fun initAtPriceTakeProfitStopLoss(atPriceValue: String, stopLimitPriceValue: String) {
        if (tradeType == TRADE_SELL) {
            computerSellTakeProfitStopLossRange(atPriceValue, stopLimitPriceValue, stopLossLevel)
        } else {
            computerBuyTakeProfitStopLossRange(atPriceValue, stopLimitPriceValue, stopLossLevel)
        }
    }

    /**
     * 计算挂单 Sell 止盈止损范围
     * Sell Limit：   止损  大于等于（挂单价+止损水平）
     * Sell Stop：     止损  大于等于（挂单价+止损水平）
     * Sell Stop Limit 止损 大于等于（Stop Limit 价+止损水平）
     *
     * Sell Limit：   止盈 低于等于（挂单价-止损水平）
     * Sell Stop：     止盈 低于等于（挂单价-止损水平）
     * Sell Stop Limit 止盈  低于等于（Stop Limit 价-止损水平
     *
     */
    private fun computerSellTakeProfitStopLossRange(atPriceValue: String, stopLimitPriceValue: String, stopLossLevel: String) {
        var stopLossRange = "0"
        var takeProfitRange = "0"
        // sell 止盈范围
        if (tradeTypeIndex != INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
            stopLossRange = atPriceValue.mathAdd(stopLossLevel).numFormat(digits)
            takeProfitRange = atPriceValue.mathSub(stopLossLevel).numFormat(digits)
        } else {
            stopLossRange = stopLimitPriceValue.mathAdd(stopLossLevel).numFormat(digits)
            takeProfitRange = stopLimitPriceValue.mathSub(stopLossLevel).numFormat(digits)
        }
        this.takeProfitRange = takeProfitRange
        this.stopLossRange = stopLossRange
    }

    /**
     * 计算Buy 止盈止损范围
     * Buy 止损
     * Buy Limit：止损 低于等于（挂单价-止损水平）
     * Buy Stop：  止损 低于等于（挂单价-止损水平）
     * Buy Stop Limit 止损 低于等于（Stop Limit 价-止损水平）
     *
     * Buy 止盈计算
     * Buy Limit：止盈 大于等于（挂单价+止损水平）
     * Buy Stop：  止盈    大于等于（挂单价+止损水平）
     * Buy Stop Limit 止盈  大于等于（Stop Limit 价+止损水平）
     *
     */
    private fun computerBuyTakeProfitStopLossRange(atPriceValue: String, stopLimitPriceValue: String, stopLossLevel: String) {
        var stopLossRange = "0"
        var takeProfitRange = "0"
        if (tradeTypeIndex != INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT) {
            stopLossRange = atPriceValue.mathSub(stopLossLevel).numFormat(digits)
            takeProfitRange = atPriceValue.mathAdd(stopLossLevel).numFormat(digits)
        } else {
            stopLossRange = stopLimitPriceValue.mathSub(stopLossLevel).numFormat(digits)
            takeProfitRange = stopLimitPriceValue.mathAdd(stopLossLevel).numFormat(digits)
        }
        this.takeProfitRange = takeProfitRange
        this.stopLossRange = stopLossRange
    }

    /**
     * 点击加号，止盈价格计算
     */
    fun addTakeProfit(currentCount: String): String {
        if (currentCount.isEmpty()) {
            return takeProfitRange
        }
        val editCount = currentCount.mathAdd(minProfit)
        tradePagePlusMinusBtnClick("Take Profit", "Plus")
        return editCount.numFormat(digits, false)

    }

    /**
     * 点击减号，止盈价格计算
     */
    fun subTakeProfit(currentCount: String): String {
        if (currentCount.isEmpty()) {
            return takeProfitRange
        }
        if (currentCount.mathCompTo("0") != 1) {
            return currentCount
        }
        val editCount = currentCount.mathSub(minProfit)
        tradePagePlusMinusBtnClick("Take Profit", "Minus")
        return editCount.numFormat(digits)

    }

    /**
     * 点击减号，止损价格计算
     */
    fun subStopLoss(currentCount: String): String {
        if (currentCount.isEmpty()) {
            return stopLossRange
        }
        if (currentCount.mathCompTo("0") != 1) {
            return currentCount
        }
        val editCount = currentCount.mathSub(minProfit)
        tradePagePlusMinusBtnClick("Stop Loss", "Minus")
        return editCount.numFormat(digits, false)
    }

    /**
     * 点击加号，止损价格计算
     */
    fun addStopLoss(currentCount: String): String {
        if (currentCount.isEmpty()) {
            return stopLossRange
        }
        val editCount = currentCount.mathAdd(minProfit)
        tradePagePlusMinusBtnClick("Stop Loss", "Plus")
        return editCount.numFormat(digits, false)

    }

    /**
     * 检查止盈价格是否合法
     */
    fun checkTakeProfit(takeProfit: String) {
        if (tpSlIsCheckEd.not()) {
            tpInputValue = ""
            return
        }
        if (takeProfit.isEmpty()) {
            tpInputValue = ""
            takeProfitCheckLiveData.value = ""
            return
        }
        tpInputValue = takeProfit
        val tpCompareResults = if (tradeType == TRADE_BUY) -1 else 1
        var tipStr = ""
        if (takeProfit.mathCompTo(takeProfitRange) == tpCompareResults) {
            tipStr = if (tpCompareResults == 1) {
                "${getString(R.string.price)} ≤ ${takeProfitRange.addComma(digits)}"
            } else {
                "${getString(R.string.price)} ≥ ${takeProfitRange.addComma(digits)}"
            }
        }
        takeProfitCheckLiveData.value = tipStr
    }

    /**
     * 检查止止损格是否合法
     */
    fun checkStopLess(stopLess: String) {
        if (tpSlIsCheckEd.not()) {
            slInputValue = ""
            return
        }
        if (stopLess.isEmpty()) {
            slInputValue = ""
            stopLessCheckLiveData.value = ""
            return
        }
        slInputValue = stopLess
        val tpCompareResults = if (tradeType == TRADE_BUY) 1 else -1
        var tipStr = ""
        if (stopLess.mathCompTo(stopLossRange) == tpCompareResults) {
            tipStr = if (tpCompareResults == 1) {
                "${getString(R.string.price)} ≤ ${stopLossRange.addComma(digits)}"
            } else {
                "${getString(R.string.price)} ≥ ${stopLossRange.addComma(digits)}"
            }
        }
        stopLessCheckLiveData.value = tipStr
    }

    /**
     * 以止盈止损价格成交后的预估盈亏
     */
    fun getTpEstimated(tpPrice: String): String {
        if (productData == null) {
            return ""
        }
        val pl = VAUSdkUtil.getProfitLoss(
            productData ?: ShareProductData(),
            inputPrice,
            inputVolume,
            tradeType,
            tpPrice
        ).toString().numCurrencyFormat()

        return pl
    }

    /**
     * 止损止损价格格式化，小数位和产品支持的小数位相同
     */
    fun formatTpSl(currentCount: String): String {
        if (currentCount.isEmpty()) {
            return currentCount
        }
        return currentCount.numFormat(digits, false)
    }

    /**
     * 判断最小可开和交易步长的小数位是否相同
     */
    private fun isSameDigits(stepVolumeField: String): Boolean {
        val minDigit = if (minVolume.contains(".")) minVolume.length - minVolume.indexOf(".") else 0
        val stepDigit = if (stepVolumeField.contains(".")) stepVolumeField.length - stepVolumeField.indexOf(".") else 0
        return minDigit == stepDigit
    }

    /**
     * 获取币种支持的小数位
     */
    fun getCurrencyDigits(): Int {
        return when (UserDataUtil.currencyType()) {
            "JPY", "USC" -> 0
            "BTC", "ETH" -> 8
            else -> 2
        }
    }

    /**
     * 最小手数对应的金额
     */
    fun getMinAmount(inputPrice: String = ""): String {
        return OrderUtil.getAmountFromVolume(productData, productData?.minvolume ?: "0.01", tradeType, inputPrice)
    }

    fun getMinStepAmount(inputPrice: String = ""): String {
        return OrderUtil.getAmountFromVolume(productData, stepVolume ?: "0.01", tradeType, inputPrice)
    }

    /**
     * 1.当最大可开手数小于当前产品最小开仓手数时，数量输入框默认填充0，结束
     * 2.当前交易的产品从本地取出当前交易产品上次下单存储的数量（手数）,如果取不到，则默认使用最小开仓手数  Y
     * 3.当Y>=最小开仓手数 则使用：Y= Min(Y,最大可开手数）
     * 4.当Y< 最小可开，则使用 Y= 最小开仓手数。
     * 5.当使用金额下单时，Y转成金额
     */
    fun readStorageProductLots(): String {
        if (minVolume.mathCompTo(maxOpenVolume) == 1 ) {
            return "0"
        }
        var lots = SpManager.getProductLots(productName, minVolume)
        if (lots.mathCompTo(minVolume) >= 0 && lots.mathCompTo(maxOpenVolume) == 1) {
            lots = maxOpenVolume
            return lots
        }

        if (lots.mathCompTo(minVolume) == -1) {
            lots = minVolume
            return lots
        }

        return lots
    }

    /**
     * 保存交易量
     */
    fun storageProduceLots(lot: String) {
        SpManager.putProductLots(productName, lot)
    }

    /**
     * 手数单位：金额或者手
     */
    fun initUintTypeList(context: Context) {
        unitTypeList.clear()
        unitTypeList.add(SelectBean(context.getString(R.string.lots)))
        unitTypeList.add(SelectBean(UserDataUtil.currencyType()))
    }

    /**
     * 初始订单类型
     */
    fun initTradeTypeList(context: Context) {
        tradeTypeList.clear()
        tradeTypeList.add(SelectBean(context.getString(R.string.market)))
        tradeTypeList.add(SelectBean(context.getString(R.string.limit)))
        tradeTypeList.add(SelectBean(context.getString(R.string.stop)))
        if (UserDataUtil.isMT5()) {
            tradeTypeList.add(SelectBean(context.getString(R.string.stop_limit)))
        }
    }

    fun isNeedResetTradeType():Boolean {
       return tradeTypeIndex >= tradeTypeList.size
    }

    /**
     * 可用保证金
     */
    fun getFreeMargin(): String {
        val isStLogin = UserDataUtil.isStLogin()
        return if (isStLogin)
            VAUSdkUtil.stShareAccountBean().freeMargin.numCurrencyFormat()
        else
            VAUSdkUtil.shareAccountBean().freeMargin.numCurrencyFormat()
    }

    /**
     * 获取参考保证金
     */
    fun getMargin(volume: String, inputPrice: String = ""): String {
        val typeAskBid = if (tradeType == TRADE_SELL) "bid" else "ask" //选中类型（卖出/买入）
        // 参考保证金
        val allMoney = OrderUtil.getRequiredMargin(productData, if (TextUtils.isEmpty(volume)) "0.01" else volume, typeAskBid, inputPrice)
        requestMargin = allMoney.numCurrencyFormat()
        return requestMargin
    }

    /**
     * 交易后预付款比
     */
    fun getMarginLevelAfterTrading(data: ShareProductData?, volume: String, inputPrice: String = ""): String {
        // 交易后预付款比 =（ 现净值 + 当前订单盈亏 ） / （ 已用保证金 + 参考保证金 ）
        if (data == null) return "0.0%"
        val isStLogin = UserDataUtil.isStLogin()
        val equity = if (isStLogin)
            VAUSdkUtil.stShareAccountBean().equity
        else
            VAUSdkUtil.shareAccountBean().equity
        val margin = if (isStLogin)
            VAUSdkUtil.stShareAccountBean().margin
        else
            VAUSdkUtil.shareAccountBean().margin

        // 参考保证金
        val typeAskBid = if (tradeType == TRADE_SELL) "bid" else "ask" //选中类型（卖出/买入）
        val allMoney = OrderUtil.getRequiredMargin(productData, if (TextUtils.isEmpty(volume)) "0.01" else volume, typeAskBid, inputPrice)

        val s1 = equity.toString().mathAdd(
            VAUSdkUtil.getProfitLoss(
                data,
                "${if (TRADE_SELL == tradeType) data.bid else data.ask}",
                volume,
                tradeType
            ).toString()
        )
        val s2 = margin.toString().mathAdd(allMoney)
        return s1.mathMul("100").mathDiv(s2, 2)
    }

    /**
     * 接口参数赋值
     */
    fun createApiParam() {
        volumeParam = inputVolume
        tpParam = tpInputValue
        slParam = slInputValue
        atPriceParam = atPriceInput
        stopLimitPriceParam = stopLimitPriceInput
    }

    /**
     * 检查交易量是否合法，true 合法
     */
    fun checkInputVolumeValid(context: Context): Boolean {
        if (unit == UNIT_LOTS && inputVolume.mathCompTo(minVolume) == -1) {
            val tipStr = "${context.getString(R.string.the_minimum_value_volume_is, minVolume)} ${getString(R.string.lots)} "
            ToastUtil.showToast(tipStr)
            return false
        }

        if (unit == UNIT_AMOUNT && inputAmount.mathCompTo(getMinAmount(inputPrice)) == -1) {
            val tipStr = "${context.getString(R.string.the_minimum_value_volume_is, getMinAmount(inputPrice))} ${UserDataUtil.currencyType()} "
            ToastUtil.showToast(tipStr)
            return false
        }

        if (!OrderVolumeUtil.isDivideExactly(inputVolume.toDoubleCatching(), stepVolume.toDoubleCatching())) {
            // 输入手数有误，请重新输入
            ToastUtil.showToast(getString(R.string.number_of_lots_re_enter))
            return false
        }

        return true
    }

    /**
     * 跟单提交订单
     */
    fun stSubmitOrder() {
        val jsonObject = JsonObject()
        jsonObject.addProperty(
            "portfolioId", UserDataUtil.stMasterPortfolioId()
        )
        // 订单价
        jsonObject.addProperty(
            "priceOrder", if (tradeType == "0") productData?.bid else productData?.ask
        )
        jsonObject.addProperty("symbol", productData?.symbol ?: "")
        // 止盈
        jsonObject.addProperty("takeProfit", if (TextUtils.isEmpty(tpParam)) "0.0" else tpParam)
        // 止损
        jsonObject.addProperty("stopLoss", if (TextUtils.isEmpty(slParam)) "0.0" else slParam)
        //  BUY, SELL
        jsonObject.addProperty("tradeAction", if (tradeType == "0") "BUY" else "SELL")
        // 手数
        jsonObject.addProperty("volume", volumeParam)
        val startTimeMillisCreateOrder = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "open order:${if (tradeType == "0") "buy" else "sell"}  " + "volume:${volumeParam}  " + "symbol:${productData?.symbol ?: ""}  " + "take profit:${if (TextUtils.isEmpty(tpParam)) "0" else tpParam}  " + "stop loss:${
                if (TextUtils.isEmpty(
                        slParam
                    )
                ) "0" else slParam
            }", startTimeMillisCreateOrder
        )

        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({
            stTradingService.tradePositionOpenApi(requestBody)
        }, {
            if ("10019" == it.getResponseCode()) {
                fundLackLiveData.value = it.data
                DealLogUtil.saveFailedDealLog(
                    it.getResponseCode(), "open order", startTimeMillisCreateOrder
                )
                return@requestNet
            }
            if ("200" != it.getResponseCode()) {
                ToastUtil.showToast(it.getResponseMsg())
                DealLogUtil.saveFailedDealLog(
                    it.getResponseCode(), "open order", startTimeMillisCreateOrder
                )
                return@requestNet
            }
            submitOrderSuccessLiveData.value = it.data
            DealLogUtil.saveSuccessDealLog(
                "open order:#${it.data}", "open order", startTimeMillisCreateOrder
            )
        }, isShowDialog = true)
    }

    /**
     * 非跟单提交订单
     */
    fun submitOrder(checkDelay: Int = 1) {
        val isMt5 = UserDataUtil.isMT5()
        var handleCount = volumeParam.mathMul(if (isMt5) "10000" else "100")
        if (handleCount.contains("."))
            handleCount = handleCount.split(".").getOrNull(0) ?: ""

        // 价格
        val price = if (tradeType == TRADE_BUY) productData?.ask else productData?.bid

        val startTimeMillisCreateOrder = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "open order:${if (tradeType == "0") "buy" else "sell"}  " + "volume:${volumeParam}  " + "symbol:${productData?.symbol ?: ""}  " + "take profit:${if (TextUtils.isEmpty(tpParam)) "0" else tpParam}  " + "stop loss:${
                if (TextUtils.isEmpty(
                        slParam
                    )
                ) "0" else slParam
            }", startTimeMillisCreateOrder
        )
        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("symbol", productData?.symbol ?: "")
        jsonObject.addProperty("cmd", tradeType)

        jsonObject.addProperty("volume", handleCount)
        jsonObject.addProperty("price", price.toString())
        jsonObject.addProperty("tp", if (TextUtils.isEmpty(tpParam)) "0" else tpParam)
        jsonObject.addProperty("sl", if (TextUtils.isEmpty(slParam)) "0" else slParam)
        // 最大偏移量，非必传【接口文档标注，需找后端同事核对】
        jsonObject.addProperty("maxOffset", *********)
        jsonObject.addProperty("checkDelay", checkDelay)
        jsonObject.addProperty("lasttime", productData?.lasttime ?: "")

        // 非必传【接口文档标注，需找后端同事核对】
        jsonObject.addProperty("comment", "")
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("serverId", UserDataUtil.serverId())
        jsonObject.addProperty("st", VAUSdkUtil.serverTimeMillis)

        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())
        val requestBody = jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({
            tradingService.tradeOrdersOpenApi(requestBody)
        }, {
            if ("10100051" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisCreateOrder
                )
                tokenErrorLiveData.value = it.info
                return@requestNet
            }

            if ("10500070" == it.code) {
                fundLackLiveData.value = it.info
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisCreateOrder
                )
                return@requestNet
            }

            if ("10500181" == it.code) {
                checkDelayLiveData.value = it.info
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisCreateOrder
                )
                return@requestNet
            }

            if ("10500173" == it.code) {
                hintDataDialogLiveData.value = it.info ?: ""
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisCreateOrder
                )
                return@requestNet
            }

            if ("200" != it.code) {
                ToastUtil.showToast(it.info ?: "")
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisCreateOrder
                )
                return@requestNet
            }

            //成功
            submitOrderSuccessLiveData.value = it.info
            val orderNum = it.obj?.order
            DealLogUtil.saveSuccessDealLog(
                "open order:#${orderNum}", "open order", startTimeMillisCreateOrder
            )
        }, isShowDialog = true)
    }

    /**
     * 非跟单挂单
     */
    fun submitPendingOrder(checkDelay: Int = 1) {
        val isMt5 = UserDataUtil.isMT5()
        var handleCount = volumeParam.mathMul(if (isMt5) "10000" else "100")
        if (handleCount.contains("."))
            handleCount = handleCount.split(".").elementAtOrElse(0) { "" }
        val cmd = if (tradeType == TRADE_BUY) {
            if (tradeTypeIndex == 1) 2 else if (tradeTypeIndex == 2) 4 else 6
        } else {
            if (tradeTypeIndex == 1) 3 else if (tradeTypeIndex == 2) 5 else 7
        }
        val logTrade = when (cmd) {
            2 -> "buylimit"
            3 -> "selllimit"
            4 -> "buystop"
            5 -> "sellstop"
            6 -> "buystoplimit"
            else -> "sellstoplimit"
        }
        val startTimeMillisPendingOrder = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "open order:${logTrade}  " + "volume:$volumeParam  " + "symbol:${productData?.symbol ?: ""}  " + "at price:$atPriceParam  " + "take profit:${if (TextUtils.isEmpty(tpParam)) "0" else tpParam}  " + "stop loss:${
                if (TextUtils.isEmpty(
                        slParam
                    )
                ) "0" else slParam
            }", startTimeMillisPendingOrder
        )
        val jsonObject = JsonObject().apply {
            addProperty("login", UserDataUtil.accountCd())
            addProperty("symbol", productData?.symbol ?: "")
            addProperty("cmd", cmd)
            addProperty("volume", handleCount)
            addProperty("price", atPriceParam)
            addProperty("sl", if (TextUtils.isEmpty(slParam)) "0" else slParam)
            addProperty("tp", if (TextUtils.isEmpty(tpParam)) "0" else tpParam)

            addProperty("token", UserDataUtil.tradeToken())
            addProperty("maxOffset", *********)
            addProperty("lasttime", productData?.lasttime ?: "")
            addProperty("serverId", UserDataUtil.serverId())
            addProperty("comment", "")
            addProperty("st", VAUSdkUtil.serverTimeMillis)
            addProperty("checkDelay", checkDelay)
            addProperty("slPrice", stopLimitPriceParam)
        }
        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())
        val requestBody = jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({
            tradingService.tradeOrdersPendingApi(requestBody)
        }, {
            if ("10100051" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisPendingOrder
                )
                tokenErrorLiveData.value = it.info
                return@requestNet
            }

            if ("10500070" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisPendingOrder
                )
                fundLackLiveData.value = it.info
                return@requestNet
            }

            if ("10500181" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisPendingOrder
                )
                checkDelayLiveData.value = it.info
                return@requestNet
            }
            if ("10500173" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisPendingOrder
                )
                hintDataDialogLiveData.value = it.info
                return@requestNet
            }

            if ("200" != it.code) {
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisPendingOrder
                )
                ToastUtil.showToast(it.info)
                return@requestNet
            }

            val orderNum = it.obj?.order
            DealLogUtil.saveSuccessDealLog(
                "open order:#${orderNum}", "open order", startTimeMillisPendingOrder
            )

            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)

            submitOrderSuccessLiveData.value = it.info
        }, isShowDialog = true)
    }

    /**
     * 跟单挂单
     */
    fun stSubmitPendingOrder() {
        val jsonObject = JsonObject()
        jsonObject.addProperty(
            "portfolioId", UserDataUtil.stMasterPortfolioId()
        )
        jsonObject.addProperty("comment", "")
        jsonObject.addProperty("symbol", productData?.symbol ?: "")
        jsonObject.addProperty("volume", volumeParam)
        jsonObject.addProperty("priceTrigger", 0.0)
        jsonObject.addProperty("timeExpiration", 0)
        jsonObject.addProperty("priceOrder", atPriceParam)
        jsonObject.addProperty("stopLoss", if (TextUtils.isEmpty(slParam)) "0.0" else slParam)
        jsonObject.addProperty("takeProfit", if (TextUtils.isEmpty(tpParam)) "0.0" else tpParam)
        val tradeAction = if (tradeType == TRADE_SELL) "Sell" else "Buy"
        val tradeType = if (tradeTypeIndex == 2) "Stop" else "Limit"
        jsonObject.addProperty("tradeAction", tradeAction + "_" + tradeType)
        val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val startTimeMillisPendingOrder = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "open order:${tradeAction + "_" + tradeType}  " + "volume:$volumeParam  " + "symbol:${productData?.symbol ?: ""}  " + "at price:$atPriceParam  " + "take profit:${
                if (TextUtils.isEmpty(
                        tpParam
                    )
                ) "0" else tpParam
            }  " + "stop loss:${if (TextUtils.isEmpty(slParam)) "0" else slParam}", startTimeMillisPendingOrder
        )
        requestNet({
            stTradingService.tradeOrderOpenApi(requestBody)
        }, {
            if ("200" != it.code) {
                ToastUtil.showToast(it.msg)
                DealLogUtil.saveFailedDealLog(
                    it.code.toString(), "open order", startTimeMillisPendingOrder
                )
                return@requestNet
            }
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)
            submitOrderSuccessLiveData.value = it.info
            DealLogUtil.saveSuccessDealLog(
                "open order:#${it.data?.id}", "open order", startTimeMillisPendingOrder
            )
        }, isShowDialog = true)
    }

    /**
     * 设置订单确认
     */
    fun setOrderConfirmation(checked: Boolean) {
        val paramMap = hashMapOf<String, Any>()
        paramMap["userToken"] = UserDataUtil.loginToken()
        // 订单确认key
        paramMap["code"] = Constants.KEY_ORDER_CONFIRM
        paramMap["value"] = if (checked) 1 else 0
        requestNet({ baseService.usersetItemsetApi(paramMap) }, {
            if (it.isSuccess()) {
                UserDataUtil.setOrderConfirmState(if (checked) "1" else "0")
            } else {
                ToastUtil.showToast(it.getResponseMsg())
            }
        }, isShowDialog = false)
    }

    private fun getTradeMode(): String {
        return if (tradeType == TRADE_SELL) {
            when (tradeTypeIndex) {
                1 -> "Sell Limit"
                2 -> "Sell Stop"
                3 -> "Sell Stop Limit"
                else -> "Market Execution"
            }
        } else {
            when (tradeTypeIndex) {
                1 -> "Buy Limit"
                2 -> "Buy Stop"
                3 -> "Buy Stop Limit"
                else -> "Market Execution"
            }
        }
    }

    /**
     * 神策自定义埋点
     */
    fun sensorsTrack() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TRADE_TYPE, SensorsHelper.getTradeType()) // 交易类型
        properties.put(SensorsConstant.Key.PRODUCT_GROUP, "") // 交易产品组
        properties.put(SensorsConstant.Key.PRODUCT_SYMBOL, productName.ifNull()) // 交易产品
        properties.put(SensorsConstant.Key.BUTTON_NAME, if (tradeType == "1") "Sell" else "Buy") // 按钮名称
        properties.put(SensorsConstant.Key.IS_PROFIT, if (takeProfitCb) 1 else 0) // 是否选择止盈
        properties.put(SensorsConstant.Key.IS_LOSS, if (stopLossCb) 1 else 0) // 是否选择止损
        properties.put(SensorsConstant.Key.ORDER_UNIT, if (unit == UNIT_AMOUNT) "Value" else "Lots")
        properties.put(SensorsConstant.Key.TRADE_MODE, getTradeMode()) // 交易方式
        properties.put(SensorsConstant.Key.ACCOUNT_CURRENCY, UserDataUtil.currencyType()) // 账户币种
        // 交易详情页按钮点击 -> 交易详情页点击买卖开仓按钮时触发
        SensorsDataUtil.track(SensorsConstant.V3500.TRADE_OPEN_SUBMIT, properties)
    }

    /**
     * 交易详情页浏览
     */
    fun tradePageView() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.ACCOUNT_CURRENCY,UserDataUtil.currencyType())
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_VIEW, properties)
    }

    fun tradeOpenConfirm() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.PRODUCT_SYMBOL, productName.ifNull()) // 交易产品
        properties.put(SensorsConstant.Key.BUTTON_NAME, if (tradeType == "1") "Sell" else "Buy") // 按钮名称
        properties.put(SensorsConstant.Key.IS_PROFIT, if (takeProfitCb) 1 else 0) // 是否选择止盈
        properties.put(SensorsConstant.Key.IS_LOSS, if (stopLossCb) 1 else 0) // 是否选择止损
        properties.put(SensorsConstant.Key.ORDER_UNIT, if (unit == UNIT_AMOUNT) "Value" else "Lots")
        properties.put(SensorsConstant.Key.TRADE_MODE, getTradeMode()) // 交易方式
        properties.put(SensorsConstant.Key.ACCOUNT_CURRENCY, UserDataUtil.currencyType()) // 账户币种
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_OPEN_CONFIRM, properties)
    }

    fun tradePageKChartBtnClick() {
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_K_CHART_BTN_CLICK)
    }

    fun tradePageProductClick() {
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_PRODUCT_CLICK)
    }

    /**
     * fileName:Volume / Pending Price / Stop Limit Price / Take Profit / Stop Loss
     * buttonName: Plus / Minus
     */
    fun tradePagePlusMinusBtnClick(fileName: String, buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.FIELD_NAME, fileName)
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_PLUS_MINUS_BTN_CLICK, properties)
    }

    fun tradePageVolumeControlClick() {
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_VOLUME_CONTROL_CLICK)
    }

    fun tradePageOrderTypesAnnotationClick() {
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_ORDER_TYPES_ANNOTATION_CLICK)
    }

    fun tradePageMarginAnnotationClick() {
        SensorsDataUtil.track(SensorsConstant.V3540.TRADE_PAGE_MARGIN_ANNOTATION_CLICK)
    }

    /**
     * 切换账户信息item
     */
    fun changeAccountInfoItem(bean: AccountInfoItemBean) {
        _accountInfoItemLiveData.value = bean
    }

    /**
     * 产品状态改变
     */
    fun changeProductState(state: ProductState) {
        _productStateLiveData.value = state
    }

    /**
     * 获取挂单价
     */
    fun getDefaultAtPrice() =
        if (tradeType == TRADE_SELL) {
            if (tradeTypeIndex == INDEX_SELL_LIMIT_BUY_LIMIT)
                "${productData?.bid.ifNull()}".mathAdd(stopLossLevel)
            else
                "${productData?.bid.ifNull()}".mathSub(stopLossLevel)
        } else {
            if (tradeTypeIndex == INDEX_SELL_LIMIT_BUY_LIMIT)
                "${productData?.ask.ifNull()}".mathSub(stopLossLevel)
            else
                "${productData?.ask.ifNull()}".mathAdd(stopLossLevel)
        }.numFormat(digits, false)

    /**
     * 获取stop limit 价格
     */
    fun getStopLimitPrice(atPrice: String) =
        if (tradeType == TRADE_SELL) {
            atPrice.mathAdd(stopLossLevel)
        } else {
            atPrice.mathSub(stopLossLevel)
        }.numFormat(digits, false)

    /**
     * 点击减号，atPrice计算 stop limit Price 计算
     */
    fun subPendingPrice(currentCount: String, defaultAtPrice: String): String {
        if (currentCount.isEmpty()) {
            return defaultAtPrice
        }
        if (currentCount.mathCompTo("0") != 1) {
            return currentCount
        }
        return currentCount.mathSub(minProfit).numFormat(digits)
    }

    /**
     * 点击加号，atPrice计算 stop limit Price 计算
     */
    fun addPendingPrice(currentCount: String, defaultAtPrice: String): String {
        if (currentCount.isEmpty()) {
            return defaultAtPrice
        }
        return currentCount.mathAdd(minProfit).numFormat(digits)
    }

    companion object {
        const val TRADE_SELL = "1"
        const val TRADE_BUY = "0"

        const val UNIT_LOTS = "1"
        const val UNIT_AMOUNT = "2"

        /**
         * 市价: 0
         */
        const val INDEX_MARKET = 0

        /**
         * SELL_LIMIT/BUY_LIMIT: 1
         */
        const val INDEX_SELL_LIMIT_BUY_LIMIT = 1

        /**
         * SELL_STOP/BUY_STOP: 2
         */
        const val INDEX_SELL_STOP_BUY_STOP = 2

        /**
         * SELL_STOP_LIMIT/BUY_STOP_LIMIT: 3
         */
        const val INDEX_SELL_STOP_LIMIT_BUY_STOP_LIMIT = 3

        @JvmStatic
        fun openOrder(context: Context, symbolName: String?, sellOrBuy: String = TRADE_SELL, defaultLots: String? = "", checkReadOnly: Boolean = true) {
            if (checkReadOnly && UserDataUtil.isStLogin() && UserDataUtil.isReadOnly()) {
                CenterActionDialog.Builder(context as Activity)
                    .setContent(context.getString(R.string.your_account_is_trade_now))
                    .setSingleButton(true)
                    .setSingleButtonText(context.getString(R.string.confirm).ifNull())
                    .build()
                    .showDialog()
                return
            }
            val bundle = Bundle()
            bundle.putString(Constants.PARAM_ORDER_TYPE, sellOrBuy)
            bundle.putString(Constants.PARAM_PRODUCT_NAME, symbolName)
            bundle.putString(Constants.PARAM_ORDER_VOLUME, defaultLots)
            EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.OpenOrder.OPEN_ORDER, bundle))
        }

    }
}