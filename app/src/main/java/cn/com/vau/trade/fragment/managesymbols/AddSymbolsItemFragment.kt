package cn.com.vau.trade.fragment.managesymbols

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.*
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.FragmentAddSymbolsItemBinding
import cn.com.vau.trade.adapter.OptionalUnselectedRecyclerAdapter
import org.greenrobot.eventbus.*

private const val ARG_PARAM1 = "param1"

class AddSymbolsItemFragment : BaseFragment() {

    private val mBinding by lazy { FragmentAddSymbolsItemBinding.inflate(layoutInflater) }

    private var position: Int = -1
    private var dataList: ArrayList<ShareProductData> = arrayListOf()

    private var unAdapter: OptionalUnselectedRecyclerAdapter? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            position = it.getInt(ARG_PARAM1)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View = mBinding.root

    override fun initParam() {
        super.initParam()
        EventBus.getDefault().register(this)
    }

    override fun initView() {
        super.initView()
        mBinding.myRecyclerView.layoutManager = WrapContentLinearLayoutManager(getActivity())
        mBinding.myRecyclerView.setHasFixedSize(true)
        unAdapter = OptionalUnselectedRecyclerAdapter(requireContext(), dataList)
        mBinding.myRecyclerView.adapter = unAdapter
        mBinding.myRecyclerView.setEmptyView(mBinding.tvNoAssets)
    }

    override fun initListener() {
        super.initListener()
        unAdapter?.setOnItemClickListener(object : OptionalUnselectedRecyclerAdapter.OnItemClickListener {
            @SuppressLint("NotifyDataSetChanged")
            override fun onItemClickListener(position: Int) {
                dataList.getOrNull(position)?.let { data ->
                    VAUSdkUtil.collectSymbolList.add(data)
                    dataList.removeAt(position)
                    EventBus.getDefault().post(NoticeConstants.REFRESH_MY_SYMBOLS_LIST)
                    unAdapter?.notifyDataSetChanged()
                }
            }
        })
    }

    override fun initData() {
        super.initData()

        dataList.clear()

        val shareGoodList = VAUSdkUtil.shareGoodList()

        val symbolList = shareGoodList.getOrNull(position)?.symbolList ?: return

        for1@ for (dataBean in symbolList) {

            if (dataBean.enable != "2") continue

            for (symbolData in VAUSdkUtil.collectSymbolList) {
                if (dataBean.symbol == symbolData.symbol) continue@for1
            }

            dataList.add(dataBean)

        }

        unAdapter?.notifyDataSetChanged()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(msgTag: String) {
        when (msgTag) {
            NoticeConstants.REFRESH_ADD_SYMBOLS_LIST -> {
                initData()
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        fun newInstance(position: Int) = AddSymbolsItemFragment().apply {
            arguments = Bundle().apply {
                putInt(ARG_PARAM1, position)
            }
        }
    }

}