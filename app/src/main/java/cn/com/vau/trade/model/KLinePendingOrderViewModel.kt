package cn.com.vau.trade.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.util.DealLogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import com.google.gson.Gson
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus
import java.util.concurrent.CopyOnWriteArrayList

/**
 * 挂单
 */
class KLinePendingOrderViewModel : BaseViewModel() {

    var isDataLoading = false
    var orderList = CopyOnWriteArrayList<ShareOrderData>()
    var productName: String = ""

    var orderData: ShareOrderData? = null

    val pendingOrderListLiveData = MutableLiveData<Long>()
    val pendingOrderCancelLiveData = MutableLiveData<String>()

    val updatePendingOrderSuccessLiveData by lazy {
        MutableLiveData<String>()
    }
    val hintDataDialogLiveData by lazy {
        MutableLiveData<String>()
    }
    val tokenErrorLiveData by lazy {
        MutableLiveData<String>()
    }

    /**
     * 获取挂单列表
     */
    fun getPendingOrderList() {
        if (UserDataUtil.isStLogin())
            stTradeListOrderV2()
        else
            tradeOrderPendingList()
    }

    /**
     * 取消挂单
     */
    fun pendingOrderCancel(position: Int) {
        if (UserDataUtil.isStLogin())
            stTradePositionCancel(position)
        else
            tradeOrdersCancel(position)
    }

    private fun tradeOrderPendingList() {

        if (isDataLoading) return
        isDataLoading = true

        val startTimeMillisPendingOrder = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("check pending order", startTimeMillisPendingOrder)

        val paramMap = HashMap<String, Any>()
        paramMap["serverId"] = UserDataUtil.serverId()
        paramMap["type"] = "0"
        paramMap["login"] = UserDataUtil.accountCd()
        paramMap["token"] = UserDataUtil.tradeToken()

        val dataObject = JsonObject()
        dataObject.addProperty("data", Gson().toJson(paramMap))
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet(
            { tradingService.tradeOrderPendingListApi(requestBody) },
            onSuccess = {

                isDataLoading = false

                // 归档
                if ("********" == it?.code) {
                    DealLogUtil.saveFailedDealLog(
                        it.code.toString(),
                        "check pending order",
                        startTimeMillisPendingOrder
                    )
                    pendingOrderListLiveData.value = System.currentTimeMillis()
                    return@requestNet
                }

                if ("200" != it?.code) {
                    DealLogUtil.saveFailedDealLog(
                        it?.code ?: "-1",
                        "check pending order",
                        startTimeMillisPendingOrder
                    )
                    ToastUtil.showToast(it?.info.ifNull())
                    pendingOrderListLiveData.value = System.currentTimeMillis()
                    return@requestNet
                }

                orderList.clear()
                val orderBeanList = it.obj ?: arrayListOf()
                orderList.addAll(orderBeanList.filter { product -> product.symbol == productName })

                pendingOrderListLiveData.value = System.currentTimeMillis()

                val ordersSb = StringBuilder()
                for (orderBean in orderList) {
                    ordersSb.append("#${orderBean.order},")
                }
                var ordersStr = ordersSb.toString()
                if (ordersStr.isNotEmpty()) {
                    ordersStr = ordersStr.substring(0, ordersStr.length - 1)
                }
                DealLogUtil.saveSuccessDealLog(
                    "order:$ordersStr",
                    "check pending order",
                    startTimeMillisPendingOrder
                )
            },
            onError = {
                DealLogUtil.saveFailedDealLog(
                    "-1",
                    "check pending order",
                    startTimeMillisPendingOrder
                )
                isDataLoading = false
                pendingOrderListLiveData.value = System.currentTimeMillis()
            }
        )
    }

    private fun stTradeListOrderV2() {

        if (isDataLoading) return
        isDataLoading = true

        val startTimeMillisPendingOrder = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog("pending order", startTimeMillisPendingOrder)

        requestNet(
            { stTradingService.stTradeListOrderV2Api("LIMIT", UserDataUtil.stMasterPortfolioId()) },
            onSuccess = { it ->

                isDataLoading = false

                if ("200" != it?.code) {
                    DealLogUtil.saveFailedDealLog(
                        it?.code ?: "-1", "pending order", startTimeMillisPendingOrder
                    )
                    ToastUtil.showToast(it?.msg.ifNull())
                    pendingOrderListLiveData.value = System.currentTimeMillis()
                    return@requestNet
                }

                orderList.clear()
                val orderBeanList = it.data?.positionList ?: arrayListOf()
                orderList.addAll(orderBeanList.filter { product -> product.symbol == productName })

                pendingOrderListLiveData.value = System.currentTimeMillis()

                val ordersSb = StringBuilder()
                for (orderBean in orderList) {
                    ordersSb.append("#${orderBean.stOrder},")
                }
                var ordersStr = ordersSb.toString()
                if (ordersStr.isNotEmpty()) {
                    ordersStr = ordersStr.substring(0, ordersStr.length - 1)
                }
                DealLogUtil.saveSuccessDealLog(
                    "order:$ordersStr", "pending order", startTimeMillisPendingOrder
                )

            },
            onError = {
                DealLogUtil.saveFailedDealLog("-1", "pending order", startTimeMillisPendingOrder)
                isDataLoading = false
                pendingOrderListLiveData.value = System.currentTimeMillis()
            }
        )

    }

    private fun tradeOrdersCancel(position:Int) {

        val mGson = Gson()
        val orderData = orderList.getOrNull(position) ?: return

        val paramMap = HashMap<String, Any>().apply {
            put("login", UserDataUtil.accountCd())
            put("order", orderData.order ?: "")

            put("token", UserDataUtil.tradeToken())
            put("symbol", orderData.symbol ?: "")
            put("cmd", orderData.cmd)
            put("serverId", UserDataUtil.serverId())
        }

        val dataObject = JsonObject()
        dataObject.addProperty("data", mGson.toJson(paramMap))
        val requestBody =
            dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet(
            { tradingService.tradeOrdersCancelApi(requestBody)},
            onSuccess = {
                if ("********" == it?.code) {
                    pendingOrderCancelLiveData.value = it.info
                    return@requestNet
                }

                if ("200" != it?.code) {
                    ToastUtil.showToast(it?.info.ifNull())
                    return@requestNet
                }

                pendingOrderCancelLiveData.value = "200"
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)
            },
            isShowDialog = true
        )
    }

    private fun stTradePositionCancel(position: Int) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
        jsonObject.addProperty("orderId", orderList.getOrNull(position)?.stOrder ?: "")
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet(
            { stTradingService.stTradePositionCancelApi(requestBody)},
            onSuccess = {
                if (it?.code != "200") {
                    ToastUtil.showToast(it?.msg)
                    return@requestNet
                }

                pendingOrderCancelLiveData.value = "200"
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)

            },
            isShowDialog = true
        )
    }

    fun tradeModifyOrder( jsonObject: JsonObject) {
        if (UserDataUtil.isStLogin()){
            tradeOrderUpdate(jsonObject)
        }else{
            tradeOrdersUpdate(jsonObject)
        }
    }

    /**
     * 跟单 改单
     */
    private fun tradeOrderUpdate(jsonObject: JsonObject) {
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet({
            stTradingService.tradeOrderUpdateApi(requestBody)
        }, {
            if ("200" != it.code) {
                ToastUtil.showToast(it.msg)
                return@requestNet
            }
            updatePendingOrderSuccessLiveData.value = it.info
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)
        }, isShowDialog = true)
    }

    /**
     * 非跟单改单
     */
    private fun tradeOrdersUpdate(jsonObject: JsonObject) {
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        val startTimeMillis = System.currentTimeMillis()
        requestNet({
            tradingService.tradeOrdersUpdateApi(requestBody)
        }, {
            // token过期
            if ("********" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${jsonObject.get("order")}",
                    "${it.code}",
                    "modify",
                    startTimeMillis
                )
                tokenErrorLiveData.value = it.info
                return@requestNet
            }

            if ("10500173" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${jsonObject.get("order")}",
                    "${it.code}",
                    "modify",
                    startTimeMillis
                )
                hintDataDialogLiveData.value = it.info
                return@requestNet
            }

            if ("200" != it.code) {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${jsonObject.get("order")}",
                    "${it.code}",
                    "modify",
                    startTimeMillis
                )
                ToastUtil.showToast(it.info)
                return@requestNet
            }

            DealLogUtil.saveSuccessDealLog(
                "modify order:#${jsonObject.get("order")}",
                "modify",
                startTimeMillis
            )
            updatePendingOrderSuccessLiveData.value = it.info
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)

        }, {
            DealLogUtil.saveFailedDealLog(
                "modify order:#${jsonObject.get("order")}",
                "-1",
                "modify",
                startTimeMillis
            )
        }, isShowDialog = true)
    }
}