package cn.com.vau.trade.viewmodel

import android.text.TextUtils
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.livedata.UnPeekLiveData
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.util.DealLogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.mathMul
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

/**
 * Created by array on 2025/2/14 18:16
 * Desc: 设置止盈止损
 */
class TakeProfitStopLossViewModel : BaseViewModel() {

    var orderBean: ShareOrderData? = null
    var productData: ShareProductData? = null
    var digits = 0
    var minProfit = "0.0"
    val isBuy by lazy {
        OrderUtil.isBuyOfOrder(orderBean?.cmd)
    }

    var slParam = ""
    var tpParam = ""

    val stDealSuccessLiveData = UnPeekLiveData<Boolean>()

    val tokenErrorLiveData = UnPeekLiveData<String?>()
    val hintDataLiveData = UnPeekLiveData<String>()
    val dealSuccessLiveData = UnPeekLiveData<Boolean>()

    /**
     * 设置止盈止损（跟单）
     */
    fun stTradePositionUpdate() {

        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
        jsonObject.addProperty("positionId", orderBean?.stOrder ?: "")
        jsonObject.addProperty("stopLoss", slParam)
        jsonObject.addProperty("takeProfit", tpParam)
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { stTradingService.tradePositionUpdateApi(requestBody) },
            onSuccess = {
                if (it.code != "200") {
                    ToastUtil.showToast(it.msg)
                    return@requestNet
                }

                stDealSuccessLiveData.value = true
                EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
            },
            onError = {

            },
            isShowDialog = true
        )

    }

    /**
     * 设置止盈止损
     */
    fun tradeOrdersUpdate() {
        orderBean?.order ?: return

        var handleCount = orderBean?.volume.mathMul(
            if (UserDataUtil.isMT5()) "10000" else "100"
        )
        if (handleCount.contains("."))
            handleCount = handleCount.split(".")[0]

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("price", orderBean?.openPrice)
        jsonObject.addProperty("sl", if (TextUtils.isEmpty(slParam)) "0" else slParam)
        jsonObject.addProperty("tp", if (TextUtils.isEmpty(tpParam)) "0" else tpParam)
        jsonObject.addProperty("order", orderBean?.order)
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("cmd", orderBean?.cmd)
        jsonObject.addProperty("symbol", orderBean?.symbol)
        jsonObject.addProperty("volume", handleCount)
        jsonObject.addProperty("serverId", UserDataUtil.serverId())

        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "modify order:#${orderBean?.order}  " +
                    "take profit:${if (TextUtils.isEmpty(tpParam)) "0" else tpParam}  " +
                    "stop loss:${if (TextUtils.isEmpty(slParam)) "0" else slParam}",
            "modify", startTimeMillis
        )

        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())

        val requestBody =
            jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet(
            { tradingService.tradeOrdersUpdateApi(requestBody) },
            onSuccess = {
                val baseBean = it
                when (baseBean.code) {
                    // token 过期
                    "10100051" -> {
                        DealLogUtil.saveFailedDealLog(
                            "modify order:#${orderBean?.order}",
                            "${baseBean.code}",
                            "modify",
                            startTimeMillis
                        )
                        tokenErrorLiveData.value = baseBean.info
                    }

                    "10500173" -> {
                        DealLogUtil.saveFailedDealLog(
                            "modify order:#${orderBean?.order}",
                            "${baseBean.code}",
                            "modify",
                            startTimeMillis
                        )
                        hintDataLiveData.value = baseBean.info ?: ""
                    }

                    "200" -> {
                        DealLogUtil.saveSuccessDealLog(
                            "modify order:#${orderBean?.order}",
                            "modify",
                            startTimeMillis
                        )
                        dealSuccessLiveData.value = true
                        EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_OPEN_ORDER)
                    }

                    else -> {
                        DealLogUtil.saveFailedDealLog(
                            "modify order:#${orderBean?.order}",
                            "${baseBean.code}",
                            "modify",
                            startTimeMillis
                        )
                        ToastUtil.showToast(baseBean.info)
                    }
                }
            },
            onError = {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${orderBean?.order}",
                    "-1", "modify",
                    startTimeMillis
                )
            },
            isShowDialog = true
        )

    }
}
