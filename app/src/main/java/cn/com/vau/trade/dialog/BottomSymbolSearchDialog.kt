package cn.com.vau.trade.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.DialogBottomSymbolSearchBinding
import cn.com.vau.trade.ext.SymbolSearchConstants.FROM_K_LINE
import cn.com.vau.trade.ext.SymbolSearchConstants.FROM_ORDER
import cn.com.vau.trade.ext.SymbolSearchConstants.FROM_TRADE_ORDER
import cn.com.vau.trade.ext.SymbolSearchConstants.TAG_SS_CATEGORY_FRAGMENT
import cn.com.vau.trade.ext.SymbolSearchConstants.TAG_SS_SEARCH_FRAGMENT
import cn.com.vau.trade.perform.SSDispatcherPerformance
import cn.com.vau.trade.viewmodel.SymbolSearchDialogViewModel
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.dp2px
import cn.com.vau.util.getStatusHeight
import cn.com.vau.util.screenHeight
import cn.com.vau.util.widget.dialog.base.BaseMvvmBottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * Created by array on 2025/4/24 15:24
 * Desc: 产品搜索底部弹窗
 */
@SuppressLint("ViewConstructor")
class BottomSymbolSearchDialog private constructor(
    activity: FragmentActivity,
    val from: Int,
    private val onSelectItem: ((productData: ShareProductData) -> Unit)? = null,
    private val onFollowClick: ((select: Boolean, symbol: String) -> Unit)? = null,
) : BaseMvvmBottomDialog<DialogBottomSymbolSearchBinding, SymbolSearchDialogViewModel>(
    activity = activity,
    title = null,
    viewBinding = DialogBottomSymbolSearchBinding::inflate,
) {
    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    /**弹窗layout状态管理*/
    private val dispatcherPerformance: SSDispatcherPerformance by lazy {
        SSDispatcherPerformance(activity, this, mContentBinding, mViewModel)
    }

    override fun initViewModel(): SymbolSearchDialogViewModel {
        return ViewModelProvider(getMyViewModelStoreOwner())[SymbolSearchDialogViewModel::class.java]
    }

    override fun onCreate() {
        super.onCreate()
        addPerformances()
    }

    override fun setContentView() {
        super.setContentView()
        initView()
        initListener()
    }

    fun initView() {

    }

    fun initListener() {
        /** 关闭弹窗-事件流 */
        lifecycleScope.launch {
            mViewModel.dismissDialog.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
                .collectLatest {
                    if (it) {
                        dismiss()
                    }
                }
        }

        /** 关闭键盘-事件流 */
        lifecycleScope.launch {
            mViewModel.hideSoftInput.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
                .collectLatest {
                    if (it) {
                        KeyboardUtil.hideSoftInput(hostWindow)
                    }
                }
        }

        /** 选择切换产品-事件流 */
        lifecycleScope.launch {
            mViewModel.selectProduct.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
                .collectLatest {
                    when (from) {
                        FROM_K_LINE -> {
                            dismiss()
                            onSelectItem?.invoke(it)
                        }

                        FROM_ORDER -> {
                            dismiss()
                            onSelectItem?.invoke(it)
                        }

                        FROM_TRADE_ORDER -> {
                            dismiss()
                            onSelectItem?.invoke(it)
                        }
                    }
                }
        }

        /** 选择切换产品-收藏自选流 */
        lifecycleScope.launch {
            mViewModel.followProduct.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED)
                .collectLatest {
                    onFollowClick?.invoke(it.isSelected, it.symbol)
                }
        }

    }

    override fun onDismiss() {
        super.onDismiss()
        val fragmentManager = activity.supportFragmentManager
        val transaction = fragmentManager.beginTransaction()
        val categoryFragment = fragmentManager.findFragmentByTag(TAG_SS_CATEGORY_FRAGMENT)
        val searchFragment = fragmentManager.findFragmentByTag(TAG_SS_SEARCH_FRAGMENT)
        if (categoryFragment != null && !categoryFragment.isRemoving && categoryFragment.isAdded) {
            transaction.remove(categoryFragment)
        }
        if (searchFragment != null && !searchFragment.isRemoving && searchFragment.isAdded) {
            transaction.remove(searchFragment)
        }
        transaction.commit()

        KeyboardUtil.hideSoftInput(hostWindow)
    }

    override fun getPopupHeight(): Int {
        return screenHeight - context.getStatusHeight() - 44.dp2px()
        //screenHeight - context.getStatusHeight() - 203.dp2px()
    }

    /**
     * 添加功能模块
     */
    private fun addPerformances() {
        performManager.addPerformance(dispatcherPerformance)
    }

    class Builder(activity: Activity) : IBuilder<DialogBottomSymbolSearchBinding, Builder>(activity) {

        private var from: Int = FROM_K_LINE
        private var onSelectItem: ((productData: ShareProductData) -> Unit)? = null
        private var onFollowClick: ((select: Boolean, symbol: String) -> Unit)? = null

        fun setFrom(from: Int): Builder {
            this.from = from
            return this
        }

        fun setOnSelectItem(onSelectItem: ((productData: ShareProductData) -> Unit)?): Builder {
            this.onSelectItem = onSelectItem
            return this
        }

        fun setOnFollowClick(onFollowClick: ((select: Boolean, symbol: String) -> Unit)?): Builder {
            this.onFollowClick = onFollowClick
            return this
        }

        override fun createDialog(context: Context): IDialog<DialogBottomSymbolSearchBinding> {
            return BottomSymbolSearchDialog(context as FragmentActivity, from, onSelectItem, onFollowClick)
        }

        override fun build(): BottomSymbolSearchDialog {
            return super.build() as BottomSymbolSearchDialog
        }
    }

}

