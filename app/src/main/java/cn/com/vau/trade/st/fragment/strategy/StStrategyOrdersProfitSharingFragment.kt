package cn.com.vau.trade.st.fragment.strategy

import android.annotation.SuppressLint
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.FragmentStStrategyOrdersProfitSharingBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.trade.st.model.StStrategyOrdersViewModel
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.BottomInfoListDialog

/**
 * 跟随策略 分润统计
 */
class StStrategyOrdersProfitSharingFragment : BaseMvvmBindingFragment<FragmentStStrategyOrdersProfitSharingBinding>() {

    private val mViewModel by activityViewModels<StStrategyOrdersViewModel>()

    private val currencyType = UserDataUtil.currencyType()

    @SuppressLint("SetTextI18n")
    override fun initView() {
    }

    @SuppressLint("SetTextI18n")
    override fun initData() {
        super.initData()
        mViewModel.stProfitSharingProfileFollowerPortfolio()
    }

    @SuppressLint("SetTextI18n")
    override fun createObserver() {
        super.createObserver()

        mViewModel.responseLiveData.observe(this) {
            if (it == getString(R.string.profit_sharing)) {
                mBinding.mRefreshLayout.finishRefresh(Constants.finishRefreshOrMoreTime)
            }
        }

        mViewModel.stProfitSharingProfileFollowerPortfolioLiveData.observe(this) {
            // 可分润收益
            mBinding.tvEligibleProfitsForSharing.text = "${(it.netProfit ?: "").numCurrencyFormat()} $currencyType".arabicReverseTextByFlag(" ").ifNull()
            // 已支付分润
            mBinding.tvTotalSharedProfit.text = "${(it.totalSharedProfit ?: "").numCurrencyFormat()} $currencyType".arabicReverseTextByFlag(" ").ifNull()
            // 历史最高收益
            mBinding.tvHighWaterMark.text = "${(it.lastTotalProfit ?: "").numCurrencyFormat()} $currencyType".arabicReverseTextByFlag(" ").ifNull()
            // 分润比例
            mBinding.tvProfitSharingRatio.text = "${it.profitShareRatioPercentage.mathMul("100").numFormat(0)}%"
            // 结算周期
            mBinding.tvSettlementFrequency.text = getString(
                when (it.settlementFrequency ?: 0) {
                    1 -> R.string.daily
                    2 -> R.string.weekly
                    else -> R.string.monthly
                }
            )
        }

    }

    @SuppressLint("SetTextI18n")
    override fun initListener() {
        super.initListener()

        mBinding.tvProfitSharingSummaryTitle.clickNoRepeat {
            BottomInfoListDialog.Builder(requireActivity())
                .setTitle(getString(R.string.profit_sharing_summary))
                .setDataList(
                    arrayListOf(
                        HintLocalData(
                            getString(R.string.eligible_profits_for_sharing),
                            getString(R.string.glossary_copiers_eligible_profits_for_sharing)
                        ),
                        HintLocalData(
                            getString(R.string.total_shared_profit),
                            getString(R.string.glossary_copiers_total_shared_profit)
                        ),
                        HintLocalData(
                            getString(R.string.high_water_mark),
                            getString(R.string.glossary_copiers_high_water_mark)
                        ),
                        HintLocalData(
                            getString(R.string.profit_sharing_ratio),
                            getString(R.string.glossary_profit_sharing_ratio)
                        ),
                        HintLocalData(
                            getString(R.string.settlement_frequency),
                            getString(R.string.the_profit_sharing_amount_settlement_cycle)
                        )
                    )
                )
                .build()
                .show()
        }
        mBinding.tvViewStatement.clickNoRepeat {
            NewHtmlActivity.openActivity(context, url = UrlConstants.HTML_FOLLOWERSTMT)
        }

        mBinding.mRefreshLayout.setOnRefreshListener {
            mViewModel.stProfitSharingProfileFollowerPortfolio()
        }

    }
}