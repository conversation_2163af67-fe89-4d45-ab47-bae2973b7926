package cn.com.vau.trade.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.state.ListUIState
import cn.com.vau.data.strategy.SearchStrategyBean
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import kotlin.collections.List

/**
 * @description:
 * @author: GG
 * @createDate: 2024 10月 21 15:51
 * @updateUser:
 * @updateDate: 2024 10月 21 15:51
 */
class KLineCopyViewModel : BaseViewModel() {

    val uiListLiveData: MutableLiveData<ListUIState<List<SearchStrategyBean>?>> by lazy { MutableLiveData() }

    fun getTopTraderData(prodName: String) {
        requestNet({
            val jsonObject = JsonObject()
            val accountId = if (!UserDataUtil.isStLogin()) {
                ""
            } else {
                UserDataUtil.stAccountId()
            }
            jsonObject.addProperty("accountId", accountId)
            jsonObject.addProperty("numberOfSignals", 10)
            jsonObject.addProperty("pageNumber", 1)
            jsonObject.addProperty("productName", prodName)
            val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
            stTradingService.signalListMonthlySignalsByProduct(requestBody)
        }, onSuccess = { dataBean ->
            if (!dataBean.isSuccess()) {
                return@requestNet
            }
            val objList = dataBean.data
            if (objList.isNullOrEmpty()) {
                uiListLiveData.value = ListUIState.Empty
                return@requestNet
            }

            uiListLiveData.value = ListUIState.RefreshSuccess(objList)
        }, {
            uiListLiveData.value = ListUIState.Error()
        })
    }

}