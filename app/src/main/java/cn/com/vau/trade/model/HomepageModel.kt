package cn.com.vau.trade.model

import HomepageContract
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.*
import cn.com.vau.data.init.MaintenanceBean
import io.reactivex.disposables.Disposable

/**
 * Created by roy on 2018/12/1.
 * 订单持仓
 */
class HomepageModel : HomepageContract.Model {

    override fun queryMT4AccountState(map: HashMap<String, String>, baseObserver: BaseObserver<MT4AccountTypeBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().crmGetMt4AccountApplyType(map), baseObserver)
        return baseObserver.disposable
    }

    override fun accountOpeningGuide(map: HashMap<String, Any>, baseObserver: BaseObserver<AccountOpeningGuideBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().accountOpeningGuide(map), baseObserver)
        return baseObserver.disposable
    }

    override fun userQueryUserLevel(map: HashMap<String, Any>, baseObserver: BaseObserver<KycVerifyLevelDataBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().userQueryUserLevel(map), baseObserver)
        return baseObserver.disposable
    }

    override fun queryVirtualAccount(baseObserver: BaseObserver<CheckVirtualAccountBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().queryVirtualAccount(), baseObserver)
        return baseObserver.disposable
    }

    override fun queryFirstAccountAuditStatus(baseObserver: BaseObserver<AccountAuditBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().queryFirstAccountAuditStatus(), baseObserver)
        return baseObserver.disposable
    }

    override fun checkMaintenanceV2(
        imgType: Int,
        baseObserver: BaseObserver<MaintenanceBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().maintenanceV2(imgType), baseObserver)
        return baseObserver.disposable
    }

    override fun imgCloseApi(map: HashMap<String, Any>, baseObserver: BaseObserver<DataObjStringBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().imgCloseApi(map), baseObserver)
        return baseObserver.disposable
    }
}