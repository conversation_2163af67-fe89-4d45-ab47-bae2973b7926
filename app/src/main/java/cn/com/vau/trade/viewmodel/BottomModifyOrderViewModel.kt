package cn.com.vau.trade.viewmodel

import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.util.DealLogUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.mathMul
import cn.com.vau.util.numCurrencyFormat
import com.google.gson.JsonObject
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

/**
 * Created by array on 2025/5/27 15:39
 * Desc: 改单
 */
class BottomModifyOrderViewModel : BaseViewModel() {

    val updatePendingOrderSuccessLiveData by lazy {
        MutableLiveData<String>()
    }
    val hintDataDialogLiveData by lazy {
        MutableLiveData<String>()
    }
    val tokenErrorLiveData by lazy {
        MutableLiveData<String>()
    }

    /** 关闭键盘 */
    private val _hideSoftInput = MutableSharedFlow<Boolean>()
    val hideSoftInput = _hideSoftInput.asSharedFlow()

    /**
     * 挂单价格（停损或者限价单的Stop Price、Limit Price）
     */
    private val _atPriceFlow = MutableSharedFlow<String>()
    val atPriceFlow: SharedFlow<String> = _atPriceFlow.asSharedFlow()

    /**
     * 限价价格（停损限价单的Limit Price）
     */
    private val _limitPriceFlow = MutableSharedFlow<String>()
    val limitPriceFlow: SharedFlow<String> = _limitPriceFlow.asSharedFlow()

    var orderBean: ShareOrderData? = null
    var productBean: ShareProductData? = null
    var digits = 0
    var minProfit = "0.0"
    val isBuy by lazy {
        OrderUtil.isBuyOfOrder(orderBean?.cmd)
    }

    fun notifyAtPriceTextChange(atPrice: String) {
        viewModelScope.launch {
            _atPriceFlow.emit(atPrice)
        }
    }

    fun notifyLimitPriceTextChange(limitPrice: String) {
        viewModelScope.launch {
            _limitPriceFlow.emit(limitPrice)
        }
    }

    /**
     * 关闭键盘
     */
    fun hideSoftInput() {
        viewModelScope.launch {
            _hideSoftInput.emit(true)
        }
    }

    /**
     * 可用保证金
     */
    fun getFreeMargin(): String {
        val isStLogin = UserDataUtil.isStLogin()
        return if (isStLogin)
            VAUSdkUtil.stShareAccountBean().freeMargin.numCurrencyFormat()
        else
            VAUSdkUtil.shareAccountBean().freeMargin.numCurrencyFormat()
    }

    /**
     * 非跟单改单
     */
    fun tradeOrdersUpdate(
        atPrice: String,
        limitPrice: String,
        takeProfitPrice: String,
        stopLossPrice: String,
    ): JsonObject {
        if (orderBean == null || productBean == null) {
            return JsonObject()
        }

        val jsonObject = JsonObject()
        jsonObject.addProperty("login", UserDataUtil.accountCd())
        jsonObject.addProperty("order", orderBean?.order)
        jsonObject.addProperty("price", atPrice)
        jsonObject.addProperty("tp", if (TextUtils.isEmpty(takeProfitPrice)) "0" else takeProfitPrice)
        jsonObject.addProperty("sl", if (TextUtils.isEmpty(stopLossPrice)) "0" else stopLossPrice)
        jsonObject.addProperty("st", VAUSdkUtil.serverTimeMillis)
        jsonObject.addProperty("token", UserDataUtil.tradeToken())
        jsonObject.addProperty("cmd", orderBean?.cmd)
        jsonObject.addProperty("symbol", orderBean?.symbol)
        jsonObject.addProperty("slPrice", limitPrice)

        var handleCount = orderBean?.volume.mathMul(
            if (UserDataUtil.isMT5()) "10000" else "100"
        )
        if (handleCount.contains("."))
            handleCount = handleCount.split(".")[0]
        jsonObject.addProperty("volume", handleCount)

        jsonObject.addProperty("serverId", UserDataUtil.serverId())


        val jsonObject2 = JsonObject()
        jsonObject2.addProperty("data", jsonObject.toString())
        return jsonObject2

    }

    fun tradeOrdersUpdateRequest(
        atPrice: String,
        limitPrice: String,
        takeProfitPrice: String,
        stopLossPrice: String,
        jsonObject: JsonObject
    ) {
        val startTimeMillis = System.currentTimeMillis()
        DealLogUtil.saveStartDealLog(
            "modify order:#${orderBean?.order}  " +
                    "at price:${atPrice}  " +
                    "take profit:${if (TextUtils.isEmpty(takeProfitPrice)) "0" else takeProfitPrice}  " +
                    "stop loss:${if (TextUtils.isEmpty(stopLossPrice)) "0" else stopLossPrice}",
            "modify",
            startTimeMillis
        )
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({
            tradingService.tradeOrdersUpdateApi(requestBody)
        }, {

            // token过期
            if ("10100051" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${orderBean?.order}",
                    "${it.code}",
                    "modify",
                    startTimeMillis
                )
                tokenErrorLiveData.value = it.info
                return@requestNet
            }

            if ("10500173" == it.code) {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${orderBean?.order}",
                    "${it.code}",
                    "modify",
                    startTimeMillis
                )
                hintDataDialogLiveData.value = it.info
                return@requestNet
            }

            if ("200" != it.code) {
                DealLogUtil.saveFailedDealLog(
                    "modify order:#${orderBean?.order}",
                    "${it.code}",
                    "modify",
                    startTimeMillis
                )
                ToastUtil.showToast(it.info)
                return@requestNet
            }

            DealLogUtil.saveSuccessDealLog(
                "modify order:#${orderBean?.order}",
                "modify",
                startTimeMillis
            )
            updatePendingOrderSuccessLiveData.value = it.info
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)

        }, {
            DealLogUtil.saveFailedDealLog(
                "modify order:#${orderBean?.order}",
                "-1",
                "modify",
                startTimeMillis
            )
        }, isShowDialog = true)
    }

    fun tradeOrderUpdate(
        atPrice: String,
        takeProfitPrice: String,
        stopLossPrice: String,
    ): JsonObject {
        val jsonObject = JsonObject()
        jsonObject.addProperty("portfolioId", UserDataUtil.stMasterPortfolioId())
        jsonObject.addProperty("orderId", orderBean?.stOrder ?: "")
        jsonObject.addProperty("priceOrder", atPrice)
        jsonObject.addProperty("stopLoss", stopLossPrice)
        jsonObject.addProperty("takeProfit", takeProfitPrice)
        jsonObject.addProperty("volume", orderBean?.volume)
        jsonObject.addProperty(
            "tradeAction",
            if (OrderUtil.isBuyOfOrder(orderBean?.cmd)) "BUY" else "SELL"
        )
        return jsonObject
    }

    fun stTradeOrdersUpdateRequest(jsonObject: JsonObject) {
        val requestBody =
            jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())

        requestNet({
            stTradingService.tradeOrderUpdateApi(requestBody)
        }, {
            if ("200" != it.code) {
                ToastUtil.showToast(it.msg)
                return@requestNet
            }
            updatePendingOrderSuccessLiveData.value = it.info
            EventBus.getDefault().post(NoticeConstants.WS.CHANGE_OF_PENDING_ORDER)
        }, isShowDialog = true)
    }
}
