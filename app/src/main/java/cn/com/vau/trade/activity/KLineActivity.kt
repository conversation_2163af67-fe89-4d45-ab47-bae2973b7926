package cn.com.vau.trade.activity

import android.content.Intent
import android.content.res.Configuration
import android.os.Build
import android.os.Bundle
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import android.widget.TextView
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.*
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.*
import cn.com.vau.common.view.popup.AttchKLineSymbolPopup
import cn.com.vau.common.view.popup.BottomKLineNewGuideDialog
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.ActivityKlineBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.setting.activity.LandKlineChartNewFragment
import cn.com.vau.page.setting.viewmodel.KLineChartNewViewModel
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.profile.activity.pricealert.activity.PriceAlterListActivity
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.dialog.BottomSymbolSearchDialog
import cn.com.vau.trade.ext.SymbolSearchConstants.FROM_K_LINE
import cn.com.vau.trade.fragment.kchart.KLineAnalysisFragment
import cn.com.vau.trade.fragment.kchart.KLineChartFragment
import cn.com.vau.trade.fragment.kchart.KLineChartNewFragment
import cn.com.vau.trade.fragment.kchart.KLineCopyFragment
import cn.com.vau.trade.fragment.kchart.KLineInfoFragment
import cn.com.vau.trade.fragment.kchart.KLineOrdersFragment
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.kchart.KlineSettingConfigManager
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.util.AppUtil
import cn.com.vau.util.BitmapUtil.loadBitmapFromView
import cn.com.vau.util.TabType
import cn.com.vau.util.addFragment
import cn.com.vau.util.arabicText
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.ifNull
import cn.com.vau.util.init
import cn.com.vau.util.removeFragment
import cn.com.vau.util.setVp
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.tracking.SensorsHelper.getTradeType
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.enums.PopupAnimation
import com.lxj.xpopup.enums.PopupPosition
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class KLineActivity : BaseMvvmActivity<ActivityKlineBinding, KLineViewModel>(), SDKIntervalCallback {

    val red: Int by lazy { ContextCompat.getColor(this, R.color.cf44040) }
    val green: Int by lazy { ContextCompat.getColor(this, R.color.c00c79c) }
    private val klineViewModel: KLineChartNewViewModel by lazy {
        ViewModelProvider(this)[KLineChartNewViewModel::class.java]
    }
    val titleList by lazy {
        mutableListOf<String>().apply {
            // K Chart
            add(getString(R.string.chart))
            // Copy Tab根据接口加载
            // 分析师观点
            add(getString(R.string.analysis))
            // 订单Tab未登录不显示
            if (UserDataUtil.isLogin()) {
                add(getString(R.string.orders))
            }
            // 产品属性
            add(getString(R.string.info))
        }
    }
    val fragmentList by lazy {
        mutableListOf<Fragment>().apply {
            // K Chart
            add(if (KlineSettingConfigManager.getEnableNewKline()) kLineFragment else KLineChartFragment.instance())
            // Copy Fragment根据接口加载
            // KLineCopyFragment
            // 分析师观点
            add(KLineAnalysisFragment.newInstance())
            // 订单Tab未登录不显示
            if (UserDataUtil.isLogin()) {
                add(KLineOrdersFragment.newInstance())
            }
            // 产品属性
            add(KLineInfoFragment.newInstance())
        }
    }
    var isShowTitle = false

    private var mSearchDialog: BottomSymbolSearchDialog? = null

    private val kLineFragment by lazy {
        KLineChartNewFragment.instance()
    }

    private val landKLineFragment by lazy {
        LandKlineChartNewFragment.instance()
    }

    private val performManager by lazy {
        PerformManager(this)
    }
    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    override fun onCallback() {
        mViewModel.refreshCallBack.value = true
        updateTitleInfo()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        if(KlineSettingConfigManager.getEnableNewKline()) {
            klineViewModel.isSwitching = true
        } else {
            mViewModel.isSwitching = true
        }
        val symbol = intent?.extras?.getString(Constants.PARAM_PRODUCT_NAME, "") ?: ""
//        Log.i("wj", "[KlineActivity] onNewIntent: symbol: $symbol")
        mViewModel.selectedOrderNo = intent?.extras?.getString(Constants.PARAM_ORDER_NUMBER, "") ?: ""
        mViewModel.isFcm = intent?.extras?.getBoolean(Constants.PARAM_IS_FCM, false) == true
        // 没加mViewModel.typeFrom的传值判断是因为有可能会从订单页过来，切换，因为从order过来再次进下单页是会直接关闭的
        val data = VAUSdkUtil.symbolList().find { it.symbol == symbol }
        if (data == null) return
        if (mViewModel.data == data) {
            if(KlineSettingConfigManager.getEnableNewKline()) {
                klineViewModel.isSwitching = false
            } else {
                mViewModel.isSwitching = false
            }
            return
        }
        mViewModel.data = data
        mViewModel.symbol = symbol
        EventBus.getDefault().post(NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT)
        switchSymbol(data)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        KlineSettingConfigManager.registerKlineActivityCount()
        mViewModel.symbol = intent.extras?.getString(Constants.PARAM_PRODUCT_NAME, "") ?: ""
//        Log.i("wj", "initParam: mViewModel.symbol: ${mViewModel.symbol}")
        mViewModel.selectedOrderNo = intent.extras?.getString(Constants.PARAM_ORDER_NUMBER, "") ?: ""
        mViewModel.isFcm = intent.extras?.getBoolean(Constants.PARAM_IS_FCM, false) == true
        mViewModel.typeFrom = intent.extras?.getString(Constants.IS_FROM, "") ?: ""
        // 从用户产品组查找该产品，未找到则退出页面
        mViewModel.data = VAUSdkUtil.symbolList().find { it.symbol == mViewModel.symbol }
        if (mViewModel.data == null) {
            finish()
            return
        }
        if (mViewModel.isFcm && "0" == mViewModel.data?.enable) {
            finish()
            return
        }
        SDKIntervalUtil.instance.addCallBack(this)
        mViewModel.isAddOptional = VAUSdkUtil.collectSymbolList.any { it.symbol == mViewModel.symbol }
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initView() {
        mBinding.ivAlert.isVisible = !KlineSettingConfigManager.getEnableNewKline()
        mBinding.tvProductName.text = mViewModel.symbol
        mBinding.mViewPager.init(fragmentList, titleList, supportFragmentManager, this)
        mBinding.mViewPager.offscreenPageLimit = fragmentList.size
        mBinding.mViewPager.isUserInputEnabled = false
        mBinding.mTabLayout.setVp(mBinding.mViewPager, titleList, TabType.LINE_INDICATOR, selectCallback = { position ->
            if (position != 0) {
                mViewModel.showTitleLiveData.value = true
            } else {
                mViewModel.showTitleLiveData.value = getChartFragment()?.isShowTitle.ifNull()
            }
            // 埋点
            val tvTab = mBinding.mTabLayout.getChildAt(position)?.findViewById<TextView>(R.id.tvTab)
            val tabName = when (tvTab?.text) {
                getString(R.string.chart) -> "Chart"
                getString(R.string.copy) -> "Copy"
                getString(R.string.analysis) -> "Analysis"
                getString(R.string.orders) -> "Orders"
                getString(R.string.info) -> "Info"
                else -> ""
            }
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_PAGE_TAB_CLICK, JSONObject().apply {
                put(SensorsConstant.Key.TAB_NAME, tabName)
            })
        })
        initEditProdBg()
        addPerformance()
        mViewModel.getTopTraderData()
        // 检查版本 是否需要弹出产品名搜索气泡
        checkSymbolGuide()
    }

    private fun addPerformance() {
        performManager.addPerformance(tradePermissionPerformance)
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.recommendRequestLiveData.observe(this){
            mBinding.overView.isVisible = false
        }
        // 是否展示Title
        mViewModel.showTitleLiveData.observe(this) {
            isShowTitle = it
            mBinding.tvSellPrice.isVisible = it
        }
        // 跟单推荐
        mViewModel.recommendLiveData.observe(this) {
            if (it) {
                mBinding.mViewPager.addFragment(KLineCopyFragment.newInstance(), getString(R.string.copy), 1)
            } else {
                mBinding.mViewPager.removeFragment(getString(R.string.copy))
            }
        }
        if(KlineSettingConfigManager.getEnableNewKline()) {
            klineViewModel.screenChangeNewGuideLiveData.observe(this) {
                BottomKLineNewGuideDialog.Builder(this)
                    .build()
                    .show()
            }
        }
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                when (it) {
                    NoticeConstants.SEND_EVENT_TAG_KLINE_REFRESH_OPTIONAL -> {
                        initEditProdBg()
                    }
                }
            }
        }
    }

    override fun initListener() {
        super.initListener()
        if(KlineSettingConfigManager.getEnableNewKline()) {
            lifecycle.addObserver(klineViewModel)
        }
        mBinding.ivLeft.setOnClickListener {
            finish()
        }
        // 产品名搜索
        mBinding.tvProductName.clickNoRepeat {
            if (!UserDataUtil.isLogin()) {
                openActivity(LoginActivity::class.java)
                return@clickNoRepeat
            }
            if(KlineSettingConfigManager.getEnableNewKline()) {
                klineViewModel.resetLineLiveData.postValue(false)
            }
            showSearchDialog()
        }
        // 分享
        mBinding.ivShare.clickNoRepeat {
            if (!UserDataUtil.isLogin()) {
                openActivity(LoginActivity::class.java)
                return@clickNoRepeat
            }
            if(KlineSettingConfigManager.getEnableNewKline()) {
                klineViewModel.resetLineLiveData.postValue(false)
            }
            fun share() {
                ShareHelper.kLineShare(this, kLineBitmap = loadBitmapFromView(mBinding.root), symbol = mViewModel.data?.symbol, dismissCallback = {
                    mViewModel.shareLiveData.value = false
                })
                mViewModel.shareLiveData.value = true
                val bundle = Bundle()
                bundle.putString(BuryPointConstant.PositionType.KEY_POSITION, BuryPointConstant.PositionType.K_LINE)
                LogEventUtil.setLogEvent(BuryPointConstant.V342.GENERAL_SHARE_BUTTON_CLICK, bundle)
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { share() }) return@clickNoRepeat
            }
            share()
        }
        // 价格提醒
        mBinding.ivAlert.clickNoRepeat {
            if(KlineSettingConfigManager.getEnableNewKline()) {
               return@clickNoRepeat
            }
            if (!UserDataUtil.isLogin()) {
                openActivity(LoginActivity::class.java)
                return@clickNoRepeat
            }
            fun alert() {
                openActivity(PriceAlterListActivity::class.java, Bundle().apply {
                    putString(Constants.PARAM_PRODUCT_NAME, mViewModel.symbol)
                })
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { alert() }) return@clickNoRepeat
            }
            alert()
        }
        // 自选收藏
        mBinding.ivOptional.setOnClickListener {
            if (!UserDataUtil.isLogin()) {
                openActivity(LoginActivity::class.java)
                return@setOnClickListener
            }
            fun optional() {
                if (mViewModel.isAddOptional) {
                    mViewModel.updOptionalProd(0)
                } else {
                    mViewModel.updOptionalProd(1)
                }
                // 神策埋点，按钮点击事件
                sensorsTrack(true, "Wishlist")
            }
            tradePermissionPerformance.run {
                if (handleTradeBlockType { optional() }) return@setOnClickListener
            }
            optional()

        }

        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                if (mSearchDialog?.isShowDialog() == true) {
                    dismissSearchDialog()
                    return
                }
                finish()
            }
        })
    }

    fun initEditProdBg() {
        mBinding.ivOptional.setImageDrawable(
            ContextCompat.getDrawable(
                this,
                if (mViewModel.isAddOptional) R.drawable.bitmap1_favorite_cf44040 else R.drawable.draw_bitmap1_favorite_c1e1e1e_cebffffff
            )
        )
    }

    fun switchSymbol(data: ShareProductData) {
        mBinding.tvProductName.text = data.symbol
        mViewModel.isAddOptional = VAUSdkUtil.collectSymbolList.any { it.symbol == mViewModel.symbol }
        initEditProdBg()
        mViewModel.getTopTraderData()
    }

    private fun updateTitleInfo() {
        val data = mViewModel.data
        if (data != null && isShowTitle) {

            val rose = data.rose
            val add = if (rose > 0) "+" else ""
            val diff = data.diff
            val rateColor = if (diff < 0) {
                red
            } else {
                green
            }
            mBinding.tvSellPrice.text = buildSpannedString {
                color(rateColor) {
                    append(if (data.bidUI == "-") Constants.DOUBLE_LINE else data.bidUI)
                    val roseUI = (add + data.roseUI + "%").arabicText()
                    append(" $roseUI")
                }
            }
        }
    }

    private fun checkSymbolGuide() {
        if (!SpManager.getKlineSymbolGuide() && AppUtil.getVersionName() == "3.61.0") {
            XPopup.Builder(this)
                .isDestroyOnDismiss(false)
                .atView(mBinding.tvProductName)
                .hasShadowBg(false)
                .popupPosition(PopupPosition.Bottom)
                .popupAnimation(PopupAnimation.ScrollAlphaFromTop)
                .borderRadius(8f.dp2px())
//            .offsetX(24.dp2px())
                .asCustom(AttchKLineSymbolPopup(this)).show()
        }
    }

    private fun showSearchDialog() {
        mSearchDialog = BottomSymbolSearchDialog.Builder(this)
            .moveUpToKeyboard(false)
            .setViewMode(true)
            .setDestroyOnDismiss(true)
            .setFrom(FROM_K_LINE)
            .setOnSelectItem {
                lifecycleScope.launch {
                    delay(500)
                    if (isDestroyed) return@launch
                    val bundle = Bundle()
                    bundle.putString(Constants.PARAM_PRODUCT_NAME, it.symbol)
                    openActivity(KLineActivity::class.java, bundle)
                }
            }
            .setOnFollowClick { select, symbol ->
                mViewModel.isAddOptional = VAUSdkUtil.collectSymbolList.any { it.symbol == mViewModel.symbol }
                initEditProdBg()
            }
            .build()
        mSearchDialog?.showDialog()
    }

    private fun dismissSearchDialog() {
        mSearchDialog?.dismissDialog()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && data != null) {
            val showNewGuide = data.getBooleanExtra("showNewGuide", false)
            if (showNewGuide) {
                BottomKLineNewGuideDialog.Builder(this)
                    .build()
                    .show()
            }
        }
    }

    fun getChartFragment(): KLineChartFragment? {
        fragmentList.elementAtOrNull(0)?.let {
            if (it is KLineChartFragment) {
                return it
            }
            return null
        }
        return null
    }

    /**
     * 神策自定义埋点(v3500)
     */
    fun sensorsTrack(isClickBtnTrack: Boolean, buttonName: String?) {
        try {
            val properties = JSONObject()
            properties.put(SensorsConstant.Key.TRADE_TYPE, getTradeType()) // 交易类型
            properties.put(SensorsConstant.Key.IS_OPTIONAL, if (mViewModel.isAddOptional) 1 else 0) // 是否自选
            properties.put(SensorsConstant.Key.PRODUCT_GROUP, "") // 交易产品组
            properties.put(SensorsConstant.Key.PRODUCT_SYMBOL, mViewModel.data?.symbol) // 交易产品
            if (isClickBtnTrack) {
                properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
                properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
                properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
                properties.put(SensorsConstant.Key.MKT_ID, "") // 素材id
                properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
                properties.put(SensorsConstant.Key.MKT_RANK, "") // 素材排序
                properties.put(SensorsConstant.Key.TARGET_URL, "") // 跳转链接
                // 交易产品详情页点击 -> 交易产品详情页点击时触发
                SensorsDataUtil.track(SensorsConstant.V3500.PRODUCT_DETAIL_PAGE_CLICK, properties)
            } else {
                // 交易产品详情页浏览 -> 交易产品详情页加载完成时触发
                SensorsDataUtil.track(SensorsConstant.V3500.PRODUCT_DETAIL_PAGE_VIEW, properties)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onDestroy() {
        if(KlineSettingConfigManager.getEnableNewKline()) {
            lifecycle.removeObserver(klineViewModel)
        }
        SDKIntervalUtil.instance.removeCallBack(this)
        KLineDataUtils.startWithSubCross = false
        KLineDataUtils.isStartActivity = false
        KLineDataUtils.isJumpToHKLine = false
        KLineDataUtils.selectedOrderNo = "0"
        mBinding.mViewPager.adapter = null
        EventBus.getDefault().unregister(this)
        if(KlineSettingConfigManager.getEnableNewKline()) {
            klineViewModel.updateAndSaveKLineSettingData()
        } else {
            if (KLineDataUtils.userData != null) {
                KLineDataUtils.userData.save()
            }
        }
        KlineSettingConfigManager.unRegisterKlineActivityCount()
        super.onDestroy()
    }

    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        if(!KlineSettingConfigManager.getEnableNewKline()) {
            return
        }
        val fm = supportFragmentManager
        if (fm.isStateSaved) return
        when (newConfig.orientation) {
            Configuration.ORIENTATION_LANDSCAPE -> {
                setLandTheme()
                klineViewModel.isScreenPortrait = false
                mBinding.flLandKLineContainer.visibility = View.VISIBLE
                fm.beginTransaction()
                    .replace(R.id.flLandKLineContainer, landKLineFragment)
                    .addToBackStack("LANDSCAPE_TAG")
                    .commit()
                if (kLineFragment.isAdded && kLineFragment.isResumed) {
                    fm.beginTransaction().apply {
                        setMaxLifecycle(kLineFragment, Lifecycle.State.STARTED)
                        commitNow()
                    }
                }
            }
            Configuration.ORIENTATION_PORTRAIT -> {
                setPortraitTheme()
                klineViewModel.isScreenPortrait = true
                mBinding.flLandKLineContainer.visibility = View.GONE
                fm.popBackStack("LANDSCAPE_TAG", FragmentManager.POP_BACK_STACK_INCLUSIVE)
                if (kLineFragment.isAdded) {
                    fm.beginTransaction().apply {
                        setMaxLifecycle(kLineFragment, Lifecycle.State.RESUMED)
                        commitNow()
                    }
                }
            }
        }
    }

    private fun setLandTheme() {
        with(window) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                setDecorFitsSystemWindows(true)
                insetsController?.apply {
                    hide(WindowInsets.Type.statusBars())
                    systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                }
            } else {
                decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                attributes = attributes.apply {
                    layoutInDisplayCutoutMode =
                        WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_SHORT_EDGES
                }
            }
            setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
            setFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON, WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    private fun setPortraitTheme() {
        with(window) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                setDecorFitsSystemWindows(true)
                insetsController?.apply {
                    show(WindowInsets.Type.statusBars())
                    systemBarsBehavior = WindowInsetsController.BEHAVIOR_DEFAULT
                }
            } else {
                decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                attributes = attributes.apply {
                    layoutInDisplayCutoutMode =
                        WindowManager.LayoutParams.LAYOUT_IN_DISPLAY_CUTOUT_MODE_DEFAULT
                }
            }
            window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
            window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    companion object {
        @JvmStatic
        fun getPointAccountType(): String {
            if (UserDataUtil.isLogin()) {
                return if (UserDataUtil.isDemoAccount()) {
                    BuryPointConstant.AccountType.DEMO
                } else {
                    BuryPointConstant.AccountType.LIVE
                }
            }
            return BuryPointConstant.AccountType.NOLOGIN
        }
    }
}

//class TestAdapter: BaseQuickAdapter<ShareProductData, BaseViewHolder>(R.layout.item_search_symbol) {
//    override fun convert(holder: BaseViewHolder, item: ShareProductData) {
//        holder.setText(R.id.tv_name, item.symbol)
//        holder.setText(R.id.tv_content, item.description.ifNull())
//    }
//}