package cn.com.vau.trade.model

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.init.TradeAccountLoginBean
import cn.com.vau.data.trade.StTradePositionUpdateBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * Created by roy on 2018/12/1.
 * 订单持仓
 */
class OpenTradesModel : OpenTradesContract.Model {

    override fun userSetItemset(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().usersetItemsetApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun tradeOrdersBatchCloseV2(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService2().tradeOrdersBatchCloseV2(requestBody),baseObserver)
        return baseObserver.disposable
    }

    override fun tradeAccountLogin(requestBody: RequestBody, baseObserver: BaseObserver<TradeAccountLoginBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService2().tradeAccountLogin(requestBody), baseObserver)
        return baseObserver.disposable
    }

    override fun tradeOrdersClose(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService2().tradeOrdersClose(requestBody), baseObserver)
        return baseObserver.disposable
    }


    override fun stTradePositionClose(
        requestBody: RequestBody,
        baseObserver: BaseObserver<BaseBean>
    ): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().tradePositionClose(requestBody),baseObserver)
        return baseObserver.disposable
    }

    override fun tradePositionBatchClose(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().tradePositionBatchClose(requestBody),baseObserver)
        return baseObserver.disposable
    }

    override fun tradeOrdersUpdate(requestBody: RequestBody, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService2().tradeOrdersUpdate(requestBody), baseObserver)
        return baseObserver.disposable
    }

    override fun stTradePositionUpdate(body: RequestBody, baseObserver: BaseObserver<StTradePositionUpdateBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getStHttpService().tradePositionUpdate(body),baseObserver)
        return baseObserver.disposable
    }

}