package cn.com.vau.trade.bean.kchart

import androidx.annotation.Keep

/**
 * 交易价格变化
 */
@Keep
data class ChartPriceChange(
    var symbol: String? = null,
    var presentOpen: String? = null,    // 1日开盘价
    var yesterdayClose: String? = null, // 1日收盘价
    var weekClose: String? = null,      // 7日收盘价
    var weekOpen: String? = null,       // 7日开盘价
    var monthClose: String? = null,     // 30日收盘价
    var monthOpen: String? = null,      // 30日开盘价
    var hourHigh: String? = null,       // 1小时最高价
    var dayHigh: String? = null,        // 1日最高价
    var monthHigh: String? = null,      // 30日最高价
    var hourLow: String? = null,        // 1小时最低价
    var dayLow: String? = null,         // 1日最低价
    var monthLow: String? = null        // 30日最低价
)
