package cn.com.vau.trade.model

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.data.trade.ClosedHistoryBean
import cn.com.vau.data.trade.TradeClosedHistoryBean
import com.google.gson.Gson
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

class CloseHistoryViewModel:BaseViewModel() {
    val currencyType by lazy { UserDataUtil.currencyType() }

    var positionId:String = ""
    var openTime:String = ""
    var portfolioId:String = ""

    val closedHistoryLiveData by lazy {
        MutableLiveData<ClosedHistoryBean>()
    }

    fun tradeListCloseHistory(onField: ()->Unit) {
        requestNet({
            if (UserDataUtil.isStLogin()) {
                stTradingService.tradeListCloseHistory(positionId,
                    portfolioId.ifEmpty { UserDataUtil.stMasterPortfolioId() })
            } else {
                val map = mutableMapOf(
                    "positionId" to positionId,
                    "openTime" to openTime,
                    "login" to UserDataUtil.accountCd(),
                    "serverId" to UserDataUtil.serverId()
                )
                val dataObject = JsonObject()
                val mGson = Gson()
                dataObject.addProperty("data", mGson.toJson(map))
                val requestBody =
                    dataObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
                tradingService.tradeOrdersListCloseHistory(requestBody)
            }
        },
            {
                it?.let { result ->
                    if (UserDataUtil.isStLogin()) {
                        closedHistoryLiveData.value = (result as ApiResponse<*>).data as ClosedHistoryBean?
                    }else{
                        closedHistoryLiveData.value = (result as TradeClosedHistoryBean).obj
                    }

                }

            },{
                onField.invoke()
            }
        )
    }

}