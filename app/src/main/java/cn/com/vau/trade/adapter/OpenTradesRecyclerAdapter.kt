package cn.com.vau.trade.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.*
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.OrderUtil
import cn.com.vau.data.enums.EnumAdapterPosition
import cn.com.vau.data.init.ShareOrderData
import cn.com.vau.util.*
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Created by roy on 2018/12/1.
 * 订单持仓
 */
class OpenTradesRecyclerAdapter(
    var mContext: Context,
    var dataList: CopyOnWriteArrayList<ShareOrderData>,
    var adapterEnum: EnumAdapterPosition
) : RecyclerView.Adapter<OpenTradesRecyclerAdapter.ViewHolder>() {

    private val currencyType by lazy { UserDataUtil.currencyType() }
    private val c00c79c by lazy { ContextCompat.getColor(mContext, R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(mContext, R.color.ce35728) }
    private val cf44040 by lazy { ContextCompat.getColor(mContext, R.color.cf44040) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c1e1e1e_cebffffff) }
    private val color_c0a1e1e1e_c0affffff by lazy { AttrResourceUtil.getColor(mContext, R.attr.color_c0a1e1e1e_c0affffff) }
    private val pnl by lazy { mContext.getString(R.string.pnl) }
    private val volume by lazy { mContext.getString(R.string.volume) }
    private val lot by lazy { mContext.getString(R.string.lot) }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val holder = ViewHolder(
            LayoutInflater.from(mContext).inflate(R.layout.item_recycler_open_trades, parent, false)
        )

        // K线
        holder.ivKLine.setOnClickListener {
            mOnItemClickListener?.onStartKLine(holder.bindingAdapterPosition)
        }

        // 分享（持仓列表 - 跟单自主&多品牌)
        holder.ivShare.setOnClickListener {
            mOnItemClickListener?.onShareClick(holder.bindingAdapterPosition)
        }

        // 编辑
        holder.tvEdit.setOnClickListener {
            mOnItemClickListener?.onEditClick(holder.bindingAdapterPosition)
        }

        // 平仓
        holder.tvClose.setOnClickListener {
            mOnItemClickListener?.onCloseClick(holder.bindingAdapterPosition)
        }

        holder.itemView.setOnClickListener {
            mOnItemClickListener?.onItemClick(holder.bindingAdapterPosition)
        }

        return holder
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {

        val data = dataList.getOrNull(position) ?: return

        holder.ivKLine.isVisible = (adapterEnum != EnumAdapterPosition.K_LINE)
        holder.ivShare.isVisible = (adapterEnum == EnumAdapterPosition.MAIN_ORDER)
        holder.tvEdit.isVisible = (adapterEnum != EnumAdapterPosition.STRATEGY)

        holder.tvVolTitle.text = "$volume ($lot)"

        // 产品名称
        holder.tvProdName.setTextDiff(data.symbol.ifNull())

        holder.tvOrderType.setTextDiff(OrderUtil.getOrderTypeName(data.cmd))

        // 买卖类型
        if (OrderUtil.isBuyOfOrder(data.cmd)) {
            holder.tvOrderType.setTextColorDiff(c00c79c)
            holder.tvOrderType.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c1f00c79c_r100)
        } else {
            holder.tvOrderType.setTextColorDiff(ce35728)
            holder.tvOrderType.background = ContextCompat.getDrawable(mContext, R.drawable.shape_c1fe35728_r100)
        }

        holder.tvOrderId.setTextDiff("#${data.order.ifNull()}")

        // 手数
        holder.tvVolume.setTextDiff(data.volumeUI.ifNull())
        // 开仓价
        holder.tvOpenPrice.setTextDiff(data.openPrice)
        // 现价
        holder.tvCurrentPrice.setTextDiff(if ("-" == data.closePrice) "-" else data.currentPriceUI.ifNull())
        // 设置货币
        holder.tvPnlTitle.setTextDiff("$pnl (${currencyType})".arabicReverseTextByFlag(" ").ifNull())

        // 订单盈亏
        holder.tvPnl.setTextDiff(if ("-" == data.closePrice) "-" else data.profitUI.ifNull())

        if ("-" == data.closePrice) {
            holder.tvPnl.setTextColorDiff(color_c1e1e1e_cebffffff)
        } else {
            holder.tvPnl.setTextColorDiff(if (data.profit >= 0) c00c79c else cf44040)
        }

        holder.offView.setBackgroundColor(color_c0a1e1e1e_c0affffff)

    }

    override fun getItemCount(): Int = dataList.size

    @SuppressLint("NotifyDataSetChanged")
    fun setData(orderList: CopyOnWriteArrayList<ShareOrderData>?) {
        dataList.clear()
        dataList.addAll(orderList ?: CopyOnWriteArrayList<ShareOrderData>())
        notifyDataSetChanged()
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvProdName: TextView = view.findViewById(R.id.tvProdName)
        val ivKLine: ImageView = view.findViewById(R.id.ivKLine)
        val ivShare: AppCompatImageView = view.findViewById(R.id.ivShare)
        val tvPnlTitle: TextView = view.findViewById(R.id.tvPnlTitle)
        val tvOrderType: TextView = view.findViewById(R.id.tvOrderType)
        val tvOrderId: TextView = view.findViewById(R.id.tvOrderId)
        val tvPnl: TextView = view.findViewById(R.id.tvPnl)
        val tvVolTitle: TextView = view.findViewById(R.id.tvVolTitle)
        val tvVolume: TextView = view.findViewById(R.id.tvVolume)
        val tvOpenPriceTitle: TextView = view.findViewById(R.id.tvOpenPriceTitle)
        val tvOpenPrice: TextView = view.findViewById(R.id.tvOpenPrice)
        val tvCurrentPriceTitle: TextView = view.findViewById(R.id.tvCurrentPriceTitle)
        val tvCurrentPrice: TextView = view.findViewById(R.id.tvCurrentPrice)
        val tvEdit: TextView = view.findViewById(R.id.tvEdit)
        val tvClose: TextView = view.findViewById(R.id.tvClose)
        val offView: View = view.findViewById(R.id.offView)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onStartKLine(position: Int)
        fun onShareClick(position: Int)
        fun onEditClick(position: Int)
        fun onCloseClick(position: Int)
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}
