package cn.com.vau.trade.viewmodel

import android.annotation.SuppressLint
import android.content.Context
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.trade.bean.SymbolItemBean
import cn.com.vau.trade.fragment.search.SymbolListPageState
import cn.com.vau.util.ToastUtil.showToast
import com.google.gson.JsonObject
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

/**
 * Created by array on 2025/4/21 14:18
 * Desc: 产品搜索
 */
class SSCategorySymbolListViewModel : BaseViewModel() {

    //Fragment列表状态
    private val _symbolListState: MutableStateFlow<SymbolListPageState> = MutableStateFlow(SymbolListPageState.Empty)
    val symbolListState: StateFlow<SymbolListPageState> = _symbolListState.asStateFlow()

    //Fragment数据列表
    private val _symbolList = MutableSharedFlow<HashMap<String, List<SymbolItemBean>>>()
    val symbolList = _symbolList.asSharedFlow()

    @SuppressLint("StaticFieldLeak")
    private var context: Context? = null

    /**
     * 确保在不需要时清除引用
     */
    fun setContext(context: Context) {
        this.context = context
    }

    fun getContext(): Context {
        return context ?: VauApplication.context
    }

    override fun onCleared() {
        super.onCleared()
        context = null
    }

    fun setSymbolListState(state: SymbolListPageState) {
        viewModelScope.launch {
            _symbolListState.emit(state)
        }
    }

    /**
     * 搜索内容发送服务器
     */
    fun addSearchRecord(token: String, code: String, type: String?) {
        val params = HashMap<String, Any>()
        if ("" != token) {
            params["token"] = token
        }
        params["code"] = code
        requestNet({ baseService.productRecord(params) }, {})
    }

    /**
     * isSelected:  true表示已收藏   false表示未收藏
     */
    fun toggleOptional(groupName: String, itemBean: SymbolItemBean, currentList: List<SymbolItemBean>) {
        // 1. 更新本地自选列表
        syncCollectSymbolList(itemBean)
        // 2. 生成更新后的列表
        val updatedSymbolList = buildUpdatedSymbolList(groupName, itemBean, currentList)
        // 3. 更新UI状态
        updateListUiState(groupName, updatedSymbolList)
        // 4. 同步自选列表到服务器
        syncUdp(itemBean)
    }

    /**
     * 更新本地自选列表
     */
    private fun syncCollectSymbolList(itemBean: SymbolItemBean) {
        VAUSdkUtil.collectSymbolList.toggleSymbol(
            symbol = itemBean.product.symbol,
            isSelected = itemBean.isSelected,
            allSymbols = VAUSdkUtil.symbolList()
        )
    }

    /**
     * 1、取消自选，从自选列表移除
     * 2、添加自选，添加到自选列表
     */
    private fun MutableList<ShareProductData>.toggleSymbol(symbol: String, isSelected: Boolean, allSymbols: List<ShareProductData>) {
        if (isSelected) {
            removeAll { it.symbol == symbol }
        } else {
            allSymbols.firstOrNull { it.symbol == symbol }?.let { add(it) }
        }
    }

    /**
     * 生成更新后的列表
     */
    private fun buildUpdatedSymbolList(
        groupName: String,
        itemBean: SymbolItemBean,
        currentList: List<SymbolItemBean>
    ): List<SymbolItemBean> {
        val isWatchlist = groupName == getContext().getString(R.string.watchlist)

        return if (isWatchlist) {
            handleOptionalList(itemBean, currentList)
        } else {
            handleNoOptionalList(itemBean, currentList)
        }
    }

    /**
     * 处理自选列表的选中状态
     */
    private fun handleOptionalList(
        itemBean: SymbolItemBean,
        currentList: List<SymbolItemBean>
    ): List<SymbolItemBean> {
        return if (itemBean.isSelected) {
            // 从自选列表移除
            currentList.filterNot { it.product.symbol == itemBean.product.symbol }
        } else {
            // 添加到自选列表（理论上自选列表不应出现未选中状态）
            currentList.map {
                if (it.product.symbol == itemBean.product.symbol) it.copy(isSelected = true) else it
            }
        }

    }

    /**
     * 处理非自选列表的选中状态
     */
    private fun handleNoOptionalList(
        itemBean: SymbolItemBean,
        currentList: List<SymbolItemBean>
    ): List<SymbolItemBean> {
        return currentList.map {
            if (it.product.symbol == itemBean.product.symbol) it.copy(isSelected = !itemBean.isSelected) else it
        }
    }

    /**
     * 更新List UI状态
     */
    private fun updateListUiState(groupName: String, updatedList: List<SymbolItemBean>) {
        viewModelScope.launch {
            _symbolList.emit(hashMapOf(groupName to updatedList))
            if (groupName == getContext().getString(R.string.watchlist)) {
                _symbolListState.emit(
                    if (updatedList.isEmpty()) SymbolListPageState.Empty else SymbolListPageState.NotEmpty
                )
            }
        }
    }

    /**
     * 跟单or非跟单，添加自选接口
     */
    private fun syncUdp(itemBean: SymbolItemBean) {
        val symbols = VAUSdkUtil.collectSymbolList.joinToString(",") { it.symbol }
        if (UserDataUtil.isStLogin()) {
            stAccountProductMyUpd(itemBean, symbols)
        } else {
            prodUpd(itemBean, symbols)
        }
    }

    /**
     * 跟单账户，添加自选接口
     */
    private fun stAccountProductMyUpd(itemBean: SymbolItemBean, symbols: String) {
        val jsonObject = JsonObject()
        jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
        jsonObject.addProperty("symbols", symbols)

        requestNet({
            val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
            //ST 【跟单】自选产品 移动
            stTradingService.stAccountProductMyUpd(requestBody)
        }, {
            if (it.isSuccess()) {
                showToast(getContext().getString(if (itemBean.isSelected) R.string.removed_from_favorites else R.string.added_to_favorites))
            }
        })
    }

    /**
     * 非跟单账户，添加自选接口
     */
    private fun prodUpd(itemBean: SymbolItemBean, symbols: String) {
        val paramMap = HashMap<String, Any>()
        paramMap["login"] = UserDataUtil.accountCd()
        paramMap["token"] = UserDataUtil.loginToken()
        paramMap["symbols"] = symbols

        requestNet({
            //自选产品 移动
            baseService.prodUpd(paramMap)
        }, { baseData ->
            if (baseData.isSuccess()) {
                showToast(getContext().getString(if (itemBean.isSelected) R.string.removed_from_favorites else R.string.added_to_favorites))
                return@requestNet
            }
        })
    }

    /**
     * 同步产品选中状态
     * 1、如果是自选列表，移除不在collectSymbolList中的产品，添加collectSymbolList中有，自选列表中没有的产品
     * 2、如果是非自选列表，根据是否在collectSymbolList中，更新选中状态
     * @param groupName 当前分组名称（如自选列表）
     * @param currentList 当前显示的产品列表
     */
    fun syncOptionalInList(groupName: String, currentList: List<SymbolItemBean>) {

        val isWatchlist = groupName == getContext().getString(R.string.watchlist)
        val productLookup = VAUSdkUtil.collectSymbolList.associateBy { it.symbol }.keys
        val updatedList = if (isWatchlist) {
            syncWatchlist(currentList, productLookup)
        } else {
            updateSelectionStatus(currentList, productLookup)
        }
        viewModelScope.launch {
            _symbolList.emit(hashMapOf(groupName to updatedList))
            _symbolListState.emit(if (updatedList.isEmpty()) SymbolListPageState.Empty else SymbolListPageState.NotEmpty)
        }
    }

    /**
     * 同步自选列表（添加/移除元素）
     */
    private fun syncWatchlist(
        currentList: List<SymbolItemBean>,
        productLookup: Set<String>
    ): List<SymbolItemBean> {
        // 1. 保留存在于自选列表的产品
        val filteredList = currentList
            .filter { it.product.symbol in productLookup }
            .toMutableList()

        // 2. 添加自选列表中存在但当前列表没有的产品
        val newItems = VAUSdkUtil.collectSymbolList
            .filterNot { symbol -> currentList.any { it.product.symbol == symbol.symbol } }
            .map { SymbolItemBean(it, true) }

        return filteredList.apply { addAll(newItems) }
    }

    /**
     * 更新非自选列表的选中状态
     */
    private fun updateSelectionStatus(
        currentList: List<SymbolItemBean>,
        productLookup: Set<String>
    ): List<SymbolItemBean> {
        return currentList.map { item ->
            item.apply { isSelected = productLookup.contains(item.product.symbol) }
        }
    }

}
