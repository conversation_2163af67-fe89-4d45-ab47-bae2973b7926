package cn.com.vau.trade.model

import android.content.Context
import android.text.TextUtils
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.lifecycle.MutableLiveData
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.util.GsonUtil
import cn.com.vau.util.ToastUtil
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

class KLineViewModel: BaseViewModel() {

    var symbol = ""           // K线产品名
    var selectedOrderNo = ""  // 当前选中的订单号
    var isFcm = false
    var typeFrom = ""         // 原K线页面外部传参，发现里边并没有用到
    // K线产品数据
    var data: ShareProductData? = null
    // 是否为自选
    var isAddOptional = false
    @Deprecated("此属性暂为老K线保留 新K线已放到KLineChartNewViewModel中（老K线删除时，删除这个）")
    var isSwitching = false     // 切换新产品中 (避免因切换后到页面初始方法执行完毕前发生异常 | 防止K线页onResume重复刷新)

    val refreshCallBack = MutableLiveData<Boolean>()
    val shareLiveData = MutableLiveData<Boolean>()
    val showTitleLiveData = MutableLiveData<Boolean>()
    val recommendLiveData = MutableLiveData<Boolean>()
    val recommendRequestLiveData = MutableLiveData<Boolean>()

    // 收藏 or 不收藏
    fun updOptionalProd(type: Int) {
        val optionalList = VAUSdkUtil.collectSymbolList

        if (type == 1) {
            val shareSymbolList = VAUSdkUtil.symbolList()
            for (symbolData in shareSymbolList) {
                if (symbolData.symbol == symbol) {
                    optionalList.add(symbolData)
                    break
                }
            }
        } else {
            for (i in optionalList.indices) {
                if (optionalList[i].symbol == symbol) {
                    optionalList.removeAt(i)
                    break
                }
            }
        }
        isAddOptional = type != 0
        sendEvent(NoticeConstants.SEND_EVENT_TAG_KLINE_REFRESH_OPTIONAL)

        val symbols = StringBuilder()
        for (shareProductData in optionalList) {
            symbols.append(shareProductData.symbol)
            symbols.append(",")
        }
        if (!TextUtils.isEmpty(UserDataUtil.stAccountId())) {
            val jsonObject = JsonObject()
            jsonObject.addProperty("accountId", UserDataUtil.stAccountId())
            jsonObject.addProperty("symbols", symbols.toString())

            val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), GsonUtil.buildGson().toJson(jsonObject).toString())
            // 请求
            requestNet({
                stTradingService.stAccountProductMyUpd(requestBody)
            }, onSuccess = {
                if (it.isSuccess()) {
                    EventBus.getDefault().post(NoticeConstants.OPTIONAL_PRODUCT_LIST_UPDATED)
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }
            })
        } else {
            val paramMap = HashMap<String, Any>()
            paramMap.put("login", UserDataUtil.accountCd())
            paramMap.put("token", UserDataUtil.loginToken())
            paramMap.put("symbols", symbols.toString())
            // 请求
            requestNet({
                baseService.prodUpd(paramMap)
            }, onSuccess = {
                if (it.isSuccess()) {
                    EventBus.getDefault().post(NoticeConstants.OPTIONAL_PRODUCT_LIST_UPDATED)
                } else {
                    ToastUtil.showToast(it.getResponseMsg())
                }
            })
        }
    }
    // 查询跟单推荐
    fun getTopTraderData() {
        requestNet({
            val jsonObject = JsonObject()
            val accountId = if (UserDataUtil.isStLogin()) {
                UserDataUtil.stAccountId()
            } else {
                ""
            }
            jsonObject.addProperty("accountId", accountId)
            jsonObject.addProperty("limit", 0)
            jsonObject.addProperty("productName", symbol)
            val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaTypeOrNull())
            stTradingService.signalListMonthlySignalsByProduct(requestBody)
        }, onSuccess = { dataBean ->
            recommendRequestLiveData.value = true
            if (!dataBean.isSuccess()) {
                return@requestNet
            }
            val objList = dataBean.data
//            if (objList.isNullOrEmpty()) {
//                return@requestNet
//            }
            // 现在需求可以在本页面中切换产品，所以需要add或remove
            recommendLiveData.value = objList?.isNotEmpty() == true
        }, onError = {
            recommendRequestLiveData.value = true
        })
    }

    fun setTextColorByDiff(context: Context?, textView: TextView, diff: Double) {
        context?.let {
            if (diff < 0) {
                textView.setTextColor(ContextCompat.getColor(it, R.color.cf44040))
            } else {
                textView.setTextColor(ContextCompat.getColor(it, R.color.c00c79c))
            }
        }
    }
}