package cn.com.vau.trade.fragment.search

import android.os.Bundle
import android.view.View
import android.view.ViewStub
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.performance.PerformManager
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.DividerItemDecoration
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.FragmentSsCategorySymbolListBinding
import cn.com.vau.databinding.VsLayoutNoDataScrollBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.profile.performance.TradePermissionPerformance
import cn.com.vau.trade.adapter.SymbolSearchListAdapter
import cn.com.vau.trade.bean.FollowProductData
import cn.com.vau.trade.bean.SymbolItemBean
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_DOWN
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_NONE
import cn.com.vau.trade.ext.SymbolSearchConstants.SORT_UP
import cn.com.vau.trade.ext.SymbolSearchConstants.SS_CATEGORY_SYMBOL_GROUP_NAME
import cn.com.vau.trade.ext.SymbolSearchUtil
import cn.com.vau.trade.ext.addScrollListener
import cn.com.vau.trade.ext.deepCopy
import cn.com.vau.trade.perform.SSTracePerformance
import cn.com.vau.trade.viewmodel.SSCategorySymbolListViewModel
import cn.com.vau.trade.viewmodel.SymbolSearchDialogViewModel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.dp2px
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import java.util.Collections

/**
 * Created by array on 2025/4/24 15:24
 * Desc: 产品分类页-产品列表（自选列表+产品列表）
 */
class SSCategorySymbolListFragment : BaseMvvmBindingFragment<FragmentSsCategorySymbolListBinding>(), SDKIntervalCallback {

    companion object {
        fun newInstance(groupName: String): SSCategorySymbolListFragment {
            return SSCategorySymbolListFragment().apply {
                arguments = Bundle().apply {
                    putString(SS_CATEGORY_SYMBOL_GROUP_NAME, groupName)
                }
            }
        }
    }

    /** 整个Dialog的ViewModel */
    private var mViewModel: SymbolSearchDialogViewModel? = null

    /** Fragment的ViewModel */
    private lateinit var listViewModel: SSCategorySymbolListViewModel

    /** 分类名 */
    private val groupName: String by lazy {
        arguments?.getString(SS_CATEGORY_SYMBOL_GROUP_NAME) ?: ""
    }

    private val performManager: PerformManager by lazy {
        PerformManager(this)
    }

    private val tracePerformance: SSTracePerformance by lazy {
        SSTracePerformance()
    }

    private val tradePermissionPerformance by lazy {
        TradePermissionPerformance(this)
    }

    private var mAdapter: SymbolSearchListAdapter? = null
    private var dataList: ArrayList<SymbolItemBean> = arrayListOf()
    private var originList: ArrayList<SymbolItemBean> = arrayListOf()

    private var isListScrolling = false

    fun setViewModel(viewModel: SymbolSearchDialogViewModel?) {
        mViewModel = viewModel
    }

    fun setDataList(list: List<SymbolItemBean>) {
        this.dataList.clear()
        this.dataList.addAll(list)
        this.originList.clear()
        this.originList.addAll(list)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        listViewModel = ViewModelProvider(this)[SSCategorySymbolListViewModel::class.java]
        listViewModel.setContext(requireActivity())
        addPerformances()
    }

    private fun addPerformances() {
        performManager.addPerformance(tracePerformance)
        performManager.addPerformance(tradePermissionPerformance)
    }

    override fun initView() {

        mBinding.rvList.layoutManager = WrapContentLinearLayoutManager(context)
        mBinding.rvList.setHasFixedSize(true)
        mAdapter = SymbolSearchListAdapter()
        mBinding.rvList.adapter = mAdapter
        mBinding.rvList.itemAnimator = null
        mBinding.rvList.addItemDecoration(DividerItemDecoration(8.dp2px()))

        mBinding.mVsNoDataScroll.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataScrollBinding.bind(inflated)
                vs.mNoDataScrollView.setHintMessage(getString(R.string.no_symbols))
            }
        })

        mAdapter?.setOnItemClick { position, itemBean ->
            // 点击事件
            tracePerformance.traceSearchResultClick(
                "",
                itemBean.product.symbol,
                groupName,
                position,
                "1"
            )
            handleJump(itemBean)
        }

        mAdapter?.setOnFollowClick { itemBean ->
            tradePermissionPerformance.run {
                if (handleTradeBlockType { handleFollowClick(itemBean) }) return@setOnFollowClick
            }
            handleFollowClick(itemBean)
        }

        mBinding.rvList.addScrollListener { isScrolling, scrollY, direction ->
            isListScrolling = isScrolling
        }

    }

    private fun handleFollowClick(itemBean: SymbolItemBean) {
        // 点击添加自选or取消自选
        listViewModel.toggleOptional(groupName, itemBean, dataList)
        mViewModel?.followProduct(FollowProductData(itemBean.product.symbol, VAUSdkUtil.collectSymbolList.firstOrNull { it.symbol == itemBean.product.symbol } != null))
    }

    override fun onResume() {
        super.onResume()
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)

        listViewModel.syncOptionalInList(groupName, dataList)
    }

    override fun onPause() {
        super.onPause()
        cancelCallBack()
    }

    private fun cancelCallBack() {
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    override fun onCallback() {
        if (dataList.isNotEmpty() && isListScrolling.not() && mViewModel?.isCategoryPagerScrolling != true) {
            refreshAdapter()
        }
    }

    /**
     * 刷新列表
     */
    fun refreshAdapter() {
        // 提交前记录位置
        val layoutManager = mBinding.rvList.layoutManager as LinearLayoutManager
        val firstVisiblePos = layoutManager.findFirstVisibleItemPosition()
        val topOffset = layoutManager.findViewByPosition(firstVisiblePos)?.top ?: 0
        val newList = dataList.deepCopy()
        mAdapter?.submitList(newList) {
            mBinding.rvList.post {
                val safePos = firstVisiblePos.coerceAtMost(newList.size - 1)
                if (safePos >= 0) {
                    layoutManager.scrollToPositionWithOffset(safePos, topOffset)
                }
            }
        }
    }

    override fun createObserver() {
        /** 产品列表有无数据处理 */
        lifecycleScope.launch {
            listViewModel.symbolListState.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest {
                when (it) {
                    SymbolListPageState.NotEmpty -> {
                        // 有数据
                        mBinding.mVsNoDataScroll.isVisible = false
                        mBinding.rvList.isVisible = true

                    }

                    SymbolListPageState.Empty -> {
                        // 无数据
                        mBinding.mVsNoDataScroll.isVisible = true
                        mBinding.rvList.isVisible = false
                    }
                }
            }
        }

        /**
         * 1、点击收藏，更新列表
         */
        lifecycleScope.launch {
            listViewModel.symbolList.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED).collectLatest { it ->
                val list = it.get(groupName) ?: return@collectLatest
                dataList.clear()
                dataList.addAll(list)
                val isWatchlist = groupName == getString(R.string.watchlist)
                if (isWatchlist) {
                    originList.clear()
                    originList.addAll(list)
                }
                refreshAdapter()
            }
        }

        /** 分类页产品列表排序 */
        lifecycleScope.launch {
            mViewModel?.sortCategorySymbolList?.flowWithLifecycle(lifecycle, Lifecycle.State.STARTED)?.collectLatest { it ->
                if (mAdapter != null && dataList.isNotEmpty()) {
                    when (it) {
                        SORT_NONE -> {
                            // 不排序
                            setOriginList()
                        }

                        SORT_DOWN -> {
                            // 按涨幅由高到低
                            SymbolSearchUtil.sort(SORT_DOWN, dataList) {
                                refreshAdapter()
                            }
                        }

                        SORT_UP -> {
                            // 按涨幅由低到高
                            SymbolSearchUtil.sort(SORT_UP, dataList) {
                                refreshAdapter()
                            }
                        }
                    }

                }
            }
        }

        listViewModel.setSymbolListState(if (dataList.isNotEmpty()) SymbolListPageState.NotEmpty else SymbolListPageState.Empty)
    }

    private fun setOriginList() {
        dataList.clear()
        dataList.addAll(originList)
        refreshAdapter()
    }

    /**
     * 点击item跳转
     */
    private fun handleJump(itemBean: SymbolItemBean) {
        if (VAUSdkUtil.symbolList().size == 0) return
        // 禁止交易
        if ("0" == itemBean.product.enable) {
            ToastUtil.showToast(getString(R.string.this_symbol_is_untradable))
            return
        }

        // 公共数据中查找是否有该产品，有的话跳转（不使用当前展示的产品对象进行跳转，原因可能是公共数据可能发生变化）
        val dataList: List<ShareProductData> = VAUSdkUtil.symbolList()
        for (data in dataList) {
            if (data.symbol.equals(itemBean.product.symbol, ignoreCase = true) && ("2" == data.enable || "1" == data.enable)) {
                listViewModel.addSearchRecord(UserDataUtil.loginToken(), itemBean.product.symbol, itemBean.product.symbol)
                mViewModel?.selectProduct(data)
                return
            }
        }
        // 公共数据中没有该产品，查找contains symbol的产品列表
        val tempDataList: MutableList<ShareProductData> = java.util.ArrayList()
        for (data in dataList) {
            if ("2" == data.enable) {
                if (
                    data.symbol.lowercase().contains(itemBean.product.symbol.lowercase())
                ) {
                    tempDataList.add(data)
                }
            }
        }
        if (tempDataList.size > 1) {
            // 大于1时，排序后，取第一个产品跳转
            val comparator = Comparator<ShareProductData> { o1, o2 -> o1.symbol.length - o2.symbol.length }
            Collections.sort(tempDataList, comparator)
            tempDataList.firstOrNull()?.let { mViewModel?.selectProduct(it) }
            mViewModel?.dismissDialog()
        } else {
            // 等于1时，不用排序，取第一个产品跳转
            if (tempDataList.size != 0) {
                tempDataList.firstOrNull()?.let { mViewModel?.selectProduct(it) }
                mViewModel?.dismissDialog()
            }
        }
        // 搜索内容发送服务器
        if (tempDataList.size != 0) {
            listViewModel.addSearchRecord(UserDataUtil.loginToken(), itemBean.product.symbol, itemBean.product.symbol)
        }

    }

}

sealed class SymbolListPageState {
    data object NotEmpty : SymbolListPageState()
    data object Empty : SymbolListPageState()
}