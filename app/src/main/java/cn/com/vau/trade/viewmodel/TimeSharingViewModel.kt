package cn.com.vau.trade.viewmodel

import android.content.Context
import android.graphics.Paint
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.view.kchart.viewbeans.BrokenLineNewOrder
import cn.com.vau.common.view.kchart.viewbeans.IndicatorLine
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.data.trade.KChartBean
import cn.com.vau.data.trade.KChartBean.DataBean.ChartsBean
import cn.com.vau.data.trade.KChartBean.DataBean.TimeChartBean
import cn.com.vau.data.trade.SymbolsChartData
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.util.AppUtil.getTimeZoneRawOffsetToHour
import cn.com.vau.util.GsonUtil.buildGson
import cn.com.vau.util.TimeUtil.frontDateLongWithStartDate
import cn.com.vau.util.ifNull
import cn.com.vau.util.toLongCatching
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody

class TimeSharingViewModel: BaseViewModel() {

    // K线产品数据
    var data: ShareProductData? = null
    var isAutoRefresh = false   // 是否自动刷新(K线最后一根)

    var timeShareList = arrayListOf<TimeChartBean>()
    var noDataLiveData = MutableLiveData<Boolean>()
    // K线数据存放在ViewModel中，不再存放于KLineDataUtils，因为用户在改单时有可能进到K线页下单，打开下单页，造成数据冲突
    var mainOrderList = mutableListOf<ChartsBean?>()
    var timeOrderList = mutableListOf<String>()

    fun requestKChartData() {
        val mt4ToTime = (System.currentTimeMillis() / 1000 + Constants.season * 60 * 60L)
        var fromTime = ""
        val jsonMap = hashMapOf<String, String>(
            "symbol" to data?.symbol.ifNull(),
            "period" to "1",
            "size" to "31"
        )

        if (UserDataUtil.isStLogin()) {
            stChartHistory(jsonMap)
        } else {
            // MT4
            if (!UserDataUtil.isMT5()) {
                jsonMap.put("to", mt4ToTime.toString() + "")
                //如果不是跟单账户，照旧， 跟单账户不需要传
                if (isAutoRefresh) {
                    if (getLastestChecked() != null) {
                        fromTime = getLastestChecked()?.timestamp.ifNull()
                        jsonMap.put("from", getLastestChecked()?.timestamp.ifNull())
                    }
                } else {
                    fromTime = frontDateLongWithStartDate(1, mt4ToTime).toString() + ""
                    jsonMap.put("from", fromTime)
                }
            }

            if (!TextUtils.isEmpty(UserDataUtil.accountCd()) && !UserDataUtil.isRebateAccount()) {
                jsonMap.put("server", UserDataUtil.serverId())
            } else {
                jsonMap.put("server", "")
            }
            jsonMap.put("type", "1")
            if (UserDataUtil.isLogin()) {
                jsonMap.put("login", UserDataUtil.accountCd())
            }
            val bodyMap = HashMap<String, String>()
            bodyMap.put("data", buildGson().toJson(jsonMap))
            chartHistory(bodyMap)
        }
    }

    private fun stChartHistory(param: HashMap<String, String>) {
        // 无法用新网络框架，因为数据模型不规范，不符合框架要求的数据泛型
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(param).toString())
        HttpUtils.loadData<SymbolsChartData>(RetrofitHelper.getStHttpService().historyGetKLineMts(requestBody), object : BaseObserver<SymbolsChartData>() {
            override fun onHandleSubscribe(d: Disposable) {
//                rxManager.add(d)
            }

            override fun onNext(baseData: SymbolsChartData) {
                val isUnAvailable = baseData.data == null
                if ("200" != baseData.code || isUnAvailable) {
                    isAutoRefresh = false
                    // 展示缺省图
                    noDataLiveData.value = true
                    return
                }
                setKChartData(baseData.data)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                // 展示缺省图
                noDataLiveData.value = true
            }
        })
    }

    private fun chartHistory(param: HashMap<String, String>) {
        // 无法用新网络框架，因为数据模型不规范，不符合框架要求的数据泛型
        val requestBody = RequestBody.create("application/json".toMediaTypeOrNull(), buildGson().toJson(param).toString())
        HttpUtils.loadData<SymbolsChartData>(RetrofitHelper.getHttpService2().tradeOrderHistoryMarketsApi(requestBody), object : BaseObserver<SymbolsChartData>() {
            override fun onHandleSubscribe(d: Disposable) {
//                rxManager.add(d)
            }

            override fun onNext(baseData: SymbolsChartData) {
                val isUnAvailable = baseData.obj == null
                if ("200" != baseData.code || isUnAvailable) {
                    isAutoRefresh = false
                    // 展示缺省图
                    noDataLiveData.value = true
                    return
                }
                setKChartData(baseData.obj)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                // 展示缺省图
                noDataLiveData.value = true
            }
        })
    }

    private fun setKChartData(baseData: KChartBean?) {
        if (baseData != null) {
            val chartsBeanList = mutableListOf<ChartsBean>()
            synchronized(baseData) {
                baseData.data?.list?.let {
                    chartsBeanList.addAll(it)
                }
                // 保存小数位
                if (chartsBeanList.isNotEmpty() && baseData.data?.digits.ifNull() > 0) {
                    ChartUIParamUtil.digits = baseData.data?.digits.ifNull()
                }

                // 没有请求到数据
                if (chartsBeanList.isEmpty()) {
                    mainOrderList.clear()
                    // 展示缺省图
                    noDataLiveData.value = true
                    return
                }

                noDataLiveData.value = false

                sendEvent(NoticeConstants.SEND_EVENT_TAG_KLINE_DELAY_TIMER)
                if (!isAutoRefresh) {
                    for (i in chartsBeanList.indices) {
                        if (i != chartsBeanList.size - 1) {
                            val chartsBean = chartsBeanList.get(i)
                            //                            除最后一个数据，其他均打上标识
                            chartsBean.checked = true
                        }
                    }
                    mainOrderList.clear()
                    // 保存所有数据
                    mainOrderList.addAll(chartsBeanList)
                } else {
                    if (chartsBeanList.isNotEmpty() && mainOrderList.isNotEmpty()) {
//                LogUtil.i("wj", "--------------------------------检查并覆盖----------------------------------");
                        val mainLastestBean = mainOrderList.getOrNull(mainOrderList.size - 1)
                        for (x in chartsBeanList.indices) {
                            val bean = chartsBeanList.getOrNull(x)
                            if (bean?.timestamp.ifNull() > mainLastestBean?.timestamp.ifNull()) {
                                mainOrderList.add(bean)
                            }
                        }
                        for (i in chartsBeanList.indices) {
                            val chartsBean = chartsBeanList.getOrNull(i)
                            for (j in mainOrderList.size - 2 downTo 0) {
                                var mainChartsBean = mainOrderList.getOrNull(j)
                                if (chartsBean?.timestamp.ifNull().compareTo(mainChartsBean?.timestamp.ifNull()) == 0 && mainChartsBean?.checked == false) {
//                            LogUtil.i("wj", "j:" + j + ", time:" + sdf.format(new Date(Long.parseLong(mainChartsBean.getTimestamp() + "000"))));
                                    chartsBean?.checked = true
                                    mainChartsBean = chartsBean
                                    mainOrderList.set(j, mainChartsBean)
                                }
                            }
                        }
                        //                LogUtil.i("wj", "--------------------------------检查完成----------------------------------");
                    }
                }
                isAutoRefresh = false
                sendEvent(NoticeConstants.SEND_EVENT_TAG_KLINE_REFRESH_CHART)
            }
        } else {
            // 理论上不会到达这里
            isAutoRefresh = false
            // 展示缺省图
            noDataLiveData.value = true
        }
    }

    private fun getLastestChecked(): ChartsBean? {
        if (!mainOrderList.isEmpty()) {
            for (i in mainOrderList.indices) {
                val bean = mainOrderList.getOrNull(i)
                if (bean?.checked == false && i > 1) {
                    return mainOrderList.getOrNull(i - 1)
                }
            }
        }
        return null
    }

    fun getIndicatorLine(context: Context?): IndicatorLine? {
        context?.let {
            return IndicatorLine(it).apply {
                setLineColor(ChartUIParamUtil.sellIndicatorColor)
                setTextBackgroundColor(ChartUIParamUtil.sellIndicatorScaleBgColor)
                setTextColor(ChartUIParamUtil.sellIndicatorTextColor)
                setLineDashPath(ChartUIParamUtil.commonDashEffect)
                setTextGravity(Paint.Align.CENTER)
//                setScaleAlign(IndicatorLine.ScaleAlign.BELOW) 先与iOS保持一致吧
                defaultShowPointNums = ChartUIParamUtil.newOrderDefaultShowPointNum
            }
        }
        return null
    }

    fun getBuyLine(context: Context?): IndicatorLine? {
        context?.let {
            return IndicatorLine(it).apply {
                setLineColor(ChartUIParamUtil.buyIndicatorColor)
                setTextBackgroundColor(ChartUIParamUtil.buyIndicatorScaleBgColor)
                setTextColor(ChartUIParamUtil.buyIndicatorTextColor)
                setLineDashPath(ChartUIParamUtil.commonDashEffect)
                setTextGravity(Paint.Align.CENTER)
//                setScaleAlign(IndicatorLine.ScaleAlign.ABOVE) 先与iOS保持一致吧
                defaultShowPointNums = ChartUIParamUtil.newOrderDefaultShowPointNum
            }
        }
        return null
    }

    // 分时
    // 创建这个BrokenLineNewOrder类的原因是 订单页的分时图只显示30个点，而BrokenLine中计算每个点的间距是等分的，其实拆线的点显示的数据应为mShownPointNums+1个，
    // 所以这个类中更改了点的显示逻辑，但因为更改这个会影响到十字线选中的位置，所以这里单独创建一个类不影响其他
    fun getTimeBrokenLine(context: Context?): BrokenLineNewOrder? {
        context ?: return null
        val brokenLine = BrokenLineNewOrder(context)
        brokenLine.dataList = timeOrderList
        brokenLine.defaultShowPointNums = ChartUIParamUtil.newOrderDefaultShowPointNum
        brokenLine.setMinShownPointNums(ChartUIParamUtil.newOrderMinShownPointNum)
        brokenLine.setMaxShownPointNums(ChartUIParamUtil.newOrderMaxShownPointNum)
        brokenLine.isFill = true
        brokenLine.setDigits(ChartUIParamUtil.digits)
        brokenLine.setShowMaxPrice(true)
        brokenLine.setShowMinPrice(true)
        brokenLine.setDrawPointIndex(timeOrderList.size - brokenLine.defaultShowPointNums)
        return brokenLine
    }

    fun getTimeShareDataList(): ArrayList<TimeChartBean> {
        val list: ArrayList<TimeChartBean> = ArrayList<TimeChartBean>()
        val timeZone = getTimeZoneRawOffsetToHour()
        timeOrderList.clear()
        for (x in mainOrderList.indices) {
            val dataBean = mainOrderList.getOrNull(x)
            timeOrderList.add(dataBean?.close.toString())
            val timeShareBean = TimeChartBean()
            timeShareBean.close = dataBean?.close.ifNull()
            timeShareBean.timestamp = dataBean?.timestamp
            timeShareBean.mt4TimeMills = (dataBean?.timestamp?.toLongCatching().ifNull() * 1000) - (timeZone * 3600 * 1000)
            if (x == mainOrderList.size - 1 && data != null) {
                // 个人感觉这段代码没有意义(页面每次刷新都会去赋值)，故注释掉
//                boolean buyLineVisible = KLineDataUtils.userData != null && KLineDataUtils.userData.getAskLineDisplay();
//                if (buyLineVisible) {
//                }
                if (data?.bid != 0f) {
                    timeShareBean.close = if (data?.originalBid == 0f) data?.bid?.toDouble().ifNull() else data?.originalBid?.toDouble().ifNull()
                }
                if (data?.ask != 0f) {
                    timeShareBean.originalAsk = if (data?.originalAsk == 0f) data?.ask?.toDouble().ifNull() else data?.originalAsk?.toDouble().ifNull()
                }
            }
            list.add(timeShareBean)
        }
        return list
    }

}