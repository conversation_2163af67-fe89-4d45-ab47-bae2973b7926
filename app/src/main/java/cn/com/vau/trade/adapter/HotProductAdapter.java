package cn.com.vau.trade.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.res.ColorStateList;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Space;
import android.widget.TextView;

import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import cn.com.vau.R;
import cn.com.vau.common.application.InitHelper;
import cn.com.vau.common.constants.Constants;
import cn.com.vau.common.view.custom.WeeklyTrendBesselChart;
import cn.com.vau.data.init.ShareProductData;
import cn.com.vau.util.AttrResourceUtil;
import cn.com.vau.util.ExpandKt;

/**
 * 搜索-热门产品Adapter
 * Created by zhy on 2018/10/30.
 */
public class HotProductAdapter extends RecyclerView.Adapter<HotProductAdapter.SearchHotViewHolder> {

    private Context mContext;
    private OnItemClickListener onItemClickListener;
    private List<ShareProductData> mHotList;
    private HashMap<String, ArrayList<String>> mWeekTrendMap;

    private ColorStateList ce35728;
    private ColorStateList c00c79c;

    public HotProductAdapter(Context mContext, List<ShareProductData> hotList, HashMap<String, ArrayList<String>> weekTrendMap) {
        this.mContext = mContext;
        this.mHotList = hotList;
        this.mWeekTrendMap = weekTrendMap;
        ce35728 = ColorStateList.valueOf(ContextCompat.getColor(mContext, R.color.ce35728));
        c00c79c = ColorStateList.valueOf(ContextCompat.getColor(mContext, R.color.c00c79c));
    }

    @Override
    public SearchHotViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.item_search_hot, parent, false);
        return new SearchHotViewHolder(view);
    }

    @Override
    public void onBindViewHolder(final SearchHotViewHolder holder, @SuppressLint ("RecyclerView") final int position) {
        if(InitHelper.isNotSuccess()) {
            holder.tvHotProdName.setText("...");
            holder.tvHotProdPrice.setText("...");
            holder.tvHotProdRose.setText("...");
            holder.tvHotProdPrice.setTextColor(AttrResourceUtil.getColor(mContext, R.attr.color_ca61e1e1e_c99ffffff));
            holder.ivPriceUpDown.setVisibility(View.GONE);
            return;
        }
        ShareProductData data = mHotList.get(position);
        holder.tvHotProdName.setText(data.getSymbol());
        holder.tvHotProdPrice.setText(ExpandKt.numFormat(data.getBid(), data.getDigits(), false));
        if(Math.abs(data.getRose()) == 0f) {
            holder.tvHotProdRose.setText("0.0%");
        } else {
            holder.tvHotProdRose.setText(ExpandKt.numFormat(data.getRose(), 2, false) + "%");
        }

        holder.weekTrendChart.setData(mWeekTrendMap.get(data.getSymbol()), true);
        String bidUI = TextUtils.isEmpty(data.getBidUI())? ExpandKt.formatProductPrice(data.getBid(), data.getDigits(), true, Constants.DOUBLE_LINE) : data.getBidUI();
//        String roseUI = TextUtils.isEmpty(data.getRoseUI())?
//                (ExpandKt.toFloatCatching(data.getClosePrice(), 0f) == 0f || bidUI.equals(Constants.DOUBLE_LINE)) ? Constants.DOUBLE_LINE : ExpandKt.numFormat(data.getRose(), 2, true)
//                : data.getRoseUI();
        int roseType = (ExpandKt.toFloatCatching(data.getClosePrice(), 0f) == 0f || bidUI.equals(Constants.DOUBLE_LINE))? -1 : (data.getRose() >= 0? 0 : 1);
        switch (roseType) {
            case -1: {
                holder.tvHotProdPrice.setTextColor(AttrResourceUtil.getColor(mContext, R.attr.color_ca61e1e1e_c99ffffff));
                holder.ivPriceUpDown.setVisibility(View.GONE);
                break;
            }
            case 0: {
                holder.tvHotProdPrice.setTextColor(ContextCompat.getColor(mContext, R.color.c00c79c));
                holder.ivPriceUpDown.setImageResource(R.drawable.img_source_arrow_up);
                holder.ivPriceUpDown.setImageTintList(c00c79c);
                holder.ivPriceUpDown.setRotation(0f);
                holder.ivPriceUpDown.setVisibility(View.VISIBLE);
                break;
            }
            case 1: {
                holder.tvHotProdPrice.setTextColor(ContextCompat.getColor(mContext, R.color.ce35728));
                holder.ivPriceUpDown.setImageResource(R.drawable.img_source_arrow_up);
                holder.ivPriceUpDown.setImageTintList(ce35728);
                holder.ivPriceUpDown.setRotation(180f);
                holder.ivPriceUpDown.setVisibility(View.VISIBLE);
                break;
            }
        }

        if(onItemClickListener != null) {
            holder.itemView.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    onItemClickListener.onItemClick(holder.itemView, position);
                }
            });
        }
    }

    @Override
    public int getItemCount() {
        return mHotList != null ? mHotList.size() : 0;
    }

    public class SearchHotViewHolder extends RecyclerView.ViewHolder {
        TextView tvHotProdName, tvHotProdPrice, tvHotProdRose;
        ImageView ivPriceUpDown;
        WeeklyTrendBesselChart weekTrendChart;
        Space emptySpace;
        public SearchHotViewHolder(View itemView) {
            super(itemView);
            tvHotProdName = itemView.findViewById(R.id.tvHotProdName);
            tvHotProdPrice = itemView.findViewById(R.id.tvHotProdPrice);
            ivPriceUpDown = itemView.findViewById(R.id.ivPriceUpDown);
            tvHotProdRose = itemView.findViewById(R.id.tvHotProdRose);
            weekTrendChart = itemView.findViewById(R.id.weekTrendChart);
            //emptySpace = itemView.findViewById(R.id.emptySpace);
        }
    }

    public void setOnItemClickListener(OnItemClickListener onItemClickListener) {
        this.onItemClickListener = onItemClickListener;
    }

    public interface OnItemClickListener {
        void onItemClick(View view, int position);
    }
}
