package cn.com.vau.trade.view

import android.content.Context
import androidx.core.content.ContextCompat
import cn.com.vau.R
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.storage.SpManager
import cn.com.vau.databinding.PopupTradeSwitchModeBinding
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import com.lxj.xpopup.core.AttachPopupView
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

class TradeSwitchModePopup(context: Context) : AttachPopupView(context) {

    private var mBinding: PopupTradeSwitchModeBinding? = null

    private val color_c1f1e1e1e_c1fffffff by lazy { AttrResourceUtil.getColor(context, R.attr.color_c1f1e1e1e_c1fffffff) }

    private val img_classic_selected by lazy { ContextCompat.getDrawable(context, R.drawable.img_classic_selected) }
    private val imgAskUnselected by lazy { ContextCompat.getDrawable(context, AttrResourceUtil.getDrawable(context, R.attr.imgAskUnselected)) }
    private val img_ask_selected by lazy { ContextCompat.getDrawable(context, R.drawable.img_ask_selected) }
    private val imgClassicUnselected by lazy { ContextCompat.getDrawable(context, AttrResourceUtil.getDrawable(context, R.attr.imgClassicUnselected)) }

    override fun getImplLayoutId(): Int = R.layout.popup_trade_switch_mode

    override fun onCreate() {
        super.onCreate()
        val selectedMode = SpManager.getTradeSwitchMode()
        mBinding = PopupTradeSwitchModeBinding.bind(popupImplView)
        when (selectedMode) {
            0 -> {
                if (LanguageHelper.isRtlLanguage()) {
                    mBinding?.tvClassic?.setCompoundDrawablesWithIntrinsicBounds(null, null, img_classic_selected, null)
                    mBinding?.tvBuySell?.setCompoundDrawablesWithIntrinsicBounds(null, null, imgAskUnselected, null)
                } else {
                    mBinding?.tvClassic?.setCompoundDrawablesWithIntrinsicBounds(img_classic_selected, null, null, null)
                    mBinding?.tvBuySell?.setCompoundDrawablesWithIntrinsicBounds(imgAskUnselected, null, null, null)
                }
                mBinding?.tvClassic?.setBackgroundColor(color_c1f1e1e1e_c1fffffff)
                mBinding?.tvBuySell?.setBackgroundColor(0)
            }

            1 -> {
                if (LanguageHelper.isRtlLanguage()) {
                    mBinding?.tvBuySell?.setCompoundDrawablesWithIntrinsicBounds(null, null, img_ask_selected, null)
                    mBinding?.tvClassic?.setCompoundDrawablesWithIntrinsicBounds(null, null, imgClassicUnselected, null)
                } else {
                    mBinding?.tvBuySell?.setCompoundDrawablesWithIntrinsicBounds(img_ask_selected, null, null, null)
                    mBinding?.tvClassic?.setCompoundDrawablesWithIntrinsicBounds(imgClassicUnselected, null, null, null)
                }
                mBinding?.tvBuySell?.setBackgroundColor(color_c1f1e1e1e_c1fffffff)
                mBinding?.tvClassic?.setBackgroundColor(0)
            }
        }
        mBinding?.tvClassic?.setBackgroundColor(if (selectedMode == 0) {
            color_c1f1e1e1e_c1fffffff
        } else {
            0
        })
        mBinding?.tvBuySell?.setBackgroundColor(if (selectedMode == 1) {
            color_c1f1e1e1e_c1fffffff
        } else {
            0
        })

        mBinding?.tvClassic?.clickNoRepeat {
            if (selectedMode != 0) {
                // Classic Mode:
                SpManager.putTradeSwitchMode(0)
                EventBus.getDefault().post(NoticeConstants.Quotes.TRADE_SWITCH_MODE_CLASSIC)
            }
            // 埋点
            SensorsDataUtil.track(SensorsConstant.V3560.LISTMODETOGGLE_CLICK, JSONObject().apply {
                put("mode_type", "classic")
            })
            dismiss()
        }
        mBinding?.tvBuySell?.clickNoRepeat {
            if (selectedMode != 1) {
                // Buy/Sell Mode:
                SpManager.putTradeSwitchMode(1)
                EventBus.getDefault().post(NoticeConstants.Quotes.TRADE_SWITCH_MODE_BUYSELL)
            }
            // 埋点
            SensorsDataUtil.track(SensorsConstant.V3560.LISTMODETOGGLE_CLICK, JSONObject().apply {
                put("mode_type", "buy/Sell")
            })
            dismiss()
        }
    }

}