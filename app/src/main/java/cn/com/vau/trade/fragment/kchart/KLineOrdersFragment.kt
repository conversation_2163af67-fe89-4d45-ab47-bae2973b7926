package cn.com.vau.trade.fragment.kchart

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentKlineOrdersBinding
import cn.com.vau.trade.fragment.order.OpenTradesFragment
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.util.TabType
import cn.com.vau.util.init
import cn.com.vau.util.setVp
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * author：lvy
 * date：2024/10/21
 * desc：k线页->Orders
 */
class KLineOrdersFragment : BaseMvvmBindingFragment<FragmentKlineOrdersBinding>() {

    private val activityViewModel: KLineViewModel by activityViewModels()

    private var prodName = ""

    val titleList = arrayListOf<String>()

    private val openTradeFragment by lazy {
        OpenTradesFragment()
    }
    private val pendingOrderFragment by lazy {
        KLinePendingOrderFragment.newInstance()
    }

    override fun initParam(savedInstanceState: Bundle?) {
        prodName = activityViewModel.symbol
        if (!EventBus.getDefault().isRegistered(this)) {
            EventBus.getDefault().register(this)
        }
    }

    override fun initView() {
        initTabLayout()
    }

    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        val bundle = Bundle()
        bundle.putString(Constants.PARAM_PRODUCT_NAME, activityViewModel.symbol)
        bundle.putBoolean("isKline", true)
        // 持仓列表
        openTradeFragment.arguments = bundle
        fragments.add(openTradeFragment)
        fragments.add(pendingOrderFragment)
        titleList.clear()
        titleList.add(getString(R.string.positions))
        titleList.add(getString(R.string.pending_orders))

        mBinding.mViewPager2.init(fragments, titleList, childFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, titleList, TabType.LINE_INDICATOR)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventBus(tag: String) {
        if (tag == NoticeConstants.EVENT_KLINE_SWITCH_PRODUCT) {
            prodName = activityViewModel.symbol
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    companion object {
        fun newInstance() = KLineOrdersFragment()
    }
}