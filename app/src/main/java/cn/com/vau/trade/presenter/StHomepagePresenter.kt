package cn.com.vau.trade.presenter

import StHomepageContract
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.utils.HandlerUtil
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.KycVerifyLevelDataBean
import cn.com.vau.data.discover.StrategyMostCopied
import cn.com.vau.data.discover.StrategyRecommendAllBean
import cn.com.vau.data.init.ImgAdvertInfoObj
import cn.com.vau.util.*
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import io.reactivex.disposables.Disposable
import kotlinx.coroutines.*
import org.json.JSONObject

/**
 * Created by roy on 2018/12/1.
 * 订单持仓
 */
class StHomepagePresenter : StHomepageContract.Presenter() {

    var isViewCreated: Boolean = false
    var isUIVisible: Boolean = false

    var userGroupChange = true
    var willToTabGroupName = ""
    private var clickAble = true

    // 用户是否入过金  默认：入过金
    var isDeposited: Boolean = true

    // Banner数据
    var bannerData: ImgAdvertInfoObj? = null

    override fun imgCloseApi(idList: String) {
        if (clickAble) {
            clickAble = false
            HandlerUtil().postDelayTask(1000) { clickAble = true }
            val paramMap = hashMapOf<String, Any>(
                "imgType" to 21,     // 21:首页广告位
                "eventIdList" to idList,
                "userId" to UserDataUtil.userId(),
                "accountId" to UserDataUtil.accountCd(),
                "token" to UserDataUtil.loginToken()
            )
            mModel?.imgCloseApi(paramMap, object : BaseObserver<DataObjStringBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(dataBean: DataObjStringBean?) {
                    if (dataBean?.resultCode != "********" && dataBean?.resultCode != "V00000") {
//                        ToastUtil.showToast(dataBean?.msgInfo)
                        return
                    }
                    bannerData = null

                }
            })
        }
    }

    override fun strategyDiscoverListAllApi(accountId: String) {
        mModel.strategyDiscoverListAllApi(accountId, object : BaseObserver<StrategyRecommendAllBean>() {
            override fun onNext(dataBean: StrategyRecommendAllBean?) {
                if (dataBean?.code != "200") {
                    ToastUtil.showToast(dataBean?.msgInfo)
                    mView?.finishRefresh()
                    return
                }
                CoroutineScope(Dispatchers.Default).launch {
                    dataBean.data?.profitShield?.forEach { item ->
                        item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                        item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                    }
                    dataBean.data?.mostCopied?.forEach { item ->
                        item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                        item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                    }
                    dataBean.data?.highestReturn?.forEach { item ->
                        item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                        item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                    }
                    dataBean.data?.lowRisk?.forEach { item ->
                        item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                        item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                    }
                    dataBean.data?.highestWinRate?.forEach { item ->
                        item.returnRateUI = "${item.returnRate.mathMul("100").numFormat2(2, true)}%"
                        item.winRateUI = "${item.winRate.mathMul("100").numFormat2(2, true)}%"
                    }

                    MainScope().launch {
                        mView?.showStrategyRecommend(dataBean.data)
                    }
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.finishRefresh()
            }
        })
    }

    override fun userQueryUserLevel() {
        val map = hashMapOf<String, Any>()
//        map["countryId"] =
        mModel?.userQueryUserLevel(map, object : BaseObserver<KycVerifyLevelDataBean>() {
            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: KycVerifyLevelDataBean) {
                if (dataBean.resultCode != "V00000") {
                    return
                }
                mView?.kycGuideInfo(dataBean.data?.obj)
            }
        })
    }

    /**
     * 神策自定义埋点(v3500)
     * App_发现页面点击 -> 点击app发现页面内容时触发
     */
    fun sensorsTrack(mktId: String, mktPos: Int, targetUrl: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.BELONG_TAB_NAME, "") // 所属Tab 名称
        properties.put(SensorsConstant.Key.MODULE_ID, "") // 模块id
        properties.put(SensorsConstant.Key.MODULE_NAME, "") // 模块名称
        properties.put(SensorsConstant.Key.MODULE_RANK, "") // 模块序号
        properties.put(SensorsConstant.Key.MKT_ID, mktId) // 素材id
        properties.put(SensorsConstant.Key.MKT_NAME, "") // 素材名称
        properties.put(SensorsConstant.Key.MKT_RANK, mktPos + 1) // 素材排序
        properties.put(SensorsConstant.Key.TARGET_URL, targetUrl) // 跳转链接
        SensorsDataUtil.track(SensorsConstant.V3500.APP_DISCOVER_PAGE_CLICK, properties)
    }

    fun sensorsCopyTradingStrategyClick(data: StrategyMostCopied, typeForm: Int) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.TARGET_NAME, data.nickname.ifNull()) // 策略名称
        properties.put(SensorsConstant.Key.STRATEGY_ID, data.strategyId.ifNull()) // 策略ID
        properties.put("section_name", when (typeForm) {
            -1 -> "Growth Shield"
            0 -> "Most Copied"
            1 -> "Highest Annual Return"
            2 -> "Low Risk and Stable Return"
            else -> "None"
        })  // 栏位名称
        properties.put("current_tab", "Trades") // 当前页面
        SensorsDataUtil.track(SensorsConstant.V3610.COPYTRADINGSTRATEGY_CLICK, properties)
    }

    fun sensorUpgradeClick(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.CURRENT_PAGE_NAME, "Home Page")
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName)
        SensorsDataUtil.track(SensorsConstant.V3700.UPGRADE_CLICK, properties)
    }
}