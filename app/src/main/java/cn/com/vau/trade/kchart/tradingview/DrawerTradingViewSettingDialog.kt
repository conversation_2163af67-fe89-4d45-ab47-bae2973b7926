package cn.com.vau.trade.kchart.tradingview

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.graphics.RectF
import android.os.Bundle
import android.text.Editable
import android.view.*
import android.widget.*
import androidx.core.content.res.ResourcesCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.transition.ChangeBounds
import androidx.transition.TransitionManager
import cn.com.vau.R
import cn.com.vau.common.view.popup.bean.*
import cn.com.vau.databinding.DialogDrawerTradingViewSettingBinding
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.base.DrawerDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import com.lxj.xpopup.core.*
import com.lxj.xpopup.enums.PopupPosition
import java.util.*

/**
 * Filename: TradingViewSettingPopup
 * Author: GG
 * Date: 2023/9/4 0004 14:49
 * Description:
 */
@SuppressLint("ViewConstructor")
class DrawerTradingViewSettingDialog private constructor(context: Context, private val bridge: TradingViewInterface?) : DrawerDialog<DialogDrawerTradingViewSettingBinding>(context, DialogDrawerTradingViewSettingBinding::inflate) {

    private var data: TradingViewSettingData? = null
    private var mainSelectedIndex: Int = -1
    private var subSelectedIndex: Int = -1
    private var selectedTabIndex: Int = -1
    var stillOpenTabIndex: Int = -1

    override fun setContentView() {
        super.setContentView()

        data = KLineDataUtils.userDataTV ?: TradingViewSettingData.getHistoryData()
        mContentBinding.run {
            // 设置main 的 tab
            setMainTab()
            // 设置sub 的 tab
            setSubTab()
            // 设置line
            setLine()

            setClickListener()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun DialogDrawerTradingViewSettingBinding.setMainTab() {
        with(viewMain) {
            cb1.text = context.getString(R.string.ma)
            cb2.text = context.getString(R.string.ema)
            cb3.text = context.getString(R.string.boll)
            cb4.text = "MIKE"
            cb5.text = "BBI"
            cb6.text = "SAR"
            cb7.isVisible = false
        }

        randerMainTab(data?.mainSelectedIndex.ifNull())
        // 设置 TabLayout 监听器
        tlMain.setOnTabSelectedListener {
            viewMain.nsView.isVisible = it != 0
        }
    }

    @SuppressLint("SetTextI18n")
    private fun DialogDrawerTradingViewSettingBinding.setSubTab() {
        with(viewSub) {
            cb1.text = context.getString(R.string.macd)
            cb2.text = context.getString(R.string.kdj)
            cb3.text = context.getString(R.string.rsi)
            cb4.text = context.getString(R.string.wr)
            cb5.text = "CCI"
            cb6.text = "KD"
            cb7.text = "DMI"
        }

        randerSubTab(data?.subSelectedIndex.ifNull())
        // 设置 TabLayout 监听器
        tlSub.setOnTabSelectedListener {
            viewSub.nsView.isVisible = it != 0
        }
    }

    private fun DialogDrawerTradingViewSettingBinding.setLine() {
        cbAsk.isChecked = data?.line?.ask?.status == 1
        cbBid.isChecked = data?.line?.bid?.status == 1
        cbTake.isChecked = data?.line?.tp?.status == 1
        cbStop.isChecked = data?.line?.sl?.status == 1
        cbOpen.isChecked = data?.line?.position?.status == 1
    }

    private fun DialogDrawerTradingViewSettingBinding.setClickListener() {
        clMainView.post {
            if (stillOpenTabIndex != -1) {
                val tabOpenIndex = stillOpenTabIndex
                stillOpenTabIndex = -1
//                LogUtil.d("wj", "still: $tabOpenIndex")
                selectedTab(tabOpenIndex, tabOpenIndex)
            }
        }

        ivMainMore.setOnClickListener { selectedTab(0) }
        tvMainTitle.setOnClickListener { selectedTab(0) }
        ivSubMore.setOnClickListener { selectedTab(1) }
        tvSubTitle.setOnClickListener { selectedTab(1) }
        ivLineMore.setOnClickListener { selectedTab(2) }
        tvLineTitle.setOnClickListener { selectedTab(2) }
        cbAsk.setOnCheckedChangeListener { _, isChecked ->
            data?.line?.ask?.status = if (isChecked) 1 else 0
            lineBuryPoint("Ask", if (isChecked) "On" else "Off")
        }
        cbBid.setOnCheckedChangeListener { _, isChecked ->
            data?.line?.bid?.status = if (isChecked) 1 else 0
            lineBuryPoint("Bid", if (isChecked) "On" else "Off")
        }
        cbTake.setOnCheckedChangeListener { _, isChecked ->
            data?.line?.tp?.status = if (isChecked) 1 else 0
            /** 止盈线或者止损线开启，持仓线同步开启 */
            if (isChecked) {
                cbOpen.isChecked = true
                data?.line?.position?.status = 1
            }
            lineBuryPoint("Open", if (isChecked) "On" else "Off")
        }
        cbStop.setOnCheckedChangeListener { _, isChecked ->
            data?.line?.sl?.status = if (isChecked) 1 else 0
            /** 止盈线或者止损线开启，持仓线同步开启 */
            if (isChecked) {
                cbOpen.isChecked = true
                data?.line?.position?.status = 1
            }
            lineBuryPoint("TP", if (isChecked) "On" else "Off")
        }
        cbOpen.setOnCheckedChangeListener { _, isChecked ->
            data?.line?.position?.status = if (isChecked) 1 else 0
            /** 持仓线关闭，止盈线和止损线同步关闭 */
            if (isChecked.not()) {
                cbTake.isChecked = false
                data?.line?.tp?.status = 0
                cbStop.isChecked = false
                data?.line?.sl?.status = 0
            }
            lineBuryPoint("SL", if (isChecked) "On" else "Off")
        }

        viewMain.apply {
            ivHandCountDown1.setOnClickListener { mainCalculate(0, false) }
            ivHandCountDown2.setOnClickListener { mainCalculate(1, false) }
            ivHandCountDown3.setOnClickListener { mainCalculate(2, false) }
            ivHandCountUp1.setOnClickListener { mainCalculate(0, true) }
            ivHandCountUp2.setOnClickListener { mainCalculate(1, true) }
            ivHandCountUp3.setOnClickListener { mainCalculate(2, true) }
            etValue1.doAfterTextChanged { mainEtWatcher(0, it) }
            etValue2.doAfterTextChanged { mainEtWatcher(1, it) }
            etValue3.doAfterTextChanged { mainEtWatcher(2, it) }
            cb1.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6)
                randerMainTab(0)
                indicatorBuryPoint("Main", "MA")
            }
            cb2.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6)
                randerMainTab(1)
                indicatorBuryPoint("Main", "EMA")
            }
            cb3.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6)
                randerMainTab(2)
                indicatorBuryPoint("Main", "BOLL")
            }
            cb4.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6)
                randerMainTab(3)
                indicatorBuryPoint("Main", "MIKE")
            }
            cb5.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6)
                randerMainTab(4)
                indicatorBuryPoint("Main", "BBI")
            }
            cb6.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6)
                randerMainTab(5)
                indicatorBuryPoint("Main", "SAR")
            }
            tvReset.setOnClickListener {
                mainDataReset()
            }
            tvDetail.setOnClickListener {}
        }

        viewSub.apply {
            ivHandCountDown1.setOnClickListener { subCalculate(0, false) }
            ivHandCountDown2.setOnClickListener { subCalculate(1, false) }
            ivHandCountDown3.setOnClickListener { subCalculate(2, false) }
            ivHandCountUp1.setOnClickListener { subCalculate(0, true) }
            ivHandCountUp2.setOnClickListener { subCalculate(1, true) }
            ivHandCountUp3.setOnClickListener { subCalculate(2, true) }
            etValue1.doAfterTextChanged { subEtWatcher(0, it) }
            etValue2.doAfterTextChanged { subEtWatcher(1, it) }
            etValue3.doAfterTextChanged { subEtWatcher(2, it) }
            cb1.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(0)
                indicatorBuryPoint("Sub", "MACD")
            }
            cb2.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(1)
                indicatorBuryPoint("Sub", "KDJ")
            }
            cb3.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(2)
                indicatorBuryPoint("Sub", "RSI")
            }
            cb4.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(3)
                indicatorBuryPoint("Sub", "WR")
            }
            cb5.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(4)
                indicatorBuryPoint("Sub", "CCI")
            }
            cb6.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(5)
                indicatorBuryPoint("Sub", "KD")
            }
            cb7.setOnClickListener {
                setCheckState(it.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                randerSubTab(6)
                indicatorBuryPoint("Sub", "DMI")
            }
            tvReset.setOnClickListener {
                subDataReset()
            }
            tvDetail.setOnClickListener {}
        }
    }

    /**
     * 重置主指标对应tab 的数据
     */
    private fun mainDataReset() {
        when (mainSelectedIndex) {
            0 -> {
                data?.main?.ma?.child1?.num = 5
                data?.main?.ma?.child2?.num = 10
                data?.main?.ma?.child3?.num = 30
                randerMainTab(0)
            }

            1 -> {
                data?.main?.ema?.child1?.num = 5
                data?.main?.ema?.child2?.num = 10
                data?.main?.ema?.child3?.num = 30
                randerMainTab(1)
            }

            2 -> {
                data?.main?.boll?.child1?.num = 20
                data?.main?.boll?.child2?.num = 2
                randerMainTab(2)
            }

            3 -> {
                data?.main?.mike?.child1?.num = 12
                randerMainTab(3)
            }

        }
    }

    /**
     * 重置副指标对应tab 的数据
     */
    private fun subDataReset() {
        when (subSelectedIndex) {
            0 -> {
                data?.sub?.macd?.child1?.num = 12
                data?.sub?.macd?.child2?.num = 26
                data?.sub?.macd?.child3?.num = 9
                randerSubTab(0)
            }

            1 -> {
                data?.sub?.kdj?.child1?.num = 14
                data?.sub?.kdj?.child2?.num = 1
                data?.sub?.kdj?.child3?.num = 3
                randerSubTab(1)
            }

            2 -> {
                data?.sub?.rsi?.child1?.num = 14
                randerSubTab(2)
            }

            3 -> {
                data?.sub?.wr?.child1?.num = 14
                randerSubTab(3)
            }

            4 -> {
                data?.sub?.cci?.child1?.num = 14
                randerSubTab(4)
            }

            5 -> {
                data?.sub?.kd?.child1?.num = 14
                data?.sub?.kd?.child2?.num = 3
                data?.sub?.kd?.child3?.num = 3
                randerSubTab(5)
            }

            6 -> {
                data?.sub?.dmi?.child1?.num = 14
                data?.sub?.dmi?.child2?.num = 6
                randerSubTab(6)
            }
        }
    }

    private fun lineBuryPoint(selectLine: String, toggle: String) {
        val bundle = Bundle()
        bundle.putString("Account_type", KLineActivity.getPointAccountType())
        bundle.putString("Mode", "Pro-horizontal")
        bundle.putString("Line", selectLine)
        bundle.putString("Toggle", toggle)
        LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_SETTINGS_LINE_BUTTON_CLICK, bundle)
    }

    private fun indicatorBuryPoint(category: String, indicator: String) {
        val bundle = Bundle()
        bundle.putString("Account_type", KLineActivity.getPointAccountType())
        bundle.putString("Mode", "Pro-horizontal")
        bundle.putString("Category", category)
        bundle.putString("Indicators", indicator)
        LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_INDICATORS_BUTTON_CLICK, bundle)
    }

    private fun mainEtWatcher(list: Int, editable: Editable?) {
        val pair = getMainPair(list)
        val et = pair.first
        val item = pair.second
        if (et != null && item != null && editable != null) {
            if (editable.toString().isNotEmpty()) {
                var num = editable.toString().toIntCatching()
                if (num > item.maxNum) {
                    num = item.default
                    et.setText("$num")
                    et.setSelection(et.text.length)
                } else if (num < item.minNum) {
                    num = item.default
                    et.setText("$num")
                    et.setSelection(et.text.length)
                }
                item.num = num
            } else {
                if (item.minNum == 0) {
                    item.num = 0
                } else {
                    item.num = item.default
                    ToastUtil.showToast(context.getString(R.string.the_required_param_empty))
                }
                et.setText("${item.num}")
                et.setSelection(et.text.length)
            }
        }
    }

    private fun subEtWatcher(list: Int, editable: Editable?) {
        val pair = getSubPair(list)
        val et = pair.first
        val item = pair.second
        if (et != null && item != null && editable != null) {
            if (editable.toString().isNotEmpty()) {
                var num = editable.toString().toIntCatching()
                if (num > item.maxNum) {
                    num = item.default
                    et.setText("$num")
                    et.setSelection(et.text.length)
                } else if (num < item.minNum) {
                    num = item.default
                    et.setText("$num")
                    et.setSelection(et.text.length)
                }
                item.num = num
            } else {
                if (item.minNum == 0) {
                    item.num = 0
                } else {
                    item.num = item.default
                    ToastUtil.showToast(context.getString(R.string.the_required_param_empty))
                }
                et.setText("${item.num}")
                et.setSelection(et.text.length)
            }
        }
    }

    /**
     * * from  0:child1  1:child2  2:child3
     */
    private fun mainCalculate(list: Int, isAdd: Boolean) {
        val pair = getMainPair(list)
        val et = pair.first
        val item = pair.second
        if (et != null && item != null) {
            val num = et.text.toString().toIntCatching()
            val calc = if (isAdd) num + 1 else num - 1
            if (isAdd) {
                if (calc <= item.maxNum) {
                    item.num = calc
                    et.setText("$calc")
                    et.setSelection(et.text.length)
                }
            } else {
                if (calc >= item.minNum) {
                    item.num = calc
                    et.setText("$calc")
                    et.setSelection(et.text.length)
                }
            }
            et.requestFocus()
        }
    }

    /**
     * * from  0:child1  1:child2  2:child3
     */
    private fun subCalculate(list: Int, isAdd: Boolean) {
        val pair = getSubPair(list)
        val et = pair.first
        val item = pair.second
        if (et != null && item != null) {
            val num = et.text.toString().toIntCatching()
            val calc = if (isAdd) num + 1 else num - 1
            if (isAdd) {
                if (calc <= item.maxNum) {
                    item.num = calc
                    et.setText("$calc")
                    et.setSelection(et.text.length)
                }
            } else {
                if (calc >= item.minNum) {
                    item.num = calc
                    et.setText("$calc")
                    et.setSelection(et.text.length)
                }
            }
            et.requestFocus()
        }
    }

    private fun getMainPair(list: Int): Pair<EditText?, ListItem?> {
        var et: EditText? = null
        var item: ListItem? = null
        when (list) {
            0 -> {
                et = mContentBinding.viewMain.etValue1
                item = when (mainSelectedIndex) {
                    0 -> data?.main?.ma?.child1
                    1 -> data?.main?.ema?.child1
                    2 -> data?.main?.boll?.child1
                    3 -> data?.main?.mike?.child1
                    else -> null
                }
            }

            1 -> {
                et = mContentBinding.viewMain.etValue2
                item = when (mainSelectedIndex) {
                    0 -> data?.main?.ma?.child2
                    1 -> data?.main?.ema?.child2
                    2 -> data?.main?.boll?.child2
                    else -> null
                }
            }

            2 -> {
                et = mContentBinding.viewMain.etValue3
                item = when (mainSelectedIndex) {
                    0 -> data?.main?.ma?.child3
                    1 -> data?.main?.ema?.child3
                    else -> null
                }
            }
        }
        return Pair(et, item)
    }

    private fun getSubPair(list: Int): Pair<EditText?, ListItem?> {
        var et: EditText? = null
        var item: ListItem? = null
        when (list) {
            0 -> {
                et = mContentBinding.viewSub.etValue1
                when (subSelectedIndex) {
                    0 -> item = data?.sub?.macd?.child1
                    1 -> item = data?.sub?.kdj?.child1
                    2 -> item = data?.sub?.rsi?.child1
                    3 -> item = data?.sub?.wr?.child1
                    4 -> item = data?.sub?.cci?.child1
                    5 -> item = data?.sub?.kd?.child1
                    6 -> item = data?.sub?.dmi?.child1
                }
            }

            1 -> {
                et = mContentBinding.viewSub.etValue2
                when (subSelectedIndex) {
                    0 -> item = data?.sub?.macd?.child2
                    1 -> item = data?.sub?.kdj?.child2
                    2 -> item = data?.sub?.rsi?.child2
                    3 -> item = data?.sub?.wr?.child2
                    4 -> item = data?.sub?.cci?.child2
                    5 -> item = data?.sub?.kd?.child2
                    6 -> item = data?.sub?.dmi?.child2
                }
            }

            2 -> {
                et = mContentBinding.viewSub.etValue3
                when (subSelectedIndex) {
                    0 -> item = data?.sub?.macd?.child3
                    1 -> item = data?.sub?.kdj?.child3
                    2 -> item = data?.sub?.rsi?.child3
                    3 -> item = data?.sub?.wr?.child3
                    5 -> item = data?.sub?.kd?.child3
                }
            }
        }
        return Pair(et, item)
    }

    private fun selectedTab(type: Int, stillOpenIndex: Int = -1) {
        if (selectedTabIndex != 0) {
//            LogUtil.d("wj", "main ${if (type == 0) "打开" else "关闭"}")
            mainDrawerVisible(type == 0)
        } else {
//            LogUtil.d("wj", "main 判断关闭")
            if (0 != stillOpenIndex) {
//                LogUtil.d("wj", "main 关闭")
                selectedTabIndex = -1
                mainDrawerVisible(false)
            }
        }

        if (selectedTabIndex != 1) {
//            LogUtil.d("wj", "sub ${if (type == 1) "打开" else "关闭"}")
            subDrawerVisible(type == 1)
        } else {
//            LogUtil.d("wj", "sub 判断关闭")
            if (1 != stillOpenIndex) {
//                LogUtil.d("wj", "sub 关闭")
                selectedTabIndex = -1
                subDrawerVisible(false)
            }
        }

        if (selectedTabIndex != 2) {
//            LogUtil.d("wj", "line ${if (type == 2) "打开" else "关闭"}")
            lineDrawerVisible(type == 2)
        } else {
//            LogUtil.d("wj", "line 判断关闭")
            if (2 != stillOpenIndex) {
//                LogUtil.d("wj", "line 关闭")
                selectedTabIndex = -1
                lineDrawerVisible(false)
            }
        }
    }

    private fun mainDrawerVisible(visible: Boolean) {

        if (visible) {
            mContentBinding.ivMainMore.rotation = 0f
            selectedTabIndex = 0
            mContentBinding.groupMain.isVisible = true
        } else {
            mContentBinding.ivMainMore.rotation = 180f
            mContentBinding.groupMain.isVisible = false
        }
    }

    private fun subDrawerVisible(visible: Boolean) {
        TransitionManager.beginDelayedTransition(mContentBinding.root, ChangeBounds())
        if (visible) {
            mContentBinding.ivSubMore.rotation = 0f
            selectedTabIndex = 1
            mContentBinding.groupSub.isVisible = true
        } else {
            mContentBinding.ivSubMore.rotation = 180f
            mContentBinding.groupSub.isVisible = false
        }
    }

    private fun lineDrawerVisible(visible: Boolean) {
        TransitionManager.beginDelayedTransition(mContentBinding.root, ChangeBounds())

        if (visible) {
            mContentBinding.ivLineMore.rotation = 0f
            selectedTabIndex = 2
            mContentBinding.groupLine.isVisible = true
        } else {
            mContentBinding.ivLineMore.rotation = 180f
            mContentBinding.groupLine.isVisible = false
        }
    }

    private fun randerMainTab(index: Int) {
        mainSelectedIndex = index
        mContentBinding.viewMain.run {
            when (index) {
                0 -> {
                    tvNoData.isVisible = false
                    groupChild1.visibility = VISIBLE
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = VISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.main?.ma?.child1?.title.ifNull()
                    tvTitle2.text = data?.main?.ma?.child2?.title.ifNull()
                    tvTitle3.text = data?.main?.ma?.child3?.title.ifNull()
                    etValue1.setText(data?.main?.ma?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.main?.ma?.child2?.num.toString().ifNull())
                    etValue3.setText(data?.main?.ma?.child3?.num.toString().ifNull())
                    data?.mainChartName = context.getString(R.string.ma)
                    data?.mainSelectedIndex = 0
                    tvDetail.text = context.getString(R.string.ma_indicator_intro)
                    setCheckState(cb1.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                1 -> {
                    tvNoData.isVisible = false
                    groupChild1.visibility = VISIBLE
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = VISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.main?.ema?.child1?.title.ifNull()
                    tvTitle2.text = data?.main?.ema?.child2?.title.ifNull()
                    tvTitle3.text = data?.main?.ema?.child3?.title.ifNull()
                    etValue1.setText(data?.main?.ema?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.main?.ema?.child2?.num.toString().ifNull())
                    etValue3.setText(data?.main?.ema?.child3?.num.toString().ifNull())
                    data?.mainChartName = context.getString(R.string.ema)
                    data?.mainSelectedIndex = 1
                    tvDetail.text = context.getString(R.string.ema_indicator_intro)
                    setCheckState(cb2.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                2 -> {
                    tvNoData.isVisible = false
                    groupChild1.visibility = VISIBLE
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.main?.boll?.child1?.title.ifNull()
                    tvTitle2.text = data?.main?.boll?.child2?.title.ifNull()
                    etValue1.setText(data?.main?.boll?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.main?.boll?.child2?.num.toString().ifNull())
                    data?.mainChartName = context.getString(R.string.boll)
                    data?.mainSelectedIndex = 2
                    tvDetail.text = context.getString(R.string.boll_indicator_intro)
                    setCheckState(cb3.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                3 -> {
                    tvNoData.isVisible = false
                    groupChild1.visibility = VISIBLE
                    groupChild2.visibility = INVISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.main?.mike?.child1?.title.ifNull()
                    etValue1.setText(data?.main?.mike?.child1?.num.toString().ifNull())
                    data?.mainChartName = "MIKE"
                    data?.mainSelectedIndex = 3
                    tvDetail.text = context.getString(R.string.mike_indicator_intro)
                    setCheckState(cb4.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                4 -> {
                    tvNoData.isVisible = true
                    groupChild1.visibility = INVISIBLE
                    groupChild2.visibility = INVISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = INVISIBLE
                    data?.mainChartName = "BBI"
                    data?.mainSelectedIndex = 4
                    tvDetail.text = context.getString(R.string.bbi_indicator_intro)
                    setCheckState(cb5.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                5 -> {
                    tvNoData.isVisible = true
                    groupChild1.visibility = INVISIBLE
                    groupChild2.visibility = INVISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = INVISIBLE
                    data?.mainChartName = "SAR"
                    data?.mainSelectedIndex = 5
                    tvDetail.text = context.getString(R.string.sar_indicator_intro)
                    setCheckState(cb6.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }
            }
        }
    }

    private fun randerSubTab(index: Int) {
        subSelectedIndex = index
        mContentBinding.viewSub.apply {
            when (index) {
                0 -> {
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = VISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.macd?.child1?.title.ifNull()
                    tvTitle2.text = data?.sub?.macd?.child2?.title.ifNull()
                    tvTitle3.text = data?.sub?.macd?.child3?.title.ifNull()
                    etValue1.setText(data?.sub?.macd?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.sub?.macd?.child2?.num.toString().ifNull())
                    etValue3.setText(data?.sub?.macd?.child3?.num.toString().ifNull())
                    data?.subChartName = context.getString(R.string.macd)
                    data?.subSelectedIndex = 0
                    tvDetail.text = context.getString(R.string.macd_indicator_intro)
                    setCheckState(cb1.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                1 -> {
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = VISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.kdj?.child1?.title.ifNull()
                    tvTitle2.text = data?.sub?.kdj?.child2?.title.ifNull()
                    tvTitle3.text = data?.sub?.kdj?.child3?.title.ifNull()
                    etValue1.setText(data?.sub?.kdj?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.sub?.kdj?.child2?.num.toString().ifNull())
                    etValue3.setText(data?.sub?.kdj?.child3?.num.toString().ifNull())
                    data?.subChartName = context.getString(R.string.kdj)
                    data?.subSelectedIndex = 1
                    tvDetail.text = context.getString(R.string.kdj_indicator_intro)
                    setCheckState(cb2.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                2 -> {
                    groupChild2.visibility = INVISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.rsi?.child1?.title.ifNull()
                    etValue1.setText(data?.sub?.rsi?.child1?.num.toString().ifNull())
                    data?.subChartName = context.getString(R.string.rsi)
                    data?.subSelectedIndex = 2
                    tvDetail.text = context.getString(R.string.rsi_indicator_intro)
                    setCheckState(cb3.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                3 -> {
                    groupChild2.visibility = INVISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.wr?.child1?.title.ifNull()
                    etValue1.setText(data?.sub?.wr?.child1?.num.toString().ifNull())
                    data?.subChartName = context.getString(R.string.wr)
                    data?.subSelectedIndex = 3
                    tvDetail.text = context.getString(R.string.wr_indicator_intro)
                    setCheckState(cb4.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                4 -> {
                    groupChild2.visibility = INVISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.cci?.child1?.title.ifNull()
                    etValue1.setText(data?.sub?.cci?.child1?.num.toString().ifNull())
                    data?.subChartName = "CCI"
                    data?.subSelectedIndex = 4
                    tvDetail.text = context.getString(R.string.cci_indicator_intro)
                    setCheckState(cb5.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                5 -> {
                    groupChild1.visibility = VISIBLE
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = VISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.kd?.child1?.title.ifNull()
                    tvTitle2.text = data?.sub?.kd?.child2?.title.ifNull()
                    tvTitle3.text = data?.sub?.kd?.child3?.title.ifNull()
                    etValue1.setText(data?.sub?.kd?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.sub?.kd?.child2?.num.toString().ifNull())
                    etValue3.setText(data?.sub?.kd?.child3?.num.toString().ifNull())
                    data?.subChartName = "KD"
                    data?.subSelectedIndex = 5
                    tvDetail.text = context.getString(R.string.kd_indicator_intro)
                    setCheckState(cb6.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }

                6 -> {
                    groupChild2.visibility = VISIBLE
                    groupChild3.visibility = INVISIBLE
                    tvReset.visibility = VISIBLE
                    tvTitle1.text = data?.sub?.dmi?.child1?.title.ifNull()
                    tvTitle2.text = data?.sub?.dmi?.child2?.title.ifNull()
//                    tvTitle3.text = data.sub?.wr?.child3?.title.ifNull()
                    etValue1.setText(data?.sub?.dmi?.child1?.num.toString().ifNull())
                    etValue2.setText(data?.sub?.dmi?.child2?.num.toString().ifNull())
//                    etValue3.setText(data.sub?.wr?.child3?.num.toString().ifNull())
                    data?.subChartName = "DMI"
                    data?.subSelectedIndex = 6
                    tvDetail.text = context.getString(R.string.dmi_indicator_intro)
                    setCheckState(cb7.id, cb1, cb2, cb3, cb4, cb5, cb6, cb7)
                }
            }
        }
    }

    private fun setCheckState(selectId: Int, vararg views: CheckBox) {
        views.forEach {
            val selected = it.id == selectId
            it.isChecked = selected
            it.setTextColor(AttrResourceUtil.getColor(context, if (it.id == selectId) R.attr.color_cebffffff_c1e1e1e else R.attr.color_c731e1e1e_c61ffffff))
            val typeface = ResourcesCompat.getFont(context, if (selected) R.font.gilroy_semi_bold else R.font.gilroy_medium)
            it.typeface = typeface
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent): Boolean {
        hideSoftKeyboard(event)
        return super.dispatchTouchEvent(event)
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        hideSoftKeyboard(event)
        return super.onTouchEvent(event)
    }

    /**
     * 隐藏键盘
     */
    private fun hideSoftKeyboard(event: MotionEvent) {
        try {
            if (event.action == MotionEvent.ACTION_DOWN) {
                val focusView: View? = activity.currentFocus
                //除了点击EditText自身区域的其他任何区域，都将键盘收起
                if (focusView?.windowToken != null && !isTouchView(event, focusView)) {
                    KeyboardUtil.hideSoftInput(this)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 是否触摸了当前焦点控件
     * @param event
     * @param focusView
     * @return
     */
    fun isTouchView(event: MotionEvent?, focusView: View?): Boolean {
        if (null == event || null == focusView) {
            return false
        }
        val x = event.x
        val y = event.y
        val outLocation = IntArray(2)
        focusView.getLocationOnScreen(outLocation)
        val rectF = RectF(
            outLocation[0].toFloat(),
            outLocation[1].toFloat(),
            (outLocation[0] + focusView.width).toFloat(),
            (outLocation[1] + focusView.height).toFloat()
        )
        return x >= rectF.left && (x <= rectF.right) && y >= rectF.top && y <= rectF.bottom
    }

    override fun show(): BasePopupView {
        if (stillOpenTabIndex != -1) {
            val tabOpenIndex = stillOpenTabIndex
            stillOpenTabIndex = -1
            selectedTab(tabOpenIndex, tabOpenIndex)
        }
        return super.show()
    }

    override fun beforeDismiss() {
        super.beforeDismiss()
        // 保存数据到本地
//        data.save()
        // 调用tradingView方法
        bridge?.callSettings()

        LogEventUtil.setLogEvent(
            BuryPointConstant.V345.TRADE_KLINE_PRO_HORIZONTAL_TOOLS_EDIT_BUTTON_CLICK, bundleOf(
                "Account_type" to KLineActivity.getPointAccountType(),
            )
        )
    }

    override fun dismiss() {
        if (KeyboardUtil.isSoftInputVisible(activity)) {
            KeyboardUtil.hideSoftInput(activity)
        } else {
            super.dismiss()
        }
    }

    class Builder(activity: Activity) : IBuilder<DialogDrawerTradingViewSettingBinding, Builder>(activity) {

        private var bridge: TradingViewInterface? = null

        fun setBridge(bridge: TradingViewInterface?): Builder {
            this.bridge = bridge
            return this
        }

        override fun getPopupPosition(): PopupPosition {
            val locale: Locale = LanguageHelper.getAppLocale()
            val isArabic = locale.language == "ar" || locale.language == "ara"
            return if (isArabic) {
                PopupPosition.Left
            } else {
                PopupPosition.Right
            }
        }

        override fun createDialog(context: Context): DrawerTradingViewSettingDialog {
            return DrawerTradingViewSettingDialog(activity, bridge)
        }

        override fun build(): DrawerTradingViewSettingDialog {
            setViewMode(true)
            return super.build() as DrawerTradingViewSettingDialog
        }
    }
}