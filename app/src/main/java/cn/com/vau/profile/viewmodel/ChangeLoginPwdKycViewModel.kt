package cn.com.vau.profile.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.page.login.*
import cn.com.vau.util.ToastUtil

/**
 * author：lvy
 * date：2025/04/01
 * desc：kyc-修改登录密码
 */
class ChangeLoginPwdKycViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    val getWithdrawRestrictionMsgSuccessLiveData = MutableLiveData<String?>() // 出金限制横幅文案获取成功

    /**
     * 获取出金限制横幅文案
     */
    fun getWithdrawRestrictionMsgApi(type: Int) {
        val map = hashMapOf<String, Any?>()
        map["userToken"] = UserDataUtil.loginToken()
        map["type"] = type // 1=更新手机号；2=更改登录密码；3=重置密码
        requestNet({ baseService.fundWithdrawRestrictionMessageApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            getWithdrawRestrictionMsgSuccessLiveData.value = it.data?.obj
        })
    }

    /**
     * 修改登录密码
     */
    fun changeLoginPwdApi(oldPwd: String?, newPwd: String?) {
        if (oldPwd == newPwd) {
            ToastUtil.showToast(getString(R.string.the_new_password_original_password))
            return
        }
        val map = hashMapOf<String, Any?>()
        map["token"] = UserDataUtil.loginToken()
        map["userPassword"] = oldPwd
        map["userNewPassword"] = newPwd
        map["userPasswordConfirm"] = newPwd
        if (signUpRequestBean?.validateType != 0) { // 未认证不传这些参数
            if (signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
                map["txId"] = signUpRequestBean?.txId
                map["authOptType"] = 2
            } else {
                map["authOptType"] = 1
            }
            map["validateCode"] = signUpRequestBean?.smsCode
        }
        requestNet({ baseService.updatePasswordApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 设置退出状态为true
            SpManager.putExitStatus(true)
        }, isShowDialog = true)
    }
}