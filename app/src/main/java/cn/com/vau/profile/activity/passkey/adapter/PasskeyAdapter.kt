package cn.com.vau.profile.activity.passkey.adapter

import cn.com.vau.R
import cn.com.vau.data.account.PasskeyBean
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class PasskeyAdapter : BaseQuickAdapter<PasskeyBean, BaseViewHolder>(R.layout.item_passkey_layout) {
    val added by lazy {
        context.getString(R.string.added)
    }
    init {
        addChildClickViewIds(R.id.deleteIv,R.id.editIv)
    }

    override fun convert(holder: BaseViewHolder, item: PasskeyBean) {

        holder.setText(R.id.passkeyNameTv, item.displayName)
            .setText(R.id.deviceTv,item.model)
            .setText(R.id.timeTv, "$added: ${item.createDateTime}")
    }
}