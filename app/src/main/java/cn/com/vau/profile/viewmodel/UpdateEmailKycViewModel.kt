package cn.com.vau.profile.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.util.ToastUtil

/**
 * author：lvy
 * date：2025/03/27
 * desc：kyc-修改邮箱
 */
class UpdateEmailKycViewModel : BaseViewModel() {

    val getWithdrawRestrictionMsgSuccessLiveData = MutableLiveData<String?>() // 出金限制横幅文案获取成功

    /**
     * 获取出金限制横幅文案
     */
    fun getWithdrawRestrictionMsgApi(type: Int) {
        val map = hashMapOf<String, Any?>()
        map["userToken"] = UserDataUtil.loginToken()
        map["type"] = type // 1=更新手机号；2=更改登录密码；3=重置密码；5=修改邮箱
        requestNet({ baseService.fundWithdrawRestrictionMessageApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            getWithdrawRestrictionMsgSuccessLiveData.value = it.data?.obj
        })
    }
}