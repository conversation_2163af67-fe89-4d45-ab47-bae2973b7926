package cn.com.vau.profile.activity.manageFunds

import android.os.Bundle
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.depositcoupon.*
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.openAccountFifth.OpenFifthAddressSelectActivity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import io.reactivex.disposables.Disposable

/**
 * Created by zhy on 2018/11/16.
 */
class FundsPresenter : FundsContract.Presenter() {

    override fun queryManageFunds(userToken: String?, accountId: String?, isDemo: String?, isShowDialog: Boolean) {
        if (isShowDialog) {
            mView?.showNetDialog()
        }
        val params = HashMap<String, Any?>()
        params["userToken"] = userToken
        params["accountId"] = accountId
        params["isDemo"] = isDemo
        mModel?.queryManageFunds(params, object : BaseObserver<ManageFundsBean?>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: ManageFundsBean?) {
                mView?.hideNetDialog()

                if ("********" != dataBean?.resultCode) {
                    ToastUtil.showToast(dataBean?.msgInfo)
                    return
                }

                if (mView != null && dataBean.data?.obj != null)
                    mView?.refreshManageFunds(dataBean.data?.obj)
            }
        })
    }

    override fun needUploadAddressProof() {
        mView?.showNetDialog()
        val supervisionType = SpManager.getSuperviseNum("")
        if (supervisionType != "1") {
            addressproofWithrawNeedUploadIdPoaProofApi()
            return
        }

        val params = HashMap<String, Any?>()
        params["token"] = UserDataUtil.loginToken()
        mModel?.addressproofWithrawNeedUploadAddressProofApi(params, object : BaseObserver<NeedUploadAddressProofBean?>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(data: NeedUploadAddressProofBean?) {
                if ("V00000" == data?.resultCode) {
                    var needUploadAddressProof: String? = ""
                    if (data.data != null && data.data?.obj != null && data.data?.obj?.needUploadAddressProof != null)
                        needUploadAddressProof = data.data?.obj?.needUploadAddressProof

                    if (TextUtils.equals("0", needUploadAddressProof)) {
                        fundIsH5WithdrawApi()
                    } else if (TextUtils.equals("3", needUploadAddressProof) || TextUtils.equals("1", needUploadAddressProof)) {
                        val bundle = Bundle()
                        bundle.putInt(Constants.IS_FROM, 1)
                        openActivity(OpenFifthAddressSelectActivity::class.java, bundle)
                        mView?.hideNetDialog()
                    } else if (TextUtils.equals("2", needUploadAddressProof)) {
                        mView?.hideNetDialog()
                        ToastUtil.showToast(data.data?.obj?.msg)
                    } else {
                        mView?.hideNetDialog()
                        ToastUtil.showToast(data.data?.obj?.msg)
                    }
                } else {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(data?.msgInfo)
                }
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun addressproofWithrawNeedUploadIdPoaProofApi() {
        val params = HashMap<String, Any?>()
        params["token"] = UserDataUtil.loginToken()
        mModel?.addressproofWithrawNeedUploadIdPoaProofApi(params, object : BaseObserver<NeedUploadIdProofData?>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(dataBean: NeedUploadIdProofData?) {
                mView?.hideNetDialog()

                if ("V00000" != dataBean?.resultCode) {
                    ToastUtil.showToast(dataBean?.msg)
                    return
                }

                val data = dataBean.data?.obj?.needUploadIdPoaProof

                if ("0" == data) {
                    fundIsH5WithdrawApi()
                    return
                }

                val buryHashMap = HashMap<String, String>()
                buryHashMap["Position"] = "Withdraw_button"

                // 1/3 lv2
                if ("1" == data || "3" == data) {
//                    openActivity(OpenAccoGuideLv2Activity::class.java)
                    SumSubJumpHelper.isJumpSumSub(mView?.ac, Constants.SUMSUB_TYPE_POI)
                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK,
                        buryHashMap
                    )
                    return
                }

                // 4/6 lv3
                if ("4" == data || "6" == data) {
//                    openActivity(OpenAccoGuideLv3Activity::class.java)
                    SumSubJumpHelper.isJumpSumSub(mView?.ac, Constants.SUMSUB_TYPE_POA)

                    LogEventUtil.setLogEvent(
                        BuryPointConstant.V334.REGISTER_LIVE_LVL3_BUTTON_CLICK,
                        buryHashMap
                    )
                    return
                }

                ToastUtil.showToast(dataBean.data?.obj?.msg)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun fundIsH5WithdrawApi() {
        mView?.showNetDialog()
        val params = HashMap<String, Any?>()
        params["userToken"] = UserDataUtil.loginToken()
        mModel?.fundIsH5WithdrawApi(params, object : BaseObserver<NeedH5WithdrawBean?>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(data: NeedH5WithdrawBean?) {
                mView?.hideNetDialog()
                if ("********" == data?.resultCode) {
                    val obj = data.data?.obj

                    val bundle = Bundle()
                    var htmlUrl = obj?.h5Url ?: ""
                    htmlUrl += if (htmlUrl.contains("?")) "" else "?"

                    var mt4AccountId = UserDataUtil.accountCd()
                    var currency = UserDataUtil.currencyType()
                    var socialTradingType = ""
                    if (UserDataUtil.isStLogin()) {
                        socialTradingType = "1"
                    } else {
                        socialTradingType = "0"
                    }
                    if (!htmlUrl.contains("userToken=")) htmlUrl = htmlUrl + "&userToken=" + UserDataUtil.loginToken()
                    if (!htmlUrl.contains("mt4AccountId=")) htmlUrl = "$htmlUrl&mt4AccountId=$mt4AccountId"
                    if (!htmlUrl.contains("currency=")) htmlUrl = "$htmlUrl&currency=$currency"
                    if (!htmlUrl.contains("type=")) htmlUrl = htmlUrl + "&type=" + SpManager.getSuperviseNum("")
                    htmlUrl = "$htmlUrl&socialtradingtype=$socialTradingType"

                    bundle.putString("url", htmlUrl)
                    bundle.putString("title", mView?.ac?.getString(R.string.withdraw))
                    bundle.putInt("tradeType", 3)
                    bundle.putBoolean("isNoTitleCanBack", true)
                    openActivity(HtmlActivity::class.java, bundle)
                } else ToastUtil.showToast(data?.msgInfo)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }
}
