package cn.com.vau.profile.activity.kycLink

import android.content.*
import android.os.Bundle
import android.view.MotionEvent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.databinding.ActivityKycUpdatePhoneBinding
import cn.com.vau.page.common.selectArea.SelectAreaCodeActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.activity.VerifySmsCodeActivity
import cn.com.vau.page.login.activity.VerifySmsCodeActivity.VerifySmsCodeType
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.profile.viewmodel.UpdatePhoneKycViewModel
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha

/**
 * author：lvy
 * date：2025/04/23
 * desc：kyc-修改手机号
 */
class UpdatePhoneKycActivity : BaseMvvmActivity<ActivityKycUpdatePhoneBinding, UpdatePhoneKycViewModel>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var mCaptcha: Captcha? = null

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean") ?: SignUpRequestBean()
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        // 按钮默认不可点击
        mBinding.tvNext.isEnabled = false
        mBinding.llWhatsApp.isEnabled = false
    }

    override fun initData() {
        mViewModel.getWithdrawRestrictionMsgApi(1)
        mViewModel.setDefaultAreaCode()
    }

    override fun createObserver() {
        // 出金限制横幅文案获取成功
        mViewModel.getWithdrawRestrictionMsgSuccessLiveData.observe(this) {
            mBinding.tvTips.isVisible = !it.isNullOrBlank()
            mBinding.tvTips.text = it
        }
        // 国家区号设置成功
        mViewModel.areaCodeLiveData.observe(this) {
            mBinding.mobileView.setAreaCodeText(it)
        }
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证码发送成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            VerifySmsCodeActivity.open(this, mViewModel.signUpRequestBean)
        }
        // 密码校验成功
        sendCodeViewModel.checkPwdSuccessLiveData.observe(this) {
            mViewModel.signUpRequestBean?.verifySmsCodeType = VerifySmsCodeType.UPDATE_PHONE_NEW_KYC
            sendCodeViewModel.sendPhoneCodeApi(type = "4", validate = it)
        }
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 选择国家区号
        mBinding.mobileView.areaCodeClickListener {
            selectAreaCodeLauncher.launch(Intent().apply {
                setClass(this@UpdatePhoneKycActivity, SelectAreaCodeActivity::class.java)
                putExtras(bundleOf().apply {
                    putString("selectAreaCode", mViewModel.signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum))
                })
            })
        }
        // 手机号输入框
        mBinding.mobileView.afterTextChangedListener {
            checkNextBtn()
        }
        // 密码
        mBinding.pwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.PHONE
            checkUserPassword()
        }
        // whatsApp
        mBinding.llWhatsApp.clickNoRepeat {
            mViewModel.signUpRequestBean?.sendCodeType = SendCodeType.WHATSAPP
            checkUserPassword()
        }
    }

    private fun checkUserPassword() {
        val code = mViewModel.signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
        val mobile = mBinding.mobileView.getInputText()
        // 判断手机号是否正确
        if (mobile.isBlank() || (code == "86" && mobile.length != 11) || (code != "86" && mobile.length > 15)) {
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_number))
            return
        }

        mViewModel.signUpRequestBean?.mobile = mobile
        mViewModel.signUpRequestBean?.pwd = mBinding.pwdView.getInputText()
        sendCodeViewModel.checkUserPasswordApi()
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        val isNext = mBinding.mobileView.getInputText().isNotBlank() && mBinding.pwdView.getInputText().isNotBlank()
        mBinding.tvNext.isEnabled = isNext
        if (isNext) {
            mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
            mBinding.llWhatsApp.isEnabled = true
        } else {
            mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
            mBinding.llWhatsApp.isEnabled = false
        }
    }

    /**
     * 触发网易易盾验证
     */
    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendCodeViewModel.checkUserPasswordApi(it)
        }
        mCaptcha?.validate()
    }

    private val selectAreaCodeLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Constants.SELECT_AREA) {
            val areaData = it?.data?.getParcelableExtra<SelectCountryNumberObjDetail>(Constants.SELECT_AREA_CODE)
            areaData?.let {
                mViewModel.setAreaCodeData(it)
            }
        }
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }

    override fun onDestroy() {
        super.onDestroy()
        mCaptcha?.destroy()
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, UpdatePhoneKycActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }
}