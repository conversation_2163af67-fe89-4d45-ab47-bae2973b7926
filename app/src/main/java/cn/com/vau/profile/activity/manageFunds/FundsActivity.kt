package cn.com.vau.profile.activity.manageFunds

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import android.view.ViewStub
import androidx.core.os.bundleOf
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.utils.SDKIntervalUtil
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.depositcoupon.ManageFundsObj
import cn.com.vau.data.depositcoupon.ManageFundsObjFundFlows
import cn.com.vau.databinding.ActivityFundsBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.page.common.SDKIntervalCallback
import cn.com.vau.page.depositNew.DepositDetailsActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.openAccountFifth.OpenFifthAddressSelectActivity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.profile.activity.manageFundsDetails.FundsDetailsActivity
import cn.com.vau.profile.adapter.ManageFundsAdapter
import cn.com.vau.profile.viewmodel.FundsViewModel
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.mathAdd
import cn.com.vau.util.noRepeat
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * 资金管理
 * Created by zhy on 2018/11/16.
 */
class FundsActivity : BaseMvvmActivity<ActivityFundsBinding, FundsViewModel>(), SDKIntervalCallback,
    View.OnClickListener {

    private var manageFundsAdapter: ManageFundsAdapter? = null
    private val objFundFlowsList: MutableList<ManageFundsObjFundFlows> = ArrayList()
    private var profit = "0.0"

    private val shareAccountInfoBean by lazy { VAUSdkUtil.shareAccountBean() }

    var accountId: String? = ""

    override fun useEventBus(): Boolean = true

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        SDKIntervalUtil.instance.removeCallBack(this)
        SDKIntervalUtil.instance.addCallBack(this)
    }

    override fun initView() {

        // TODO xml内布局需优化
        mBinding.mVsNoData.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setHintMessage(getString(R.string.no_records_found))
            }
        })

        if (InitHelper.isNotSuccess()) {
            //信用额
            mBinding.tvCreditAmount.text = "- -"
            //净值
            mBinding.tvEquity.text = "- -"
            //可用余额（可用保证金）
            mBinding.tvFreeMargin.text = "- -"
            //总收益 = 已平仓收益 + 浮动收益
            mBinding.tvTotalProfit.text = "- -"
            //浮动收益（盈亏）
            mBinding.tvFloatingProfit.text = "- -"
        }

        val linearLayoutManager = WrapContentLinearLayoutManager(this)
        linearLayoutManager.orientation = RecyclerView.VERTICAL
        mBinding.mRecyclerView.layoutManager = linearLayoutManager
        mBinding.mRecyclerView.isNestedScrollingEnabled = false
        manageFundsAdapter = ManageFundsAdapter(this, objFundFlowsList, 0)
        mBinding.mRecyclerView.adapter = manageFundsAdapter
        mBinding.mRecyclerView.setEmptyView(mBinding.mVsNoData)
        manageFundsAdapterListener()
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvWithdraw.setOnClickListener(this)
        mBinding.tvDeposit.setOnClickListener(this)
    }

    override fun initData() {
        super.initData()
        mBinding.tvCurrencyType.text = UserDataUtil.currencyType()
    }

    override fun onResume() {
        super.onResume()
        accountId = UserDataUtil.accountCd()
        // 查询资金管理
        mViewModel.queryManageFunds(accountId, "1")
    }

    override fun createObserver() {

        mViewModel.fundsObjLiveData.observe(this) {
            refreshManageFunds(it)
        }

        mViewModel.jumpAddressProofLiveData.observe(this) {
            val bundle = Bundle()
            bundle.putInt(Constants.IS_FROM, 1)
            openActivity(OpenFifthAddressSelectActivity::class.java, bundle)
        }

        mViewModel.jumpSumSubLiveData.observe(this) {
            SumSubJumpHelper.isJumpSumSub(this, Constants.SUMSUB_TYPE_POI)
            LogEventUtil.setLogEvent(BuryPointConstant.V334.REGISTER_LIVE_LVL2_BUTTON_CLICK, it)
        }

        mViewModel.jumpAccoGuideLv3LiveData.observe(this) {
            SumSubJumpHelper.isJumpSumSub(this, Constants.SUMSUB_TYPE_POA)
            LogEventUtil.setLogEvent(BuryPointConstant.V334.REGISTER_LIVE_LVL3_BUTTON_CLICK, it)
        }

        mViewModel.htmlUrlLiveData.observe(this) {
            val bundle = Bundle()
            bundle.putString("url", it)
            bundle.putString("title", getString(R.string.withdraw))
            bundle.putInt("tradeType", 3)
            bundle.putBoolean("isNoTitleCanBack", true)
            openActivity(HtmlActivity::class.java, bundle)
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {

            R.id.tvWithdraw -> noRepeat {
                mViewModel.needUploadAddressProof()
            }

            R.id.tvDeposit -> {
                NewHtmlActivity.openActivity(this, url = UrlConstants.HTML_FUND_DEPOSIT)
                LogEventUtil.setLogEvent(
                    BuryPointConstant.V331.DEPOSIT_TRAFFIC_BUTTON_CLICK, bundleOf(
                        "Position" to "Funds"
                    )
                )
            }
        }
    }

    override fun onCallback() {
        //信用额
        mBinding.tvCreditAmount.text = shareAccountInfoBean.credit.numCurrencyFormat()
        // 净值 , 可用余额（可用保证金） , 总收益 = 已平仓收益 + 浮动收益 , 浮动收益（盈亏）
        mBinding.tvEquity.text = shareAccountInfoBean.equity.numCurrencyFormat()
        mBinding.tvFreeMargin.text = shareAccountInfoBean.freeMargin.numCurrencyFormat()
        mBinding.tvTotalProfit.text =
            profit.mathAdd(shareAccountInfoBean.profit.toString()).numCurrencyFormat()
        mBinding.tvFloatingProfit.text = shareAccountInfoBean.profit.numCurrencyFormat()
    }

    @SuppressLint("WrongConstant", "NotifyDataSetChanged")
    fun refreshManageFunds(bean: ManageFundsObj?) {
        objFundFlowsList.clear()
        objFundFlowsList.addAll(bean?.fundFlows ?: arrayListOf())

        //已平仓收益
        profit = bean?.profit ?: ""
        mBinding.tvProfit.text = profit.numCurrencyFormat()

        // 优惠券图标
        ImageLoaderUtil.loadImage(this, bean?.discountIcon, mBinding.ivEvent)

        manageFundsAdapter?.notifyDataSetChanged()
        //initChart()
    }

    /**
     * Adapter点击事件
     */
    private fun manageFundsAdapterListener() {
        manageFundsAdapter?.setOnItemClickListener(object : ManageFundsAdapter.OnItemClickListener {
            override fun onItemClick(view: View?, position: Int) {
                val bundle = Bundle()
                val orderType = objFundFlowsList.elementAtOrNull(position)?.actionCode ?: ""
                bundle.putString(
                    "orderNo", objFundFlowsList.elementAtOrNull(position)?.orderNo ?: ""
                )
                bundle.putString("orderType", orderType) //明细交易代码，00入金，01出金，10转账，20收益
                openActivity(
                    if ("00" == orderType) DepositDetailsActivity::class.java else FundsDetailsActivity::class.java,
                    bundle
                )
            }

        })

    }

    override fun onDestroy() {
        super.onDestroy()
        SDKIntervalUtil.instance.removeCallBack(this)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onMsgEvent(eventTag: String) {
        if (eventTag == NoticeConstants.Init.APPLICATION_START) {  // 初始化开始
            //信用额
            mBinding.tvCreditAmount.text = "- -"
            //净值
            mBinding.tvEquity.text = "- -"
            //可用余额（可用保证金）
            mBinding.tvFreeMargin.text = "- -"
            //总收益 = 已平仓收益 + 浮动收益
            mBinding.tvTotalProfit.text = "- -"
            //浮动收益（盈亏）
            mBinding.tvFloatingProfit.text = "- -"
        }
    }

}