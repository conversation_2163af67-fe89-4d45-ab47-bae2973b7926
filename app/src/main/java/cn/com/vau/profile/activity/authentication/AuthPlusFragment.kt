package cn.com.vau.profile.activity.authentication

import androidx.core.view.isGone
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.UrlConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.data.account.AuditStatusData
import cn.com.vau.databinding.FragmentAuthPlusBinding
import cn.com.vau.page.html.NewHtmlActivity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil

/**
 * Filename: AuthStandardFragment.kt
 * Author: GG
 * Date: 2024/1/11
 * Description:
 */
class AuthPlusFragment : BaseMvvmBindingFragment<FragmentAuthPlusBinding>() {
    private val mViewModel by activityViewModels<AuthViewModel>()

    override fun initView() {
    }

    override fun initListener() {
        super.initListener()
        mViewModel.accountStatusLiveData.observe(this) {
            updateView(it)
        }
        mBinding.tvLv3VerifyNow.clickNoRepeat(1000) {
            // 如果 POA 审核状态为待审核或审核不通过，则打开 Lv3 开户引导界面
            SumSubJumpHelper.isJumpSumSub(requireContext(), Constants.SUMSUB_TYPE_POA)
            LogEventUtil.setLogEvent(
                BuryPointConstant.V334.REGISTER_LIVE_LVL3_BUTTON_CLICK,
                hashMapOf("Position" to "Profile_info")
            )
        }

        mBinding.tvBankVerifyNow.clickNoRepeat {
            NewHtmlActivity.openActivity(requireContext(), UrlConstants.URL_BANK_VERIFY)
        }
    }

    private fun updateView(data: AuditStatusData.Obj?) {
        data?.let {
            updateLv3(it)
            updateBank(it)
        }
    }

    private fun updateBank(data: AuditStatusData.Obj) {
        when {
            (data.ibtPoaAuditStatus == AuthenticationActivity.TYPE_SUBMITTED || data.ibtPoaAuditStatus == AuthenticationActivity.TYPE_REAUDIT) ||
                (data.ibtPoiAuditStatus == AuthenticationActivity.TYPE_SUBMITTED || data.ibtPoiAuditStatus == AuthenticationActivity.TYPE_REAUDIT) -> {
                AuthenticationActivity.updateTextView(mBinding.tvBankVerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UNDER_REVIEW)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe1, AuthenticationActivity.PASS_STATUS_ORANGE)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe2, AuthenticationActivity.PASS_STATUS_ORANGE)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe3, AuthenticationActivity.PASS_STATUS_ORANGE)
                mBinding.groupBank.isVisible = true
                mBinding.tvBankVerifyNow.isVisible = false
            }

            data.ibtPoaAuditStatus == AuthenticationActivity.TYPE_REJECTED || data.ibtPoiAuditStatus == AuthenticationActivity.TYPE_REJECTED -> {
                AuthenticationActivity.updateTextView(mBinding.tvBankVerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe1, AuthenticationActivity.PASS_STATUS_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe2, AuthenticationActivity.PASS_STATUS_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe3, AuthenticationActivity.PASS_STATUS_REJECTED)
                mBinding.groupBank.isVisible = true
                mBinding.tvBankVerifyNow.isVisible = true
            }

            data.ibtPoaAuditStatus == AuthenticationActivity.TYPE_PENDING || data.ibtPoiAuditStatus == AuthenticationActivity.TYPE_PENDING -> {
                AuthenticationActivity.updateTextView(mBinding.tvBankVerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe2, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe3, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.groupBank.isVisible = true
                mBinding.tvBankVerifyNow.isVisible = true
            }

            data.ibtPoaAuditStatus == AuthenticationActivity.TYPE_COMPLETED && data.ibtPoiAuditStatus == AuthenticationActivity.TYPE_COMPLETED -> {
                AuthenticationActivity.updateTextView(mBinding.tvBankVerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe1, AuthenticationActivity.PASS_STATUS_GREEN)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe2, AuthenticationActivity.PASS_STATUS_GREEN)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvBankRe3, AuthenticationActivity.PASS_STATUS_GREEN)
                mBinding.groupBank.isVisible = true
                mBinding.tvBankVerifyNow.isVisible = false
            }

            else -> {
                mBinding.groupBank.isGone = true
            }
        }
    }

    private fun updateLv3(data: AuditStatusData.Obj) {
        when (data.poaAuditStatus) {
            AuthenticationActivity.TYPE_SUBMITTED, AuthenticationActivity.TYPE_REAUDIT -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv3VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UNDER_REVIEW)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re1, AuthenticationActivity.PASS_STATUS_ORANGE)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re2, AuthenticationActivity.PASS_STATUS_ORANGE)
                mBinding.tvLv3VerifyNow.isVisible = false
            }

            AuthenticationActivity.TYPE_PENDING -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv3VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re2, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.tvLv3VerifyNow.isVisible = !mViewModel.isLv1VerifyNowShow
            }

            AuthenticationActivity.TYPE_COMPLETED -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv3VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re1, AuthenticationActivity.PASS_STATUS_GREEN)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re2, AuthenticationActivity.PASS_STATUS_GREEN)
                mBinding.tvLv3VerifyNow.isVisible = false
            }

            AuthenticationActivity.TYPE_REJECTED -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv3VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re1, AuthenticationActivity.PASS_STATUS_REJECTED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re2, AuthenticationActivity.PASS_STATUS_REJECTED)
                mBinding.tvLv3VerifyNow.isVisible = !mViewModel.isLv1VerifyNowShow
            }

            else -> {
                AuthenticationActivity.updateTextView(mBinding.tvLv3VerifiedStatus, AuthenticationActivity.TEXTVIEW_TYPE_UN_VERIFIED)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re1, AuthenticationActivity.PASS_STATUS_GREY)
                AuthenticationActivity.checkChildPassStatus(mBinding.tvLv3Re2, AuthenticationActivity.PASS_STATUS_GREY)
                mBinding.tvLv3VerifyNow.isVisible = !mViewModel.isLv1VerifyNowShow
            }
        }
    }

    companion object {
        fun newInstance(): AuthPlusFragment {
            val fragment = AuthPlusFragment()
            return fragment
        }
    }
}