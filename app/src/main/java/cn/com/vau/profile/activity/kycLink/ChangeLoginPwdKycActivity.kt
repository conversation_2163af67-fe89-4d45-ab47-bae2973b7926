package cn.com.vau.profile.activity.kycLink

import android.content.*
import android.os.Bundle
import android.view.MotionEvent
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityKycChangeLoginPwdBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.profile.viewmodel.ChangeLoginPwdKycViewModel
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/03/25
 * desc：kyc-修改登录密码
 */
class ChangeLoginPwdKycActivity : BaseMvvmActivity<ActivityKycChangeLoginPwdBinding, ChangeLoginPwdKycViewModel>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
        sendCodeViewModel.signUpRequestBean = mViewModel.signUpRequestBean
    }

    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        mBinding.newPwdView.setHintText("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append("!@#$%^&*.()")
        }
    }

    override fun initData() {
        mViewModel.getWithdrawRestrictionMsgApi(2)
    }

    override fun createObserver() {
        // 出金限制横幅文案获取成功
        mViewModel.getWithdrawRestrictionMsgSuccessLiveData.observe(this) {
            mBinding.tvTips.isVisible = !it.isNullOrBlank()
            mBinding.tvTips.text = it
        }
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 原密码
        mBinding.currentPwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 新密码
        mBinding.newPwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 确认密码
        mBinding.confirmPwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            mViewModel.changeLoginPwdApi(mBinding.currentPwdView.getInputText(), mBinding.newPwdView.getInputText())
        }
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        val password = mBinding.newPwdView.getInputText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected = password.isNotBlank() && mBinding.confirmPwdView.getInputText() == password
        // 对下方的按钮的判断进行赋值
        val isNext = mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected &&
                mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected
        val isCurrentPwdNotBlank = mBinding.currentPwdView.getInputText().isNotBlank()
        mBinding.tvNext.isEnabled = isNext && isCurrentPwdNotBlank
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }

    companion object {
        fun open(context: Context, signUpRequestBean: SignUpRequestBean?) {
            val intent = Intent(context, ChangeLoginPwdKycActivity::class.java)
            intent.putExtras(bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
            context.startActivity(intent)
        }
    }
}