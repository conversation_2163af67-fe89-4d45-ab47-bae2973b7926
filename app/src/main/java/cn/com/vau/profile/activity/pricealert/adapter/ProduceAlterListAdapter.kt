package cn.com.vau.profile.activity.pricealert.adapter

import cn.com.vau.R
import cn.com.vau.data.pricealtert.ProduceAlterData
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_EDIT
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_ENABLE
import cn.com.vau.profile.activity.pricealert.viewmodel.PriceAlertsManageViewModel.Companion.ADAPTER_SELECT
import cn.com.vau.util.widget.SwitchButton
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class ProduceAlterListAdapter(var isEdit: Boolean = false) : BaseQuickAdapter<ProduceAlterData, BaseViewHolder>(R.layout.item_recycler_price_alert_list) {

    init {
        addChildClickViewIds(R.id.mSwitchButton, R.id.ivSelect)
    }

    private var switchListener: ((ProduceAlterData) -> Unit)? = null

    override fun convert(holder: BaseViewHolder, item: ProduceAlterData) {
        holder.setGone(R.id.ivSelect, !isEdit)
            .setImageResource(R.id.ivIcon, if (item.alertType == "0" || item.alertType == "2") R.drawable.img_price_up else R.drawable.img_price_down)
            .setText(R.id.tvAlterName, item.getAlertName())
            .setText(R.id.tvFrequency, item.getFrequencyName())
            .setImageResource(R.id.ivSelect, if (item.isSelect) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
            .setGone(R.id.mSwitchButton, isEdit)
            .setVisible(R.id.tvTrigged, item.isExecute == "1" && item.frequency == "0")

        holder.itemView.findViewById<SwitchButton>(R.id.mSwitchButton).run {
            setState(item.enable == "1")
            setStateChangeListener {
                if (it) {
                    item.enable = "1"
                } else {
                    item.enable = "0"
                }
                switchListener?.invoke(item)
            }
        }

        if (item.enable == "1") {
            holder.setGone(R.id.tvTrigged, true)
        }
    }

    fun setSwitchListener(block: ((ProduceAlterData) -> Unit)?) {
        switchListener = block
    }

    override fun convert(holder: BaseViewHolder, item: ProduceAlterData, payloads: List<Any>) {
        super.convert(holder, item, payloads)
        if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_ENABLE) {
            holder.itemView.findViewById<SwitchButton>(R.id.mSwitchButton).setState(
                item.enable == "1"
            )
            if (item.enable == "1") {
                holder.setGone(R.id.tvTrigged, true)
            }
        } else if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_EDIT) {
            holder.setGone(R.id.ivSelect, !isEdit)
                .setGone(R.id.mSwitchButton, isEdit)
        } else if (payloads.isNotEmpty() && payloads.getOrNull(0) == ADAPTER_SELECT) {
            holder.setImageResource(R.id.ivSelect, if (item.isSelect) R.drawable.icon2_cb_tick_circle_c15b374 else R.drawable.draw_shape_oval_stroke_c731e1e1e_c61ffffff_s14)
        } else {
            convert(holder, item)
        }
    }
}