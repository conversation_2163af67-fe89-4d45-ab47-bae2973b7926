package cn.com.vau.profile.activity.updateMobileNumber

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.util.*
import io.reactivex.disposables.Disposable

/**
 * Created by zhy on 2018/11/29.
 */
class UpdateMobileNumberPresenter : UpdateMobileNumberContract.Presenter() {

    var telCode: String? = Constants.defaultCountryNum //手机区号

    var countryCode: String? = "AU" //手机号对应国家代码

    val countryName: String? = "" //手机号对应国家名称

    var smsSendType = VerificationActivity.TYPE_SEND_SMS

    /**
     * 获取短信验证码
     */
    override fun getBindingTelSMSApi(userTel: String, userPassword: String, type: String, validateCode: String) {
        val params = HashMap<String, Any>()
        if (validateCode.isNotEmpty()) {
            params["recaptcha"] = validateCode
            params["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        params["userTel"] = userTel
        params["userPassword"] = userPassword
        params["type"] = type //类型
        params["phoneCountryCode"] = countryCode ?: ""
        params["smsSendType"] = smsSendType
        params["code"] = telCode ?: ""
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mView?.showNetDialog()
        mModel?.getBindingTelSMSApi(paramMap, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: ForgetPwdVerificationCodeBean) {
                SpManager.putSmsCodeId("")
                mView?.hideNetDialog()
                if ("V00000" == baseBean.resultCode) {
                    ToastUtil.showToast(baseBean.msgInfo)
                    mView?.refreshSMSCode()
                } else {
                    if ("V10060" == baseBean.resultCode) { //易盾 需要滑动窗口
                        if (baseBean.data != null && baseBean.data?.obj != null) {
                            SpManager.putSmsCodeId(baseBean.data?.obj?.smsCodeId)
                        }
                        mView?.showCaptcha()
                        return
                    }
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }
        })
    }

    /**
     * 获取出金限制横幅文案
     */
    override fun getWithdrawRestrictionMsgApi(type: Int) {
        val map = hashMapOf<String, Any>()
        map["userToken"] = UserDataUtil.loginToken()
        map["type"] = type //1=更新手机号；2=更改登录密码；3=重置密码

        mModel?.getWithdrawRestrictionMsgApi(map, object : BaseObserver<DataObjStringBean>() {
            override fun onNext(baseBean: DataObjStringBean) {
                if (baseBean.resultCode == "00000000") {
                    mView?.showWithdrawRestrictionMsg(baseBean.data?.obj)
                } else {
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }
        })
    }
}
