package cn.com.vau.profile.activity.twoFactorAuth.activity

import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityTfaSettingBinding
import cn.com.vau.util.clickNoRepeat

/**
 * @description: 2fa的分页设置页面，已经设置了2fa以后会再次设置2fa跳转的页面，可以调整修改或重置2fa的页面
 * @author: GG
 * @createDate: 2024 11月 29 15:19
 * @updateUser:
 * @updateDate: 2024 11月 29 15:19
 */
class TFASettingActivity : BaseMvvmBindingActivity<ActivityTfaSettingBinding>() {
    override fun initView() {
        mBinding.stvChange.clickNoRepeat {
            openActivity(TFAChangePromptActivity::class.java)
        }

        mBinding.stvReset.clickNoRepeat {
            TFAResetActivity.open(this)
        }
    }
}