package cn.com.vau.profile.activity.addOrForgotSecurityPWD

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.util.*
import io.reactivex.disposables.Disposable

/**
 * Created by zhy on 2018/12/3.
 */
class AddOrForgotSecurityPWDPresenter : AddOrForgotSecurityPWDContract.Presenter() {

    var sourceState = ""

    var smsSendType = VerificationActivity.TYPE_SEND_SMS

    override fun getBindingTelSMSApi(userTel: String?, phoneCountryCode: String?, code: String?, type: String?, validateCode: String?) {
        mView?.showNetDialog()
        val params = HashMap<String, Any>()
        params["userTel"] = userTel ?: ""
        params["type"] = type ?: ""
        params["phoneCountryCode"] = phoneCountryCode ?: ""
        params["code"] = code ?: ""//手机区号
        params["smsSendType"] = smsSendType
        if (!validateCode.isNullOrEmpty()) {
            params["recaptcha"] = validateCode
            params["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mModel?.getBindingTelSMSApi(paramMap, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(baseBean: ForgetPwdVerificationCodeBean) {
                mView?.hideNetDialog()
                SpManager.putSmsCodeId("")
                if ("V00000" == baseBean.resultCode) {
                    ToastUtil.showToast(baseBean.msgInfo)
                    mView?.refreshSMSCode()
                } else {
                    if ("V10060" == baseBean.resultCode) { //易盾 需要滑动窗口
                        if (baseBean.data != null && baseBean.data?.obj != null) {
                            SpManager.putSmsCodeId(baseBean.data?.obj?.smsCodeId)
                        }
                        mView?.showCaptcha()
                        return
                    }
                    ToastUtil.showToast(baseBean.msgInfo)
                }
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }
}
