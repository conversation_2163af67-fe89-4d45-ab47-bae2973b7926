package cn.com.vau.profile.performance

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleCoroutineScope
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.base.mvvm.BaseDataBindingActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.performance.AbsPerformance
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.KycFunctionPermissionObj
import cn.com.vau.util.KycVerifyHelper
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.toIntCatching
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.collections.HashMap
import kotlin.collections.firstOrNull
import kotlin.collections.set

/**
 * 交易权限管理
 * 1、请求获取交易权限接口后，保存到本地
 * 2、获取确定交易是否阻断时，需要判断的类型
 * 3、处理阻断交易逻辑
 *
 * 注意：
 * activity和fragment都可能为空，获取string等资源时通过context对象获取！！！
 */
class TradePermissionPerformance private constructor(val activity: FragmentActivity?, val fragment: Fragment?) : AbsPerformance() {

    private val context: FragmentActivity by lazy {
        activity ?: fragment?.requireActivity() ?: throw IllegalStateException(
            "No valid context provided. Must initialize with either Activity or Fragment"
        )
    }

    private val viewModel: TradePermissionViewModel by lazy {
        ViewModelProvider(getViewModelOwner())[TradePermissionViewModel::class.java]
    }

    /**
     * 本地没有找到存储的交易权限对象，阻断了相关的操作，将该操作转移到Performance中，然后调接口，根据接口情况判断要不要执行该操作
     * 有权限时，执行该操作
     */
    private var nextBlock: (() -> Unit)? = null

    constructor(activity: FragmentActivity) : this(activity, null)

    constructor(fragment: Fragment) : this(null, fragment)

    private fun getViewModelOwner(): ViewModelStoreOwner {
        return fragment ?: activity ?: throw IllegalStateException(
            "No ViewModelStoreOwner available"
        )
    }

    private fun getLifecycleScope(): LifecycleCoroutineScope {
        return fragment?.lifecycleScope ?: activity?.lifecycleScope ?: throw IllegalStateException(
            "No LifecycleCoroutineScope available"
        )
    }

    private fun getLifecycle(): Lifecycle {
        return fragment?.lifecycle ?: activity?.lifecycle ?: throw IllegalStateException(
            "No Lifecycle available"
        )
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        initView()
    }

    private fun initView() {

        getLifecycleScope().launch {
            viewModel.eventFlow.flowWithLifecycle(getLifecycle(), Lifecycle.State.RESUMED).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    TradePermissionViewModel.EVENT_SUCCESS_REQUEST_PERMISSION -> {
                        LoadingHelper.hideLoading(context)
                        if (it.data is KycFunctionPermissionObj) {
                            handleTradeBlockType(getTradeBlockType(it.data))
                        }
                    }

                    TradePermissionViewModel.EVENT_ERROR_REQUEST_PERMISSION -> {
                        LoadingHelper.hideLoading(context)
                    }

                }
            }
        }

    }

    /**
     * 请求对应功能权限，并且存入本地
     * 调用时机
     * 1、App启动后
     * 2、切换账户后
     * 3、收到Socket消息后
     */
    fun requestTradePermission() {

        if (UserDataUtil.isLogin() && SpManager.isV1V2()) {
            viewModel.requestTradePermission()
        }
    }

    /**
     * 获取确定交易是否阻断时，需要判断的类型
     */
    private fun getTradeBlockType(isOrderContext: Boolean = false): TradeBlockType {
        //未登录
        if (!UserDataUtil.isLogin()) {
            return TradeBlockType.NotLogin
        }

        //demo账户
        if (UserDataUtil.isDemoAccount()) {
            return TradeBlockType.DemoAccount
        }

        //非V1V2监管账户
        if (SpManager.isV1V2().not()) {
            return TradeBlockType.NotKycAccount
        }

        //需要KYC认证的账户（处于V1/V2监管）
        if (SpManager.isV1V2()) {
            if (isOrderContext.not() && UserDataUtil.isLiveVirtualAccount().not()) {
                //不是mt5虚拟账户
                return TradeBlockType.NotLiveVirtualAccount
            }
            if (UserDataUtil.isLiveVirtualAccount()) {
                //是mt5虚拟账户
                return TradeBlockType.LiveVirtualAccount
            }
            val permissionObj = SpManager.getTradePermission()
            return if (permissionObj == null) {
                //未获取到交易权限对象，请求接口
                TradeBlockType.NoStoredPermission
            } else {
                getTradeBlockType(permissionObj)
            }
        }

        //默认不阻断
        return TradeBlockType.HasPermission
    }

    /**
     * 获取阻断类型
     */
    private fun getTradeBlockType(obj: KycFunctionPermissionObj): TradeBlockType {

        if (obj.hasPermission == true) {
            //有权限
            return TradeBlockType.HasPermission
        } else {
            //无权限
            val guidaLevel = obj.hasPermissionLevels?.firstOrNull()
            return TradeBlockType.NoPermission(guidaLevel)
        }
    }

    /**
     * 处理阻断交易逻辑
     * @param isOrderContext 是否是订单上下文
     * @param nextBlock 下一步操作
     * @return Boolean true: 阻断交易，false: 不阻断交易
     */
    fun handleTradeBlockType(
        isOrderContext: Boolean = false,
        nextBlock: (() -> Unit)? = null
    ): Boolean {
        val type: TradeBlockType = getTradeBlockType(isOrderContext)//阻断类型
        this.nextBlock = nextBlock
        return when {
            type == TradeBlockType.NotLogin -> {
                //未登录，不阻断（外面处理了）
                false
            }

            type == TradeBlockType.DemoAccount -> {
                //demo账户，不阻断
                false
            }

            type == TradeBlockType.NotKycAccount -> {
                //不需要KYC认证，不阻断
                false
            }

            isOrderContext.not() && type == TradeBlockType.NotLiveVirtualAccount -> {
                //非下单页面&&非mt5虚拟账户，不阻断
                false
            }

            type == TradeBlockType.LiveVirtualAccount -> {
                //mt5虚拟账户，阻断，弹出补全用户信息半模态弹窗
                KycVerifyHelper.showSetupTradingAccountDialog(context, context.getString(R.string.please_complete_your_trade_feature))
                true
            }

            type == TradeBlockType.HasPermission -> {
                //有交易权限，不阻断
                false
            }

            type == TradeBlockType.NoStoredPermission -> {
                //交易权限对象还没有存储到本地，阻断，调接口
                LoadingHelper.showLoading(context)
                viewModel.requestTradePermission2()
                true
            }

            type is TradeBlockType.NoPermission -> {
                //无交易权限
                if (type.guideLevel?.isNotEmpty() == true) {
                    //无交易权限，阻断，弹出引导页
                    KycVerifyHelper.checkKycVerify(context, type.guideLevel.toIntCatching(), context.getString(R.string.verify_to_unlock_access), context.getString(R.string.please_complete_your_trade_feature))
                    true
                } else {
                    //无交易权限，阻断，guideLevel为空无法引导
                    true
                }
            }

            else -> {
                //其他情况，不阻断
                false
            }

        }
    }

    /**
     * 处理阻断后的交易逻辑
     * @param type 阻断类型
     */
    private fun handleTradeBlockType(type: TradeBlockType) {
        if (type is TradeBlockType.HasPermission) {
            //有交易权限
            nextBlock?.invoke()
        } else if (type is TradeBlockType.NoPermission) {
            //无交易权限
            if (type.guideLevel?.isNotEmpty() == true) {
                //无交易权限，阻断，弹出引导页
                KycVerifyHelper.checkKycVerify(context, type.guideLevel.toIntCatching(), context.getString(R.string.verify_to_unlock_access), context.getString(R.string.please_complete_your_trade_feature))
            }
        }
    }
}

object LoadingHelper {
    fun showLoading(activity: FragmentActivity) {
        when (activity) {
            is BaseMvvmBindingActivity<*> -> activity.showLoadDialog()
            is BaseDataBindingActivity<*> -> activity.showLoadDialog()
            is BaseActivity -> activity.showNetDialog()
        }
    }

    fun hideLoading(activity: FragmentActivity) {
        when (activity) {
            is BaseMvvmBindingActivity<*> -> activity.hideLoadDialog()
            is BaseDataBindingActivity<*> -> activity.hideLoadDialog()
            is BaseActivity -> activity.hideNetDialog()
        }
    }
}

class TradePermissionViewModel : BaseViewModel() {
    /**
     * 判断用户是否有对应功能权限
     */
    fun requestTradePermission() {
        val params = HashMap<String, Any?>()
        params["functionModuleCode"] = FunctionType.LIVE_TRADING
        requestNet(
            { baseService.userHasFunctionPermission(params) },
            onSuccess = {
                val obj = it.data?.obj
                if (obj != null) {
                    SpManager.putTradePermission(obj)
                }
            }
        )
    }

    /**
     * 判断用户是否有对应功能权限
     */
    fun requestTradePermission2() {
        val params = HashMap<String, Any?>()
        params["functionModuleCode"] = FunctionType.LIVE_TRADING
        requestNet(
            { baseService.userHasFunctionPermission(params) },
            onSuccess = {
                if (!it.isSuccess()) {
                    ToastUtil.showToast(it.getResponseMsg())
                    return@requestNet
                }
                val obj = it.data?.obj
                if (obj != null) {
                    SpManager.putTradePermission(obj)
                }
                sendEvent(DataEvent(EVENT_SUCCESS_REQUEST_PERMISSION, obj))
            },
            onError = {
                sendEvent(DataEvent(EVENT_ERROR_REQUEST_PERMISSION, null))
            }
        )
    }

    companion object {
        const val EVENT_SUCCESS_REQUEST_PERMISSION = "EVENT_SUCCESS_REQUEST_PERMISSION"
        const val EVENT_ERROR_REQUEST_PERMISSION = "EVENT_ERROR_REQUEST_PERMISSION"
    }

}

class FunctionType {
    companion object {
        // 入金
        const val DEPOSIT = 1

        // 出金
        const val WITHDRAWAL = 2

        // 交易
        const val LIVE_TRADING = 3

        // 开通主交易账号
        const val OPEN_LIVE_TRADING_ACCOUNT = 4

        // 开通同名交易账号
        const val OPEN_ADDITIONAL_ACCOUNT = 5

        // 钱包闪兑
        const val WALLET_CONVERT = 6

        // 钱包入金
        const val WALLET_DEPOSIT = 7

        // 钱包出金
        const val WALLET_WITHDRAWAL = 8
    }
}

/**
 * 确定交易是否阻断时，需要判断的类型
 */
sealed class TradeBlockType {
    //未登录
    data object NotLogin : TradeBlockType()

    //demo账户
    data object DemoAccount : TradeBlockType()

    //非Kyc账户
    data object NotKycAccount : TradeBlockType()

    //非虚拟MT5的假Live账户
    data object NotLiveVirtualAccount : TradeBlockType()

    //虚拟MT5的假Live账户
    data object LiveVirtualAccount : TradeBlockType()

    //交易权限对象还没有存储到本地
    data object NoStoredPermission : TradeBlockType()

    //有交易权限，KycFunctionPermissionObj != null
    data object HasPermission : TradeBlockType()

    //无交易权限，KycFunctionPermissionObj != null
    data class NoPermission(var guideLevel: String? = null) : TradeBlockType()

}

