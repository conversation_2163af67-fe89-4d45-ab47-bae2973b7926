package cn.com.vau.profile.viewmodel

import androidx.lifecycle.MutableLiveData
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.page.login.*
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/04/07
 * desc：kyc-设置/修改资金密码
 */
class SetFundsPwdKycViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null

    val setFundsPwdSuccessLiveData = MutableLiveData<String>() // 设置/修改资金密码成功
    val refreshFundsPwdSuccessLiveData = MutableLiveData<Boolean>() // 忘记资金密码刷新成功

    /**
     * 设置/修改资金密码
     *
     * @param optType 0=新增；1=修改
     */
    fun setFundsPwdApi(optType: String, newPwd: String?, oldPwd: String? = null) {
        val map = hashMapOf<String, Any?>()
        map["userToken"] = UserDataUtil.loginToken()
        map["optType"] = optType
        if (signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
            map["email"] = signUpRequestBean?.email
            map["txId"] = signUpRequestBean?.txId
            map["authOptType"] = "2"
        } else {
            map["code"] = signUpRequestBean?.countryNum
            map["phone"] = signUpRequestBean?.mobile
            map["authOptType"] = "1"
        }
        map["validateCode"] = signUpRequestBean?.smsCode
        map["fundPwd"] = newPwd
        map["confirmPwd"] = newPwd
        if (optType == "1") {
            map["oldFundPwd"] = oldPwd // 原密码，optType=1时必填
        }
        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.usersetUpfundpwdApi(aesMap) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            ToastUtil.showToast(it.getResponseMsg())
            setFundsPwdSuccessLiveData.value = optType
        }, isShowDialog = true)
    }

    /**
     * 忘记资金密码
     */
    fun forgetFundsPwdApi(newPwd: String?) {
        val map = hashMapOf<String, Any?>()
        map["userToken"] = UserDataUtil.loginToken()
        if (signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
            map["email"] = signUpRequestBean?.email
            map["txId"] = signUpRequestBean?.txId
            map["authOptType"] = "2"
        } else {
            map["code"] = signUpRequestBean?.countryNum
            map["phone"] = signUpRequestBean?.mobile
            map["authOptType"] = "1"
        }
        map["validateCode"] = signUpRequestBean?.smsCode
        map["fundPwd"] = newPwd
        map["confirmPwd"] = newPwd
        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        requestNet({ baseService.usersetForgetSafePwdApi(aesMap) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            ToastUtil.showToast(it.getResponseMsg())
            refreshFundsPwdSuccessLiveData.value = true
        }, isShowDialog = true)
    }
}