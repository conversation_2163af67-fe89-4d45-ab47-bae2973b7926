package cn.com.vau.profile.activity.inputPWD

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.View
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.databinding.ActivityInputPwdBinding
import cn.com.vau.page.WithdrawalBundleBean
import cn.com.vau.profile.activity.addOrForgotSecurityPWD.AddOrForgotSecurityPWDActivity
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionWithIconDialog
import com.lxj.xpopup.core.BasePopupView
import com.lxj.xpopup.interfaces.SimpleCallback
import kotlinx.coroutines.delay

/**
 * 请输入密码
 * Created by zhy on 2018/11/22.
 */
class InputPWDActivity : BaseFrameActivity<InputPWDPresenter, InputPWDModel>(), InputPWDContract.View {

    private val mBinding: ActivityInputPwdBinding by lazy { ActivityInputPwdBinding.inflate(layoutInflater) }

    //    private ImageView ivPWD;
    private var sourceType = 0 //0=出金；1=入金；3表示账户转账

    //出金必备
    private var withdrawalType = 0 //出金方式判断显示
    private var withdrawalBundleBean: WithdrawalBundleBean? = null //出金Bean Bundle

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        val bundle = intent.extras
        if (bundle != null) {
            sourceType = bundle.getInt("sourceType")
            if (sourceType == 0) {
                withdrawalType = bundle.getInt("withdrawalType")
                withdrawalBundleBean = bundle.getSerializable("withdrawalBean") as WithdrawalBundleBean?
            }
        }
    }

    @SuppressLint("SetTextI18n", "WrongViewCast")
    override fun initView() {
        super.initView()
        mBinding.tvForgotPWD.text = getString(R.string.forgot_funds_password) + "?"
        mBinding.tvForgotPWD.setOnClickListener(this)
        mBinding.tvNext.setOnClickListener(this)
    }

    override fun onClick(view: View) {
        super.onClick(view)
        val bundle = Bundle()
        when (view.id) {
            R.id.tv_ForgotPWD -> {
                AddOrForgotSecurityPWDActivity.openActivity(
                    this,
                    AddOrForgotSecurityPWDActivity.TYPE_CHANGE,
                    AddOrForgotSecurityPWDActivity.STATE_TRANSFER,
                    withdrawalType,
                    withdrawalBundleBean
                )
            }

            R.id.tv_Next -> {
                if (mBinding.etPassword.getText().isBlank()) {
                    ToastUtil.showToast(getString(R.string.please_enter_your_security_code))
                    return
                }

                if (sourceType == 0) {
                    inputPWDWithdrawal()
                } else if (sourceType == 3) { //3表示账户转账
                    bundle.putString("payPwd", mBinding.etPassword.getText().trim())
                    setResult(Constants.RESULT_CODE, intent.putExtras(bundle))
                    finish()
                }
            }
        }
    }

    /**
     * 出金
     */
    private fun inputPWDWithdrawal() {
        val tempInputPWD = mBinding.etPassword.getText().trim()
        withdrawalBundleBean?.let {
            mPresenter?.withdrawal(UserDataUtil.loginToken(), UserDataUtil.accountCd(), UserDataUtil.currencyType(), it, tempInputPWD, withdrawalType)
        }
    }

    override fun refreshWithdrawal(number: String?) {
        CenterActionWithIconDialog.Builder(this)
            .setLottieIcon(R.raw.lottie_dialog_ok)
            .setTitle("No. $number")
            .setContent(getString(R.string.congratulations_your_withdrawal_request_days))
            .setSingleButton(true)
            .setSingleButtonText(getString(R.string.ok))
            .setOnSingleButtonListener {
                dialogAction()
            }.setXPopupCallback(object : SimpleCallback() {
                override fun onShow(popupView: BasePopupView?) {
                    dialogAction()
                }
            }).build().showDialog()
    }

    private fun dialogAction() {
        lifecycleScope.launchWhenResumed {
            delay(2000)
            finish()
        }
    }
}
