package cn.com.vau.profile.activity.changeLoginPWD

import android.annotation.SuppressLint
import android.os.*
import android.text.TextUtils
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.databinding.ActivityChangeLoginPwdBinding
import cn.com.vau.page.user.forgotPwdSecond.ForgetPwdSecondModel
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.page.user.loginPwd.LoginVeriParam
import cn.com.vau.util.*
import com.netease.nis.captcha.Captcha
import com.netease.nis.captcha.CaptchaListener

/**
 * 修改登录密码
 * Created by zhy on 2018/11/29.
 */
class ChangeLoginPWDActivity : BaseFrameActivity<ChangeLoginPWDPresenter, ForgetPwdSecondModel>(), ChangePwdContract.View {

    private val mBinding: ActivityChangeLoginPwdBinding by lazy { ActivityChangeLoginPwdBinding.inflate(layoutInflater) }

    private var captcha: Captcha? = null

    private var isNext = false

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(this, R.attr.color_cebffffff_c1e1e1e)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        mPresenter.telCode = UserDataUtil.areaCode()
        mPresenter.telNum = UserDataUtil.userTel()
        //手机号对应国家代码
        mPresenter.countryCode = UserDataUtil.countryCode()
    }

    override fun initView() {
        super.initView()
        mBinding.mHeaderBar.setTitleText(getString(R.string.change_log_in_password))
        mBinding.etNewPassword.setHint("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
        mBinding.etConfirmPassword.setHint(getString(R.string.re_enter_password))

        mBinding.tvAreaCode.text = "+${mPresenter.telCode}"
        mBinding.tvMobile.text = UserDataUtil.userTel()

        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append(" !@#\$%^&*.()")
        }
        mBinding.etNewPassword.doAfterTextChanged {
            checkNewPassword()
        }

        mBinding.etConfirmPassword.doAfterTextChanged {
            checkNewPassword()
        }
        mBinding.tvSendEms.clickNoRepeat {
            noRepeat {
                if (!isNext) {
                    return@noRepeat
                }
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_SMS
                mPresenter.getVerificationCodeApi("")
            }
        }
        mBinding.llWhatsApp.clickNoRepeat {
            noRepeat {
                if (!isNext) {
                    return@noRepeat
                }
                mPresenter.smsSendType = VerificationActivity.TYPE_SEND_WA
                mPresenter.getVerificationCodeApi("")
            }
        }
    }

    override fun initData() {
        //获取出金限制横幅文案
        mPresenter.getWithdrawRestrictionMsgApi(2)
    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter.getVerificationCodeApi(validate)
                }
            }

            //建议直接打印错误码，便于排查问题
            @SuppressLint("LogNotTimber")
            override fun onError(code: Int, msg: String) {
                LogUtil.e("Captcha", "验证出错，错误码:$code 错误信息:$msg")
            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }

        captcha = CaptchaUtil.getCaptcha(this, loginCaptchaListener)
    }

    /**
     * 判断密码是否符合规范，并且会直接改变对应的checkBox的状态
     */
    private fun checkNewPassword() {
        val password = mBinding.etNewPassword.getText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        //正则表达式的意思是 包含字母数字和特殊字符
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected = password.isNotBlank() && mBinding.etConfirmPassword.getText() == password

        updateButton()
    }

    /**
     * 更新按钮的背景样式
     */
    private fun updateButton() {
        if (mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected
        ) {
            isNext = true
            mBinding.tvSendEms.setTextColor(color_cffffff_c1e1e1e)
            mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c1e1e1e_cebffffff_r100)
            mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
        } else {
            isNext = false
            mBinding.tvSendEms.setTextColor(color_c731e1e1e_c61ffffff)
            mBinding.tvSendEms.setBackgroundResource(R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100)
            mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
        }
    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    /**
     * 发送验证码成功，后跳转到验证码校验页面
     */
    override fun sendCodeSuccess() {
        VerificationActivity.openActivity(
            this, VerificationActivity.TYPE_CHANGE_PWD,
            mPresenter.smsSendType,
            LoginVeriParam(
                userPassword = mBinding.etNewPassword.getText(),
                mobile = mPresenter.telNum,
                areaCode = mPresenter.telCode,
                countryCode = mPresenter.countryCode,
                nextType = 0
            )
        )
    }

    /**
     * 出金限制横幅
     */
    override fun showWithdrawRestrictionMsg(msg: String?) {
        if (!msg.isNullOrBlank()) {
            mBinding.tvTips.isVisible = true
            mBinding.tvTips.text = msg
        }
    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        super.onDestroy()
        captcha?.destroy()
    }

}
