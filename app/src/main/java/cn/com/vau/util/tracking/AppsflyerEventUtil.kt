package cn.com.vau.util.tracking

import android.os.Bundle
import cn.com.vau.common.application.VauApplication
import com.appsflyer.AppsFlyerLib

class AppsflyerEventUtil {

    companion object {
        @JvmStatic
        fun getInstance(): AppsflyerEventUtil {
            return Holder.instance
        }
    }

    private object Holder {
        val instance = AppsflyerEventUtil()
    }

    fun logEvent(eventName: String, bundle: Bundle) {
        val eventValue = hashMapOf<String, Any>()
        bundle.keySet().forEach { param ->
            bundle.getString(param)?.let { eventValue.put(param, it) }
        }
        AppsFlyerLib.getInstance().logEvent(VauApplication.context, eventName, eventValue)
    }

    fun logEvent(eventName: String, param: LinkedHashMap<String, Any>) {
        AppsFlyerLib.getInstance().logEvent(VauApplication.context, eventName, param)
    }

}