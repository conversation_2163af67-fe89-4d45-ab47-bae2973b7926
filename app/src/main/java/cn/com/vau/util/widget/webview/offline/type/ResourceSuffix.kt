package cn.com.vau.util.widget.webview.offline.type

object ResourceSuffix {
    /**
     * html
     */
    const val html: String = "html"
    const val htm: String = "htm"
    const val txt: String = "txt"
    const val css: String = "css"
    const val js: String = "js"

    /**
     * image
     */
    const val jpg: String = "jpg"
    const val jpeg: String = "jpeg"
    const val png: String = "png"
    const val gif: String = "gif"
    const val webp: String = "webp"
    const val svg: String = "svg"

    /**
     * video
     */
    const val mp4: String = "mp4"
    const val flv: String = "flv"
    const val mp3: String = "mp3"
    const val wav: String = "wav"
    const val aac: String = "aac"

    /**
     * data
     */
    const val json: String = "json"
    const val xml: String = "xml"

    /**
     * font
     */
    const val ttf: String = "ttf"
    const val woff: String = "woff"
    const val woff2: String = "woff2"

    /**
     * document
     */
    const val pdf: String = "pdf"
    const val doc: String = "doc"
    const val docx: String = "docx"
    const val xls: String = "xls"
    const val xlsx: String = "xlsx"
}
