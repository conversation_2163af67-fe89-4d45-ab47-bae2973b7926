package cn.com.vau.util.tracking

import cn.com.vau.common.greendao.dbUtils.UserDataUtil

/**
 * author：lvy
 * date：2024/8/22
 * desc：
 */
object SensorsHelper {

    /**
     * 交易类型 trade_type
     */
    @JvmStatic
    fun getTradeType(): String {
        return when {
            UserDataUtil.isStLogin() -> "copy"
            UserDataUtil.isDemoAccount() -> "demo"
            else -> "live"
        }
    }
}