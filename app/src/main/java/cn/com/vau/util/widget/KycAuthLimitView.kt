package cn.com.vau.util.widget

import android.app.Activity
import android.content.Context
import android.os.Build
import android.text.style.ImageSpan
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.content.ContextCompat
import androidx.core.content.withStyledAttributes
import androidx.core.text.buildSpannedString
import androidx.core.text.inSpans
import androidx.core.view.isGone
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.databinding.LayoutKycAuthLimitViewBinding
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.widget.dialog.BottomContentDialog

/**
 * Filename: KycAuthLimitView
 * Author: GG
 * Date: 2025/3/12
 * Description:
 */
class KycAuthLimitView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mBinding: LayoutKycAuthLimitViewBinding by lazy(LazyThreadSafetyMode.NONE) {
        LayoutKycAuthLimitViewBinding.inflate(LayoutInflater.from(context), this, true)
    }

    init {
        initXmlAttrs(context, attrs)
        initListener()
    }

    private fun initListener() {
        mBinding.tvTitle.clickNoRepeat {
            if (context as? Activity is Activity)
                BottomContentDialog.Builder(context as Activity)
                    .setTitle(context.getString(R.string.limit_detail))
                    .setContent(context.getString(R.string.the_limit_stated_and_withdrawals))
                    .build()
                    .showDialog()
        }
    }

    private fun initXmlAttrs(context: Context, attrs: AttributeSet?) {
        context.withStyledAttributes(attrs, R.styleable.KycAuthLimitView) {
            val numberTitle = getString(R.styleable.KycAuthLimitView_number_title)
            val depositNumber = getString(R.styleable.KycAuthLimitView_deposit_number)
            val depositNumberDesc = getString(R.styleable.KycAuthLimitView_deposit_number_desc)
            val withdrawalNumber = getString(R.styleable.KycAuthLimitView_withdrawal_number)
            val withdrawalNumberDesc = getString(R.styleable.KycAuthLimitView_withdrawal_number_desc)

            setNumberTitle(numberTitle)
            setDepositNumber(depositNumber)
            setDepositNumberDesc(depositNumberDesc)
            setWithdrawalNumber(withdrawalNumber)
            setWithdrawalNumberDesc(withdrawalNumberDesc)
        }
    }

    /**
     * 设置标题
     */
    fun setNumberTitle(title: String?) {
        val image = ContextCompat.getDrawable(context, R.drawable.draw_bitmap2_info12x12_c731e1e1e_c61ffffff)?.apply {
            setBounds(0,0,intrinsicWidth,intrinsicHeight)
        }
        mBinding.tvTitle.text = buildSpannedString {
            append(context.getString(R.string.live_trading_account_limit))
            title?.let {
                append(title)
            }
            append(" ")
            image?.let {
                // 使用 inSpans 包裹一个空格，将 ImageSpan 应用到该空格上
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    inSpans(ImageSpan(image,ImageSpan.ALIGN_CENTER)) {
                        append(" ") // 图片会显示在这个空格的位置
                    }
                }
            }
        }
    }

    /**
     * 设置入金数量
     */
    fun setDepositNumber(number: String?) {
        mBinding.tvDepositNumber.text = number
        mBinding.groupDeposit.isVisible = !number.isNullOrEmpty()
        checkViewVisible()
    }

    /**
     * 设置入金数量描述
     */
    fun setDepositNumberDesc(desc: String?) {
        mBinding.tvDepositNumberDesc.text = desc
        mBinding.tvDepositNumberDesc.isVisible = !desc.isNullOrEmpty()
    }

    /**
     * 设置出金数量
     */
    fun setWithdrawalNumber(number: String?) {
        mBinding.tvWithdrawalNumber.text = number
        mBinding.groupWithdraw.isVisible = !number.isNullOrEmpty()
        checkViewVisible()
    }

    /**
     * 设置出金数量描述
     */
    fun setWithdrawalNumberDesc(desc: String?) {
        mBinding.tvWithdrawalNumberDesc.text = desc
        mBinding.tvWithdrawalNumberDesc.isVisible = !desc.isNullOrEmpty()
    }

    private fun checkViewVisible() {
        mBinding.root.isGone = mBinding.groupDeposit.isGone && mBinding.groupWithdraw.isGone
    }

}