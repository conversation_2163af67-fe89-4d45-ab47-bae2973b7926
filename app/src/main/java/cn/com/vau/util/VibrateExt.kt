package cn.com.vau.util

import android.content.Context
import android.os.Build
import android.os.Vibrator
import android.os.VibratorManager
import cn.com.vau.common.constants.Constants

/**
 * author：lvy
 * date：2025/06/13
 * desc：震动
 */
fun Context.vibrate(duration: Long = 50) {
    if (!PermissionUtil.isGranted(this, Constants.PERMISSION_VIBRATE)) {
        return // 如果没有在 Manifest 文件声明权限，则不执行
    }
    runCatching {
        val vibrator = if (Build.VERSION.SDK_INT >= 31) {
            val vibratorManager = this.getSystemService(Context.VIBRATOR_MANAGER_SERVICE) as? VibratorManager
            vibratorManager?.defaultVibrator
        } else {
            this.getSystemService(Context.VIBRATOR_SERVICE) as? Vibrator
        }
        if (vibrator != null && vibrator.hasVibrator()) {
            vibrator.vibrate(duration)
        }
    }.onFailure {
        // 震动可能会报 SecurityException 安全异常
        LogUtil.e("震动失败: ${it.message}")
    }
}