package cn.com.vau.util.widget.webview.preload

import android.annotation.SuppressLint
import android.content.*
import android.util.AttributeSet
import android.view.ViewGroup
import android.webkit.*
import cn.com.vau.common.constants.Constants
import com.github.lzyzsd.jsbridge.BridgeWebView

/**
 * 可重复使用的WebView
 */
@Suppress("unused", "DEPRECATION")
open class PreloadWebView : BridgeWebView {

    private var isCached = false

    constructor(context: Context?) : super(context)

    constructor(context: Context?, attrs: AttributeSet?) : super(context, attrs)

    constructor(context: Context?, attrs: AttributeSet?, defStyle: Int) :
            super(context, attrs, defStyle)

    override fun setWebChromeClient(client: WebChromeClient?) {
        val newClient = if (client == null) {
            PreloadWebChromeClient()
        } else {
            PreloadWebChromeClient(client)
        }
        super.setWebChromeClient(newClient)
    }

    override fun setWebViewClient(client: WebViewClient) {
        super.setWebViewClient(PreloadWebViewClient(client))
    }

    @SuppressLint("SetJavaScriptEnabled")
    fun initSettings() {
        val settings = settings
        // 自适应屏幕
        settings.layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        settings.loadWithOverviewMode = true
        //自适应可不设置，但是华为P10有问题
        settings.textZoom = 100
        //允许与js进行交互
        // Android 在4.4开始加入对 IndexedDB 的支持，只需打开允许 JS 执行的开关就好了
        settings.javaScriptEnabled = true
        settings.cacheMode = WebSettings.LOAD_DEFAULT
        settings.domStorageEnabled = true
        settings.databaseEnabled = true

        // 允许多窗口，telegram授权时会调用window.open()，加上这个原生才会触发onCreateWindow()方法
        settings.setSupportMultipleWindows(true)
        settings.userAgentString = WebSettings.getDefaultUserAgent(context) + "/${Constants.PRODUCT_NAME}_android"
        settings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW

        //pdf使用
        settings.allowFileAccess = true
        settings.allowFileAccessFromFileURLs = true
        settings.allowUniversalAccessFromFileURLs = true
        settings.javaScriptCanOpenWindowsAutomatically = true
    }

    fun setWebViewContext(newContext: Context) {
        val contextWrapper = context
        if (contextWrapper is MutableContextWrapper) {
            contextWrapper.baseContext = newContext
        }
    }

    /**
     * 清空WebView状态
     */
    fun clear() {
        //停止加载
        stopLoading()
        //情况缓存
        clearCache(false)
        removeAllViews()
        clearFormData()
        clearSslPreferences()
        clearHistory()
        //setWebViewClient置空
        super.setWebViewClient(object : WebViewClient() {})
        //setWebChromeClient置空
        super.setWebChromeClient(null)

        val parent = parent
        if (parent is ViewGroup) {
            parent.removeView(this)
        }
        setWebViewContext(context.applicationContext)
    }

    open fun setCached() {
        isCached = true
    }

    fun resetCached() {
        isCached = false
    }

    fun isCached(): Boolean {
        return isCached
    }

}
