package cn.com.vau.util

import android.animation.ObjectAnimator
import android.annotation.SuppressLint
import android.graphics.Rect
import android.graphics.drawable.Drawable
import android.text.InputFilter
import android.text.Spanned
import android.text.TextUtils
import android.view.TouchDelegate
import android.view.View
import android.view.View.LAYOUT_DIRECTION_RTL
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import android.view.animation.LayoutAnimationController
import android.widget.EditText
import android.widget.TextView
import androidx.annotation.ColorInt
import androidx.annotation.IdRes
import androidx.coordinatorlayout.widget.ViewGroupUtils
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

/**
 * author：lvy
 * date：2024/03/26
 * desc：
 */

/**
 * 防止重复点击事件 默认350毫秒内不可重复点击 --- 只会作用到当前 view 上，不影响其他 view 点击
 * @param interval 时间间隔 默认350毫秒
 * @param action 执行方法
 */
const val FAST_CLICK_INTERVAL = 350L
fun View.clickNoRepeat(interval: Long = FAST_CLICK_INTERVAL, action: (view: View) -> Unit) {
    setOnClickListener {
        val currentTime = System.currentTimeMillis()
        val lastTime = getTag(R.id.view_last_click_time_key) as? Long ?: 0L
        if (lastTime != 0L && (currentTime - lastTime < interval)) {
            return@setOnClickListener
        }
        setTag(R.id.view_last_click_time_key, currentTime)
        action(it)
    }
}

/**
 * 防止重复执行 默认350毫秒内不可重复 --- 作用到当前方法上
 */
var lastTime = 0L
fun noRepeat(interval: Long = FAST_CLICK_INTERVAL, action: () -> Unit) {
    val currentTime = System.currentTimeMillis()
    if (lastTime != 0L && (currentTime - lastTime < interval)) {
        return
    }
    lastTime = currentTime
    action()
}

/**
 * 给adapter拓展的，防止重复点击item --- 只会作用到当前 item 上，不影响其他 item 点击
 */
fun BaseQuickAdapter<*, *>.setNbOnItemClickListener(interval: Long = 500, action: (adapter: BaseQuickAdapter<*, *>, view: View, position: Int) -> Unit) {
    setOnItemClickListener { adapter, view, position ->
        val currentTime = System.currentTimeMillis()
        val lastTime = view.getTag(R.id.adapter_item_last_click_time_key) as? Long ?: 0L
        if (lastTime != 0L && (currentTime - lastTime < interval)) {
            return@setOnItemClickListener
        }
        view.setTag(R.id.adapter_item_last_click_time_key, currentTime)
        action(adapter, view, position)
    }
}

/**
 * 给adapter拓展的，防止 重复点击 子view --- 只会作用到当前 item 的子 view 上，不影响 item 其他子 view 的点击
 */
fun BaseQuickAdapter<*, *>.setNbOnItemChildClickListener(interval: Long = 500, action: (adapter: BaseQuickAdapter<*, *>, view: View, position: Int) -> Unit) {
    setOnItemChildClickListener { adapter, view, position ->
        val currentTime = System.currentTimeMillis()
        val lastTime = view.getTag(R.id.adapter_child_view_last_click_time_key) as? Long ?: 0L
        if (lastTime != 0L && (currentTime - lastTime < interval)) {
            return@setOnItemChildClickListener
        }
        view.setTag(R.id.adapter_child_view_last_click_time_key, currentTime)
        action(adapter, view, position)
    }
}

/**
 * 输入框光标放最后
 */
fun EditText.setSelectionEnd() = this.apply {
//    requestFocus()
    setSelection(this.text.toString().length)
}

/**
 * 字体设置700字重 【gilroy_bold】
 */
fun TextView.setFontG700() {
    FontResourceUtil.typefaceGilroyBold(context)?.run {
        typeface = this
    }
}

/**
 * 字体设置600字重 【gilroy_semi_bold】
 */
fun TextView.setFontG600() {
    FontResourceUtil.typefaceGilroySemiBold(context)?.run {
        typeface = this
    }
}

/**
 * 字体设置500字重 【gilroy_medium】
 */
fun TextView.setFontG500() {
    FontResourceUtil.typefaceGilroyMedium(context)?.run {
        typeface = this
    }
}

/**
 * 字体设置400字重 【gilroy_regular】
 */
fun TextView.setFontG400() {
    FontResourceUtil.typefaceGilroyRegular(context)?.run {
        typeface = this
    }
}

/**
 * 设置 EditText 只能输入指定范围内的数字，并可传入限制小数位（默认2位）
 * 如只需输入整数，可设置EditText的 android:inputType="number" 属性
 * 如需输入小数，可设置EditText的 android:inputType="numberDecimal" 属性
 * 优先级：filter() -> doBeforeTextChanged() -> doAfterTextChanged()
 */
fun EditText.setRangeAndDecimalPlaces(minValue: Number, maxValue: Number, decimal: Int = 2) {
    val filter = object : InputFilter {
        private val min = minValue.toDouble()
        private val max = maxValue.toDouble()

        override fun filter(source: CharSequence?, start: Int, end: Int, dest: Spanned?, dstart: Int, dend: Int): CharSequence? = try {
            val input = dest?.subSequence(0, dstart).toString() +
                source?.subSequence(start, end) +
                dest?.subSequence(dend, dest.length).toString()
            var value = input.toDoubleCatching()
            if (value < min) {
                value = min
            }
            if (value in min..max && isMatchDecimalPlaces(input, decimal)) {
                null // 返回 null 表示接受输入
            } else {
                "" // 返回空字符串表示拒绝输入
            }
        } catch (e: NumberFormatException) {
            "" // 返回空字符串表示拒绝输入
        }

        private fun isMatchDecimalPlaces(input: String, decimal: Int): Boolean {
            // 如果起始位置为 0,且第二位跟的不是".",则无法后续输入
            if (input.startsWith("0") && input.trim().length > 1 && !input.contains(".")) {
                return false
            }
            val decimalSeparatorIndex = input.indexOf('.')
            if (decimalSeparatorIndex != -1) {
                val decimalPlaces = input.length - decimalSeparatorIndex - 1
                return decimalPlaces <= decimal
            }
            return true
        }
    }

    filters = arrayOf(filter)
}

/**
 * brvah 设置背景的拓展函数
 */
fun BaseViewHolder.setBackground(@IdRes viewId: Int, background: Drawable?): BaseViewHolder {
    getView<View>(viewId).background = background
    return this
}

/**
 * 相当于setTextColor，但是该方法可以避免重复的设置相同的内容
 * 【切记: 仅适用频繁设置场景】
 */
fun TextView.setTextColorDiff(@ColorInt color: Int) {
    if (textColors.defaultColor == color) {
        return
    }
    setTextColor(color)
}

/**
 * 相当于setText，但是该方法可以避免重复的设置相同的内容
 * 【切记: 仅适用频繁设置场景】
 */
fun TextView.setTextDiff(content: String) {
    if (TextUtils.equals(content, text)) {
        return
    }
    text = content
}

fun View.isRtl(): Boolean = this.layoutDirection == LAYOUT_DIRECTION_RTL

/**
 * 设置点击事件，并扩大点击范围；默认扩大 10dp。 注意同个 ViewGroup 下只能有一个 view 可扩大范围。
 * @param action 点击事件
 */
@SuppressLint("RestrictedApi")
inline fun View?.onClickWithDefaultDelegate(crossinline action: (view: View) -> Unit) {
    if (this == null) return
    post {
        val rect = Rect()
        ViewGroupUtils.getDescendantRect(parent as ViewGroup, this, rect)
        rect.inset((-10).dp2px(), (-10).dp2px())
        TouchDelegate(rect, this).also { (parent as ViewGroup).touchDelegate = it }
    }
    clickNoRepeat { action(it) }
}

/**
 * @param paddingDistance 触摸范围距离View的padding的距离,单位 dp
 * @param action 点击事件
 */
@SuppressLint("RestrictedApi")
inline fun View?.onClickWithPaddingDelegate(paddingDistance: Int, crossinline action: (view: View) -> Unit) {
    if (this == null) return
    post {
        val rect = Rect()
        ViewGroupUtils.getDescendantRect(parent as ViewGroup, this, rect)
        rect.inset((-paddingDistance).dp2px(), (-paddingDistance).dp2px())
        TouchDelegate(rect, this).also { (parent as ViewGroup).touchDelegate = it }
    }
    clickNoRepeat { action(it) }
}

/**
 * @param rect 自定义的触摸范围
 * @param action 点击事件
 */
@SuppressLint("RestrictedApi")
inline fun View?.onClickWithRectDelegate(rect: Rect, crossinline action: (view: View) -> Unit) {
    if (this == null) return
    post {
        TouchDelegate(rect, this).also { (parent as ViewGroup).touchDelegate = it }
    }
    clickNoRepeat { action(it) }
}

fun View.setVisibilityDiff(visibility: Int) {
    if (this.visibility == visibility) {
        return
    }
    this.visibility = visibility
}

/**
 * RecyclerView首次显示渐变动画
 */
fun RecyclerView?.setMaidenLayoutAnimation500() {
    if (this == null) {
        return
    }
    val controller: LayoutAnimationController = AnimationUtils.loadLayoutAnimation(this.context, R.anim.item_fade_in)
    this.setLayoutAnimation(controller)
}

/**
 * 设置View显示，并带有渐变动画
 * @param duration 动画的时间
 * @param isFirstNeedAnim 限制只有首次显示的时候才会有渐变动画
 */
fun View?.setVisibleWithAlphaAnim(duration: Long = 300, isFirstNeedAnim: Boolean = true) {
    if (this == null) {
        return
    }
    this.visibility = View.VISIBLE
    if (isFirstNeedAnim && this.getTag(R.id.view_tag_alpha_anim) != null) { // 代表已经显示过动画了，那么就不再显示
        return
    }
    val alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f)
    this.addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {}

        override fun onViewDetachedFromWindow(v: View) {
            if (alphaAnimator.isRunning) {
                alphaAnimator.cancel()
            }
        }
    })
    alphaAnimator.setDuration(duration)
    alphaAnimator.start()
    this.setTag(R.id.view_tag_alpha_anim, true)
}