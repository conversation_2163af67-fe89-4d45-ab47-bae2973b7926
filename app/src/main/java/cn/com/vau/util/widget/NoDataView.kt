package cn.com.vau.util.widget

import android.content.Context
import android.graphics.drawable.Drawable
import android.text.TextUtils
import android.util.AttributeSet
import android.view.*
import android.widget.*
import androidx.annotation.DrawableRes
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.util.*

/**
 *  没有数据显示的布局 封装
 */
class NoDataView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private lateinit var mIvIcon: ImageView
    private lateinit var mTvHintMessage: TextView
    private var mTvBottomBtn: TextView? = null

    private var hintMessage: String? = ""
    private var bottomButtonText: String? = ""
    private var iconDrawable: Drawable? = null

    init { //获取自定义属性
        val typedArray = context.obtainStyledAttributes(attrs, R.styleable.NoDataView)

        hintMessage = typedArray.getString(R.styleable.NoDataView_ndv_hintMessage)
        bottomButtonText = typedArray.getString(R.styleable.NoDataView_ndv_bottomButtonText)
        iconDrawable = typedArray.getDrawable(R.styleable.NoDataView_ndv_icon)

        initView()
        typedArray.recycle()
    }

    /*
        初始化视图
     */
    private fun initView() {
        View.inflate(context, R.layout.layout_no_data_view, this)

        orientation = VERTICAL
        gravity = Gravity.CENTER

        mIvIcon = findViewById(R.id.ivIconNd)
        ImageLoaderUtil.loadImage(context, AttrResourceUtil.getDrawable(context, R.attr.imgNoDataBase), mIvIcon)

        mTvHintMessage = findViewById(R.id.tvMsgNd)

        mTvHintMessage.text = hintMessage ?: ""

        showBtnTextView(bottomButtonText)

        if (iconDrawable != null) {
            mIvIcon.setImageDrawable(iconDrawable)
        }
    }

    private fun showBtnTextView(text: String?) {
        if (!TextUtils.isEmpty(text)) {
            if (mTvBottomBtn == null) { //说明没有inflate过
                findViewById<ViewStub>(R.id.viewStubBottomBtn).isVisible = true
                mTvBottomBtn = findViewById(R.id.tvBottomBtn)
                mTvBottomBtn?.setFontG600()
            }
            mTvBottomBtn?.text = text
        } else {
            if (mTvBottomBtn != null) {
                mTvBottomBtn?.text = text
            }
        }
    }

    /**
     * 一旦设置了paddingTop，则会由默认CENTER改为默认CENTER_HORIZONTAL
     */
    fun setPaddingTop(paddingTopDp: Int) {
        gravity = Gravity.CENTER_HORIZONTAL
        setPadding(paddingLeft, paddingTopDp.dp2px(), paddingRight, paddingBottom)
    }

    fun setHintMessage(hintMessage: String?) {
        mTvHintMessage.text = hintMessage ?: ""
    }

    fun getHintMessageView(): TextView {
        return mTvHintMessage
    }

    fun setIconVisible(visible: Boolean) {
        mIvIcon.isVisible = visible
    }

    fun setIconResource(@DrawableRes resId: Int) {
        mIvIcon.setImageResource(resId)
    }

    fun setIconDrawable(drawable: Drawable?) {
        mIvIcon.setImageDrawable(drawable)
    }

    /**
     * 设置并显示底部按钮文本
     */
    fun setBottomBtnText(text: String?) {
        showBtnTextView(text)
    }

    fun setBottomBtnViewClickListener(listener: (() -> Unit)? = null) {
        mTvBottomBtn?.setOnClickListener {
            listener?.invoke()
        }
    }
}
