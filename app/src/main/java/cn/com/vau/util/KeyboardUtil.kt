package cn.com.vau.util

import android.R
import android.app.Activity
import android.content.Context
import android.graphics.Rect
import android.os.*
import android.util.Log
import android.view.*
import android.view.ViewTreeObserver.OnGlobalLayoutListener
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.annotation.IdRes
import cn.com.vau.common.view.PasswordView
import java.util.Stack
import kotlin.math.abs

/**
 * author：lvy
 * date：2024/8/19
 * desc：
 */
object KeyboardUtil {

    private const val TAG_ON_GLOBAL_LAYOUT_LISTENER = -8
    private var sDecorViewDelta = 0

    /**
     * 打开软键盘
     *
     * @param mEditText
     * @param mContext
     */
    fun openKeyboard(mEditText: EditText?, mContext: Context) {
        val imm = mContext.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(mEditText, InputMethodManager.RESULT_SHOWN)
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY)
    }

    /**
     * Show the soft input.
     */
    fun showSoftInput() {
        val imm =
            UtilApp.getApp().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
        if (imm == null) {
            return
        }
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY)
    }

    /**
     * Show the soft input.
     */
    fun showSoftInput(activity: Activity?) {
        if (activity == null) {
            return
        }
        if (!isSoftInputVisible(activity)) {
            toggleSoftInput()
        }
    }

    /**
     * Show the soft input.
     *
     * @param view The view.
     * @param flags Provides additional operating flags.  Currently may be
     * 0 or have the [InputMethodManager.SHOW_IMPLICIT] bit set.
     */
    /**
     * Show the soft input.
     *
     * @param view The view.
     */
    @JvmOverloads
    fun showSoftInput(view: View, flags: Int = 0) {
        val imm = UtilApp.getApp().getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager?
        if (imm == null) {
            return
        }
        view.setFocusable(true)
        view.setFocusableInTouchMode(true)
        view.requestFocus()
        imm.showSoftInput(view, flags, object : ResultReceiver(Handler()) {
            override fun onReceiveResult(resultCode: Int, resultData: Bundle?) {
                if (resultCode == InputMethodManager.RESULT_UNCHANGED_HIDDEN
                    || resultCode == InputMethodManager.RESULT_HIDDEN
                ) {
                    toggleSoftInput()
                }
            }
        })
        imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, InputMethodManager.HIDE_IMPLICIT_ONLY)
    }

    private fun toggleSoftInput() {
        val imm = UtilApp.getApp().getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager?
        if (imm == null) {
            return
        }
        imm.toggleSoftInput(0, 0)
    }

    /**
     * Hide the soft input.
     *
     * @param activity The activity.
     */
    fun hideSoftInput(activity: Activity?) {
        if (activity == null) {
            return
        }
        hideSoftInput(activity.window)
    }

    /**
     * Hide the soft input.
     *
     * @param window The window.
     */
    @JvmStatic
    fun hideSoftInput(window: Window?) {
        if (window == null) {
            return
        }
        var view = window.currentFocus
        if (view == null) {
            val decorView = window.decorView
            val focusView = decorView.findViewWithTag<View?>("keyboardTagView")
            if (focusView == null) {
                view = EditText(window.context)
                view.setTag("keyboardTagView")
                (decorView as ViewGroup).addView(view, 0, 0)
            } else {
                view = focusView
            }
            view.requestFocus()
        }
        hideSoftInput(view)
    }

    /**
     * Hide the soft input.
     *
     * @param view The view.
     */
    @JvmStatic
    fun hideSoftInput(view: View) {
        val imm = UtilApp.getApp().getSystemService(Context.INPUT_METHOD_SERVICE) as? InputMethodManager
        if (imm == null) {
            return
        }
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    /**
     * 设置点击到edittext和PasswordView以外的区域隐藏软键盘
     * 设置到activity内 ，使用方法如下：
     *
     *
     *     override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
     *         KeyboardUtil.hideSoftKeyboard(this, binding.root, event)
     *         return super.dispatchTouchEvent(event)
     *     }
     *
     *     override fun onTouchEvent(event: MotionEvent?): Boolean {
     *         KeyboardUtil.hideSoftKeyboard(this, binding.root, event)
     *         return super.onTouchEvent(event)
     *     }
     *
     */
    fun hideSoftKeyboard(activity: Activity, rootView: View?, event: MotionEvent?, @IdRes vararg ignoreResIds: Int?) {
        try {
            val tappedView = findViewAtPosition(
                rootView,
                event?.rawX?.toInt().ifNull(),
                event?.rawY?.toInt().ifNull()
            )
            if (tappedView !is EditText && tappedView !is PasswordView && !ignoreResIds.any { it == tappedView?.id }) {
                hideSoftInput(activity)
                rootView?.clearFocus() // 隐藏软键盘后清除输入框焦点
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * 查询点击的位置x，y组件
     */
    private fun findViewAtPosition(rootView: View?, x: Int, y: Int): View? {
        val rect = Rect()

        // 查看rootView是否为ViewGroup，没有则没有子组件
        if (rootView !is ViewGroup) {
            rootView?.getGlobalVisibleRect(rect)
            return if (rect.contains(x, y)) rootView else null
        }

        val stack = Stack<View>()
        stack.push(rootView)

        var tappedView: View? = null

        while (stack.isNotEmpty()) {
            val view = stack.pop()

            view.getGlobalVisibleRect(rect)

            if (rect.contains(x, y)) {
                tappedView = view

                if (view is ViewGroup) {
                    for (i in 0 until view.childCount) {
                        stack.push(view.getChildAt(i))
                    }
                }
            }
        }
        return tappedView
    }

    /**
     * Register soft input changed listener.
     *
     * @param activity The activity.
     * @param listener The soft input changed listener.
     */
    fun registerSoftInputChangedListener(
        activity: Activity,
        listener: ((Int) -> Unit)? = null
    ) {
        registerSoftInputChangedListener(activity.window, listener)
    }

    /**
     * Register soft input changed listener.
     *
     * @param window The window.
     * @param listener The soft input changed listener.
     */
    fun registerSoftInputChangedListener(
        window: Window,
        listener: ((Int) -> Unit)? = null
    ) {
        val flags = window.attributes.flags
        if ((flags and WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS) != 0) {
            window.clearFlags(WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS)
        }
        val contentView = window.findViewById<FrameLayout>(R.id.content)
        val decorViewInvisibleHeightPre = intArrayOf(getDecorViewInvisibleHeight(window))
        val onGlobalLayoutListener: OnGlobalLayoutListener = object : OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                val height: Int = getDecorViewInvisibleHeight(window)
                if (decorViewInvisibleHeightPre[0] != height) {
                    listener?.invoke(height)
                    decorViewInvisibleHeightPre[0] = height
                }
            }
        }
        contentView.getViewTreeObserver().addOnGlobalLayoutListener(onGlobalLayoutListener)
        contentView.setTag(TAG_ON_GLOBAL_LAYOUT_LISTENER, onGlobalLayoutListener)
    }

    /**
     * Unregister soft input changed listener.
     *
     * @param window The window.
     */
    fun unregisterSoftInputChangedListener(window: Window) {
        val contentView = window.findViewById<View?>(R.id.content)
        if (contentView == null) {
            return
        }
        val tag = contentView.getTag(TAG_ON_GLOBAL_LAYOUT_LISTENER)
        if (tag is OnGlobalLayoutListener) {
            contentView.getViewTreeObserver()
                .removeOnGlobalLayoutListener(tag)
        }
    }

    /**
     * Return whether soft input is visible.
     *
     * @param activity The activity.
     * @return `true`: yes<br></br>`false`: no
     */
    fun isSoftInputVisible(activity: Activity): Boolean {
        return getDecorViewInvisibleHeight(activity.window) > 0
    }

    private fun getDecorViewInvisibleHeight(window: Window): Int {
        val decorView = window.decorView
        val outRect = Rect()
        decorView.getWindowVisibleDisplayFrame(outRect)
        Log.d(
            "KeyboardUtil",
            "getDecorViewInvisibleHeight: " + (decorView.bottom - outRect.bottom)
        )
        val delta: Int = abs((decorView.bottom - outRect.bottom).ifNullToInt())
        if (delta <= BarUtil.navBarHeight + BarUtil.statusBarHeight) {
            sDecorViewDelta = delta.toInt()
            return 0
        }
        return delta - sDecorViewDelta
    }

}

