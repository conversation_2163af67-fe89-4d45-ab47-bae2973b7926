package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.*
import androidx.appcompat.widget.AppCompatImageView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.util.LogUtil


class BottomSelectListAdapter(var mContext: Context, var dataList: List<String>, var selectIndex: Int = -1, var itemType: Int = 0) :
    RecyclerView.Adapter<BottomSelectListAdapter.ViewHolder>() {

    private var onItemClickListener: ((position: Int) -> Unit)? = null

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            LayoutInflater.from(mContext)
                .inflate(R.layout.item_rcy_select_list_bottom, parent, false)
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        super.onBindViewHolder(holder, position, payloads)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val currentItem = dataList.getOrNull(position) ?: return
        LogUtil.e("lwcccc","position: $position,selectIndex: $selectIndex,itemType: $itemType, orderBean: $currentItem")
        if (itemType == 0) {
            holder.mCheckBox.visibility = View.VISIBLE
            holder.ifvArrowRight.visibility = View.GONE
        } else {
            holder.mCheckBox.visibility = View.GONE
            if (
                (currentItem == mContext.getString(R.string.pause_copying) && position == 3)
                || (currentItem == mContext.getString(R.string.resume_copying) && position == 3)
                || (currentItem == mContext.getString(R.string.stop_copy) && position == 4)
            ) {
                holder.ifvArrowRight.visibility = View.GONE
            } else {
                holder.ifvArrowRight.visibility = View.VISIBLE
            }
        }

        holder.tvData.text = currentItem
        holder.mCheckBox.isChecked = selectIndex == position

        holder.itemView.setOnClickListener {
            if(selectIndex == holder.bindingAdapterPosition){
                holder.mCheckBox.isChecked = true
            }
            updateSelectIndex(holder.bindingAdapterPosition)
            onItemClickListener?.invoke(holder.bindingAdapterPosition)
        }

        holder.mCheckBox.setOnClickListener {
            if(selectIndex == holder.bindingAdapterPosition){
                holder.mCheckBox.isChecked = true
            }
            updateSelectIndex(holder.bindingAdapterPosition)
            onItemClickListener?.invoke(holder.bindingAdapterPosition)
        }
    }


    override fun getItemCount(): Int = dataList.size

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val mCheckBox: CheckBox = view.findViewById(R.id.mCheckBox)
        val ifvArrowRight: AppCompatImageView = view.findViewById(R.id.ifvArrowRight)
        val tvData: TextView = view.findViewById(R.id.tvData)
    }


    fun setOnItemClickListener(onItemClickListener: ((position: Int) -> Unit)? = null) {
        this.onItemClickListener = onItemClickListener
    }


    fun updateData(newDataList: List<String>, newSelect: Int, newItemType: Int) {
        val diffCallback = DiffCallback(dataList, newDataList, itemType, newItemType, selectIndex, newSelect)
        val diffResult = DiffUtil.calculateDiff(diffCallback)
        dataList = newDataList
        selectIndex = newSelect
        itemType = newItemType
        diffResult.dispatchUpdatesTo(this)
    }

    fun updateSelectIndex(newIndex: Int) {
        if (selectIndex != newIndex) {
            val oldIndex = selectIndex
            selectIndex = newIndex
            notifyItemChanged(oldIndex)
            notifyItemChanged(newIndex)
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    fun updateItemType(newItemType: Int) {
        if (itemType != newItemType) {
            this.itemType = newItemType
            notifyDataSetChanged()
        }
    }

    class DiffCallback(
        private val oldList: List<String>,
        private val newList: List<String>,
        private val oldItemType: Int,
        private val newItemType: Int,
        private val oldSelectIndex: Int,
        private val newSelectIndex: Int
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int {
            return oldList.size
        }

        override fun getNewListSize(): Int {
            return newList.size
        }

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition] && oldItemType == newItemType && oldSelectIndex == newSelectIndex
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            return oldList[oldItemPosition] == newList[newItemPosition] && oldItemType == newItemType && oldSelectIndex == newSelectIndex
        }
    }

}
