package cn.com.vau.util.opt

import android.annotation.SuppressLint
import cn.com.vau.BuildConfig
import cn.com.vau.util.LogUtil

@SuppressLint("LogNotTimber")
fun Any.xhLoge(content:String){
    if (!BuildConfig.DEBUG) {
        return
    }
    LogUtil.e("xinhuan", "${this.javaClass.simpleName} $content")
}
@SuppressLint("LogNotTimber")
fun Any.xhLogi(content:String){
    if (!BuildConfig.DEBUG) {
        return
    }
    LogUtil.i("xinhuan", "${this.javaClass.simpleName} $content")
}
@SuppressLint("LogNotTimber")
fun Any.xhLogd(content:String){
    if (!BuildConfig.DEBUG) {
        return
    }
    LogUtil.d("xinhuan", "${this.javaClass.simpleName} $content")
}