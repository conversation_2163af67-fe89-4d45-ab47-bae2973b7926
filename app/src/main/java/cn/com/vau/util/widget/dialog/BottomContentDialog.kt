package cn.com.vau.util.widget.dialog

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.databinding.DialogBottomContentBinding
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog

@SuppressLint("ViewConstructor")
@Suppress("unused")
class BottomContentDialog private constructor(
    context: Context,
    title: CharSequence?,
    private var content: CharSequence?,//可以是String或者SpannableString
    private var subTitle: CharSequence? = null,
    private var subContent: CharSequence? = null,//可以是String或者SpannableString
    onCreateListener: ((DialogBottomContentBinding) -> Unit)? = null,
    onDismissListener: (() -> Unit)? = null
) : BottomDialog<DialogBottomContentBinding>(
    context,
    DialogBottomContentBinding::inflate,
    title,
    onCreateListener,
    onDismissListener
) {
    override fun setContentView() {
        super.setContentView()
        setContent()
        setSubTitleView()
        setSubContentView()
        fixContentTextViewColor()
    }

    private fun fixContentTextViewColor() {
        if (subContent.isNullOrEmpty() && subTitle.isNullOrEmpty()) {
            mContentBinding.tvContent.setTextColor(
                AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff)
            )
        } else {
            mContentBinding.tvContent.setTextColor(
                AttrResourceUtil.getColor(context, R.attr.color_c1e1e1e_cebffffff)
            )
        }
    }

    fun setContent(content: CharSequence?): BottomContentDialog {
        this.content = content
        setContent()
        fixContentTextViewColor()
        return this
    }

    private fun setContent() {
        mContentBinding.tvContent.isVisible = !content.isNullOrEmpty()
        mContentBinding.tvContent.text = content
    }

    fun setSubTitle(subTitle: CharSequence?): BottomContentDialog {
        this.subTitle = subTitle
        setSubTitleView()
        fixContentTextViewColor()
        return this
    }

    private fun setSubTitleView() {
        mContentBinding.tvSubTitle.isVisible = !subTitle.isNullOrEmpty()
        mContentBinding.tvSubTitle.text = subTitle
    }

    fun setSubContent(subContent: CharSequence?): BottomContentDialog {
        this.subContent = subContent
        setSubContentView()
        fixContentTextViewColor()
        return this
    }

    private fun setSubContentView() {
        mContentBinding.tvSubContent.isVisible = !subContent.isNullOrEmpty()
        mContentBinding.tvSubContent.text = subContent
    }

    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogBottomContentBinding, Builder>(activity) {

        //标题
        private var title: CharSequence? = null

        //弹窗内容
        private var content: CharSequence? = null

        //二级标题
        private var subTitle: CharSequence? = null

        //二级内容
        private var subContent: CharSequence? = null


        /**
         * 设置内容
         */
        fun setTitle(title: CharSequence?) = apply {
            this.title = title
            return this
        }


        /**
         * 设置内容
         */
        fun setContent(content: CharSequence?) = apply {
            this.content = content
            return this
        }

        /**
         * 设置二级标题
         */

        fun setSubTitle(subTitle: CharSequence?) = apply {
            this.subTitle = subTitle
            return this
        }

        /**
         * 设置二级内容
         */
        fun setSubContent(subContent: CharSequence?) = apply {
            this.subContent = subContent
            return this
        }

        override fun build(): BottomContentDialog {
            return super.build() as BottomContentDialog
        }

        override fun createDialog(context: Context): IDialog<DialogBottomContentBinding> {
            return BottomContentDialog(
                context,
                title,
                content,
                subTitle,
                subContent,
                config.onCreateListener,
                config.onDismissListener
            )
        }
    }
}
