package cn.com.vau.util

import android.annotation.SuppressLint
import android.content.Context
import android.media.MediaDrm
import android.os.Build
import android.provider.Settings
import java.security.MessageDigest
import java.util.*

/**
 * Filename: OneCodeUtils.kt
 * Author: GG
 * Date: 2023/12/21
 * Description:
 */
object DeviceUtil {

    /**
     * 获得设备硬件标识
     *
     * @param context 上下文
     * @return 设备硬件标识
     */
    @JvmStatic
    fun getDeviceId(context: Context): String {
        val sbDeviceId = StringBuilder()
        //获取数字版权管理设备ID（无需权限）
        val widevineID = getWidevineID()
        //获得AndroidId（无需权限）
        val androidId = getAndroidId(context)
        //获得设备序列号（无需权限）
        val serial = getSerial()
        //获得硬件uuid（根据硬件相关属性，生成uuid）（无需权限）
        val uuid = getDeviceUUID().replace("-", "")

        if (widevineID.isNotEmpty()) {
            sbDeviceId.append(widevineID)
            sbDeviceId.append("|")
        }

        //追加androidId
        if (androidId.isNotEmpty()) {
            sbDeviceId.append(androidId)
            sbDeviceId.append("|")
        }
        //追加serial
        if (serial.isNotEmpty()) {
            sbDeviceId.append(serial)
            sbDeviceId.append("|")
        }
        //追加硬件uuid
        if (uuid.isNotEmpty()) {
            sbDeviceId.append(uuid)
        }

        //生成SHA1，统一DeviceId长度
        if (sbDeviceId.isNotEmpty()) {
            try {
                val hash = getHashByString(sbDeviceId.toString())
                val sha1 = bytesToHex(hash)
                if (sha1.isNotEmpty()) {
                    //返回最终的DeviceId
                    return sha1
                }
            } catch (ex: java.lang.Exception) {
                ex.printStackTrace()
                return getUUID()
            }
        }

        //如果以上硬件标识数据均无法获得，
        //则DeviceId默认使用系统随机数，这样保证DeviceId不为空
        return getUUID()
    }

    private fun getUUID(): String = UUID.randomUUID().toString().replace("-", "")

    /**
     * 获取数字版权管理设备ID
     *
     * @return WidevineID，可能为空
     */
    private fun getWidevineID(): String {
        
        try {
            //See https://stackoverflow.com/questions/16369818/how-to-get-crypto-scheme-uuid
            //You can find some UUIDs in the https://github.com/google/ExoPlayer source code
            val WIDEVINE_UUID = UUID(-0x121074568629b532L, -0x5c37d8232ae2de13L)
            val mediaDrm = MediaDrm(WIDEVINE_UUID)
            val widevineId = mediaDrm.getPropertyByteArray(MediaDrm.PROPERTY_DEVICE_UNIQUE_ID) ?: return ""
            val sb = StringBuilder()
            for (aByte in widevineId) {
                sb.append(String.format("%02x", aByte))
            }
            return sb.toString()
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        } catch (e: Error) {
            e.printStackTrace()
        }
        return ""
    }

    /**
     * 获得设备的AndroidId
     *
     * @param context 上下文
     * @return 设备的AndroidId
     */
    @SuppressLint("HardwareIds")
    private fun getAndroidId(context: Context): String {
        try {
            return Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ANDROID_ID
            )
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
        return ""
    }

    /**
     * 获得设备序列号（如：WTK7N16923005607）, 个别设备无法获取
     *
     * @return 设备序列号
     */
    @SuppressLint("HardwareIds")
    private fun getSerial(): String {
        try {
            return Build.SERIAL
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
        }
        return ""
    }

    /**
     * 获得设备硬件uuid
     * 使用硬件信息，计算出一个随机数
     *
     * @return 设备硬件uuid
     */
    @SuppressLint("HardwareIds")
    private fun getDeviceUUID(): String {
        try {
            val dev =
                "3883756" + Build.BOARD.length % 10 + Build.BRAND.length % 10 + Build.DEVICE.length % 10 + Build.HARDWARE.length % 10 + Build.ID.length % 10 +
                        Build.MODEL.length % 10 + Build.PRODUCT.length % 10 + Build.SERIAL.length % 10
            return UUID(
                dev.hashCode().toLong(),
                Build.SERIAL.hashCode().toLong()
            ).toString()
        } catch (ex: java.lang.Exception) {
            ex.printStackTrace()
            return ""
        }
    }

    /**
     * 取SHA1
     * @param data 数据
     * @return 对应的hash值
     */
    private fun getHashByString(data: String): ByteArray {
        try {
            val messageDigest = MessageDigest.getInstance("SHA1")
            messageDigest.reset()
            messageDigest.update(data.toByteArray(charset("UTF-8")))
            return messageDigest.digest()
        } catch (e: java.lang.Exception) {
            return "".toByteArray()
        }
    }

    /**
     * 转16进制字符串
     * @param data 数据
     * @return 16进制字符串
     */
    private fun bytesToHex(data: ByteArray): String {
        val sb = java.lang.StringBuilder()
        var stmp: String
        for (n in data.indices) {
            stmp = (Integer.toHexString(data[n].toInt() and 0xFF))
            if (stmp.length == 1) sb.append("0")
            sb.append(stmp)
        }
        return sb.toString().uppercase(Locale.CHINA)
    }
}