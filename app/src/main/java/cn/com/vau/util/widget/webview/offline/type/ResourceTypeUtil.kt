package cn.com.vau.util.widget.webview.offline.type

import android.webkit.MimeTypeMap
import android.webkit.URLUtil
import cn.com.vau.util.widget.webview.offline.type.ResourceSuffix.gif
import cn.com.vau.util.widget.webview.offline.type.ResourceSuffix.jpeg
import cn.com.vau.util.widget.webview.offline.type.ResourceSuffix.jpg
import cn.com.vau.util.widget.webview.offline.type.ResourceSuffix.png
import cn.com.vau.util.widget.webview.offline.type.ResourceSuffix.webp

object ResourceTypeUtil {

    fun getMimeType(url: String): String? {
        val extension = getExtensionType(url)
        val mimeType = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension)
//        WebViewLogUtil.e("extension:${extension} mimeType:${mimeType}")
        return mimeType
    }

    fun getExtensionType(url: String): String? {
        return MimeTypeMap.getFileExtensionFromUrl(url)
    }

    fun isImageUrl(url: String?): Boolean {
        if (url.isNullOrEmpty()) {
            return false
        }
        if (!URLUtil.isHttpUrl(url) && !URLUtil.isHttpsUrl(url)) {
            return false
        }
        val resType = getExtensionType(url)
        if (resType.isNullOrEmpty()) {
            return false
        }
        return resType.endsWith(jpg) ||
                resType.endsWith(jpeg) ||
                resType.endsWith(png) ||
                resType.endsWith(gif) ||
                resType.endsWith(webp)
    }

    @JvmStatic
    fun isPdfUrl(url: String?): Boolean {
        if (url.isNullOrEmpty()) {
            return false
        }
        return url.contains(".pdf") || url.contains(".PDF")
    }
}