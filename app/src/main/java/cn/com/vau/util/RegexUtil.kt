package cn.com.vau.util

import java.util.regex.Pattern

/**
 * Created by ZHEN2 on 2017/8/24.
 * 1
 */
object RegexUtil {

    /*----------常用输入验证------*/
    /**
     * 利用正则表达式判断字符串是否是数字
     *
     * @param str 待判断的字符串
     * @return 如果字符串是数字，则返回true；否则返回false
     */
    fun isNumeric(str: String?): <PERSON><PERSON><PERSON> {
        // 如果字符串为空，则直接返回false
        if (str.isNullOrBlank()) return false
        // 编译正则表达式，模式为"[0-9]*"，即匹配任意长度的数字
        val pattern = Pattern.compile("[0-9]*")
        // 使用编译好的正则表达式创建匹配器
        val isNum = pattern.matcher(str ?: "")
        // 执行匹配，并返回匹配结果
        return isNum.matches()
    }

    // 邮箱规则：用户名@服务器名.后缀
    fun isEmail(inputString: String?): Bo<PERSON>an {
        return inputString != null && inputString.contains("@")
    }

    /**
     * 是否包含以下特殊字符  "[!@#$%^&*.()]"
     *
     * @param inputString
     * @return
     */
    fun isContainsSpecial(inputString: String?): Boolean {
        if (inputString.isNullOrBlank()) return false
        val pattern = Pattern.compile("[!@#$%^&*.()]")
        val matcher = pattern.matcher(inputString)
        return matcher.find()
    }

    /**
     * 判断字符串中是否包含至少一个数字。
     * 此方法使用正则表达式来检查输入字符串是否包含至少一个数字字符（0-9）。
     *
     * @param inputString 要检查的输入字符串。
     * @return 如果字符串中包含至少一个数字，则返回 true；否则返回 false。
     */
    fun isContainsNumber(inputString: String?): Boolean {
        if (inputString.isNullOrBlank()) return false
        // 使用正则表达式 "\\d" 来匹配任意一个数字字符
        val pattern = Pattern.compile("\\d")

        // 创建一个匹配器来查找输入字符串中是否存在数字
        val matcher = pattern.matcher(inputString)

        // 如果找到至少一个数字，返回 true；否则返回 false
        return matcher.find()
    }

    /**
     * 判断字符串中是否包含至少一个字母。
     *
     *
     * 此方法使用正则表达式检查输入字符串中是否包含至少一个中文或英文的字母字符。
     *
     * @param inputString 要检查的输入字符串。
     * @return 如果字符串中包含至少一个字母，则返回 true；否则返回 false。
     */
    fun isContainsLetter(inputString: String?): Boolean {
        if (inputString.isNullOrBlank()) return false
        val pattern = Pattern.compile("(?=.*[a-z])(?=.*[A-Z]).*")
        val matcher = pattern.matcher(inputString)
        return matcher.find()
    }

    /**
     * 判断字符串是否仅包含有效的字符（中文字符、英文字符、数字和空格）。
     *
     *
     * 此方法使用正则表达式检查输入字符串是否只包含中文字符、英文字符、数字以及空格。
     *
     * @param text 要检查的输入字符串。
     * @return 如果字符串只包含有效字符，则返回 true；否则返回 false。
     */
    fun isValidText(text: String?): Boolean {
        if (text.isNullOrBlank()) return false
        val regex = "^[a-zA-Z0-9\\s]+$"
        return Pattern.matches(regex, text)
    }
}
