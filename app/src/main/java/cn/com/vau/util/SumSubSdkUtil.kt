package cn.com.vau.util

import android.app.Activity
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.util.SumSubSdkUtil.snsSDK
import cn.com.vau.util.language.LanguageHelper
import com.sumsub.sns.core.SNSMobileSDK
import com.sumsub.sns.core.data.listener.*
import com.sumsub.sns.core.data.model.*
import com.sumsub.sns.core.theme.*

/**
 * Filename: SumSubSdkUtil
 * Author: GG
 * Date: 2024/8/28
 * Description:
 */
object SumSubSdkUtil {

    private var snsSDK: SNSMobileSDK.SDK? = null

    /**
     * 审核通过后，自动关闭SDK的秒数
     *
     * https://docs.sumsub.com/docs/get-started-android#dismissal
     */
    private const val AUTO_CLOSE_IN_SECONDS: Int = 2

    /**
     * 启动SDK
     *
     * https://docs.sumsub.com/docs/get-started-android#initialization
     */
    fun launchSumSubSdk(activity: Activity, accessToken: String?, tokenCallback: (() -> String), stateCallback: ((Boolean) -> Unit)? = null, clickCallback: (() -> Unit)? = null) {
        val tokenExpirationHandler = object : TokenExpirationHandler {
            override fun onTokenExpired(): String? {
                return tokenCallback.invoke()
            }
        }
        var isSuccess = false
        snsSDK = SNSMobileSDK.Builder(activity)
            .withAccessToken(accessToken, tokenExpirationHandler)
            .withTheme(getSNSTheme(activity))
            .withLocale(LanguageHelper.getAppLocale())
            .withHandlers(
                onStateChanged = fun(newState: SNSSDKState, _) {
                    // 读取是不是中途审核通过
                    when (newState) {
                        // 审核成功 🔓
                        is SNSSDKState.Approved -> {
                            LogUtil.w("SumSubSDKUtils.kt ---> Approved", newState.message)
                            isSuccess = true
                        }
                        // 审核失败 🔒
                        is SNSSDKState.FinallyRejected -> {
                            LogUtil.w("SumSubSDKUtils.kt ---> FinallyRejected", newState.message)
                            isSuccess = false
                        }

                        else -> LogUtil.w("onStateChangedHandler", newState.name)
                    }

                },
                onCompleted = fun(snsCompletionResult: SNSCompletionResult, snsSdkState: SNSSDKState) {
                    when (snsCompletionResult) {

                        // 审核成功关闭弹窗
                        is SNSCompletionResult.SuccessTermination -> {
                            LogUtil.w("SumSub Sdk closed with : ${snsSdkState.message}")
                            stateCallback?.invoke(isSuccess)
                        }

                        // 审核异常关闭弹窗
                        is SNSCompletionResult.AbnormalTermination -> {
                            LogUtil.w("SumSub Sdk closed with : ${snsSdkState.message}")
                            stateCallback?.invoke(false)
                        }

                        else -> LogUtil.w("Unknown state : ${snsSdkState.message}")
                    }
                },
                /**
                 * SDK报错查询信息
                 * https://docs.sumsub.com/docs/callbacks-android#on-sdk-errors
                 */
                onError = fun(exception: SNSException) {
                    LogUtil.d("SumSubSDKUtils.kt ---> Error", "${exception.message}")
                    when (exception) {
                        is SNSException.Api -> LogUtil.d("Api exception.", "${exception.description}")
                        is SNSException.Network -> LogUtil.d("Network exception.", "${exception.message}")
                        is SNSException.Unknown -> LogUtil.d("Unknown exception", "${exception.message}")
                    }
                },
                onEvent = fun(snsEvent: SNSEvent) {
                    LogUtil.w("onEvent", snsEvent.eventType)
                }
            )
            .withSupportItems(
                listOf(
                    SNSSupportItem(
                        title = activity.getString(R.string.customer_supports),
                        subtitle = activity.getString(R.string.dialog_maintenance_customer_service_msg_x, AppUtil.getSuperviseEmail()),
                        type = SNSSupportItem.Type.Email,
                        value = AppUtil.getSuperviseEmail(),
                        iconDrawable = ContextCompat.getDrawable(activity, AttrResourceUtil.getDrawable(activity, R.attr.imgLogoMark)),
                        iconName = SNSIconHandler.SNSCommonIcons.MAIL.imageName,
                        onClick = fun(snsSupportItem: SNSSupportItem) {
                            LogUtil.w("onEvent", "${snsSupportItem.title}")
                            clickCallback?.invoke()
                        }
                    )))
            .withAnalyticsEnabled(false)
            .withDebug(!HttpUrl.official)
            .withAutoCloseOnApprove(AUTO_CLOSE_IN_SECONDS)
            .build()

        snsSDK?.launch()
    }

    /**
     * @see snsSDK
     * 手动关闭SumSub SDK
     *
     * https://docs.sumsub.com/docs/get-started-android#dismissal
     */
    fun closeSumSubSDK() {
        snsSDK?.dismiss()
    }

    /**
     * 查询主题API
     * https://docs.sumsub.com/docs/android-theme-api
     *
     * 修改SDK主题色
     * @see colors 背景颜色 / 按键颜色自定义
     * @see fonts 字体自定义
     * @see metrics 调整按钮边角
     */
    private fun getSNSTheme(activity: Activity): SNSTheme {
        return SNSTheme {
            colors {
                statusBarColor = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.mainLayoutBg))
                navigationBarItem = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))
                backgroundCommon = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.mainLayoutBg))
                bottomSheetBackground = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.mainLayoutBg))

                contentStrong = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff))
                contentNeutral = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))
                backgroundNeutral = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1f1e1e1e_c1fffffff))
                contentWeak = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))

                primaryButtonBackground = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff))
                primaryButtonBackgroundHighlighted = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff))
                primaryButtonBackgroundDisabled = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff))
                primaryButtonContentDisabled = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_cebffffff_c1e1e1e))

                secondaryButtonContent = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_cebffffff_c1e1e1e))
                secondaryButtonContentHighlighted = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.mainLayoutBg))
                secondaryButtonContentDisabled = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff))

                fieldContent = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1e1e1e_cebffffff))
                fieldPlaceholder = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c731e1e1e_c61ffffff))
                fieldBackground = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1f1e1e1e_c1fffffff))

                // backgroundSuccess = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1f18b494_c4d18b494))
                // backgroundWarning = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c21f2aa0d_c4dF2aa0d))
                // backgroundCritical = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_c1ff05c5c_c4df05c5c))
                // contentInfo = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))
                // contentLink = SNSThemeColor(ContextCompat.getColor(activity, R.color.ce35728))
                // cardPlainBackground = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_cffffff_c162232))
                // fieldTint = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))
                // cameraContent = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))
                // bottomSheetHandle = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_ca61e1e1e_c99ffffff))
                primaryButtonContent = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_cebffffff_c1e1e1e))
                //                cameraBackgroundOverlay = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_cf8f8f8_c0d1520))
                primaryButtonContentHighlighted = SNSThemeColor(AttrResourceUtil.getColor(activity, R.attr.color_cebffffff_c1e1e1e))
            }

            fonts {
                headline1 = ResourcesCompat.getFont(activity, R.font.gilroy_semi_bold)?.let { SNSThemeFont(it, 22) }
                headline2 = ResourcesCompat.getFont(activity, R.font.gilroy_semi_bold)?.let { SNSThemeFont(it, 13) }

                // Continue
                // Identity Document
                // Selfie
                subtitle1 = ResourcesCompat.getFont(activity, R.font.gilroy_semi_bold)?.let { SNSThemeFont(it, 16) }
                subtitle2 = ResourcesCompat.getFont(activity, R.font.gilroy_semi_bold)?.let { SNSThemeFont(it, 16) }

                // Take a photo of your ID
                // Take a selfie
                body = ResourcesCompat.getFont(activity, R.font.gilroy_regular)?.let { SNSThemeFont(it, 12) }

                // Power By Sum sub 底部链接
                // Verify Your Identity 标题
                // Please Wait 等待文案
                // If you can't find your document type or country of issue, contact support 底部support文案
                // Support 标题
                caption = ResourcesCompat.getFont(activity, R.font.gilroy_regular)?.let { SNSThemeFont(it, 13) }
            }

            metrics {
                supportItemCardStyle = SNSThemeMetric.CardStyle.PLAIN
                documentTypeCardStyle = SNSThemeMetric.CardStyle.PLAIN
                selectedCountryCardStyle = SNSThemeMetric.CardStyle.PLAIN
                agreementCardStyle = SNSThemeMetric.CardStyle.PLAIN
                bottomSheetCornerRadius = 100f.dp2px()
                cardCornerRadius = 8f.dp2px()
                buttonCornerRadius = 100f.dp2px()
                // 输入框圆角
                fieldCornerRadius = 100f.dp2px()
            }
        }
    }

}