package cn.com.vau.util

import android.text.TextUtils
import android.view.*
import android.widget.*
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication

/**
 * Created by a on 2014/5/18.
 * Toast工具类
 *
 * <AUTHOR>
 */
object ToastUtil {

    private var toast: Toast? = null

    @JvmStatic
    fun showToast(msg: String?) {
        if (TextUtils.isEmpty(msg)) return

        toast = Toast.makeText(VauApplication.context, msg, Toast.LENGTH_LONG)

        val view = View.inflate(VauApplication.context, R.layout.toast_default, null)
        val tvMsg = view.findViewById<TextView>(R.id.text_view)
        tvMsg.text = msg

        toast?.setGravity(Gravity.CENTER_VERTICAL, 0, 0)
        toast?.setView(view)
        toast?.show()
    }
}