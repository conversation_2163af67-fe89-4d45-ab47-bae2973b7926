package cn.com.vau.util.language

import android.text.TextUtils
import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.storage.SpManager
import com.netease.nis.captcha.CaptchaConfiguration
import java.util.Locale

/**
 * author：lvy
 * date：2024/12/07
 * desc：
 */
object LanguageHelper {

    private var sCurrentLocale: Locale? = null // 当前 Locale
    private var sCurrentLanguageCode: String? = null // 当前语言代码

    /**
     * 保存 App 语种设置
     */
    @JvmStatic
    fun saveAppLanguageSetting(locale: Locale) {
        sCurrentLocale = locale
        sCurrentLanguageCode = locale.toString()
        SpManager.putLanguageLang(locale.language)
        SpManager.putLanguageRegion(locale.country)
    }

    /**
     * 获取当前App的 LanguageCode
     */
    @JvmStatic
    fun getAppLanguageCode(): String {
        if (sCurrentLanguageCode != null) {
            return sCurrentLanguageCode!!
        }

        sCurrentLanguageCode = Locale(SpManager.getLanguageLang(), SpManager.getLanguageRegion()).toString()
        return sCurrentLanguageCode!!
    }

    /**
     * 获取当前App的 Locale
     */
    @JvmStatic
    fun getAppLocale(): Locale {
        if (sCurrentLocale != null) {
            return sCurrentLocale!!
        }

        val lang = SpManager.getLanguageLang()
        if (!TextUtils.isEmpty(lang)) {
            sCurrentLocale = Locale(lang, SpManager.getLanguageRegion())
            return sCurrentLocale!!
        }
        // 不需要兼容老用户时，需要返回默认值
//        else {
//            // 逻辑如果走到这里application的attach方法会调用一次，会给sp赋值，此类中其他获取lang方法不需要再次处理默认值逻辑
//            val systemLocale = LanguageUtil.getLocale(VauApplication.context)
//            // 如果第一次安装时取系统locale，会有第三个variant参数，在这里转换一次app能用的
//            val newLocale = adaptSystemLocale(systemLocale)
//            // 存缓存
//            saveAppLanguageSetting(newLocale)
//            saveShowName(newLocale)
//            return sCurrentLocale!!
//        }
        // 兼容老用户，后期删除
        val index = SpManager.getLanguageSelect()
        if (index == -1) {
            // 用户如果直接安装的是语言优化后的版本，需要获取系统语言进行显示
            // 逻辑如果走到这里application的attach方法会调用一次，会给sp赋值，此类中其他获取lang方法不需要再次处理默认值逻辑
            val systemLocale = LanguageUtil.getLocale(VauApplication.context)
            // 如果第一次安装时取系统locale，会有第三个variant参数，在这里转换一次app能用的
            val newLocale = adaptSystemLocale(systemLocale)
            // 存缓存
            saveAppLanguageSetting(newLocale)
            saveShowName(newLocale)
            return sCurrentLocale!!
        } else {
            val locale = when (index) {
                1 -> Locale("zh", "CN") // 简体中文
                2 -> Locale("th", "TH") // 泰语
                3 -> Locale("fr", "FR") // 法语
                4 -> Locale("ja", "JP") // 日语
                5 -> Locale("de", "DE") // 德语
                6 -> Locale("pt", "PT") // 葡萄牙语
                7 -> Locale("in", "ID") // 印尼语
                8 -> Locale("ru", "RU") // 俄语
                9 -> Locale("kk", "KZ") // 哈萨克斯坦语
                10 -> Locale("mn", "MN") // 蒙古语
                11 -> Locale("vi", "VN") // 越南
                12 -> Locale("ms", "MY") // 马来
                13 -> Locale("it", "IT") // 意大利
                14 -> Locale("hi", "IN") // 印度
                15 -> Locale("es", "ES") // 西班牙
                16 -> Locale("zh", "TW") // 繁体中文
                17 -> Locale("ko", "KR") // 韩语
                18 -> Locale("tl", "PH") // 菲律宾
                19 -> Locale("ar", "SA") // 阿拉伯
                else -> Locale("en", "US") // 英语
            }
            // 这里改为存缓存等于把用户存index改为存优化后的sp了，保证后续删除index相关不用影响老用户
            saveAppLanguageSetting(locale)
            saveShowName(locale)
            return sCurrentLocale!!
        }
    }

    /**
     * 适配系统 Locale
     */
    private fun adaptSystemLocale(systemLocale: Locale): Locale {
        return when (systemLocale.language) {
            "zh" -> {
                if (systemLocale.country == "TW") {
                    Locale("zh", "TW") // 繁体中文
                } else {
                    Locale("zh", "CN") // 简体中文
                }
            }

            "th" -> Locale("th", "TH") // 泰语
            "fr" -> Locale("fr", "FR") // 法语
            "ja" -> Locale("ja", "JP") // 日语
            "de" -> Locale("de", "DE") // 德语
            "pt" -> Locale("pt", "PT") // 葡萄牙语
            "in", "id" -> Locale("in", "ID") // 印尼语
            "ru" -> Locale("ru", "RU") // 俄语
            "kk" -> Locale("kk", "KZ") // 哈萨克斯坦语
            "mn" -> Locale("mn", "MN") // 蒙古语
            "vi" -> Locale("vi", "VN") // 越南
            "ms" -> Locale("ms", "MY") // 马来
            "it" -> Locale("it", "IT") // 意大利
            "hi" -> Locale("hi", "IN") // 印度
            "es" -> Locale("es", "ES") // 西班牙
            "ko" -> Locale("ko", "KR") // 韩语
            "tl", "fil" -> Locale("tl", "PH") // 菲律宾
            "ar" -> Locale("ar", "SA") // 阿拉伯
            else -> Locale("en", "US") // 英语
        }
    }

    /**
     * 用户第一次安装或兼容老用户时需要转换一次显示的语言名称，后续全从缓存拿了
     */
    private fun saveShowName(locale: Locale): String {
        val showName = when (locale.language) {
            "zh" -> {
                if (SpManager.getLanguageRegion() == "TW") {
                    "繁體中文" // 繁体中文
                } else {
                    "简体中文" // 简体中文
                }
            }

            "th" -> "ภาษาไทย" // 泰语
            "fr" -> "Français" // 法语
            "ja" -> "日本語" // 日语
            "de" -> "Deutsch" // 德语
            "pt" -> "Português (Portugal)" // 葡萄牙语
            "in", "id" -> "Bahasa Indonesia" // 印尼语
            "ru" -> "Pусский" // 俄语
            "kk" -> "Қазақша" // 哈萨克斯坦语
            "mn" -> "Монгол" // 蒙古语
            "vi" -> "TiếngViệt" // 越南
            "ms" -> "Bahasa Melayu" // 马来
            "it" -> "Italiano" // 意大利
            "hi" -> "हिन्दी" // 印度
            "es" -> "Español (España)" // 西班牙
            "ko" -> "한국어" // 韩语
            "tl", "fil" -> "Tagalog"// 菲律宾
            "ar" -> "عربي" // 阿拉伯
            else -> "English (US)" // 英语
        }
        SpManager.putLanguageShowName(showName) // 存缓存，后续全从缓存拿值
        return showName
    }

    /**
     * 神策埋点语言
     */
    fun getSensorsDataLang(): String {
        val lang = SpManager.getLanguageLang()
        return when (lang) {
            "zh" -> {
                if (SpManager.getLanguageRegion() == "TW") {
                    "hant" // 繁体中文
                } else {
                    "hans" // 简体中文
                }
            }

            "ja" -> "jp" // 日语
            "in" -> "id" // 印尼语
            "hi" -> "in" // 印度
            else -> lang
        }
    }

    /**
     * 网易语言返回
     */
    fun getCaptchaLangType(): CaptchaConfiguration.LangType {
        return when (SpManager.getLanguageLang()) {
            "zh" -> {
                if (SpManager.getLanguageRegion() == "TW") {
                    CaptchaConfiguration.LangType.LANG_ZH_TW // 繁体中文
                } else {
                    CaptchaConfiguration.LangType.LANG_ZH_CN // 简体中文
                }
            }

            "th" -> CaptchaConfiguration.LangType.LANG_TH // 泰语
            "fr" -> CaptchaConfiguration.LangType.LANG_FR // 法语
            "ja" -> CaptchaConfiguration.LangType.LANG_JA // 日语
            "de" -> CaptchaConfiguration.LangType.LANG_DE // 德语
            "pt" -> CaptchaConfiguration.LangType.LANG_PT // 葡萄牙语
            "in", "id" -> CaptchaConfiguration.LangType.LANG_ID // 印尼语
            "ru" -> CaptchaConfiguration.LangType.LANG_RU // 俄语
            "kk" -> CaptchaConfiguration.LangType.LANG_KK // 哈萨克斯坦语
            "mn" -> CaptchaConfiguration.LangType.LANG_MN // 蒙古语
            "vi" -> CaptchaConfiguration.LangType.LANG_VI // 越南
            "ms" -> CaptchaConfiguration.LangType.LANG_MS // 马来
            "it" -> CaptchaConfiguration.LangType.LANG_IT // 意大利
            "hi" -> CaptchaConfiguration.LangType.LANG_HI // 印度
            "es" -> CaptchaConfiguration.LangType.LANG_ES // 西班牙
            "ko" -> CaptchaConfiguration.LangType.LANG_KO // 韩语
            "tl", "fil" -> CaptchaConfiguration.LangType.LANG_FIL // 菲律宾
            "ar" -> CaptchaConfiguration.LangType.LANG_AR // 阿拉伯
            else -> CaptchaConfiguration.LangType.LANG_EN // 英语
        }
    }

    /**
     * h5使用
     */
    @JvmStatic
    fun getHtmlLang(): String {
        val lang = SpManager.getLanguageLang()
        return when (lang) {
            "zh" -> {
                if (SpManager.getLanguageRegion() == "TW") {
                    "tw" // 繁体中文
                } else {
                    "zh" // 简体中文
                }
            }

            else -> lang
        }
    }

    /**
     * 是否是RTL布局的语言，如：阿拉伯语、波斯语等
     *
     * TODO: lvyang 目前AU只有阿拉伯语，后续增加别的RTL语言时需要同步增加这里
     */
    fun isRtlLanguage(): Boolean {
        return getAppLocale().language == "ar"
    }
}