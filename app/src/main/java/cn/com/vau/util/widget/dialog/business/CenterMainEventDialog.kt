package cn.com.vau.util.widget.dialog.business

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import cn.com.vau.databinding.DialogCenterMainEventBinding
import cn.com.vau.util.ImageLoaderUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.widget.dialog.base.CenterDialog
import cn.com.vau.util.widget.dialog.base.IBuilder
import cn.com.vau.util.widget.dialog.base.IDialog

@SuppressLint("ViewConstructor")
@Suppress("unused")
class CenterMainEventDialog private constructor(
    context: Context,
    private var imgUrl: String?,
    private var onImageListener: (() -> Unit)? = null,
    private var onCloseListener: (() -> Unit)? = null,
) : CenterDialog<DialogCenterMainEventBinding>(
    context,
    DialogCenterMainEventBinding::inflate
) {


    override fun setContentView() {
        super.setContentView()
        setImageView()
        initListener()
    }

    private fun initListener() {
        setImageListener()
        setCloseListener()
    }

    fun setImageUrl(imgUrl: String?): CenterMainEventDialog {
        this.imgUrl = imgUrl
        setImageView()
        return this
    }

    private fun setImageListener() {
        mContentBinding.ivEvent.setOnClickListener {
            onImageListener?.invoke()
            dismissDialog()
        }
    }

    private fun setCloseListener() {
        mContentBinding.closeEventView.setOnClickListener {
            onCloseListener?.invoke()
            dismissDialog()
        }
    }

    private fun setImageView() {
        try {
            ImageLoaderUtil.loadImage(context, imgUrl, mContentBinding.ivEvent)
        } catch (e: Exception) {
            LogUtil.i("MainEventDialog --- initView --- Exception: ${e.message}")
        }
    }

    fun setOnImageListener(onImageListener: (() -> Unit)?): CenterMainEventDialog {
        this.onImageListener = onImageListener
        setImageListener()
        return this
    }

    fun setOnCloseListener(onCloseListener: (() -> Unit)?): CenterMainEventDialog {
        this.onCloseListener = onCloseListener
        setCloseListener()
        return this
    }

    @Suppress("unused")
    class Builder(activity: Activity) :
        IBuilder<DialogCenterMainEventBinding, Builder>(activity) {
        //弹窗内容
        private var imageUrl: String? = null

        private var onImageListener: (() -> Unit)? = null

        private var onCloseListener: (() -> Unit)? = null


        fun setOnCloseListener(onCloseListener: (() -> Unit)?): Builder {
            this.onCloseListener = onCloseListener
            return this
        }

        fun setOnImageListener(onImageListener: (() -> Unit)?): Builder {
            this.onImageListener = onImageListener
            return this
        }

        fun setImageUrl(imageUrl: String?): Builder {
            this.imageUrl = imageUrl
            return this
        }

        override fun build(): CenterMainEventDialog {
            return super.build() as CenterMainEventDialog
        }

        override fun createDialog(context: Context): IDialog<DialogCenterMainEventBinding> {
            return CenterMainEventDialog(
                context,
                imageUrl,
                onImageListener,
                onCloseListener
            )
        }
    }

}
