package cn.com.vau.util.widget

import android.app.Activity
import android.app.Application
import android.os.Bundle
import com.adjust.sdk.Adjust

/**
 * 输入描述
 * Created by THINKPAD on 2020/7/27.
 */
// adjust 文档API level 14以上建议这样配置生命周期
// https://dev.adjust.com/zh/sdk/android/?version=v4#7-configure-session-recording
class AdjustLifecycleCallbacks : Application.ActivityLifecycleCallbacks {
    override fun onActivityPaused(activity: Activity) {
        Adjust.onPause()
    }

    override fun onActivityResumed(activity: Activity) {
        Adjust.onResume()
    }

    override fun onActivityStarted(activity: Activity) {
    }

    override fun onActivityDestroyed(activity: Activity) {
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
    }

    override fun onActivityStopped(activity: Activity) {
    }

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
    }
}