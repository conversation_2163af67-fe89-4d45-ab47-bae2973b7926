package cn.com.vau.util.widget.webview.offline.info

import androidx.annotation.Keep
import cn.com.vau.util.widget.webview.offline.matcher.FeatureMatcher
import cn.com.vau.util.widget.webview.offline.matcher.IMatcher

@Keep
data class BisInfo(
    val host: String,
    val bis: String,
    val bisPath: String,
    val versionName: String,
    val versionCode: Int,
    var matcher: IMatcher = FeatureMatcher(),
    val downUrl: String? = null,
    val routerMode: RouterMode = RouterMode.HASH,

) {
    fun isHistory(): Boolean {
        return routerMode == RouterMode.HISTORY
    }

    fun isHash(): Boolean {
        return routerMode == RouterMode.HASH
    }

    fun isMatch(url: String?): Boolean {
        return matcher.isMatch(this, url)
    }

}

