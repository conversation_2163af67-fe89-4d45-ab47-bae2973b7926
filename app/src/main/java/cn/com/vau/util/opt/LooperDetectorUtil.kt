package cn.com.vau.util.opt

import android.os.Looper
import cn.com.vau.BuildConfig

class LooperDetectorUtil {


    companion object {

        private val LOOPER_START = ">>>>> Dispatching to"
        private val LOOPER_END = "<<<<< Finished to"
        private var startTime = 0L
        private const val THRESHOLD = 200L //阈值 超过该阈值才会报出
        //采集频率
        private const val FREQUENCY_TIME = THRESHOLD / 3


        fun init() {
            if (!BuildConfig.DEBUG) {
                return
            }
            Thread(kotlinx.coroutines.Runnable {
                val isRun = true
                while (isRun) {
                    Thread.sleep(FREQUENCY_TIME)
                    val info = recordMainThreadStack()
                    MyHashMap.put(System.currentTimeMillis(), info)
                }
            }).start()

            Looper.getMainLooper().setMessageLogging {
                if (it.contains(LOOPER_START)) {
                    MyHashMap.clear()
                    startTime = System.currentTimeMillis()
                } else if (it.contains(LOOPER_END)) {
                    val totalTime = System.currentTimeMillis() - startTime
                    if (totalTime > THRESHOLD) {
                        xhLogi("******${System.currentTimeMillis()} 耗时了 $totalTime ms   任务是 $it")
                        MyHashMap.printLog()
                    }
                }
            }
        }


        private fun recordMainThreadStack(): String {
            val mainThread = Looper.getMainLooper().thread
            val stackTrace = mainThread.stackTrace
            val stringBuilder = StringBuilder()
            for (element in stackTrace) {
                stringBuilder.append(element.toString()).append("\n")
            } //Log.d("xinhuan CallStack", stringBuilder.toString())
            return stringBuilder.toString()
        }


        private class MyHashMap {
            companion object {
                private val mainThreadMap = LinkedHashMap<Long, String>()

                @Synchronized
                fun put(key: Long, value: String) {
                    if (mainThreadMap.size > 15) {
                        mainThreadMap.clear()
                    }
                    mainThreadMap.put(System.currentTimeMillis(), value)
                }

                fun clear() {
                    mainThreadMap.clear()
                }

                @Synchronized
                fun printLog() {
                    var count = 0
                    val list = mutableListOf<String>()
                    mainThreadMap.iterator().forEach { //                    Log.e("xinhuan", "key - >${it.key}")
                        //                    Log.e("xinhuan", "value - >${it.value}")
                        list.add("${it.key} ->  ${it.value}")
                    }

                    for (i in list.size - 1 downTo 0) {
                        if (i >= list.size - 1 - 4) {
                            xhLogi("第${++count}  *** ${list[i]}")
                        }
                    }
                }
            }
        }

    }
}