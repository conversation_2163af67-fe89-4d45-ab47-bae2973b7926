package cn.com.vau.util.widget

import android.annotation.SuppressLint
import android.content.Intent
import android.net.Uri
import android.net.http.SslError
import android.os.Message
import android.text.TextUtils
import android.view.KeyEvent
import android.view.View
import android.view.ViewGroup
import android.webkit.SslErrorHandler
import android.webkit.ValueCallback
import android.webkit.WebChromeClient
import android.webkit.WebResourceError
import android.webkit.WebResourceRequest
import android.webkit.WebResourceResponse
import android.webkit.WebSettings
import android.webkit.WebView
import android.webkit.WebViewClient
import android.widget.FrameLayout
import android.widget.ProgressBar
import androidx.activity.result.ActivityResultLauncher
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import androidx.core.view.isGone
import androidx.core.view.isVisible
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.utils.OpenUploadToH5Util
import cn.com.vau.common.view.dialog.CommonProcessDialog
import cn.com.vau.common.view.pdfview.DefaultLinkHandler
import cn.com.vau.common.view.pdfview.PDFView
import cn.com.vau.page.html.HtmlSensorsHelper
import cn.com.vau.page.html.JavaScriptInterface
import cn.com.vau.page.html.NewHtmlViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.opt.PerfTraceUtil
import cn.com.vau.util.widget.webview.offline.OfflineWebManager
import cn.com.vau.util.widget.webview.preload.PreloadWebView
import cn.com.vau.util.widget.webview.utils.WebViewLogUtil
import com.github.barteksc.pdfviewer.util.FitPolicy
import com.google.gson.JsonObject
import java.io.IOException
import java.io.InputStream
import java.net.URL

/**
 * Filename: WebViewInitializer
 * Author: GG
 * Date: 2025/3/7
 * Description: 接口用于初始化和配置 WebView，并封装常用功能。
 */
interface WebViewInitializer {

    val mViewModel: NewHtmlViewModel

    /**
     * 用于加载 URL 的 WebView，需要在实现类中初始化。
     */
    val mWebView: PreloadWebView?

    val mWebViewContainer: FrameLayout

    val pdfView: PDFView

    val progressBar: ProgressBar

    val loadingDialog: CommonProcessDialog

    val imageChooserLauncher: ActivityResultLauncher<Intent>?

    var uploadMessageAboveL: ValueCallback<Array<Uri>>?

    val openUploadToH5Util: OpenUploadToH5Util

    val sensorsHelper: HtmlSensorsHelper

    var videoView: View?

    /**
     * 配置 WebView 的设置和行为。
     *
     * @param activity 当前的 AppCompatActivity。
     */
    @SuppressLint("SetJavaScriptEnabled")
    fun setWebView(activity: AppCompatActivity) {
        if (mWebView == null) {
            return
        }
        val params = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.MATCH_PARENT
        )
        mWebViewContainer.addView(mWebView, params)
        // 设置 WebView 背景色，解决夜间模式闪白屏现象
        mWebView?.setBackgroundColor(AttrResourceUtil.getColor(activity, R.attr.mainLayoutBg))

        val webSettings = mWebView?.settings ?: return
        // 自适应屏幕
        webSettings.layoutAlgorithm = WebSettings.LayoutAlgorithm.SINGLE_COLUMN
        webSettings.loadWithOverviewMode = true
        // 自适应可不设置，但是华为P10有问题
        webSettings.textZoom = 100
        // 允许与 JS 进行交互
        webSettings.javaScriptEnabled = true
        // 允许锚点
        webSettings.domStorageEnabled = true
        webSettings.databaseEnabled = true
        // 允许多窗口，telegram 授权时会调用 window.open()，加上这个原生才会触发 onCreateWindow() 方法
        webSettings.setSupportMultipleWindows(true)
        webSettings.userAgentString = WebSettings.getDefaultUserAgent(activity) + "/${Constants.PRODUCT_NAME}_android"
        webSettings.mixedContentMode = WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
        mWebView?.let { openUploadToH5Util.setWebView(it) }
        mWebView?.addJavascriptInterface(JavaScriptInterface(activity, mViewModel), "vfx_android")

        mWebView?.webViewClient = object : WebViewClient() {

            override fun shouldInterceptRequest(
                view: WebView?,
                url: String?
            ): WebResourceResponse? {
                WebViewLogUtil.e("shouldInterceptRequest url:$url")
                return OfflineWebManager.getOfflineResourceResponse(activity, url);
            }

            // 通过重写 WebViewClient 的 onReceivedSslError 方法来接受所有网站的证书，忽略 SSL 错误。
            override fun onReceivedSslError(
                view: WebView,
                handler: SslErrorHandler,
                error: SslError,
            ) {
                handler.cancel()
                super.onReceivedSslError(view, handler, error)
                // H5 流程神策埋点 -> 加载失败
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_FAIL, url = view.url)
            }

            override fun onReceivedError(view: WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                // H5 流程神策埋点 -> 加载失败
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_FAIL, url = view?.url)
            }

            override fun onReceivedHttpError(view: WebView?, request: WebResourceRequest?, errorResponse: WebResourceResponse?) {
                super.onReceivedHttpError(view, request, errorResponse)
                // H5 流程神策埋点 -> 加载失败
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_FAIL, url = view?.url)
            }

            override fun shouldOverrideUrlLoading(view: WebView, url: String): Boolean {
                try {
                    LogUtil.w("shouldOverrideUrlLoading: url:$url")
                    if (!TextUtils.isEmpty(url) && mViewModel.isPdfUrl(url)) {
                        loadPdfFile(url)
                    } else if (
                        url.contains("item.m.jd.com") ||
                        url.contains("play.google.com") ||
                        url.startsWith("scheme://") ||
                        url.startsWith("market://")
                    ) { // 跳三方 app
                        activity.startActivity(Intent(Intent.ACTION_VIEW, url.toUri()))
                    } else {
                        if (mWebViewContainer.isGone) {
                            mWebViewContainer.isVisible = true
                            pdfView.isGone = true
                        }
                        processOfflineUrl(view, url)
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                return true
            }

            // URL 加载完成
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_H5_LoadUrl_Finish)
                PerfTraceUtil.stopTrace(PerfTraceUtil.StartTrace.Perf_v6_H5_All_Finish)

                hideLoading()
                // H5 流程神策埋点 -> 加载成功
                sensorsHelper.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.LOAD_SUCCESS, url = url)
            }

            override fun doUpdateVisitedHistory(view: WebView?, url: String?, isReload: Boolean) {
                super.doUpdateVisitedHistory(view, url, isReload)
            }
        }

        mWebView?.webChromeClient = object : WebChromeClient() {
            override fun onShowCustomView(view: View?, callback: CustomViewCallback?) {
                super.onShowCustomView(view, callback)
                val params = FrameLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                videoView = view
                mWebViewContainer.addView(view, params)
            }

            override fun onHideCustomView() {
                super.onHideCustomView()
                videoView?.let {
                    mWebViewContainer.removeView(it)
                }
            }

            // For Android >= 5.0
            override fun onShowFileChooser(
                webView: WebView,
                filePathCallback: ValueCallback<Array<Uri>>,
                fileChooserParams: FileChooserParams,
            ): Boolean {
                uploadMessageAboveL = filePathCallback
                openImageChooserActivity()
                return true
            }

            override fun onProgressChanged(view: WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                if (mViewModel.isNeedProgress()) {
                    progressBar.progress = newProgress
                    progressBar.isVisible = newProgress != 100
                }
            }

            // 设置 webSettings.setSupportMultipleWindows(true) 才会回调这里
            @SuppressLint("SetJavaScriptEnabled")
            override fun onCreateWindow(view: WebView?, isDialog: Boolean, isUserGesture: Boolean, resultMsg: Message?): Boolean {
                val newWebView = WebView(activity).apply {
                    settings.javaScriptEnabled = true
                    layoutParams = ConstraintLayout.LayoutParams(
                        ConstraintLayout.LayoutParams.MATCH_PARENT,
                        ConstraintLayout.LayoutParams.MATCH_PARENT
                    )
                    webViewClient = object : WebViewClient() {
                        override fun onPageFinished(view: WebView?, url: String?) {
                            super.onPageFinished(view, url)
                            if (mViewModel.isNeedProgress()) {
                                progressBar.isVisible = false
                                hideLoading()
                            }
                        }
                    }
                }
                (view?.parent as? ViewGroup)?.addView(newWebView)

                val transport = resultMsg?.obj as? WebView.WebViewTransport
                transport?.webView = newWebView
                resultMsg?.sendToTarget()
                return true
            }

            override fun onCloseWindow(window: WebView?) {
                super.onCloseWindow(window)
                LogUtil.e("onCloseWindow")
            }
        }
        mWebView?.setOnKeyListener { _, keyCode, event ->
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_DOWN) {
                if (mWebView?.canGoBack() == true) {
                    back()
                    return@setOnKeyListener true
                } else {
                    // 处理其他返回键事件
                    return@setOnKeyListener false
                }
            }
            return@setOnKeyListener false
        }
    }

    fun processOfflineUrl(webView: WebView?, url: String?) {
        if (url.isNullOrEmpty()) {
            return
        }
        if (!url.startsWith("http")) {
            webView?.loadDataWithBaseURL(null, url, "text/html", "utf-8", null)
            return
        }

        val newUrl = OfflineWebManager.getMatchedUrl(url)
        if (newUrl.isNullOrEmpty()) {
            webView?.loadUrl(url)
        } else {
            webView?.loadUrl(newUrl)
        }
    }

    fun loadPdfFile(input: InputStream) {  // pdfFile: File?
        pdfView.fromStream(input)
            .enableSwipe(true) // allows to block changing pages using swipe
            .swipeHorizontal(false)
            .enableDoubletap(true)
            .defaultPage(0)
            // allows to draw something on the current page, usually visible in the middle of the screen
            //                .onDraw(this)
            // allows to draw something on all pages, separately for every page. Called only for visible pages
            //                .onDrawAll(this)
            .onLoad {
                hideLoading()
                progressBar.isVisible = false
            } // called after document is loaded and starts to be rendered
            //                .onPageChange(this)
            //                .onPageScroll(this)
            .onError {
                hideLoading()
                progressBar.isVisible = false
            }
            .onPageError { _, _ ->
                hideLoading()
                progressBar.isVisible = false
            }
            .onRender {
                hideLoading()
                progressBar.isVisible = false
            } // called after document is rendered for the first time
            .onDrawAll { _, _, _, _ ->
                hideLoading()
                progressBar.isVisible = false
            }
            // called on single tap, return true if handled, false to toggle scroll handle visibility
            //                .onTap(this)
            //                .onLongPress(this)
            .enableAnnotationRendering(false) // render annotations (such as comments, colors or forms)
            .password(null)
            .scrollHandle(null)
            .enableAntialiasing(true) // improve rendering a little bit on low-res screens
            // spacing between pages in dp. To define spacing color, set view background
            .spacing(0)
            .autoSpacing(false) // add dynamic spacing to fit each page on its own on the screen
            .linkHandler(DefaultLinkHandler(pdfView))
            .pageFitPolicy(FitPolicy.WIDTH)
            .pageSnap(true) // snap pages to screen boundaries
            .pageFling(false) // make a fling change only a single page like ViewPager
            .nightMode(false) // toggle night mode
            .load()
    }

    fun loadPdfFile(htmlUrl: String) {
        mWebViewContainer.isGone = true
        pdfView.isVisible = true
        val thread = Thread {
            try {
                val input: InputStream = URL(htmlUrl).openStream()
                loadPdfFile(input)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        thread.start()
    }

    fun onActivityResultAboveL(intent: Intent?) {
        val results: MutableList<Uri> = mutableListOf<Uri>()
        var resultList: ArrayList<Uri>? = null
        if (intent != null) {
            val dataString = intent.dataString
            try {
                val clipData = intent.clipData
                if (clipData != null) {
                    resultList = ArrayList()
                    for (i in 0 until clipData.itemCount) {
                        val item = clipData.getItemAt(i)
                        resultList.add(item.uri)
                    }
                    resultList.forEach {
                        results.add(it)
                    }
                }
                if (dataString != null) results.add(dataString.toUri())
            } catch (e: Exception) {
                e.printStackTrace()
                if (dataString != null) results.add(dataString.toUri())
            }
        }
        uploadMessageAboveL?.onReceiveValue(results.toTypedArray())
        uploadMessageAboveL = null
    }

    /**
     * 返回的方法，用于处理 WebView 的返回操作。
     */
    fun back()

    fun hideLoading() {
        if (loadingDialog.isShowing)
            loadingDialog.dismiss()
    }

    fun showLoading() {
        if (!loadingDialog.isShowing) {
            loadingDialog.show()
        }
    }

    /**
     * 打开系统选择相册
     */
    fun openImageChooserActivity() {
        val i = Intent(Intent.ACTION_OPEN_DOCUMENT)
        i.addCategory(Intent.CATEGORY_OPENABLE)
        i.type = "*/*" //文件上传
        imageChooserLauncher?.launch(Intent.createChooser(i, "Image Chooser"))
    }

    /**
     * 调用 H5 的 JS 方法。
     *
     * @param methodName 要调用的 JS 方法名。
     * @param map 传递给 JS 方法的参数，可选。
     */
    fun loadVFXMethod(methodName: String, map: Map<String, Any>? = null) {

        val method = if (map != null) {
            val json = JsonObject()
            map.forEach {
                when (it.value) {
                    is Boolean -> json.addProperty(it.key, it.value as Boolean?)
                    is Number -> json.addProperty(it.key, it.value as Number?)
                    is CharSequence -> json.addProperty(it.key, it.value as String?)
                    else -> {
                        json.addProperty(it.key, it.value.toString())
                    }
                }
            }
            "window.vfx_android.${methodName}(${json})"
        } else {
            "window.vfx_android.${methodName}()"
        }
        LogUtil.w(method)
        mWebView?.evaluateJavascript(method) {}
    }

    /**
     * 调用 H5 的 JS 方法。
     *
     * @param methodName 要调用的 JS 方法名。
     * @param map 传递给 JS 方法的参数，可选。
     */
    fun loadJSMethod(methodName: String, map: Map<String, Any>? = null) {

        val method = if (map != null) {
            val json = JsonObject()
            map.forEach {
                when (it.value) {
                    is Boolean -> json.addProperty(it.key, it.value as Boolean?)
                    is Number -> json.addProperty(it.key, it.value as Number?)
                    is CharSequence -> json.addProperty(it.key, it.value as String?)
                    else -> {
                        json.addProperty(it.key, it.value.toString())
                    }
                }
            }
            "javascript:${methodName}(${json})"
        } else {
            "javascript:${methodName}()"
        }
        LogUtil.w(method)
        mWebView?.loadUrl(method)
    }

    fun showUploadBottomDialog() {
        openUploadToH5Util.showUploadBottomDialog()
    }
}