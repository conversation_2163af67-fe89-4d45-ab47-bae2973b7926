package cn.com.vau.util

import android.content.Context
import cn.com.vau.common.http.HttpUrl
import cn.com.vau.util.language.LanguageHelper
import com.netease.nis.captcha.*
import com.netease.nis.captcha.Captcha.CloseType

/**
 * Filename: CaptchaUtils.kt
 * Author: GG
 * Date: 2023/12/6
 * Description:
 */
object CaptchaUtil {

    /**
     * 通用易盾设置
     */
    private fun config(context: Context, loginCaptchaListener: CaptchaListener) =
        CaptchaConfiguration.Builder()
            .captchaId(HttpUrl.netEaseID)
            .languageType(LanguageHelper.getCaptchaLangType())
            .hideCloseButton(true)
            .apply {
                theme(if (AppUtil.isLightTheme()) CaptchaConfiguration.Theme.LIGHT else CaptchaConfiguration.Theme.DARK)
            }
            .backgroundDimAmount(0.3f)
            .listener(loginCaptchaListener)
            .build(context)

    /**
     * 获取易盾请求对象
     */
    fun getCaptcha(context: Context, loginCaptchaListener: CaptchaListener) = Captcha.getInstance().init(config(context, loginCaptchaListener))

    /**
     * ================================================ 更方便的使用方式 ================================================
     */
    private fun config(context: Context, callBack: (validate: String) -> Unit) =
        CaptchaConfiguration.Builder()
            .captchaId(HttpUrl.netEaseID)
            .languageType(LanguageHelper.getCaptchaLangType())
            .hideCloseButton(true)
            .theme(if (AppUtil.isLightTheme()) CaptchaConfiguration.Theme.LIGHT else CaptchaConfiguration.Theme.DARK)
            .backgroundDimAmount(0.3f)
            .listener(object : CaptchaListener {
                override fun onReady() {}

                override fun onValidate(result: String?, validate: String?, msg: String?) {
                    if (!validate.isNullOrBlank()) {
                        callBack.invoke(validate)
                    }
                }

                override fun onError(code: Int, msg: String) {}
                override fun onClose(closeType: CloseType) {}
            }).build(context)

    fun getCaptcha(context: Context, callBack: (validate: String) -> Unit) = Captcha.getInstance().init(config(context, callBack))
}