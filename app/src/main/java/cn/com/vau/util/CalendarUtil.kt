package cn.com.vau.util

import android.annotation.SuppressLint
import cn.com.vau.common.view.timeSelection.PickerDateUtil
import cn.com.vau.ui.common.LikeDate
import java.text.*
import java.util.*

/**
 * 日历相关
 */
class CalendarUtil {

    companion object {
        @JvmStatic
        fun getInstance(): CalendarUtil {
            return Holder.instance
        }
    }

    private object Holder {
        val instance = CalendarUtil()
    }

    @SuppressLint("SimpleDateFormat")
    fun getMonthDiffBefore(nowTime: String?, diff: Int): LikeDate {
        val dateFormat = SimpleDateFormat("dd/MM/yyyy", Locale.ENGLISH)
        var date: Date = dateFormat.parse(nowTime)
        val ca: Calendar = Calendar.getInstance()
        ca.time = date
        ca.add(Calendar.MONTH, diff)
        date = ca.time
        return LikeDate(ca.get(Calendar.YEAR), ca.get(Calendar.MONTH) + 1, ca.get(Calendar.DAY_OF_MONTH), dateFormat.format(date))
    }

    fun getCurrentMonthFirstDay(): LikeDate {
        val ca = Calendar.getInstance()
        ca.set(Calendar.DATE, 1)
        return LikeDate(ca.get(Calendar.YEAR), ca.get(Calendar.MONTH) + 1, ca.get(Calendar.DAY_OF_MONTH), "${ca.get(Calendar.DAY_OF_MONTH)}/${ca.get(Calendar.MONTH) + 1}/${ca.get(Calendar.YEAR)}")
    }

    fun getMonthLastDay(year: Int, month: Int): Int {
        val ca = Calendar.getInstance()
        ca.set(Calendar.YEAR, year)
        ca.set(Calendar.MONTH, month - 1)
        ca.set(Calendar.DATE, 1)
        ca.roll(Calendar.DATE, -1)
        return ca.get(Calendar.DAY_OF_MONTH)
    }

    @SuppressLint("SimpleDateFormat")
    fun getDayOfWeek(dateTime: String, pattern: String): Int {
        val ca = Calendar.getInstance()
        val sdf = SimpleDateFormat(pattern, Locale.ENGLISH)
        var date: Date? = null
        try {
            date = sdf.parse(dateTime)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        if (date != null) {
            ca.time = Date(date.time)
        }
        val weekNum = ca.get(Calendar.DAY_OF_WEEK) - 1
        return if (weekNum == 0) 7 else weekNum
    }

    fun getCurrentMonthFirstDayOfWeek(): Int {
        val ca = Calendar.getInstance()
        ca.set(Calendar.DATE, 1)
        val weekNum = ca.get(Calendar.DAY_OF_WEEK) - 1
        return if (weekNum == 0) 7 else weekNum
    }

    fun getDateFormatInternation(date: Date?): String {
        return getDateFormatInternation(date, false)
    }

    fun getDateFormatInternation(date: Date?, noDay: Boolean): String {
        var dateStr = ""
        try {
            val dateMon = String.format("%tb", date) // 月
            val dateDay = String.format("%td", date) // 日
            val dateYear = String.format("%tY", date) // 年

            dateStr = if (noDay) "$dateMon $dateYear" else "$dateMon $dateDay, $dateYear"
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return dateStr
    }

    fun getFormatYearMon(currDateBean: LikeDate): String {
        return getDateFormatInternation(Date(PickerDateUtil.dateStrToLong("${currDateBean.year}-${currDateBean.mon}-${currDateBean.day} 01:02:03").toLong()), true)
    }

    fun getFormatDealLogTitle(dateStr: String): String {
        val dateArray = dateStr.split("/")
        return getDateFormatInternation(Date(PickerDateUtil.dateStrToLong("${dateArray.elementAtOrNull(2)}-${dateArray.elementAtOrNull(1)}-${dateArray.elementAtOrNull(0)} 01:02:03").toLong()))
    }

    // 判断给定的时间戳是不是周末
    fun isWeekend(timeStamp: Long, timeZone: TimeZone = TimeZone.getDefault()): Boolean {
        val calendar = Calendar.getInstance(timeZone)
        calendar.timeInMillis = timeStamp
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        return dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY
    }

}