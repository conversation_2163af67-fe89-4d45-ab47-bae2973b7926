package cn.com.vau.util

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import androidx.credentials.*
import androidx.credentials.exceptions.CreateCredentialException
import com.google.android.gms.common.*
import kotlinx.coroutines.*
import okhttp3.*
import java.util.concurrent.TimeUnit

/**
 * Passkey 验证辅助类
 */
class PassKeyCredentialHelper(private val mContext: Context) {

    private val credentialManager by lazy { CredentialManager.create(mContext) }

    private suspend fun <T> safeCall(block: suspend () -> T): Result<T> {
        return runCatching {
            block()
        }.onFailure {
            LogUtil.e(TAG, it.message)
        }
    }

    /**
     * 检测设备是否支持passkey
     */
    suspend fun isSupportPassKey(): Boolean = safeCall {
        Build.VERSION.SDK_INT >= Build.VERSION_CODES.P && // 检查Android版本
                isGooglePlayServicesAvailable(mContext) && // 检查Google Play服务是否可用
                isGmsVersionSupported(mContext) && // 检查Google Play服务版本
                isPasskeyFunctionallyAvailable() // 实际passkey功能检测
    }.getOrElse { false }

    /**
     * 检测是否支持Google服务
     */
    private fun isGooglePlayServicesAvailable(context: Context): Boolean {
        val googleApiAvailability = GoogleApiAvailability.getInstance()
        return googleApiAvailability.isGooglePlayServicesAvailable(context) == ConnectionResult.SUCCESS
    }

    /**
     * 检测谷歌服务版本
     */
    private fun isGmsVersionSupported(context: Context): Boolean = runCatching {
        val pInfo = context.packageManager.getPackageInfo("com.google.android.gms", 0)
        val versionCode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) pInfo.longVersionCode else pInfo.versionCode.toLong()
        versionCode >= 223000000
    }.getOrDefault(false)

    /**
     * 实际passkey功能检测
     */
    private suspend fun isPasskeyFunctionallyAvailable(): Boolean = withContext(Dispatchers.IO) {
        runCatching {
            GetCredentialRequest.Builder()
                .addCredentialOption(GetPublicKeyCredentialOption("{}"))
                .build()
        }.isSuccess
    }

    /**
     * 检测能否访问Google服务
     */
    suspend fun canAccessGoogle(): Boolean {
        val client = OkHttpClient.Builder()
            .connectTimeout(2000, TimeUnit.MILLISECONDS)  // 设置连接超时
            .build()
        val request = Request.Builder()
            .url("https://www.google.com")
            .build()
        return withContext(Dispatchers.IO) {
            try {
                val response: Response = client.newCall(request).execute()
                response.isSuccessful
            } catch (e: Exception) {
                e.printStackTrace()
                false
            }
        }
    }

    /**
     * 创建passkey
     * jsonData : 从服务端获取的
     * 创建成功则返回CreatePublicKeyCredentialResponse，失败返回null,并回调失败信息
     */
    @SuppressLint("PublicKeyCredential")
    suspend fun createPasskey(jsonData: String, failureCallback: ((exception: Exception) -> Unit)): CreatePublicKeyCredentialResponse? {
        var response: CreatePublicKeyCredentialResponse? = null
        try {
            val request = CreatePublicKeyCredentialRequest(jsonData)
            response = credentialManager.createCredential(mContext, request) as? CreatePublicKeyCredentialResponse
        } catch (e: CreateCredentialException) {
            e.printStackTrace()
            failureCallback.invoke(e)
        } catch (ex: Exception) {
            ex.printStackTrace()
            failureCallback.invoke(ex)
        }
        return response
    }

    /**
     * 获取保存的Passkey
     * jsonData:服务端返回
     * return: 返回验证信息
     */
    suspend fun getSavedCredentials(jsonData: String): String? {
        try {
            //创建 GetPublicKeyCredentialOption()
            val getPublicKeyCredentialOption = GetPublicKeyCredentialOption(jsonData)
            //调用getCredential()
            val result = credentialManager.getCredential(
                mContext,
                GetCredentialRequest(listOf(getPublicKeyCredentialOption))
            )

            if (result.credential is PublicKeyCredential) {
                val cel = result.credential as PublicKeyCredential
                return cel.authenticationResponseJson
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }

        return null
    }

    companion object {
        private const val TAG = "PassKeyCredentialHelper"
    }
}
