package cn.com.vau.util.widget

import cn.com.vau.common.application.VauApplication
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.BaseBean
import com.google.android.gms.tasks.OnCompleteListener
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.FirebaseAnalytics.ConsentStatus
import com.google.firebase.analytics.FirebaseAnalytics.ConsentType
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.google.firebase.messaging.FirebaseMessaging
import com.google.firebase.messaging.ktx.messaging
import com.google.gson.Gson
import io.reactivex.disposables.Disposable
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody

object FirebaseManager {

    val mFirebaseAnalytics by lazy { FirebaseAnalytics.getInstance(VauApplication.context) }

    /**
     * 获取 FirebaseAnalytics 的 AppInstanceId
     * 获取频率为启动页一次，主页一次
     */
    fun updateAppInstanceId() {
        if (Constants.appInstanceId.isEmpty()) {
            try {
                val id = FirebaseAnalytics.getInstance(VauApplication.context).appInstanceId
                id.addOnCompleteListener {
                    try {
                        it.result?.let { result ->
                            Constants.appInstanceId = result
                            SpManager.putFirebaseAppInstanceIdCache(result)
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    fun init() {
        val localFcmToken = SpManager.getTokenFcm()
        FirebaseMessaging.getInstance().token.addOnCompleteListener(OnCompleteListener { task ->

            if (task.isSuccessful) {
                val fcmToken = task.result
                if (localFcmToken != fcmToken) {
                    SpManager.putTokenFcm(fcmToken)
                    deleteFcmToken(localFcmToken)
                    subscribeTopic()
                    bindFcmToken(fcmToken)
                }
            }

        })
    }

    fun bindFcmToken(fcmToken: String = SpManager.getTokenFcm()) {
//        LogUtils.w(fcmToken)
        if (fcmToken.isEmpty()) return
        val userid = UserDataUtil.userId()
        val param = hashMapOf<String, Any>(
            "userId" to userid,
            "deviceToken" to fcmToken
        )
        val requestBody = Gson().toJson(param).toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.getData(
            RetrofitHelper.getHttpService().pushDeviceTokenBind(requestBody), object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {
                }

                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
    }

    fun subscribeTopic() {
        Firebase.messaging.subscribeToTopic("visitor")
        if (UserDataUtil.isLogin()) {
            if (UserDataUtil.isIB()) {
                Firebase.messaging.unsubscribeFromTopic("commonuser")
                Firebase.messaging.subscribeToTopic("ibuser")
            } else {
                Firebase.messaging.unsubscribeFromTopic("ibuser")
                Firebase.messaging.subscribeToTopic("commonuser")
            }
        } else {
            Firebase.messaging.unsubscribeFromTopic("ibuser")
            Firebase.messaging.unsubscribeFromTopic("commonuser")
        }
    }

    fun unbindFcmToken() {
        if (!UserDataUtil.isLogin()) return
        val userid = UserDataUtil.userId()
        val localFcmToken = SpManager.getTokenFcm()
        val param = hashMapOf<String, Any>(
            "userId" to userid,
            "deviceToken" to localFcmToken
        )
        val requestBody = Gson().toJson(param).toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.getData(
            RetrofitHelper.getHttpService().pushDeviceTokenUnbind(requestBody), object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {

                }

                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
    }

    fun deleteFcmToken(fcmToken: String) {
        if (fcmToken.isEmpty()) return
        val userid = UserDataUtil.userId()
        val param = hashMapOf<String, Any>(
            "userId" to userid,
            "deviceToken" to fcmToken
        )
        val requestBody = Gson().toJson(param).toRequestBody("application/json".toMediaTypeOrNull())
        HttpUtils.getData(
            RetrofitHelper.getHttpService().pushDeviceTokenDelete(requestBody), object : BaseObserver<BaseBean>() {
                override fun onNext(t: BaseBean?) {

                }

                override fun onHandleSubscribe(d: Disposable?) {
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                }
            })
    }

    fun allowGoogleAnalytics(allow: Boolean) {
        mFirebaseAnalytics.setConsent(
            mapOf(
                ConsentType.ANALYTICS_STORAGE to if (allow) ConsentStatus.GRANTED else ConsentStatus.DENIED,
                ConsentType.AD_STORAGE to if (allow) ConsentStatus.GRANTED else ConsentStatus.DENIED,
                ConsentType.AD_USER_DATA to if (allow) ConsentStatus.GRANTED else ConsentStatus.DENIED,
                ConsentType.AD_PERSONALIZATION to if (allow) ConsentStatus.GRANTED else ConsentStatus.DENIED
            )
        )
    }

    /**
     * 用户登录绑定Firebase
     */
    fun userLogin() {
        Firebase.crashlytics.setUserId(UserDataUtil.userId())
        Firebase.crashlytics.setCustomKey("userId", UserDataUtil.userId())
        // 这里理论上可以继续添加想要关注的用户信息，比如用户名，手机号，邮箱等
    }

    /**
     * 同步解绑Firebase用户信息
     */
    fun userLogout() {
        Firebase.crashlytics.setUserId("")
        Firebase.crashlytics.setCustomKey("userId", "")
    }

    /**
     * 向Firebase平台上报异常信息
     */
    fun recordException(exception: Exception) {
        FirebaseCrashlytics.getInstance().recordException(exception)
    }
}