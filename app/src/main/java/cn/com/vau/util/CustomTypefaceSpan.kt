package cn.com.vau.util

import android.graphics.Paint
import android.graphics.Typeface
import android.text.TextPaint
import android.text.style.MetricAffectingSpan

/**
 * @description:
 * @author: GG
 * @createDate: 2025 5月 12 10:21
 * @updateUser:
 * @updateDate: 2025 5月 12 10:21
 */
class CustomTypefaceSpan(private val typeface: Typeface) : MetricAffectingSpan() {
    override fun updateDrawState(tp: TextPaint) {
        applyCustomTypeface(tp)
    }

    override fun updateMeasureState(paint: TextPaint) {
        applyCustomTypeface(paint)
    }

    private fun applyCustomTypeface(paint: Paint) {
        paint.typeface = typeface
    }
}