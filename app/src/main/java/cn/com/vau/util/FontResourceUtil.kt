package cn.com.vau.util

import android.content.Context
import android.graphics.Typeface
import androidx.core.content.res.ResourcesCompat
import cn.com.vau.R

/**
 * 设置多字体【禁止直接访问】
 */
object FontResourceUtil {

    private var gilroyBold : Typeface? = null
    private var gilroySemiBold : Typeface? = null
    private var gilroyMedium : Typeface? = null
    private var gilroyRegular : Typeface? = null

    fun typefaceGilroyBold(context:Context): Typeface? {
        if (gilroyBold == null) {
            gilroyBold = ResourcesCompat.getFont(context, R.font.gilroy_bold)
        }
        return gilroyBold
    }

    fun typefaceGilroySemiBold(context:Context): Typeface? {
        if (gilroySemiBold == null) {
            gilroySemiBold = ResourcesCompat.getFont(context, R.font.gilroy_semi_bold)
        }
        return gilroySemiBold
    }

    fun typefaceGilroyMedium(context:Context): Typeface? {
        if (gilroyMedium == null) {
            gilroyMedium = ResourcesCompat.getFont(context, R.font.gilroy_medium)
        }
        return gilroyMedium
    }

    fun typefaceGilroyRegular(context:Context): Typeface? {
        if (gilroyRegular == null) {
            gilroyRegular = ResourcesCompat.getFont(context, R.font.gilroy_regular)
        }
        return gilroyRegular
    }

}