package cn.com.vau.history.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.databinding.ItemFundingHistoryBinding
import cn.com.vau.history.data.FundingUIData
import cn.com.vau.history.viewmodel.FundingHistoryViewModel

class HistoryFundingAdapter(historyViewModel: FundingHistoryViewModel) : ListAdapter<FundingUIData, HistoryFundingAdapter.MyViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        return MyViewHolder(ItemFundingHistoryBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: <PERSON>ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    class MyViewHolder(val binding: ItemFundingHistoryBinding) : RecyclerView.ViewHolder(binding.root) {
        fun bind(item: FundingUIData) {
            binding.tvTitle.text = item.currencyName
            binding.tvTime.text = item.timeStr
            binding.tvType.text = item.typeTitle
            binding.tvTypeValue.text = item.typeName
            binding.tvAmount.text = item.amountTitle
            binding.tvAmountValue.text = item.amount
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<FundingUIData>() {
        override fun areItemsTheSame(oldItem: FundingUIData, newItem: FundingUIData): Boolean {
            return oldItem == newItem
        }

        override fun areContentsTheSame(oldItem: FundingUIData, newItem: FundingUIData): Boolean {
            return oldItem == newItem
        }
    }
}