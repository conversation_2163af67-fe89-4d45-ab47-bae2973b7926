package cn.com.vau.history.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.PathEffect
import android.graphics.RectF
import android.text.Layout
import android.text.TextUtils
import android.util.AttributeSet
import androidx.appcompat.widget.AppCompatTextView


/**
 * Create data：2025/2/28 14:05
 * @author: Brin
 * Describe:
 */
class DashedUnderlinedTextView(context: Context, attrs: AttributeSet) : AppCompatTextView(context, attrs) {
    // 初始化画笔对象及其属性
    private val mDashedLinePaint = Paint().apply {
        strokeWidth = resources.displayMetrics.density * 1 // 设置线宽
    }

    init {
        mDashedLinePaint.color = currentTextColor
        mDashedLinePaint.style = Paint.Style.FILL_AND_STROKE
        val scaledDensity = resources.displayMetrics.density
        val intervals = floatArrayOf(scaledDensity * 2, scaledDensity * 2) // 设置虚线间隔模式 [dash length, gap length]
        val effect: PathEffect = DashPathEffect(intervals, 0f)
        mDashedLinePaint.setPathEffect(effect)
    }

    @SuppressLint("DrawAllocation")
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        setLayerType(LAYER_TYPE_SOFTWARE, null)
        val layout: Layout? = layout

        if (layout != null && !TextUtils.isEmpty(text)) {
            val lineCount: Int = layout.getLineCount()

            for (i in 0..<lineCount) {
                val rect = RectF(
                    layout.getLineLeft(i),
                    layout.getLineTop(i) + textSize,
                    layout.getLineRight(i),
                    layout.getLineBaseline(i).toFloat()
                )

                canvas.drawLine(
                    rect.left,
                    rect.bottom + resources.displayMetrics.density * 2,  /* 下划线距离文本的距离 */
                    rect.right,
                    rect.bottom + resources.displayMetrics.density * 2,
                    mDashedLinePaint
                )
            }
        }
    }
}