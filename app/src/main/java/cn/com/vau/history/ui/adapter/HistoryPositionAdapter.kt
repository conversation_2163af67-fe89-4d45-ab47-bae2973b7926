package cn.com.vau.history.ui.adapter

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.*
import cn.com.vau.R
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.databinding.ItemPositionHistoryBinding
import cn.com.vau.history.data.HistoryItemUiData
import cn.com.vau.history.data.uiStatus.PositionDialogStatus
import cn.com.vau.history.ui.HistoryPositionDetailActivity
import cn.com.vau.history.viewmodel.HistoryPositionViewModel
import cn.com.vau.util.*
import cn.com.vau.util.StringUtil.getString
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.copyText

/**
 * Create data：2025/1/13 17:00
 * @author: Brin
 * Describe:
 */
class HistoryPositionAdapter(
    val historyViewModel: HistoryPositionViewModel,
) : ListAdapter<HistoryItemUiData, HistoryPositionAdapter.MyViewHolder>(DiffCallback()) {
    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int,
    ): MyViewHolder = MyViewHolder(ItemPositionHistoryBinding.inflate(LayoutInflater.from(parent.context), parent, false))

    override fun onBindViewHolder(
        holder: MyViewHolder,
        position: Int,
    ) {
        holder.bind(getItem(position))
    }

    inner class MyViewHolder(
        val binding: ItemPositionHistoryBinding,
    ) : RecyclerView.ViewHolder(binding.root) {
        @SuppressLint("SetTextI18n", "RestrictedApi")
        fun bind(item: HistoryItemUiData) {
            binding.apply {
                tvNameType.text = item.orderType
                tvNameType.backgroundTintList = ColorStateList.valueOf(item.orderTypeBackGroundColorInt)
                tvName.text = item.symbol
                tvId.text = "#${item.orderId}"
                tvId.clickNoRepeat {
                    item.orderId.copyText(getString(R.string.number_copied))
                }
                tvStatus.text = item.orderStatus
                tvClosingPnl.text = item.closingPnlTitle
                tvClosingPnl.setOnClickListener { historyViewModel.showDialog(PositionDialogStatus(pnlDialogShow = true)) }
                tvClosingPnlValue.text = item.closingPnl
                tvNetPnl.text = item.netPnlTitle
                tvNetPnl.setOnClickListener { historyViewModel.showDialog(PositionDialogStatus(netPnlDialogShow = true)) }
                tvNetPnlValue.text = item.netPnl
                tvTotal.text = item.closedTotalVolumeTitle
                tvTotalValue.text = item.closedTotalVolume
                tvEntryPrice.text = item.entryPriceTitle
                tvEntryPriceValue.text = item.entryPrice
                tvCharges.text = item.chargesSwapTitle
                tvCharges.setOnClickListener { historyViewModel.showDialog(PositionDialogStatus(chargesDialogShow = true)) }
                tvChargesValue.text = item.chargesSwap
                tvAvg.text = item.avgClosePriceTitle
                tvAvgValue.text = item.avgClosePrice
                tvOpened.text = item.openedTimeTitle
                tvOpenedTime.text = item.openedTime
                tvClosed.text = item.closedTimeTitle
                tvClosedTime.text = item.closedTime

                root.setOnClickListener {
                    HistoryPositionDetailActivity.start(
                        binding.root.context,
                        item.orderId,
                        item.recentlyCloseOrder,
                        item.openTimeLong,
                        item.closeTimeLong,
                    )
                }
                ivShare.clickNoRepeat {
                    ShareHelper.orderHistoryShare(root.context as AppCompatActivity, item.symbol, item.openPrice, item.avgClosePrice, item.closingPnl.toString(), item.cmd, item.closeTimeLong)
                }
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<HistoryItemUiData>() {
        override fun areItemsTheSame(
            oldItem: HistoryItemUiData,
            newItem: HistoryItemUiData,
        ): Boolean = oldItem.orderId == newItem.orderId

        override fun areContentsTheSame(
            oldItem: HistoryItemUiData,
            newItem: HistoryItemUiData,
        ): Boolean = oldItem.orderId == newItem.orderId
    }
}
