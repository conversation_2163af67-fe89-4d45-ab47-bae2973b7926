package cn.com.vau.history.ui.adapter

import android.content.res.ColorStateList
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.*
import cn.com.vau.R
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.databinding.ItemHistoryPositionDetailHeaderBinding
import cn.com.vau.history.data.HistoryPositionDetailUIHeader
import cn.com.vau.history.viewmodel.HistoryPositionDetailViewModel
import cn.com.vau.history.viewmodel.PositionDetailBottomDialogState
import cn.com.vau.util.*
import cn.com.vau.util.StringUtil.getString

/**
 * Create data：2025/1/24 14:29
 * @author: Brin
 * Describe:
 */
class HistoryPositionDetailHeaderAdapter(val historyViewModel: HistoryPositionDetailViewModel) : ListAdapter<HistoryPositionDetailUIHeader, HistoryPositionDetailHeaderAdapter.ItemViewHolder>(
    DiffCallback()
) {
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {
        return ItemViewHolder(ItemHistoryPositionDetailHeaderBinding.inflate(LayoutInflater.from(parent.context), parent, false))
    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ItemViewHolder(val mBinding: ItemHistoryPositionDetailHeaderBinding) : RecyclerView.ViewHolder(mBinding.root) {
        fun bind(item: HistoryPositionDetailUIHeader) {
            mBinding.apply {
                tvNameType.text = item.orderType
                tvNameType.backgroundTintList = ColorStateList.valueOf(item.orderTypeBackGroundColorInt)
                tvSymbols.text = item.symbol
                tvOrderId.text = "#${item.orderId}"
                tvOrderId.clickNoRepeat {
                    item.orderId.copyText(getString(R.string.number_copied))
                }
                tvStatus.text = item.orderStatus
                ivShare.onClickWithDefaultDelegate {
                    ShareHelper.orderHistoryShare(root.context as AppCompatActivity, item.symbol, item.openPrice, item.avgClosePrice, item.closingPnl.toString(), item.cmd, item.closeTimeLong)
                }
                tvTotalVol.text = item.closedVolumeTitle
                tvTotalVal.text = item.closedVolume
                tvPnl.text = item.closingPnlTitle
                tvPnlVal.text = item.closingPnl
                tvNetPnl.text = item.netPnlTitle
                tvNetPnlVal.text = item.netPnl
                tvNAvg.text = item.avgClosePriceTitle
                tvNAvgVal.text = item.avgClosePrice
                tvChargesSwaps.text = item.chargesTitle
                tvChargesSwapsVal.text = item.charge
                tvChargesSwaps.setOnClickListener {
                    historyViewModel.showBottomDialog(PositionDetailBottomDialogState(chargeDialog = true))
                }
                tvOpened.text = item.openedTimeTitle
                tvOpenedTime.text = item.openedTime
                tvClosed.text = item.closedTimeTitle
                tvClosedTime.text = item.closedTime
                tvClosedDetailTitle.text = item.detailsTitle
            }
        }
    }

    class DiffCallback : DiffUtil.ItemCallback<HistoryPositionDetailUIHeader>() {
        override fun areItemsTheSame(oldItem: HistoryPositionDetailUIHeader, newItem: HistoryPositionDetailUIHeader): Boolean {
            return oldItem.orderId == newItem.orderId
        }

        override fun areContentsTheSame(oldItem: HistoryPositionDetailUIHeader, newItem: HistoryPositionDetailUIHeader): Boolean {
            return oldItem == newItem
        }
    }
}