package cn.com.vau.history.ui

import android.app.Activity
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.FragmentPositionHistoryBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.data.uiStatus.PositionDialogStatus
import cn.com.vau.history.ui.adapter.BottomTipsListAdapter
import cn.com.vau.history.ui.adapter.HistoryPositionAdapter
import cn.com.vau.history.viewmodel.HistoryPositionViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.observeState
import cn.com.vau.util.onClickWithDefaultDelegate
import cn.com.vau.util.widget.dialog.BottomContentDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog

class HistoryPositionFragment : BaseMvvmBindingFragment<FragmentPositionHistoryBinding>() {
    private val historyViewModel: HistoryPositionViewModel by viewModels()
    private val positionHistoryAdapter: HistoryPositionAdapter by lazy { HistoryPositionAdapter(historyViewModel) }

    private val bottomSymbolsDialog: BottomSymbolsDialog by lazy {
        BottomSymbolsDialog.Builder(requireContext() as Activity, historyViewModel).build()
    }
    private val bottomTimesDialog: BottomTimeDialog by lazy {
        BottomTimeDialog
            .Builder(
                requireContext() as Activity,
                historyViewModel,
            ).build()
    }

    private val bottomPnlDialog: BottomContentDialog by lazy {
        BottomContentDialog
            .Builder(requireContext() as Activity)
            .setTitle(getString(R.string.closing_pnl))
            .setContent(getString(R.string.the_current_profit_excluding_other_charges))
            .build()
    }

    private val bottomNetPnlDialog: BottomContentDialog by lazy {
        BottomContentDialog
            .Builder(requireContext() as Activity)
            .setTitle(getString(R.string.net_pnl))
            .setContent(getString(R.string.the_profit_and_including_other_charges))
            .build()
    }

    private val bottomChargesDialog: BottomListDialog by lazy {
        val hintList =
            mutableListOf<HintLocalData>().apply {
                add(HintLocalData(getString(R.string.charges), getString(R.string.the_commissions_and_all_the_account)))
                add(HintLocalData(getString(R.string.swap), getString(R.string.the_rollover_interest_either_trading_hours)))
            }
        val statusAdapter = BottomTipsListAdapter(requireContext(), hintList)
        BottomListDialog
            .Builder(requireActivity())
            .setTitle(getString(R.string.charges_swap_dialog_title))
            .setAdapter(statusAdapter)
            .build()
    }

    override fun initView() {
        mBinding.tvSymbols.setOnClickListener {
            bottomSymbolsDialog.showDialog()
        }
        mBinding.ivFilter.onClickWithDefaultDelegate {
            bottomTimesDialog.showDialog()
        }
        mBinding.tvTimePeriod.onClickWithDefaultDelegate {
            bottomTimesDialog.showDialog()
        }

        mBinding.mRecyclerView.layoutManager = WrapContentLinearLayoutManager(requireContext())
        mBinding.mRecyclerView.adapter = positionHistoryAdapter
        mBinding.mRecyclerView.itemAnimator = null
        mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            historyViewModel.refreshHistoryData()
        }
        mBinding.mSmartRefreshLayout.setOnLoadMoreListener {
            historyViewModel.loadMoreHistoryData()
        }
        mBinding.mEmptyView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mEmptyView
            vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
            vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoDataBase))
            vs.mNoDataView.setHintMessage(getString(R.string.no_records_found))
        }
        mBinding.mErrorView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mErrorView
            vs.mNoDataView.setBackgroundColor(AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg))
            vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoConnection))
            vs.mNoDataView.setHintMessage(getString(R.string.slow_or_no_internet_connection))
            vs.mNoDataView.setBottomBtnText(getString(R.string.retry))
            vs.mNoDataView.setBottomBtnViewClickListener {
                historyViewModel.refreshHistoryData()
            }
        }
    }

    override fun createObserver() {
        historyViewModel.dialogStatus.run {
            observeState(viewLifecycleOwner, PositionDialogStatus::pnlDialogShow) {
                if (it) bottomPnlDialog.showDialog()
            }
            observeState(viewLifecycleOwner, PositionDialogStatus::netPnlDialogShow) {
                if (it) bottomNetPnlDialog.showDialog()
            }
            observeState(viewLifecycleOwner, PositionDialogStatus::chargesDialogShow) {
                if (it) bottomChargesDialog.showDialog()
            }
        }
        historyViewModel.selectedSymbol.observe(this) {
            mBinding.tvSymbols.text = if (it == "ALL") getString(R.string.symbols) else it
        }

        historyViewModel.timeFilter.observe(viewLifecycleOwner) {
            mBinding.tvTimePeriod.text = it
        }
        historyViewModel.historyStatus.observe(viewLifecycleOwner) {
            when (it) {
                is HistoryStatus.Initial -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }
                is HistoryStatus.Refreshing -> {
                    mBinding.mSmartRefreshLayout.autoRefreshAnimationOnly()
                    mBinding.mSmartRefreshLayout.resetNoMoreData()
                }

                is HistoryStatus.Empty -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.RefreshingEnd -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = false
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(true)
                    mBinding.mRecyclerView.smoothScrollToPosition(0)
                }

                is HistoryStatus.RefreshingFailed -> {
                    mBinding.mSmartRefreshLayout.finishRefresh(false)
                    mBinding.mErrorView.isVisible = true
                    mBinding.mEmptyView.isVisible = false
                }

                is HistoryStatus.LoadingMore -> {}
                is HistoryStatus.LoadingMoreEnd -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore()
                }

                is HistoryStatus.LoadingMoreFailed -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore(false)
                }

                is HistoryStatus.NoMore -> {
                    mBinding.mSmartRefreshLayout.finishLoadMoreWithNoMoreData()
                }

                is HistoryStatus.CloseSymbolFilter -> {
                    bottomSymbolsDialog.dismissDialog()
                }

                is HistoryStatus.CloseTimeFilter -> {
                    bottomTimesDialog.dismissDialog()
                }
                else -> {}
            }
        }

        historyViewModel.historyData.observe(viewLifecycleOwner) {
            // update history data by diffUtils
            positionHistoryAdapter.submitList(it.toMutableList())
        }
    }
}
