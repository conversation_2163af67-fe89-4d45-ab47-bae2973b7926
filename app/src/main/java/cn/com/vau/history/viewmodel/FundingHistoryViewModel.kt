package cn.com.vau.history.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.history.HistoryTrack
import cn.com.vau.history.HistoryUtil.getColoredEarningsText
import cn.com.vau.history.data.FundingData
import cn.com.vau.history.data.FundingRequestParams
import cn.com.vau.history.data.FundingUIData
import cn.com.vau.history.data.HistoryListBaseBean
import cn.com.vau.history.data.ItemFundingData
import cn.com.vau.history.data.RequestParamsWrapper
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.ui.TimeModeType
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

class FundingHistoryViewModel : BaseViewModel(), UpdateTimeFilterApi {

    private val _historyStatus = MutableLiveData<HistoryStatus>()
    val historyStatus: LiveData<HistoryStatus> = _historyStatus.distinctUntilChanged()

    private val _historyData = MutableLiveData<MutableList<FundingUIData>>()
    val historyData: LiveData<MutableList<FundingUIData>> = _historyData

    private val _timeFilter = MutableLiveData<String>()
    val timeFilter: LiveData<String> = _timeFilter

    private val _typeList = MutableLiveData<MutableList<ItemFundingData>>()
    val typeList: LiveData<MutableList<ItemFundingData>> = _typeList

    private val _selectedType = MutableLiveData<String>()
    val selectedType: LiveData<String> = _selectedType

    private var requestParams = FundingRequestParams(
        from = "2024-12-01 00:00:00",
        to = "2024-12-09 00:00:00",
        selectType = "1",
        pageSize = 20,
        pageNum = 1,
    )

    init {
        // region 初始化时间默认值
        val endTime = TimeUtil.date2String(Date(), "dd/MM/yyyy")
        val startCalendar = Calendar.getInstance().apply {
            add(Calendar.DATE, -7)
        }
        val startTime = TimeUtil.date2String(startCalendar.time, "dd/MM/yyyy")
        _timeFilter.value = "$startTime - $endTime"
        // endregion

        //region 初始化请求时间默认值并发起请求
        val to = TimeUtil.date2String(Date(), "yyyy-MM-dd 23:59:59") ?: ""
        val from = TimeUtil.date2String(startCalendar.time, "yyyy-MM-dd 00:00:00") ?: ""
        requestParams = requestParams.copy(from = from, to = to)
        initHistoryData()
        //endregion
    }

    fun initTypesList() {
        _typeList.value = mutableListOf<ItemFundingData>().apply {
            add(ItemFundingData(1, "ALL", getString(R.string.all), selected = true))
            add(ItemFundingData(2, "Deposit", getString(R.string.deposit),selected = false))
            add(ItemFundingData(3, "Withdraw",getString(R.string.withdraw), selected = false))
            add(ItemFundingData(4, "Credit Issuance", getString(R.string.credit_issuance),selected = false))
            add(ItemFundingData(5, "Credit Deduction", getString(R.string.credit_deduction),selected = false))
        }
    }

    private fun initHistoryData() {
        _historyStatus.value = HistoryStatus.Initial
        requestParams = requestParams.copy(pageNum = 1)

        requestNet({ doRequest() }, onSuccess = { result ->
            viewModelScope.launch(Dispatchers.IO) {
                processHistoryData(result, HistoryStatus.Empty, HistoryStatus.RefreshingEnd)
            }
        }, onError = {
            _historyStatus.value = HistoryStatus.RefreshingFailed
        })
    }

    /**
     * 根据账户类型发起不同的请求
     */
    private suspend fun doRequest() = if (UserDataUtil.isStLogin()) {
        stTradingService.tradeOrdersListFundPageApi(requestParams)
    } else {
        tradingService.tradeOrdersListFundPageApi(RequestParamsWrapper(requestParams.toString()))
    }

    /**
     * 根据不同的账户类型，处理不同的返回数据结果
     */
    private suspend fun processHistoryData(result: ApiResponse<*>, emptyStatus: HistoryStatus = HistoryStatus.Empty, endStatus: HistoryStatus = HistoryStatus.RefreshingEnd) {
        if (UserDataUtil.isStLogin()) {
            onStSuccess(result as ApiResponse<ArrayList<FundingData>>, emptyStatus, endStatus)
        } else {
            onSuccess(result as ApiResponse<HistoryListBaseBean<FundingData>>, emptyStatus, endStatus)
        }
    }

    private suspend fun onStSuccess(result: ApiResponse<ArrayList<FundingData>>, emptyStatus: HistoryStatus, endStatus: HistoryStatus): Boolean {

        result.data.ifNull().let { listData ->
            if (endStatus == HistoryStatus.RefreshingEnd) {
                _historyData.postValue(listData.map { mappingToFundingUIData(it) }.toMutableList())
            }else if (endStatus == HistoryStatus.LoadingMoreEnd){
                val originData = _historyData.value ?: mutableListOf()
                originData.addAll(listData.map { mappingToFundingUIData(it) })
                _historyData.postValue(originData.toMutableList())
            }
            _historyStatus.postValue(endStatus)
        }
        if (result.data.isNullOrEmpty()) {
            _historyStatus.postValue(emptyStatus)
            return true
        }
        return false
    }

    private suspend fun onSuccess(result: ApiResponse<HistoryListBaseBean<FundingData>>, emptyStatus: HistoryStatus, endStatus: HistoryStatus): Boolean {

        result.obj?.list.ifNull().let { listData ->
            if (endStatus == HistoryStatus.RefreshingEnd) {
                _historyData.postValue(listData.map { mappingToFundingUIData(it) }.toMutableList())
            }else if (endStatus == HistoryStatus.LoadingMoreEnd){
                val originData = _historyData.value ?: mutableListOf()
                originData.addAll(listData.map { mappingToFundingUIData(it) })
                _historyData.postValue(originData.toMutableList())
            }
            _historyStatus.postValue(endStatus)
        }
        if ((result.obj == null) || result.obj.list.isNullOrEmpty()) {
            _historyStatus.postValue(emptyStatus)
            return true
        }
        return false
    }

    fun refreshHistoryData() {
        _historyStatus.value = HistoryStatus.Refreshing
        requestParams = requestParams.copy(pageNum = 1)

        requestNet({ doRequest() }, onSuccess = { result ->
            viewModelScope.launch(Dispatchers.IO) {
                processHistoryData(result, HistoryStatus.Empty, HistoryStatus.RefreshingEnd)
            }
        }, onError = {
            _historyStatus.value = HistoryStatus.RefreshingFailed
        })
    }

    fun loadMoreHistoryData() {
        _historyStatus.value = HistoryStatus.LoadingMore
        requestParams = requestParams.copy(pageNum = requestParams.pageNum + 1)
        requestNet({ doRequest() }, onSuccess = { result ->
            viewModelScope.launch(Dispatchers.IO) {
                processHistoryData(result, HistoryStatus.NoMore, HistoryStatus.LoadingMoreEnd)
            }
        }, onError = {
            // report request error， restore pageNum
            requestParams = requestParams.copy(pageNum = requestParams.pageNum - 1)
            _historyStatus.value = HistoryStatus.LoadingMoreFailed

        })
    }

    private fun mappingToFundingUIData(it: FundingData) = FundingUIData(
        order = it.order.ifNull(),
        currencyName = UserDataUtil.currencyType(),
        timeStr = it.timeStr.ifNull(),
        typeTitle = getString(R.string.type),
        typeName = it.typeName.ifNull(),
        symbolTitle = getString(R.string.symbol),
        symbol = it.symbol.ifNull(),
        amountTitle = getString(R.string.amount),
        amount = getColoredEarningsText(it.amount.ifNull()),
    )

    fun updateTypeList(position: Int) {
        val oldSelected = _typeList.value?.indexOfFirst { it.selected } ?: -1
        if (oldSelected == -1) return
        val typeList = _typeList.value
        typeList?.set(oldSelected, typeList[oldSelected].copy(selected = false))
        typeList?.set(position, typeList[position].copy(selected = true))
        _typeList.value = typeList?.toMutableList()
    }

    fun updateTypeFilter(type: Int, typeUiName: String) {
        _historyStatus.value = HistoryStatus.CloseSymbolFilter
        _selectedType.value = typeUiName
        requestParams = requestParams.copy(selectType = type.toString())
        refreshHistoryData()
    }

    override fun updateTimeFilter(time: Pair<String, String>, @TimeModeType selectedTimeType: String) {
        try {
            _historyStatus.value = HistoryStatus.CloseTimeFilter
            val startTimeString = TimeUtil.string2String(time.first, "yyyy-MM-dd HH:mm:ss", "dd/MM/yyyy") ?: ""
            val endTimeString = TimeUtil.string2String(time.second, "yyyy-MM-dd HH:mm:ss", "dd/MM/yyyy") ?: ""
            _timeFilter.value = "${startTimeString}- ${endTimeString}".arabicReverseTextByFlag("-")
            requestParams = requestParams.copy(from = time.first, to = time.second)
            refreshHistoryData()
            HistoryTrack.trackFundingHistoryTimeFilter(selectedTimeType)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


}