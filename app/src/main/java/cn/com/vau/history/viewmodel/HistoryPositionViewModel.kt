package cn.com.vau.history.viewmodel

import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.distinctUntilChanged
import androidx.lifecycle.viewModelScope
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.stTradingService
import cn.com.vau.common.http.tradingService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.mvvm.network.ApiResponse
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.history.HistoryTrack
import cn.com.vau.history.HistoryUtil.getColoredEarningsText
import cn.com.vau.history.data.HistoryItemData
import cn.com.vau.history.data.HistoryItemUiData
import cn.com.vau.history.data.HistoryListBaseBean
import cn.com.vau.history.data.ItemSymbolData
import cn.com.vau.history.data.PositionRequestParams
import cn.com.vau.history.data.RequestParamsWrapper
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.data.uiStatus.PositionDialogStatus
import cn.com.vau.history.ui.TimeModeType
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.arabicReverseTextByFlag
import cn.com.vau.util.ifNull
import cn.com.vau.util.ifNullToLine
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.Date

class HistoryPositionViewModel :
    BaseViewModel(),
    UpdateTimeFilterApi {
    private val _historyStatus = MutableLiveData<HistoryStatus>()
    val historyStatus: LiveData<HistoryStatus> = _historyStatus.distinctUntilChanged()

    private val _historyData = MutableLiveData<MutableList<HistoryItemUiData>>()
    val historyData: LiveData<MutableList<HistoryItemUiData>> = _historyData

    private val originSymbolList by lazy {
        VAUSdkUtil
            .symbolList()
            .map { ItemSymbolData(it.symbol, selected = false) }
            .toMutableList()
            .apply { add(0, ItemSymbolData("ALL", selected = true, allTag = true)) }
    }
    private val _symbolList = MutableLiveData<MutableList<ItemSymbolData>>()
    val symbolList: LiveData<MutableList<ItemSymbolData>> = _symbolList

    private val _selectedSymbol = MutableLiveData<String>()
    val selectedSymbol: LiveData<String> = _selectedSymbol

    private val _timeFilter = MutableLiveData<String>()
    val timeFilter: LiveData<String> = _timeFilter

    private val _dialogStatus = MutableLiveData<PositionDialogStatus>()
    val dialogStatus: LiveData<PositionDialogStatus> = _dialogStatus

    private var requestParams =
        PositionRequestParams(
            from = "2024-12-01 00:00:00",
            to = "2024-12-09 00:00:00",
            symbol = "ALL",
            pageSize = 20,
            pageNum = 1
        )

    init {
        // region 初始化展示时间默认值
        val endTime = TimeUtil.date2String(Date(), "dd/MM/yyyy")
        val startCalendar =
            Calendar.getInstance().apply {
                add(Calendar.DATE, -7)
            }
        val startTime = TimeUtil.date2String(startCalendar.time, "dd/MM/yyyy")
        _timeFilter.value = "$startTime - $endTime".arabicReverseTextByFlag("-")
        // endregion

        //region 初始化请求时间默认值并发起请求
        val to = TimeUtil.date2String(Date(), "yyyy-MM-dd 23:59:59") ?: ""
        val from = TimeUtil.date2String(startCalendar.time, "yyyy-MM-dd 00:00:00") ?: ""
        requestParams = requestParams.copy(from = from, to = to)
        initHistoryData()
        //endregion
    }

    private fun initHistoryData() {
        // 网络请求 pageNum 从1开始
        _historyStatus.value = HistoryStatus.Initial
        requestParams = requestParams.copy(pageNum = 1)
        requestNet({
            if (UserDataUtil.isStLogin())
                stTradingService.tradeOrdersListOrderPageApi(requestParams)
            else
                tradingService.tradeOrdersListOrderPageApi(RequestParamsWrapper(requestParams.toString()))
        }, onSuccess = { result ->
            viewModelScope.launch(Dispatchers.IO) {
                processHistoryData(result)
            }
        }, onError = {
            _historyStatus.value = HistoryStatus.RefreshingFailed
        })
    }

    private suspend fun processHistoryData(
        result: ApiResponse<*>,
        emptyStatus: HistoryStatus = HistoryStatus.Empty,
        endStatus: HistoryStatus = HistoryStatus.RefreshingEnd
    ) {
        if (UserDataUtil.isStLogin()) {
            onStSuccess(result as ApiResponse<ArrayList<HistoryItemData>>, emptyStatus, endStatus)
        } else {
            onSuccess(result as ApiResponse<HistoryListBaseBean<HistoryItemData>>, emptyStatus, endStatus)
        }
    }

    private suspend fun onStSuccess(
        result: ApiResponse<ArrayList<HistoryItemData>>,
        emptyStatus: HistoryStatus,
        endStatus: HistoryStatus
    ): Boolean {
        result.data.ifNull().let { listData ->
            if (endStatus == HistoryStatus.RefreshingEnd) {
                _historyData.postValue(listData.map { transform(it) }.toMutableList())
            } else if (endStatus == HistoryStatus.LoadingMoreEnd) {
                val originData = _historyData.value ?: mutableListOf()
                originData.addAll(listData.map { transform(it) })
                _historyData.postValue(originData.toMutableList())
            }
            _historyStatus.postValue(endStatus)
        }
        if (result.data.isNullOrEmpty()) {
            _historyStatus.postValue(emptyStatus)
            return true
        }
        return false
    }

    private suspend fun onSuccess(
        result: ApiResponse<HistoryListBaseBean<HistoryItemData>>,
        emptyStatus: HistoryStatus,
        endStatus: HistoryStatus
    ): Boolean {
        result.obj?.list.ifNull().let { listData ->
            if (endStatus == HistoryStatus.RefreshingEnd) {
                _historyData.postValue(listData.map { transform(it) }.toMutableList())
            } else if (endStatus == HistoryStatus.LoadingMoreEnd) {
                val originData = _historyData.value ?: mutableListOf()
                originData.addAll(listData.map { transform(it) })
                _historyData.postValue(originData.toMutableList())
            }
            _historyStatus.postValue(endStatus)
        }
        if (result.obj == null || result.obj.list.isNullOrEmpty()) {
            _historyStatus.postValue(emptyStatus)
            return true
        }
        return false
    }

    fun refreshHistoryData() {
        // 网络请求 pageNum 从1开始
        _historyStatus.value = HistoryStatus.Refreshing
        requestParams = requestParams.copy(pageNum = 1)
        requestNet({
            if (UserDataUtil.isStLogin())
                stTradingService.tradeOrdersListOrderPageApi(requestParams)
            else
                tradingService.tradeOrdersListOrderPageApi(RequestParamsWrapper(requestParams.toString()))
        }, onSuccess = { result ->
            viewModelScope.launch(Dispatchers.IO) {
                processHistoryData(result)
            }
        }, onError = {
            _historyStatus.value = HistoryStatus.RefreshingFailed
        })
    }

    fun loadMoreHistoryData() {
        _historyStatus.value = HistoryStatus.LoadingMore
        requestParams = requestParams.copy(pageNum = requestParams.pageNum + 1)
        requestNet({
            if (UserDataUtil.isStLogin())
                stTradingService.tradeOrdersListOrderPageApi(requestParams)
            else
                tradingService.tradeOrdersListOrderPageApi(RequestParamsWrapper(requestParams.toString()))
        }, onSuccess = { result ->
            viewModelScope.launch(Dispatchers.IO) {
                processHistoryData(result, emptyStatus = HistoryStatus.NoMore, endStatus = HistoryStatus.LoadingMoreEnd)
            }
        }, onError = {
            // report request error， restore pageNum
            requestParams = requestParams.copy(pageNum = requestParams.pageNum - 1)
            _historyStatus.value = HistoryStatus.LoadingMoreFailed
        })
    }

    suspend fun transform(it: HistoryItemData): HistoryItemUiData =
        HistoryItemUiData(
            orderType = if (it.cmd == 0) "B" else "S",
            orderTypeBackGroundColorInt = if (it.cmd == 0) getColor(R.color.c00c79c) else getColor(R.color.cf44040),
            symbol = it.symbol,
            orderId = it.order,
            orderStatus =
                if (it.partClose) {
                    buildSpannedString {
                        color(getColor(R.color.ce35728)) {
                            append(getString(R.string.partially_closed))
                        }
                    }
                } else {
                    buildSpannedString {
                        append(getString(R.string.closed))
                    }
                },
            closingPnlTitle = getString(R.string.closing_pnl),
            closingPnl = getColoredEarningsText(it.profit),
            netPnlTitle = getString(R.string.net_pnl),
            netPnl = getColoredEarningsText(it.totalProfit),
            closedTotalVolumeTitle = "${getString(R.string.closed)}/${getString(R.string.vol_lots)} (${getString(R.string.lots)})",
            closedTotalVolume = "${it.volume}/${it.totalVolume}",
            entryPriceTitle = getString(R.string.entry_price),
            entryPrice = it.openPrice,
            chargesSwapTitle = "${getString(R.string.charges)}/${getString(R.string.swap)}",
            chargesSwap = "${it.commission ?: "-"}/${it.swap ?: "-"}".arabicReverseTextByFlag("/").ifNull("-/-"),
            avgClosePriceTitle = getString(R.string.avg_close_price),
            avgClosePrice = it.closePrice,
            openedTimeTitle = getString(R.string.open_time),
            openedTime = it.openTimeStr.ifNullToLine(),
            closedTimeTitle = getString(R.string.close_time),
            closedTime = it.closeTimeStr.ifNullToLine(),
            openTimeLong = it.openTime,
            closeTimeLong = it.closeTime,
            recentlyCloseOrder = it.recentlyCloseOrderDeal,
            openPrice = it.openPrice,
            cmd = it.cmd.toString()
        )

    //region symbol filter used
    fun initSymbolList() {
        _symbolList.value = originSymbolList
    }

    fun inputSymbolFilter(symbol: String) {
        if (symbol.isEmpty()) {
            _symbolList.value = originSymbolList.map { it.copy(inputKey = "") }.toMutableList()
            return
        }
        _symbolList.value =
            originSymbolList
                .filterIndexed { index, itemSymbolData ->
                    index != 0 && itemSymbolData.symbol.contains(symbol, true)
                }.map { it.copy(inputKey = symbol) }
                .toMutableList()
    }

    fun selectSymbol(position: Int) {
        try {
            val symbolList = _symbolList.value?.toMutableList() ?: mutableListOf()
            symbolList.forEachIndexed { index, itemSymbolData ->
                if (itemSymbolData.selected) {
                    symbolList[index] = symbolList[index].copy(selected = false)
                }
            }
            symbolList[position] = symbolList[position].copy(selected = true)
            _symbolList.value = symbolList.toMutableList()
            symbolList?.get(position)?.let {
                updateSymbolFilter(it.symbol)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    //endregion

    private fun updateSymbolFilter(symbol: String) {
        _historyStatus.value = HistoryStatus.CloseSymbolFilter
        _selectedSymbol.value = symbol
        requestParams = requestParams.copy(symbol = symbol)
        refreshHistoryData()
    }

    override fun updateTimeFilter(
        time: Pair<String, String>,
        @TimeModeType selectedTimeType: String
    ) {
        try {
            _historyStatus.value = HistoryStatus.CloseTimeFilter
            val startTimeString = TimeUtil.string2String(time.first, "yyyy-MM-dd HH:mm:ss", "dd/MM/yyyy") ?: ""
            val endTimeString = TimeUtil.string2String(time.second, "yyyy-MM-dd HH:mm:ss", "dd/MM/yyyy") ?: ""
            _timeFilter.value = "$startTimeString- $endTimeString".arabicReverseTextByFlag("-")
            requestParams = requestParams.copy(from = time.first, to = time.second)
            refreshHistoryData()
            HistoryTrack.trackPositionHistoryTimeFilter(selectedTimeType)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun showDialog(dialogStatus: PositionDialogStatus) {
        _dialogStatus.value = dialogStatus
    }
}