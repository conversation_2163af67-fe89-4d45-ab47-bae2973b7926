package cn.com.vau.history.ui

import android.app.Activity
import androidx.core.view.isVisible
import androidx.fragment.app.viewModels
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.databinding.FragmentFundingHistoryBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.history.data.uiStatus.HistoryStatus
import cn.com.vau.history.ui.adapter.HistoryFundingAdapter
import cn.com.vau.history.viewmodel.FundingHistoryViewModel
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.onClickWithDefaultDelegate

/**
 * Create data：2025/1/13 10:38
 * @author: Brin
 * Describe:
 */
class HistoryFundingFragment : BaseMvvmBindingFragment<FragmentFundingHistoryBinding>() {
    private val fundingViewModel: FundingHistoryViewModel by viewModels()
    private val positionHistoryAdapter: HistoryFundingAdapter by lazy {
        HistoryFundingAdapter(
            fundingViewModel,
        )
    }

    private val bottomTypesDialog: BottomFundingTypeDialog by lazy {
        BottomFundingTypeDialog
            .Builder(
                requireContext() as Activity,
                fundingViewModel,
            ).build()
    }
    private val bottomTimesDialog: BottomTimeDialog by lazy {
        BottomTimeDialog
            .Builder(
                requireContext() as Activity,
                fundingViewModel,
            ).build()
    }

    override fun initView() {
        mBinding.mSmartRefreshLayout.setOnRefreshListener {
            fundingViewModel.refreshHistoryData()
        }
        mBinding.mSmartRefreshLayout.setOnLoadMoreListener {
            fundingViewModel.loadMoreHistoryData()
        }
        mBinding.mRecyclerView.adapter = positionHistoryAdapter
        mBinding.mRecyclerView.itemAnimator = null
        mBinding.tvTypes.setOnClickListener {
            bottomTypesDialog.showDialog()
        }
        mBinding.ivFilter.onClickWithDefaultDelegate {
            bottomTimesDialog.showDialog()
        }
        mBinding.tvTimePeriod.onClickWithDefaultDelegate {
            bottomTimesDialog.showDialog()
        }
        mBinding.mEmptyView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mEmptyView
            vs.mNoDataView.setBackgroundColor(
                AttrResourceUtil.getColor(
                    requireContext(),
                    R.attr.mainLayoutBg,
                ),
            )
            vs.mNoDataView.setIconResource(
                AttrResourceUtil.getDrawable(
                    requireContext(),
                    R.attr.icNoDataBase,
                ),
            )
            vs.mNoDataView.setHintMessage(getString(R.string.no_records_found))
        }
        mBinding.mErrorView.setOnInflateListener { _, inflated ->
            val vs = VsLayoutNoDataBinding.bind(inflated)
            vs.mNoDataView.id = R.id.mErrorView
            vs.mNoDataView.setBackgroundColor(
                AttrResourceUtil.getColor(
                    requireContext(),
                    R.attr.mainLayoutBg,
                ),
            )
            vs.mNoDataView.setIconResource(
                AttrResourceUtil.getDrawable(
                    requireContext(),
                    R.attr.icNoConnection,
                ),
            )
            vs.mNoDataView.setHintMessage("No internet connection or server error, please try again later.")
            vs.mNoDataView.setBottomBtnText("try again")
            vs.mNoDataView.setBottomBtnViewClickListener {
                fundingViewModel.refreshHistoryData()
            }
        }
    }

    override fun createObserver() {
        fundingViewModel.historyStatus.observe(viewLifecycleOwner) {
            when (it) {
                is HistoryStatus.Initial -> {
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.Refreshing -> {
                    mBinding.mSmartRefreshLayout.autoRefreshAnimationOnly()
                    mBinding.mSmartRefreshLayout.resetNoMoreData()
                }

                is HistoryStatus.Empty -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(false)
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = true
                }

                is HistoryStatus.RefreshingEnd -> {
                    mBinding.mSmartRefreshLayout.finishRefresh()
                    mBinding.mErrorView.isVisible = false
                    mBinding.mEmptyView.isVisible = false
                    mBinding.mSmartRefreshLayout.setEnableLoadMore(true)
                    mBinding.mRecyclerView.smoothScrollToPosition(0)
                }

                is HistoryStatus.RefreshingFailed -> {
                    mBinding.mSmartRefreshLayout.finishRefresh(false)
                    mBinding.mErrorView.isVisible = true
                }

                is HistoryStatus.LoadingMore -> {}
                is HistoryStatus.LoadingMoreEnd -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore()
                }

                is HistoryStatus.LoadingMoreFailed -> {
                    mBinding.mSmartRefreshLayout.finishLoadMore(false)
                }

                is HistoryStatus.NoMore -> {
                    mBinding.mSmartRefreshLayout.finishLoadMoreWithNoMoreData()
                }

                is HistoryStatus.CloseSymbolFilter -> {
                    bottomTypesDialog.dismissDialog()
                }

                is HistoryStatus.CloseTimeFilter -> {
                    bottomTimesDialog.dismissDialog()
                }

                else -> {}
            }
        }

        fundingViewModel.historyData.observe(viewLifecycleOwner) {
            positionHistoryAdapter.submitList(it.toMutableList())
        }

        fundingViewModel.selectedType.observe(viewLifecycleOwner) {
            mBinding.tvTypes.text =
                if (it.equals(getString(R.string.all))) getString(R.string.type) else it
        }

        fundingViewModel.timeFilter.observe(viewLifecycleOwner) {
            mBinding.tvTimePeriod.text = it
        }
    }
}
