package cn.com.vau.history.data

import androidx.annotation.Keep
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.util.AppUtil

/**
 * Create data：2025/2/24 16:33
 * @author: Brin
 * Describe:
 */
@Keep
data class RequestParamsWrapper(
    val data: String
)

@Keep
data class PositionRequestParams(
    val symbol: String = "",
    val from: String = "",
    val to: String = "",
    val pageNum: Int = 1,
    val pageSize: Int = 0,
    val login: String = UserDataUtil.accountCd(),
    val serverId: String = UserDataUtil.serverId(),
    val token: String = if (UserDataUtil.isStLogin()) UserDataUtil.stToken() else UserDataUtil.tradeToken(),
    val zoneTime: Int = AppUtil.getTimeZoneRawOffsetToHour(),
){
    override fun toString(): String {
        // 生成一个 Gson 字符串， Gson 字符串可以直接作为请求参数
        // 这里的 Gson 字符串是手动生成的，并没有使用 Gson 库
        return "{" +
                "\"symbol\":\"" + symbol + "\"," +
                "\"from\":\"" + from + "\"," +
                "\"to\":\"" + to + "\"," +
                "\"pageNum\":" + pageNum + "," +     // Int 类型需要带上引号
                "\"pageSize\":" + pageSize + "," +
                "\"login\":\"" + login + "\"," +
                "\"serverId\":\"" + serverId + "\"," +
                "\"token\":\"" + token + "\"," +
                "\"zoneTime\":" + zoneTime +
                "}"
    }
}

@Keep
data class FundingRequestParams(
    val selectType: String = "",
    val from: String = "",
    val to: String = "",
    val pageNum: Int = 1,
    val pageSize: Int = 0,
    val login: String = UserDataUtil.accountCd(),
    val serverId: String = UserDataUtil.serverId(),
    val token: String = if (UserDataUtil.isStLogin()) UserDataUtil.stToken() else UserDataUtil.tradeToken(),
    val zoneTime: Int = AppUtil.getTimeZoneRawOffsetToHour(),
) {
    override fun toString(): String {
        // 生成一个 Gson 字符串， Gson 字符串可以直接作为请求参数
        // 这里的 Gson 字符串是手动生成的，并没有使用 Gson 库
        return "{" +
                "\"selectType\":\"" + selectType + "\"," +
                "\"from\":\"" + from + "\"," +
                "\"to\":\"" + to + "\"," +
                "\"pageNum\":" + pageNum + "," +     // Int 类型需要带上引号
                "\"pageSize\":" + pageSize + "," +
                "\"login\":\"" + login + "\"," +
                "\"serverId\":\"" + serverId + "\"," +
                "\"token\":\"" + token + "\"," +
                "\"zoneTime\":" + zoneTime +
                "}"
    }
}


@Keep
data class HistoryDetailRequestParams(
    val order:String = "",
    val closeOrder:String = "",
    val openTime:String = "",
    val closeTime:String = "",
    val login: String = UserDataUtil.accountCd(),
    val serverId: String = UserDataUtil.serverId(),
    val token: String = if (UserDataUtil.isStLogin()) UserDataUtil.stToken() else UserDataUtil.tradeToken(),
    val zoneTime: Int = AppUtil.getTimeZoneRawOffsetToHour(),
){
    override fun toString(): String {
        // 生成一个 Gson 字符串， Gson 字符串可以直接作为请求参数
        // 这里的 Gson 字符串是手动生成的，并没有使用 Gson 库
        return "{" +
                "\"order\":\"" + order + "\"," +
                "\"closeOrder\":\"" + closeOrder + "\"," +
                "\"openTime\":\"" + openTime + "\"," +
                "\"closeTime\":\"" + closeTime + "\"," +
                "\"login\":\"" + login + "\"," +
                "\"serverId\":\"" + serverId + "\"," +
                "\"token\":\"" + token + "\"," +
                "\"zoneTime\":" + zoneTime +
                "}"
    }
}