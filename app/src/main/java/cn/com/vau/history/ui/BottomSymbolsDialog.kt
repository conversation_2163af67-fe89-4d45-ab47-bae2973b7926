package cn.com.vau.history.ui

import android.app.Activity
import android.content.Context
import android.text.Editable
import android.widget.EditText
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.databinding.PopupHistorySymbolsFilterBinding
import cn.com.vau.history.viewmodel.HistoryPositionViewModel
import cn.com.vau.history.ui.adapter.SymbolsAdapter
import cn.com.vau.util.widget.dialog.base.BottomDialog
import cn.com.vau.util.widget.dialog.base.IDialog
import cn.com.vau.util.widget.dialog.buidler.ActionBuilder
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

/**
 * Create data：2025/1/14 18:39
 * @author: Brin
 * Describe:
 */
class BottomSymbolsDialog(
    context: Context, title: String, val viewModel: HistoryPositionViewModel, onCreateListener: ((PopupHistorySymbolsFilterBinding) -> Unit)? = null, onDismissListener: (() -> Unit)? = null
) : BottomDialog<PopupHistorySymbolsFilterBinding>(
    context = context, title = title, viewBinding = PopupHistorySymbolsFilterBinding::inflate, onCreateListener = onCreateListener, onDismissListener = onDismissListener
) {

    val adapter: SymbolsAdapter by lazy { SymbolsAdapter(viewModel) }

    override fun setContentView() {
        mContentBinding.etSearch.afterTextChangedFlow().debounce(300).onEach {
            viewModel.inputSymbolFilter(it)
        }.launchIn(lifecycleScope)
        mContentBinding.rvSymbols.layoutManager = WrapContentLinearLayoutManager(context)
        mContentBinding.rvSymbols.setHasFixedSize(true)
        mContentBinding.rvSymbols.adapter = adapter
        mContentBinding.rvSymbols.itemAnimator = null
        viewModel.initSymbolList()
        viewModel.symbolList.observe(this) {
            adapter.submitList(it.toMutableList()){
                mContentBinding.rvSymbols.scrollToPosition(0)
            }
        }
    }

    @Suppress("unused")
    class Builder(activity: Activity, val viewModel: HistoryPositionViewModel) : ActionBuilder<PopupHistorySymbolsFilterBinding>(activity) {

        override fun build(): BottomSymbolsDialog {
            config = config.copy(viewMode = true, isMoveUpToKeyboard = false, animationDuration = 400)
            return super.build() as BottomSymbolsDialog
        }

        override fun createDialog(context: Context): IDialog<PopupHistorySymbolsFilterBinding> {
            return BottomSymbolsDialog(
                context,
                title = context.getString(R.string.symbols),
                viewModel = viewModel,
            )
        }
    }
}

// 扩展函数：将 EditText 的文本变化转换为 Flow
fun EditText.afterTextChangedFlow(): Flow<String> = callbackFlow {
    val watcher = object : android.text.TextWatcher {
        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        override fun afterTextChanged(s: Editable?) {
            trySend(s?.toString() ?: "").isSuccess // 发射文本变化
        }
    }
    addTextChangedListener(watcher)
    awaitClose { removeTextChangedListener(watcher) } // 取消监听
}

