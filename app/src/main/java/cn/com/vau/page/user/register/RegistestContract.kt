package cn.com.vau.page.user.register

import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.*
import cn.com.vau.data.init.TradeAccountLoginBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface RegistestContract {
    interface Model : BaseModel {
        fun getCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<VerificationCodeData>): Disposable
        fun checkSmsCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable
        fun registerAcountApi(
            map: HashMap<String, Any?>,
            baseObserver: BaseObserver<LoginBean>
        ): Disposable

        fun thirdpartyRegisterApi(
            map: HashMap<String, Any?>,
            baseObserver: BaseObserver<LoginBean>
        ): Disposable

        fun bindMT4Login(body: RequestBody, baseObserver: BaseObserver<TradeAccountLoginBean>): Disposable
        fun checkEmail(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable
//        fun getAreaCode(baseObserver: BaseObserver<SelectCountryNumberBean>): Disposable
    }

    interface View : BaseView {
        fun showAreaCode() {}
        fun goSecond() {}
        fun onRegisterSuccess() {}
        fun showBindDialog(email: String?) {}
        fun jumpToLogin(handleType: Int, msg: String?) {}
        fun showCaptcha() {}
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getCodeApi(validateCode: String, smsSendType: String)
//        abstract fun registerAccount(firstName: String?, lastName: String?, email: String, userPassword: String)
//        abstract fun checkInfo(
//            firstName: String?,
//            lastName: String?,
//            email: String?,
//            userPassword: String?,
//            isCheckEmail: Boolean
//        )

//        abstract fun mt4Login(account: String)
//        abstract fun initAreaCode(accountType: Int)
//        abstract fun initSendCodeUtil(listener: SendCodeUtil.SendCodeListener)
//        abstract fun startSendCodeUtil()
//        abstract fun stopSendCodeUtil()
//        abstract fun checkSmsCode(smsCode: String?)
//        abstract fun checkEmail(email: String?)
//        abstract fun saveData(data: LoginObjBean?, userTel: String?, phoneCountryCode: String, code: String, userPassword: String)

//        abstract fun getAreaCode()
    }

}
