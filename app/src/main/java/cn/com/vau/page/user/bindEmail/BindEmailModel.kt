package cn.com.vau.page.user.bindEmail

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class BindEmailModel : BindEmailContract.Model {
    override fun bindUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BindEmailBean>): Disposable {
        HttpUtils.loadData(
                RetrofitHelper.getHttpService().bindEmailUserApi(map),
                baseObserver
        )
        return baseObserver.disposable
    }
    override fun bindRegisterEmailUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable {
        HttpUtils.loadData(
                RetrofitHelper.getHttpService().emailBinding<PERSON><PERSON><PERSON><PERSON>(map),
                baseObserver
        )
        return baseObserver.disposable
    }
}
