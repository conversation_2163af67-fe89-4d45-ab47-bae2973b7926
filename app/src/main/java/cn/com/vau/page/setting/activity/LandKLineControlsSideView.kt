package cn.com.vau.page.setting.activity

import android.content.Context
import android.graphics.Rect
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.common.view.LandKlineSideDecoration
import cn.com.vau.common.view.popup.adapter.TradingViewDrawingAdapter2
import cn.com.vau.common.view.popup.adapter.TradingViewMainAdapter2
import cn.com.vau.common.view.popup.bean.DrawingBean2
import cn.com.vau.common.view.popup.bean.MainBean
import cn.com.vau.databinding.LayoutLandKlineSideControlsBinding
import cn.com.vau.databinding.LayoutLandKlineSideMainBinding
import cn.com.vau.util.LogUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnum
import androidx.core.view.isVisible
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum

class LandKLineControlsSideView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet?,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private val TAG = "LandKLineControlsSideView"
    }

    private var drawingDataList = mutableListOf<DrawingBean2>()
    private var mainDataList = mutableListOf<MainBean>()

    private val mBinding by lazy { LayoutLandKlineSideControlsBinding.inflate(LayoutInflater.from(context), this) }
    private val itemDecoration  = LandKlineSideDecoration()

    private var drawDeleteAllClick: (() -> Unit)? = null
    private var showDrawClick: (() -> Boolean)? = null
    private var keepDrawClick: (() -> Boolean)? = null
    private var drawingItemClick: ((item: DrawingBean2?) -> Unit)? = null
    private var mainItemClick: ((item: MainBean?) -> Unit)? = null
    private var switchMenu: ((isDraw: Boolean) -> Unit)? = null
    private val drawingAdapter = TradingViewDrawingAdapter2(drawingDataList)
    private val mainAdapter = TradingViewMainAdapter2()

    init {
        initView()
        initListener()
    }

    fun setDrawingDataList(list: MutableList<DrawingBean2>) {
        drawingDataList.clear()
        drawingDataList.addAll(list)
        drawingAdapter.setList(drawingDataList)
    }

    fun setMainDataList(
        list: MutableList<MainBean>,
        mainIndicatorList: MutableList<KlineMainEnum>,
        subIndicatorList: MutableList<KlineOtherEnum>
    ) {
        mainDataList.clear()
        mainDataList.addAll(list)
        mainAdapter.setList(mainDataList)
        mainAdapter.changeChartSelected(mainIndicatorList, subIndicatorList)
    }

    private fun initView() {
        mBinding.rvDrawing.setHasFixedSize(true)
        mBinding.rvDrawing.adapter = drawingAdapter
        mBinding.rvDrawing.addItemDecoration(itemDecoration)
        mBinding.vsMain.setOnInflateListener { stub, inflated ->
            val mainBinding = LayoutLandKlineSideMainBinding.bind(inflated)
            //主图选择列表
            mainBinding.rvMain.setHasFixedSize(true)
            mainBinding.rvMain.adapter = mainAdapter

            mainBinding.rvMain.addItemDecoration(itemDecoration)
            //显示画线侧边栏
            mainBinding.ivShowDrawing.clickNoRepeat {
                mBinding.vsMain.visibility = View.INVISIBLE
                mBinding.gpDrawing.visibility = View.VISIBLE
                switchMenu?.invoke(mBinding.gpDrawing.isVisible)
            }
        }
    }

    fun showMainView() {
        mBinding.vsMain.visibility = View.VISIBLE
        mBinding.gpDrawing.visibility = View.INVISIBLE
    }

    fun setSelectShowDrawView(isSelect: Boolean) {
        mBinding.ivShowDrawView.isSelected = isSelect
    }

    private fun initListener() {
        //连续画线
        mBinding.ivKeepDraw.clickNoRepeat {
            mBinding.ivKeepDraw.isSelected = keepDrawClick?.invoke() ?: false
        }
        //删除全部线
        mBinding.ivClean.clickNoRepeat {
            drawDeleteAllClick?.invoke()
        }
        //显示画线层
        mBinding.ivShowDrawView.clickNoRepeat {
            mBinding.ivShowDrawView.isSelected = showDrawClick?.invoke() ?: false
        }
        //显示主图侧边栏
        mBinding.ivShowMain.clickNoRepeat {
            mBinding.vsMain.visibility = View.VISIBLE
            mBinding.gpDrawing.visibility = View.INVISIBLE
            drawingAdapter.resetSelectedPosition()
            switchMenu?.invoke(mBinding.gpDrawing.isVisible)
        }
        drawingAdapter.setOnItemClickListener { adapter, _, position ->
            val item = (adapter as? TradingViewDrawingAdapter2)?.getItem(position)
            LogUtil.v(TAG, "onDrawingItemClick item:$item")
            drawingItemClick?.invoke(item)
            if(mBinding.ivShowDrawView.isSelected == false) {
                mBinding.ivShowDrawView.isSelected = showDrawClick?.invoke() ?: false
            }
            drawingAdapter.setSelectedPosition(position)
        }

        mainAdapter.setOnItemClickListener { adapter, _, position ->
            val item = (adapter as TradingViewMainAdapter2).getItem(position)
            mainItemClick?.invoke(item)
            LogUtil.v(TAG, "onMainItemClick item:$item")
        }
    }

    fun cleanAllDraw() {
        drawingAdapter.resetSelectedPosition()
    }

    fun changeMainSelected(mainList: MutableList<KlineMainEnum>) {
        mainAdapter.changeMainSelected(mainList)
    }

    fun changeSubSelected(subIndicatorList: MutableList<KlineOtherEnum>) {
        mainAdapter.changeSubSelected(subIndicatorList)
    }

    fun drawViewChange(drawName: String?, currentPointCount: Int?, totalPointCount: Int?, isFinish: Boolean?, isContinuation: Boolean?) {
        LogUtil.v(TAG, "drawViewChange drawName:$drawName,currentPointCount:$currentPointCount,totalPointCount:$totalPointCount,isFinish:$isFinish")
        if (isFinish == true && isContinuation == false) {
            drawingAdapter.resetSelectedPosition()
        }
    }

    fun onShowDrawClick(action: (() -> Boolean)?) {
        showDrawClick = action
    }

    fun onDrawDeleteAll(action: (() -> Unit)?) {
        drawDeleteAllClick = action
    }

    fun onKeepDrawClick(action: (() -> Boolean)?) {
        keepDrawClick = action
    }

    fun onDrawingItemClick(action: ((item: DrawingBean2?) -> Unit)?) {
        drawingItemClick = action
    }

    fun onMainItemClick(action: ((item: MainBean?) -> Unit)?) {
        mainItemClick = action
    }

    fun onSwitchMenu(action: ((showDraw:Boolean) -> Unit)?) {
        switchMenu = action
    }
}