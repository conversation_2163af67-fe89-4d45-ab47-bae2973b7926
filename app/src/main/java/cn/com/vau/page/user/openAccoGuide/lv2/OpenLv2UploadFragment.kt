package cn.com.vau.page.user.openAccoGuide.lv2

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.*
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.base.fragment.BaseFragment
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.view.VerifyCompHandler
import cn.com.vau.databinding.FragmentOpenLv2UploadBinding
import cn.com.vau.page.UploadBean
import cn.com.vau.page.user.openAccoGuide.OpenAccoGuideBaseActivity
import cn.com.vau.page.user.openAccoGuide.lv2.adapter.OpenAccoUploadAdapter
import cn.com.vau.page.user.openAccoGuide.lv2.vm.OpenLv2ViewModel
import cn.com.vau.page.user.openAccoGuide.result.OpenAccountLvResultActivity
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.dialog.*
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject
import java.io.IOException

class OpenLv2UploadFragment : BaseFragment() {

    private val binding: FragmentOpenLv2UploadBinding by lazy { FragmentOpenLv2UploadBinding.inflate(layoutInflater) }
    private var verifyHandler = VerifyCompHandler()
    private val viewModel: OpenLv2ViewModel by lazy { ViewModelProvider(requireActivity())[OpenLv2ViewModel::class.java] }
    private val adapter = OpenAccoUploadAdapter()
    private var currentUploadIndex = 0
    private var idDocPathResultList = mutableListOf<String>()

    private val pickImage = createPhotoRequestForUri { uri ->
        uri?.let {
            selectAndUpload(it)
        }
    }

    private val cameraUri by lazy { createTempImageUri() }

    private val pickCamera = createCameraRequestForUri { isSuccess ->
        if (isSuccess) {
            cameraUri?.let { uri ->
                selectAndUpload(uri)
            }
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return binding.root
    }

    override fun initParam() {
        super.initParam()
        LogEventUtil.setLogEvent(
            BuryPointConstant.V334.REGISTER_LIVE_PAGE_VIEW, hashMapOf(
                "Page_name" to "${(activity as? OpenAccoGuideBaseActivity<*>)?.buryPointMsg}-Lvl2-2"
            )
        )
    }

    override fun initView() {
        super.initView()
        binding.recyclerView.adapter = adapter
        syncUploadState()

        verifyHandler.with(getAdapterAvailableData().isNotEmpty())
            .to(
                binding.tvNext
            ).submit {
                idDocPathResultList.clear()
                // 第一步：上传所有Photo
                currentUploadIndex = 0
                viewModel.showLiveDataLoading()
                viewModel.eachUpload(adapter.data.elementAtOrNull(currentUploadIndex))

                // 神策自定义埋点(v3500)，点击事件
                sensorsTrackClick("Finish")
            }
    }

    override fun initData() {
        super.initData()
        val idFileList = viewModel.openData?.idFileList
        val idTypeForData = viewModel.openData?.idType.ifNull(-1)
        if (idTypeForData == viewModel.selectedIDType?.id) {
            idFileList?.let {
                val pathList = arrayListOf<UploadBean>()
                it.forEach { path ->
                    pathList.add(UploadBean(path = path.filePath, isTakeForOpenDate = true, type = path.fileType))
                }
                if (pathList.isNotEmpty()) {
                    adapter.data.clear()
                    syncUploadState(list = pathList)
                }
            }
        }
        // 只有护照才展示tvTip3的文案（1-National ID Card；2-Passport；4-Driver license）
        binding.tvTip3.isVisible = viewModel.selectedIDType?.id == 2
    }

    override fun initListener() {
        super.initListener()
        adapter.setOnItemChildClickListener { _, view, position ->
            when (view.id) {
                R.id.ivClose -> {
                    adapter.removeAt(position)
                    syncUploadState()
                }

                R.id.clUpload -> {
                    val item = adapter.getItem(position)
                    if (item.path.isNullOrEmpty() && item.uri == null) {
                        activity?.let {
                            checkPermission()
                        }
                        // 神策自定义埋点(v3500)，点击事件
                        if (position == 0) {
                            sensorsTrackClick("Upload Document")
                        } else {
                            sensorsTrackClick("Add More")
                        }
                    }
                    // 可预览的
                    else if (item.type == "png" || item.type == "jpg" || item.type == "jpeg" || item.type == "bmp") {
                        val path = if (item.path?.isNotEmpty() == true || item.originPath?.isNotEmpty() == true) {
                            if (item.originPath?.isNotEmpty() == true) item.originPath else item.path
                        } else ""
                        openActivity(
                            OpenUoloadPreviewActivity::class.java, bundleOf(
                                "title" to "${context?.getString(R.string.photo_name)} ${position + 1}",
                                "path" to path,
                                "uri" to item.uri
                            )
                        )
                    }
                }
            }
        }

        viewModel.environmentStoragePermission.observe(this) {
            activity?.let {
                showBottomDialog()
            }
        }
        viewModel.initUploadPage.observe(this) {
            initData()
        }
        viewModel.destroyUploadPage.observe(this) {
            adapter.data.clear()
            syncUploadState()
            adapter.notifyDataSetChanged()
        }
        viewModel.idTypeLiveData.observe(this) {
            when (it) {
                1 -> {
                    binding.tvPassportPhoto.text = getString(R.string.identification_card_photo)
                    binding.ivPhoto.setImageResource(
                        AttrResourceUtil.getDrawable(requireContext(), R.attr.imgIdentificationCard)
                    )
                    binding.ivPhoto.visibility = View.VISIBLE
                }

                2 -> {
                    binding.tvPassportPhoto.text = getString(R.string.passport_photo)
                    binding.ivPhoto.setImageResource(
                        AttrResourceUtil.getDrawable(requireContext(), R.attr.imgPassport)
                    )
                    binding.ivPhoto.visibility = View.VISIBLE
                }

                4 -> {
                    binding.tvPassportPhoto.text = getString(R.string.drivers_license_photo)
                    binding.ivPhoto.setImageResource(
                        AttrResourceUtil.getDrawable(requireContext(), R.attr.imgDriversLicense)
                    )
                    binding.ivPhoto.visibility = View.VISIBLE
                }

                else -> {
                    binding.tvPassportPhoto.text = ""
                    binding.ivPhoto.visibility = View.INVISIBLE
                }
            }
        }
        viewModel.selectedPhotoLiveData.observe(this) {
            syncUploadState(imageBean = it)
        }
        viewModel.uploadPhotoLiveData.observe(this) {
            if ("V00000" == it.resultCode) {
                val bean = it.data?.obj
                val path = bean?.imgFile.ifNull()
                if (path.isNotEmpty()) {
                    idDocPathResultList.add(path)
//                    LogUtil.d("wj", "add: $path")
                }
                val dataSize = getAdapterAvailableData().size
                if (currentUploadIndex != dataSize - 1) {
                    currentUploadIndex++
                    viewModel.eachUpload(adapter.data.elementAtOrNull(currentUploadIndex))
                } else {
                    // 已传完
                    val processBean = viewModel.getProcessLiveData.value?.data?.obj
                    viewModel.saveProcess(
                        hashMapOf(
                            "token" to UserDataUtil.loginToken(),
                            "step" to "5",
                            "openAccountMethod" to 1,
                            "type" to 1,
                            "poiMethod" to 3,
                            "firstName" to processBean?.firstName.ifNull(),
                            "middleName" to processBean?.middleName.ifNull(),
                            "lastName" to processBean?.lastName.ifNull(),
                            "nationalityId" to processBean?.nationalityId.ifNull(-1),
                            "countryId" to processBean?.countryId.ifNull(-1),
                            "idType" to viewModel.selectedIDType?.id.ifNull(-1),
                            "idNumber" to viewModel.idSerialNum.ifNull(),
                            "idDocFilePathList" to idDocPathResultList.joinToString(",").ifNull("")
                        )
                    )
                }
            } else {
                viewModel.hideLiveDataLoading()
                ToastUtil.showToast(it.msgInfo)
            }
        }
        viewModel.saveProcessLiveData2.observe(this) {
            if ("V00000" == it.resultCode) {
                if (viewModel.fromWallet) {
                    CenterActionWithIconDialog.Builder(requireActivity())
                        .setLottieIcon(R.raw.lottie_dialog_wait)
                        .setTitle(getString(R.string.id_verification_application_submitted))
                        .setContent(getString(R.string.this_process_may_you_getting_approved)) //设置内容
                        .setSingleButton(true) //展示一个按钮，默认两个按钮
                        .setSingleButtonText(getString(R.string.got_it)) //设置单个按钮文本
                        .setOnSingleButtonListener {
                            EventBus.getDefault().post(DataEvent(NoticeConstants.HTML_JUMP_OPEN_ACCOUNT_BACK))
                            activity?.finish()
                        }
                        .build()
                        .showDialog()
                } else {
                    EventBus.getDefault().post(NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE)
                    activity?.let { activity ->
                        //绑定神策业务ID，场景5
                        SensorsDataUtil.bindBusinessIdForMerge(it.data?.obj?.emailEventID)
                        openActivity(
                            OpenAccountLvResultActivity::class.java, bundleOf(
                                Constants.NUM_STEP_OPEN_ACCOUNT to "2"
                            )
                        )
                        activity.finish()
                    }
                }
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }
    }

    private fun checkPermission() {
        // 校验权限 （读写和相机权限）
        PermissionUtil.checkPermissionWithCallback(requireActivity(), *Constants.PERMISSION_STORAGE, Constants.PERMISSION_CAMERA) {
            if (it)
                showBottomDialog()
            else
                ToastUtil.showToast(context?.getString(R.string.please_give_us_settings))
        }
    }

    private fun showBottomDialog() {
        BottomSelectListDialog.Builder(requireActivity())
            .setTitle(getString(R.string.add_picture_from))
            .setDataList(
                arrayListOf(
                    getString(R.string.camera),
                    getString(R.string.photo_library),
                    getString(R.string.file)
                )
            )
            .setItemType(1)
            .setOnItemClickListener { position ->
                if (position == 0) { // camera
                    onSelectMethod(0)
                } else if (position == 1) { // photo library
                    onSelectMethod(1)
                } else if (position == 2) { // system files
                    onSelectMethod(2)
                }
            }
            .build()
            .showDialog()
    }

    private fun onSelectMethod(selectType: Int) {
        when (selectType) {
            0 -> openCamera()
            1 -> openGallery()
            else -> openFile()
        }
    }

    private fun openCamera() {
        // 打开相机
        pickCamera.launch(cameraUri)
    }

    private fun openGallery() {
        // 打开相册
        pickImage.launchPhoto()
    }

    private fun openFile() {
        // 打开系统文件夹  (从【近期的文件】列表中选取的文件有可能是Uri格式有问题，会导致转成的文件类型无法判断触发文件类型弹窗)
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
//        intent.putExtra(Intent.EXTRA_MIME_TYPES, arrayOf(
//            MimeType.PNG, MimeType.JPG, MimeType.BMP, MimeType.PDF, MimeType.DOC, MimeType.DOCX
//        ))
        intent.type = "*/*"
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        try {
            launcher.launch(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val launcher: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        if (it.resultCode == Activity.RESULT_OK) {
            it.data.let { data ->
                val uri = data?.data
                if (uri != null) {
                    selectAndUpload(uri)
                }
            }
        }
    }

    private fun selectAndUpload(uri: Uri) {
        var pass = false
        var fileType: String? = ""
        val exts = arrayOf("png", "jpg", "jpeg", "bmp", "pdf", "doc", "docx")
        try {
            fileType = UriUtil.checkFileType(uri)
            LogUtil.w("fileType: $fileType")
            if (exts.contains(fileType)) {
                pass = true
            }
        } catch (e: IOException) {
            e.printStackTrace()
            // 无效路径弹窗 有可能是因为选择了【近期的文件】列表中的文件
            // 无效路径弹窗 有可能是因为选择了【近期的文件】列表中的文件
            CenterActionDialog.Builder(requireActivity())
                .setTitle(getString(R.string.upload_failed))//设置则展示标题，否则不展示
                .setContent(getString(R.string.unfortunately_the_files_again)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                .build()
                .showDialog()
        }

        // 类型不符合 弹窗提示
        if (!pass) {
            CenterActionDialog.Builder(requireActivity())
                .setTitle(getString(R.string.file_type_restriction))//设置则展示标题，否则不展示
                .setContent(getString(R.string.please_choose_a_format)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok)) //设置单个按钮文本
                .build()
                .showDialog()
            return
        }
        val size = UriUtil.getFileSize(uri)
        // 超过15MB 弹窗提示
        if (size > (1024 * 1024 * 15)) {
            CenterActionDialog.Builder(requireActivity())
                .setTitle(getString(R.string.file_size_restriction))//设置则展示标题，否则不展示
                .setContent(getString(R.string.the_selected_file_exceeds)) //设置内容
                .setSingleButton(true) //展示一个按钮，默认两个按钮
                .setSingleButtonText(getString(R.string.ok).ifNull()) //设置单个按钮文本
                .build()
                .showDialog()
            return
        }
        syncUploadState(UploadBean(uri = uri, type = fileType))
    }

    private fun syncUploadState(imageBean: UploadBean? = null, list: ArrayList<UploadBean>? = null) {
        val dataList = mutableListOf<UploadBean>().apply {
            addAll(getAdapterAvailableData())
            if (imageBean != null) {
                add(imageBean)
            }
            if (list?.isNotEmpty() == true) {
                addAll(list)
            }
        }
        binding.tvMaxUploadWarnTip.isVisible = dataList.size >= 6
        adapter.setList(mutableListOf<UploadBean>().apply {
            addAll(dataList)
            if (dataList.size < 6) {
                add(UploadBean())
            }
        })

        verifyHandler.change(dataList.isNotEmpty())
    }

    private fun getAdapterAvailableData(): List<UploadBean> {
        return adapter.data.filter { it.path?.isNotEmpty() == true || it.uri != null }
    }

    override fun onResume() {
        super.onResume()
        // 神策自定义埋点(v3500)，页面浏览事件
        sensorsTrackPageView()
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面浏览 -> 开户验证页面加载完成时触发
     */
    private fun sensorsTrackPageView() {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "Lv2.ID Authentication") // 验证阶段
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_VIEW, properties)
    }

    /**
     * 神策自定义埋点(v3500)
     * 开户及验证页面点击 -> 开户验证页面按钮点击成功时触发
     */
    private fun sensorsTrackClick(buttonName: String) {
        val properties = JSONObject()
        properties.put(SensorsConstant.Key.IDENTITY_LEVEL, "Lv2.ID Authentication") // 验证阶段
        properties.put(SensorsConstant.Key.IDENTITY_STEP, "ID Photo") // 验证步骤
        properties.put(SensorsConstant.Key.BUTTON_NAME, buttonName) // 按钮名称
        SensorsDataUtil.track(SensorsConstant.V3500.OPEN_IDENTITY_PAGE_CLICK, properties)
    }

    companion object {
        fun newInstance() = OpenLv2UploadFragment()
    }

}