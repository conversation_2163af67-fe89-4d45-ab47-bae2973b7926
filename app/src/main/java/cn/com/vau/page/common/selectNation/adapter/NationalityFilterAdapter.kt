package cn.com.vau.page.common.selectNation.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.SelectNationalityObjDetail

class NationalityFilterAdapter(
    var mContext: Context,
    var list: ArrayList<SelectNationalityObjDetail>?, var listener: NationalityFilterAdapter.onItemClickListener?) : RecyclerView.Adapter<NationalityFilterAdapter.ViewHolder>() {


    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder = ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_select_country_number, parent, false))

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val bean = list?.elementAtOrNull(position)
        holder.itemView.findViewById<TextView>(R.id.tvCountryName)?.text = bean?.nationality

        if(position == (list?.size?:0) - 1) {
            holder.itemView.findViewById<View>(R.id.gapline).visibility = View.GONE
        } else {
            holder.itemView.findViewById<View>(R.id.gapline).visibility = View.VISIBLE
        }

        holder.itemView.setOnClickListener {
            listener?.onItemSelect(position)
        }
    }

    fun refreshData(list: ArrayList<SelectNationalityObjDetail>) {
        this.list = list
        notifyDataSetChanged()
    }

    override fun getItemCount(): Int {
        return list?.size?:0
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view)

    interface onItemClickListener {
        fun onItemSelect(position: Int)
    }

}