package cn.com.vau.page.user.asicOpenAccount.activity

import android.annotation.SuppressLint
import android.app.DatePickerDialog
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.common.view.CustomTextWatcher
import cn.com.vau.data.account.RealAccountCacheObj
import cn.com.vau.data.account.SelectNationalityObjDetail
import cn.com.vau.databinding.ActivityAsicOpenAcountFirstBinding
import cn.com.vau.page.ResidenceEvent
import cn.com.vau.page.common.selectNation.SelectNationalityActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.html.HtmlActivity
import cn.com.vau.page.user.asicOpenAccount.viewmodel.AsicOpenViewModel
import cn.com.vau.page.user.bindEmail.BindEmailActivity
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.RegexUtil
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.widget.dialog.CenterActionDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.Calendar

@SuppressLint("SetTextI18n", "NotifyDataSetChanged")
class OpenAccountFirstActivity : BaseMvvmActivity<ActivityAsicOpenAcountFirstBinding, AsicOpenViewModel>() {

    private var isShowConfirmEmailDialog = true
    private var isNext = false

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        mViewModel.isFrom = intent?.extras?.getInt(Constants.SOUCE_OPEN_ACOUNT) ?: 0
        if (mViewModel.isFrom == -1) {
            isShowConfirmEmailDialog = false
        }
        if (mViewModel.isFrom == -1) {
            mViewModel.isFrom = 0
        }
    }

    override fun initView() {
        // 提示加 *
        mBinding.apply {
            tvFirstName.text = "${getString(R.string.first_name)}*"
            tvLastName.text = "${getString(R.string.last_name)}*"
            tvDateOfBirth.text = "${getString(R.string.date_of_birth)}* (DD/MM/YYYY)"
            tvNationality.text = "${getString(R.string.nationality)}*"
            tvEmail.text = "${getString(R.string.email)}*"

            etFirstName.hint = "${getString(R.string.first_name)}*"
            etLastName.hint = "${getString(R.string.last_name)}*"
            etMiddleName.hint = "${getString(R.string.middle_name)} ${getString(R.string.optional)}"
            etDateOfBirth.hint = "${getString(R.string.date_of_birth)}* (DD/MM/YYYY)"
            etNationality.hint = "${getString(R.string.nationality)}*"
            etEmail.hint = "${getString(R.string.email)}*"
        }

        // 神策自定义埋点(v3500)，页面浏览事件
        mViewModel.sensorsOpenIdentityPageView()
    }

    override fun initData() {
        super.initData()
        mViewModel.getAccountSelect()
    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle, Lifecycle.State.RESUMED).collectLatest {
                if (it is DataEvent) {
                    when (it.tag) {
                        Constants.EVENT_TAG_ASIC_FIRST_GET_DATA -> {
                            val data = it.data as? RealAccountCacheObj
                            val asicTxt = "Vantage Global Prime Pty Ltd's "
                            val urlTextAsic = getString(R.string.privacy_policy)
                            mBinding.tvVantageLink.text = asicTxt + urlTextAsic
                            mBinding.tvVantageLink.set(urlTextAsic, AttrResourceUtil.getColor(this@OpenAccountFirstActivity, R.attr.color_c1e1e1e_cebffffff)) {
                                goAgreementWeb()//跳转到协议页面
                            }

                            mBinding.etEmail.isEnabled = data?.updateEmail == true
                            mBinding.etEmail.setText(data?.email ?: "")

                            if (!isShowConfirmEmailDialog && !TextUtils.isEmpty(mBinding.etEmail.text.toString())) {
                                mBinding.etEmail.isEnabled = false
                            }

                            mBinding.etFirstName.setText(data?.firstName ?: "")
                            mBinding.etMiddleName.setText(data?.middleName ?: "")
                            mBinding.etLastName.setText(data?.lastName ?: "")
                            mBinding.etNationality.text = data?.nationalityName ?: ""
                            val birth = data?.dob?.split("-")
                            if ((birth?.size ?: 0) >= 3) {
                                mBinding.etDateOfBirth.text =
                                    "${birth?.elementAtOrNull(2)}/${birth?.elementAtOrNull(1)}/${birth?.elementAtOrNull(0)}"
                            }

                            mBinding.etEmail.setOnFocusChangeListener { _, hasFocus ->
                                val email = mBinding.etEmail.text.toString().trim()
                                if (!hasFocus && RegexUtil.isEmail(email)) {
                                    mViewModel.emailIsExistApi(email)
                                }
                            }

                            checkTitleTextViewShow()
                        }

                        Constants.EVENT_TAG_ASIC_FIRST_EXIST_EMAIL -> {
                            val email = it.data as? String
                            CenterActionDialog.Builder(this@OpenAccountFirstActivity)
                                .setContent(getString(R.string.this_email_has_would_account))
                                .setStartText(getString(R.string.cancel))
                                .setEndText(getString(R.string.link))
                                .setOnEndListener {
                                    val bundle = Bundle()
                                    bundle.putString("email", email)
                                    openActivity(BindEmailActivity::class.java, bundle)
                                }
                                .build()
                                .showDialog()
                        }

                        Constants.EVENT_TAG_ASIC_FIRST_SAVE_DATA -> {
                            val bundle = Bundle()
                            bundle.putInt(Constants.SOUCE_OPEN_ACOUNT, mViewModel.isFrom)
                            openActivity(OpenAccountFirstSecondActivity::class.java, bundle)
                            EventBus.getDefault().post(NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE)
                        }
                    }
                }
            }
        }
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvNext.setOnClickListener(this)
        mBinding.etDateOfBirth.setOnClickListener(this)
        mBinding.etNationality.setOnClickListener(this)
        mBinding.mHeaderBar.setStartBackIconClickListener {
            finish()
        }

        mBinding.mHeaderBar.setEndIconClickListener {
            finish()
        }

        mBinding.mHeaderBar.setEndIcon1ClickListener {
            openActivity(HelpCenterActivity::class.java)
        }

        val textChangeListener: CustomTextWatcher = object : CustomTextWatcher() {
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                checkTitleTextViewShow()
            }
        }

        mBinding.etFirstName.addTextChangedListener(textChangeListener)

        mBinding.etMiddleName.addTextChangedListener(textChangeListener)

        mBinding.etLastName.addTextChangedListener(textChangeListener)

        mBinding.etDateOfBirth.addTextChangedListener(textChangeListener)

        mBinding.etEmail.addTextChangedListener(textChangeListener)
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.etDateOfBirth -> showSelectDateDialog()
            R.id.etNationality -> {
                val bundle = Bundle()
                bundle.putString(
                    "selectNationalityId", mViewModel.selectNationData?.id
                        ?: "0"
                )
                openActivity(SelectNationalityActivity::class.java, bundle, Constants.SELECT_NATION)
            }

            R.id.tvNext -> {
                if (!isNext) return
                if (isShowConfirmEmailDialog)
                    showConfirmDialog(mBinding.etEmail.text.toString())
                else
                    goNext()
            }
        }
    }

    // 下一步
    fun goNext() {
        val firstName = mBinding.etFirstName.text.toString().trim()
        val middleName = mBinding.etMiddleName.text.toString().trim()
        val lastName = mBinding.etLastName.text.toString().trim()
        val birth = mBinding.etDateOfBirth.text.toString().trim()
        val email = mBinding.etEmail.text.toString().trim()
        val birthArray = birth.split("/")
        mViewModel.saveStep1Data(
            hashMapOf<String, Any>(
                "token" to UserDataUtil.loginToken(),
                "step" to "1-1",
                "email" to email,
                "firstName" to firstName,
                "middleName" to middleName,
                "lastName" to lastName,
                "nationalityId" to "${mViewModel.selectNationData.id ?: -1}",
                "dob" to "${birthArray.elementAtOrNull(2)}-${birthArray.elementAtOrNull(1)}-${birthArray.elementAtOrNull(0)}",
                "supervisionType" to mViewModel.cacheData?.supervisionType.ifNull()
            )
        )
        // 神策自定义埋点(v3500)，点击事件
        mViewModel.sensorsTrackClick()
    }

    fun checkTitleTextViewShow() {  // 檢查各欄位textView title是否顯示
        mBinding.apply {
            tvFirstName.visibility =
                if (!TextUtils.isEmpty(etFirstName.text) && etFirstName.visibility == View.VISIBLE) View.VISIBLE else View.GONE
            tvMiddleName.visibility =
                if (!TextUtils.isEmpty(etMiddleName.text) && etMiddleName.visibility == View.VISIBLE) View.VISIBLE else View.GONE
            tvLastName.visibility =
                if (!TextUtils.isEmpty(etLastName.text) && etLastName.visibility == View.VISIBLE) View.VISIBLE else View.GONE
            tvNationality.visibility =
                if (!TextUtils.isEmpty(etNationality.text) && etNationality.visibility == View.VISIBLE) View.VISIBLE else View.GONE
            tvDateOfBirth.visibility =
                if (!TextUtils.isEmpty(etDateOfBirth.text) && etDateOfBirth.visibility == View.VISIBLE) View.VISIBLE else View.GONE
            tvEmail.visibility =
                if (!TextUtils.isEmpty(etEmail.text) && etEmail.visibility == View.VISIBLE) View.VISIBLE else View.GONE
        }
        initNextView()
    }

    private fun initNextView() {
        if (mBinding.etEmail.length() > 0 && mBinding.etFirstName.length() > 0
            && mBinding.etLastName.length() > 0
            && !TextUtils.isEmpty(mBinding.etNationality.text.toString())
            && !TextUtils.isEmpty(mBinding.etDateOfBirth.text.toString())
        ) {
            isNext = true
            mBinding.tvNext.setBackgroundResource(R.drawable.bitmap_icon2_next_active)
        } else {
            isNext = false
            mBinding.tvNext.setBackgroundResource(R.drawable.bitmap_icon2_next_inactive)
        }
    }

    private fun showConfirmDialog(email: String?) {
        CenterActionDialog.Builder(this)
            .setTitle("${getString(R.string.please_check_your_email)}:")
            .setContent(email)
            .setStartText(getString(R.string.edit))
            .setEndText(getString(R.string.confirm))
            .setOnStartListener {
                mBinding.etEmail.text = null
                mBinding.etEmail.isFocusable = true
                mBinding.etEmail.isFocusableInTouchMode = true
                mBinding.etEmail.requestFocus()
                KeyboardUtil.openKeyboard(mBinding.etEmail, this@OpenAccountFirstActivity)
            }
            .setOnEndListener {
                goNext()
            }
            .build()
            .showDialog()
    }

    /**
     * 展示时间选择弹窗
     */
    @SuppressLint("WrongConstant")
    private fun showSelectDateDialog() {
        val calendar = Calendar.getInstance()
        val dateDialog = DatePickerDialog(
            this, R.style.VFXDateDialogTheme,
            DatePickerDialog.OnDateSetListener { _, year, monthOfYear, dayOfMonth ->
                mBinding.etDateOfBirth.text =
                    "${if (dayOfMonth < 10) "0" else ""}$dayOfMonth/${if (monthOfYear < 9) "0" else ""}${monthOfYear + 1}/$year"
                initNextView()
            }, calendar.get(Calendar.YEAR), calendar
                .get(Calendar.MONTH), calendar
                .get(Calendar.DAY_OF_MONTH)
        )

        dateDialog.datePicker.maxDate = TimeUtil.getBeforeYearTime(18)
        dateDialog.datePicker.minDate = TimeUtil.frontDateByDays(TimeUtil.getBeforeYearTime(80), -1)
        dateDialog.show()
    }

    /**
     * 跳转到协议页面
     */
    fun goAgreementWeb() {
        val bundle = Bundle()
        bundle.putInt("tradeType", 15)
        bundle.putString("title", getString(R.string.privacy_policy))
        openActivity(HtmlActivity::class.java, bundle)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataEvent(event: DataEvent) {
        if (event.data is ResidenceEvent) {
            val data: ResidenceEvent = event.data
            if (TextUtils.isEmpty(data.cityCode)) {
                mViewModel.selectPlaceOfBirth = data
                initNextView()
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (resultCode) {
            RESULT_OK -> {
                mViewModel.selectNationData =
                    data?.getSerializableExtra(Constants.SELECT_NATIONALITY_DATA) as SelectNationalityObjDetail
                mBinding.etNationality.text = mViewModel.selectNationData?.nationality
                mBinding.tvNationality.visibility = View.VISIBLE
                initNextView()
            }
        }
    }

    override fun useEventBus(): Boolean = true
}