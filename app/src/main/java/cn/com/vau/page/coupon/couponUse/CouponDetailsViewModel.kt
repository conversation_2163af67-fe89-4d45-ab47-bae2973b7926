package cn.com.vau.page.coupon.couponUse

import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.data.depositcoupon.DepositCouponDetail
import cn.com.vau.data.depositcoupon.TransferAccountInfo
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.tracking.SensorsDataUtil
import org.json.JSONObject

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 14 14:48
 * @updateUser:
 * @updateDate: 2025 4月 14 14:48
 */
class CouponDetailsViewModel : BaseViewModel() {

    var currentCoupon: DepositCouponDetail? = null
    var currentAccount: String? = UserDataUtil.accountCd()
    var currentAccountName: String? = null
    var currentCurrency = UserDataUtil.currencyType()

    fun currencyTransform() {
        requestNet({
            val map = hashMapOf<String, Any>().apply {
                put("token", UserDataUtil.loginToken())
                put("login", UserDataUtil.accountCd())
                put("currency", currentCurrency)
                put("couponId", currentCoupon?.couponId ?: "")
                put("userCouponId", currentCoupon?.userCouponId ?: "")
            }
            baseService.currencyTransform(map)
        }, {
            if (!it.isSuccess()) {
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_COUPON_MONEY, it.data?.obj?.money ?: "0"))
        }, isShowDialog = true)
    }

    fun crmMemberMt4AccountIdList() {
        requestNet({
            val map = hashMapOf<String, String>()
            map["token"] = UserDataUtil.loginToken()
            map["loginUserId"] = UserDataUtil.userId()
            map["mt4AccountId"] = UserDataUtil.accountCd()
            baseService.crmMemberMt4AccountIdList(map)
        }, { data ->
            if (!data.isSuccess()) {
                ToastUtil.showToast(data.getResponseMsg())
                return@requestNet
            }
            data.data?.obj?.fromMT4AccountList
                ?.filter { it.mt4AccountType == "1" || it.mt4AccountType == "5" }
                ?.let { list ->
                    if (UserDataUtil.isDemoAccount()) {
                        val selectAccount = list.firstOrNull() as? TransferAccountInfo
                        currentAccount = selectAccount?.code
                        currentCurrency = selectAccount?.currency.ifNull()
                        currentAccountName = selectAccount?.name
                    } else {
                        run breaking@{
                            list.forEachIndexed { index, it ->
                                if (it.code == currentAccount) {
                                    currentAccountName = it.name
                                    return@breaking
                                }
                            }
                        }
                    }
                    sendEvent(DataEvent(EVENT_ACCOUNT_LIST, list))
                }
        }, isShowDialog = true)
    }

    fun inviteWithdraw() {
        requestNet({
            val paramMap = hashMapOf<String, Any>()
            paramMap["token"] = UserDataUtil.loginToken()
            paramMap["login"] = currentAccount ?: ""
            paramMap["currency"] = currentCurrency
            paramMap["money"] = currentCoupon?.amount ?: ""
            paramMap["serverId"] = ""
            paramMap["couponId"] = currentCoupon?.couponId ?: ""
            paramMap["userCouponId"] = currentCoupon?.userCouponId ?: ""
            paramMap["version"] = "2"
            baseService.inviteWithdraw(paramMap)
        }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            sendEvent(DataEvent(EVENT_COUPON_FINISH))
        }, isShowDialog = true)
    }

    /**
     * 神策自定义埋点(v3710)
     * 优惠券夹页_主页面浏览
     */
    fun sensorsTrack(point: String) {
        currentCoupon?.let {
            val properties = JSONObject()
            properties.put("coupon_end_time", it.etime)
            properties.put("coupon_id", it.couponId)
            properties.put("coupon_name", it.couponDes)
            properties.put("coupon_amount", it.amount)
            properties.put(
                "coupon_type", when (it.couponType) {
                    "1" -> "FULL_DISCOUNT_COUPON"
                    "3" -> "CASH_COUPON"
                    "4" -> "CREDIT_COUPON"
                    "5" -> "WIPE_LOSS_COUPON"
                    "6" -> "FULL_ADD_COUPON"
                    "7" -> "FREE_ORDER_COUPON"
                    "9" -> "LOSS_PROTECTION_COUPON"
                    "10" -> "DEPOSIT_REBATE_COUPON"
                    "11" -> "COMMISSION_FREE_COUPON"
                    "12" -> "PROFIT_BOOSTER_COUPON"
                    "13" -> "LOSS_RESCUE"
                    "14" -> "DEPOSIT_DISCOUNT"
                    else -> ""
                }
            )
            SensorsDataUtil.track(point, properties)
        }
    }

    companion object {
        const val EVENT_COUPON_MONEY = "EVENT_COUPON_MONEY"
        const val EVENT_COUPON_FINISH = "EVENT_COUPON_FINISH"
        const val EVENT_ACCOUNT_LIST = "EVENT_ACCOUNT_LIST"
    }
}