package cn.com.vau.page.user.openAccoGuide.lv3.vm

import android.net.Uri
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import cn.com.vau.common.base.mvvm.BaseViewModel
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.AccoSelectData
import cn.com.vau.data.account.AccoSelectItem
import cn.com.vau.data.account.GetProcessData
import cn.com.vau.data.account.GetProcessObj
import cn.com.vau.data.account.SaveProcessData
import cn.com.vau.data.account.UploadFileData
import cn.com.vau.data.account.UploadFileObj
import cn.com.vau.data.account.UploadImageBean
import cn.com.vau.page.UploadBean
import cn.com.vau.page.user.openAccoGuide.OpenAccoRepository
import cn.com.vau.util.ImageUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import kotlinx.coroutines.launch
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.toRequestBody

class OpenLv3ViewModel : BaseViewModel() {

    var selectedPOAType: AccoSelectItem? = null
    private val repository by lazy { OpenAccoRepository() }

    var inputAddress: String? = null
    var inputCityResidence: String? = null
    var getProcessLiveData = MutableLiveData<GetProcessData>()
    var accountSelectLiveData = MutableLiveData<AccoSelectData>()
    var saveProcessLiveData2 = MutableLiveData<SaveProcessData>()
    var selectedPhotoLiveData = MutableLiveData<UploadBean>()
    var uploadPhotoLiveData = MutableLiveData<UploadImageBean>()
    var poaTypeLiveData = MutableLiveData<Int>()
    var openData: GetProcessObj? = null
    var destroyUploadPage = MutableLiveData<Boolean>()
    var initUploadPage = MutableLiveData<Boolean>()
    var environmentStoragePermission = MutableLiveData<Boolean>()

    fun pickPhoto(photo: UploadBean) {
        selectedPhotoLiveData.value = photo
    }

    fun clearUploadData() {
        destroyUploadPage.value = true
    }

    fun initUploadData() {
        initUploadPage.value = true
    }

    fun getEnvironmentSuccessful() {
        environmentStoragePermission.value = true
    }

    fun getProcess() {
        repository.getProcess(
            hashMapOf(
                "token" to UserDataUtil.loginToken(),
                "step" to "5",
                "openAccountMethod" to "1"
            )
        ).bindTLiveData(getProcessLiveData)
    }

    fun getAccountSelect() {
        repository.getAccountSelect()
            .compose(applyLoading(true))
            .bindTLiveData(accountSelectLiveData)
    }

    fun saveProcess(param: HashMap<String, Any>) {
        repository.saveProcess(param)
            .compose(applyLoading(true))
            .bindTLiveData(saveProcessLiveData2)
    }

    fun eachUpload(photo: UploadBean?) {
        if (photo == null) {
            uploadPhotoLiveData.value = applyResult()
            return
        }
        if (photo.isTakeForOpenDate) {
            uploadPhotoLiveData.value = applyResult(photo.path)
            return
        }

        var uri: Uri? = null
        if (photo.uri != null) {
            uri = photo.uri
        } else {
            hideLiveDataLoading()
            ToastUtil.showToast("Failed to get the file")
            return
        }
        val builder = MultipartBody.Builder()
            .setType(MultipartBody.FORM)//表单类型
            .addFormDataPart(
                "token",
                UserDataUtil.loginToken()
            ) // ParamKey.TOKEN 自定义参数key常量类，即参数名
        uri?.let {
            viewModelScope.launch {
                ImageUtil.compressImageToStream(uri = it)?.let { inputStream ->

                    val byteArray = inputStream.readBytes()

                    val requestBody = byteArray.toRequestBody("multipart/form-data".toMediaTypeOrNull(), 0, byteArray.size)

                    builder.addFormDataPart("imgFile", "${System.currentTimeMillis()}.${photo.type}", requestBody) // imgfile 后台接收图片流的参数名
                    val parts = builder.build()
                    repository.uploadFile(parts).bindTLiveData(uploadPhotoLiveData)
                }
            }
        }
    }

    private fun applyResult(path: String? = ""): UploadImageBean {
        return UploadImageBean().apply {
            resultCode = "V00000"
            data = UploadFileData(UploadFileObj(imgFile = path.ifNull()))
        }
    }
}