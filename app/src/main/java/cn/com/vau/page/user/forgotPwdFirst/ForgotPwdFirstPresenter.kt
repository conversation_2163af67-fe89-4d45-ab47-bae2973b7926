package cn.com.vau.page.user.forgotPwdFirst

import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.util.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class ForgotPwdFirstPresenter : ForgetPwdFirstContract.Presenter() {

    var areaCodeData: SelectCountryNumberObjDetail? = null
    var handleType = 0
    var txId = ""

    var isShowEmail = false

    var smsSendType = VerificationActivity.TYPE_SEND_SMS

    override fun initCode() {
        val countryCode = SpManager.getCountryCode(Constants.defaultCountryCode)
        val countryNum = SpManager.getCountryNum(Constants.defaultCountryNum)
        val countryName = SpManager.getCountryName(
            if (countryNum == Constants.defaultCountryNum) Constants.defaultCountryName else ""
        )
        areaCodeData = SelectCountryNumberObjDetail()
        areaCodeData?.countryCode = countryCode
        areaCodeData?.countryNum = countryNum
        areaCodeData?.countryName = countryName
    }

    /**
     * 获取修改密码的验证码
     */
    override fun getVerificationCodeApi(count: String?, validateCode: String) {
        if (TextUtils.isEmpty(count)) {
            return
        }
        val map = hashMapOf<String, Any>()
        map["smsSendType"] = smsSendType
        if (validateCode.isNotEmpty()) {
            map["recaptcha"] = validateCode
            map["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        if (smsSendType == VerificationActivity.TYPE_SEND_EMAIL) {
            map["count"] = count ?: ""
        } else {
            val phoneCountryCode = areaCodeData?.countryCode ?: Constants.defaultCountryCode
            val code = areaCodeData?.countryNum ?: Constants.defaultCountryNum
            if ((code == "86" && count?.length != 11) || (code != "86" && (count?.length ?: 0) > 15)) {
                ToastUtil.showToast(mView?.ac?.getString(R.string.please_enter_the_number))
                return
            }
            map["count"] = count ?: ""
            map["countryCode"] = phoneCountryCode
            map["code"] = code
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mView?.showNetDialog()
        mModel?.getVerificationCodeApi(paramMap, object : BaseObserver<ForgetPwdVerificationCodeBean>() {
            override fun onNext(data: ForgetPwdVerificationCodeBean) {
                SpManager.putSmsCodeId("")
                mView?.hideNetDialog()

                if (data.resultCode == "V10060") {//易盾 需要滑动窗口
                    mView?.showCaptcha()
                    SpManager.putSmsCodeId(data.data?.obj?.smsCodeId ?: "")
                    return
                }
                if (data.resultCode != "V00000") {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                ToastUtil.showToast(data.msgInfo)
                if (smsSendType == VerificationActivity.TYPE_SEND_EMAIL) {
                    txId = data.data?.obj?.txId ?: ""
                    // 邮箱
                    val userEmailHistory = UserEmailHistory()
                    userEmailHistory.email = count
                    DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
                } else {
                    val phoneCountryCode = areaCodeData?.countryCode ?: Constants.defaultCountryCode
                    val code = areaCodeData?.countryNum ?: Constants.defaultCountryNum
                    SpManager.putUserTel(count.ifNull())
                    SpManager.putCountryCode(phoneCountryCode)
                    SpManager.putCountryNum(code)
                    SpManager.putCountryName(areaCodeData?.countryName.ifNull())
                }

                mView?.goForgetSecond()

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 已选择的区号数据
     */
    override fun setSelectAreaData(areaCodeData: SelectCountryNumberObjDetail) {
        this.areaCodeData = areaCodeData
    }

}