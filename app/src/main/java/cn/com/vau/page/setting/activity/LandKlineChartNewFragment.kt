package cn.com.vau.page.setting.activity

import android.annotation.SuppressLint
import android.content.pm.ActivityInfo
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.view.Gravity
import android.view.View
import android.view.ViewStub
import android.widget.FrameLayout
import androidx.activity.OnBackPressedCallback
import androidx.core.content.ContextCompat
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.GridLayoutManager
import cn.com.vau.R
import cn.com.vau.common.application.InitHelper
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseMvvmBindingFragment
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.VAUSdkUtil
import cn.com.vau.common.view.kchart.adapter.PositionOrderAdapter
import cn.com.vau.common.view.kchart.toolbar.ToolStyleConstants.COLOR_4
import cn.com.vau.common.view.kchart.toolbar.ToolStyleConstants.LINE_WIDTH_0
import cn.com.vau.common.view.kchart.toolbar.ToolbarCallbackImpl
import cn.com.vau.common.view.popup.bean.KLineViewSettingData
import cn.com.vau.data.init.ShareProductData
import cn.com.vau.databinding.FragmentLandKlineChartNewBinding
import cn.com.vau.databinding.IncludeLandKlineChartTipsLayoutBinding
import cn.com.vau.databinding.VsLayoutNoDataBinding
import cn.com.vau.page.setting.viewmodel.KLineChartNewViewModel
import cn.com.vau.trade.activity.KLineActivity
import cn.com.vau.trade.adapter.LandKLineChartIntervalTabAdapter
import cn.com.vau.trade.kchart.ChartUIParamUtil
import cn.com.vau.trade.kchart.KLineDataUtils
import cn.com.vau.trade.kchart.pop.LandDrawerKChartSettingsDialog
import cn.com.vau.trade.model.KLineViewModel
import cn.com.vau.util.AppUtil
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.FontResourceUtil
import cn.com.vau.util.LogUtil
import cn.com.vau.util.TimeUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.dp2px
import cn.com.vau.util.formatProductPrice
import cn.com.vau.util.ifNull
import cn.com.vau.util.language.LanguageHelper
import cn.com.vau.util.mathDiv
import cn.com.vau.util.mathMul
import cn.com.vau.util.mathSub
import cn.com.vau.util.numCurrencyFormat
import cn.com.vau.util.numFormat
import cn.com.vau.util.setTextColorDiff
import cn.com.vau.util.setTextDiff
import cn.com.vau.util.toDoubleCatching
import cn.com.vau.util.toFloatCatching
import cn.com.vau.util.toLongCatching
import cn.com.vau.util.tracking.BuryPointConstant
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.base.BottomListDialog
import com.github.tifezh.kchartlib.chart.BaseKChartView.OnTakeProfitStopLossChanged
import com.github.tifezh.kchartlib.chart.draw.MainDraw
import com.github.tifezh.kchartlib.chart.formatter.DateFormatter
import com.github.tifezh.kchartlib.helper.bean.KLineEntity
import com.github.tifezh.kchartlib.helper.bean.KlineEnum
import com.github.tifezh.kchartlib.helper.bean.KlineMainEnum
import com.github.tifezh.kchartlib.helper.bean.KlineOtherEnum
import com.github.tifezh.kchartlib.helper.chart.KChartAdapter
import com.upex.common.drawTools.DrawToolsLineStyle
import com.upex.common.drawTools.DrawToolsType
import com.upex.common.language.Keys
import com.upex.common.utils.display.DisplayUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import org.json.JSONObject
import java.util.concurrent.CopyOnWriteArrayList

class LandKlineChartNewFragment : BaseMvvmBindingFragment<FragmentLandKlineChartNewBinding>() {
    companion object {
        private const val TAG = "LandKlineChartNewFragment"
        fun instance(): LandKlineChartNewFragment = LandKlineChartNewFragment()
    }

    private val mViewModel: KLineChartNewViewModel by activityViewModels()
    private val activityViewModel: KLineViewModel by activityViewModels()
    private var mAdapter = KChartAdapter()
    private val color_c0a1e1e1e_c262930 by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c0a1e1e1e_c262930) }
    private val color_ca61e1e1e_c99ffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_ca61e1e1e_c99ffffff) }
    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff) }
    private val color_cebffffff_c1e1e1e by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_cebffffff_c1e1e1e) }
    private val mainLayoutBg by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.mainLayoutBg) }
    private val imgKlineLogo by lazy { AttrResourceUtil.getDrawable(requireContext(), R.attr.imgKlineLogo) }
    private val color_c731e1e1e_c61ffffff by lazy { AttrResourceUtil.getColor(requireContext(), R.attr.color_c731e1e1e_c61ffffff) }
    private val c00c79c by lazy { ContextCompat.getColor(requireContext(), R.color.c00c79c) }
    private val ce35728 by lazy { ContextCompat.getColor(requireContext(), R.color.ce35728) }
    private val transparent by lazy { ContextCompat.getColor(requireContext(), R.color.transparent) }
    private val childH by lazy { resources.getDimension(com.example.myapplication.R.dimen.child_height) }
    private var firstPageSize = 0
    private var klineChartTipsLayoutBinding: IncludeLandKlineChartTipsLayoutBinding? = null

    /** 设置弹窗 */
    private var settingPopup: LandDrawerKChartSettingsDialog? = null

    private val intervalAdapter by lazy {
        LandKLineChartIntervalTabAdapter()
    }
    private var currentDrawName = ""
    private var dragTipShowed = false
//    private val selectOrderPopUp: SelectOrderPopupWindow by lazy {
//        SelectOrderPopupWindow(requireActivity())
//    }
    private val positionAdapter by lazy { PositionOrderAdapter(requireContext(), CopyOnWriteArrayList(mViewModel.shareOrderList),
        KLineDataUtils.selectedOrderNo) }
    private val positionDialog by lazy {
        BottomListDialog.Builder(requireActivity())
            .setViewMode(true)
            .setMaxHeight(268.dp2px())
            .setTitle(getString(R.string.select_order))
            .setAdapter(positionAdapter)
            .build()
    }

    private fun updateProdInfo() {
        if (isDestroyed()) return
        if (InitHelper.isNotSuccess()) return
        val dataBean = mViewModel.data ?: return

        mBinding.run {
            val bid = if (dataBean.bidUI == "-") Constants.DOUBLE_LINE else dataBean.bidUI
            kCharView.setCurrentPrice(dataBean.bid)
            if(dataBean.lasttime != "0") {
                mAdapter.updateLastData(dataBean.originalBid)
            }
            kCharView.setBuyPrice(dataBean.originalAsk)
            kCharView.setSellPrice(dataBean.originalBid)
            tvBoardSellPrice.text = bid
            val roseSign = if (dataBean.rose > 0) "+" else ""
            val diffSign = if (dataBean.diff > 0) "+" else ""
            tvBoardDiff.setTextDiff(buildString {
                append(diffSign)
                append(dataBean.diffUI)
                append(" ($roseSign${dataBean.roseUI}%)")
            })
            tvBoardDiff.setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    if (dataBean.diff < 0) R.color.ce35728 else R.color.c00c79c
                )
            )
            tvBoardTime.setTextDiff(TimeUtil.getSocketDateToString(dataBean.lasttime))
            initTimeShareChart()
        }
    }

    /**
     * 涨跌额、涨跌幅、振幅
     */
    private fun updateChartTip(chartsBean: KLineEntity?){
        if (chartsBean == null) {
            klineChartTipsLayoutBinding?.tvChartTipChangeAmountValue?.text = "--"
            klineChartTipsLayoutBinding?.tvChartTipChangePercentValue?.text = "--"
            klineChartTipsLayoutBinding?.tvChartTipChangeAmountValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            klineChartTipsLayoutBinding?.tvChartTipChangePercentValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            klineChartTipsLayoutBinding?.tvChartTipAmplitudeValue?.text = "0.0"
            return
        }
        val closeValue = chartsBean.close.toString().ifNull()
        val prevCloseValue = chartsBean.prevClose?.toString().ifNull()
        val highValue = chartsBean.high.toString().ifNull()
        val lowValue = chartsBean.low.toString().ifNull()
        val openValue = chartsBean.open.toString().ifNull()
        if (prevCloseValue.isNotEmpty()) {
            val difference = closeValue.mathSub(prevCloseValue)//收盘价-前周期收盘价 或 最新价-前周期收盘价，**这里收盘价和最新价统一使用了close字段，drawNextCandle 和 updateProdInfo方法中有对close设置最新价逻辑**
            //涨跌额： 历史涨跌额：收盘价-前周期收盘价；当前涨跌额：最新价-前周期收盘价。
            val changeAmountValue = difference.numFormat(ChartUIParamUtil.digits, true)
            klineChartTipsLayoutBinding?.tvChartTipChangeAmountValue?.text = changeAmountValue
            //涨跌幅： 历史涨跌幅：(收盘价-前周期收盘价)/前周期收盘价*100%； 当前涨跌幅：(最新价-前周期收盘价)/前周期收盘价*100%
            val changeNum = difference.mathDiv(prevCloseValue, 4).mathMul("100")
            val changeStr = changeNum.formatProductPrice(2, false, "0.00") + "%"
            klineChartTipsLayoutBinding?.tvChartTipChangePercentValue?.text = changeStr
            val differenceNum = difference.toDoubleCatching()
            klineChartTipsLayoutBinding?.tvChartTipChangeAmountValue?.setTextColorDiff(
                when {
                    differenceNum > 0 -> ChartUIParamUtil.candleUpColor
                    differenceNum < 0 -> ChartUIParamUtil.candleDownColor
                    else -> ChartUIParamUtil.candleMaxMinTextColor
                }
            )
            val changeNumDouble = changeNum.toDoubleCatching()
            klineChartTipsLayoutBinding?.tvChartTipChangePercentValue?.setTextColorDiff(
                when {
                    changeNumDouble > 0 -> ChartUIParamUtil.candleUpColor
                    changeNumDouble < 0 -> ChartUIParamUtil.candleDownColor
                    else -> ChartUIParamUtil.candleMaxMinTextColor
                }
            )
        } else {
            klineChartTipsLayoutBinding?.tvChartTipChangeAmountValue?.text = "--"
            klineChartTipsLayoutBinding?.tvChartTipChangePercentValue?.text = "--"
            klineChartTipsLayoutBinding?.tvChartTipChangeAmountValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
            klineChartTipsLayoutBinding?.tvChartTipChangePercentValue?.setTextColorDiff(ChartUIParamUtil.candleMaxMinTextColor)
        }
        //振幅： 历史振幅：(同一周期最高价-同一周期最低价)/同一周期开盘价*100%；  当前振幅：(当前周期最高价-当前周期最低价)/当前周期开盘价*100%
        val amplitude = if (openValue.toDoubleCatching() != 0.0) {
            (highValue.mathSub(lowValue)).mathDiv(openValue, 4)
        } else {
            "0.0"
        }
        val amplitudeStr = (amplitude.mathMul("100")).formatProductPrice(2, false, "0.00") + "%"
        klineChartTipsLayoutBinding?.tvChartTipAmplitudeValue?.text = amplitudeStr
    }

    private fun initTimeShareChart() {
        if (!mViewModel.isTimeShare) {
            return
        }
        var rate = 0f
        val dataBean = mViewModel.data
        if (dataBean != null) {
            rate = dataBean.originalBid - dataBean.open
        }
        val color = when {
            rate < 0 -> ce35728
            rate > 0 -> c00c79c
            else -> color_c731e1e1e_c61ffffff
        }
        mBinding.kCharView.setMinuteWhiteColor(color)//分时线颜色
        mBinding.kCharView.setMinuterStartColor(color)
        mBinding.kCharView.setMinuteStopColor(transparent)
    }

    override fun initParam(savedInstanceState: Bundle?) {
        super.initParam(savedInstanceState)
        if (!EventBus.getDefault().isRegistered(this)) EventBus.getDefault().register(this)
        if (KLineDataUtils.userDataKV == null) {
            KLineDataUtils.userDataKV = KLineViewSettingData.getHistoryData()
        }
        currentDrawName = ""
        firstPageSize = 0
        mViewModel.isLandEnableLoadMore = true
        mViewModel.isLandRequestLoadMore = false
    }

    override fun initView() {
        mBinding.tvName.text = mViewModel.symbol
        initKline()
        initDragTip()
        initInterval()
        initToolbar()
        initSideControl()
        changeKChartSize(isChangeSubChild = false)
        mBinding.nestedScrollView.post {
            mBinding.nestedScrollView.scrollTo(0, 0)
        }
    }

    override fun initListener() {
        super.initListener()
        requireActivity().onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                finishLandKLinePage()
                remove()
            }
        })

        mBinding.ivBack.clickNoRepeat {
            finishLandKLinePage()
        }
        mBinding.tvDragTip.clickNoRepeat {
            mBinding.tvDragTip.isVisible = false
        }
        mBinding.tvOrders.clickNoRepeat {
            if (mBinding.kCharView.isDragging) {// 拖动止盈止损中，不允许切换订单
                return@clickNoRepeat
            }
            positionAdapter.refreshData(CopyOnWriteArrayList(mViewModel.shareOrderList), KLineDataUtils.selectedOrderNo)
            positionDialog.showDialog()
//            if (!selectOrderPopUp.isShowing) {
//                selectOrderPopUp.setData(
//                    CopyOnWriteArrayList(mViewModel.shareOrderList),
//                    KLineDataUtils.selectedOrderNo,
//                    getString(R.string.select_order)
//                ).show(requireActivity(), mBinding.root)
//            }
        }
        mBinding.ivKNewGuide.clickNoRepeat(400) {
            val accountType = when {
                !UserDataUtil.isLogin() -> {
                    BuryPointConstant.AccountType.NOLOGIN
                }

                UserDataUtil.isStLogin() -> {
                    BuryPointConstant.AccountType.COPY_TRADING
                }

                UserDataUtil.isDemoAccount() -> {
                    BuryPointConstant.AccountType.DEMO
                }

                else -> {
                    BuryPointConstant.AccountType.LIVE
                }
            }
            LogEventUtil.setLogEvent(
                BuryPointConstant.V3474.TRADE_KLINE_USER_GUIDE_BUTTON_CLICK,
                bundleOf(Pair("Type_of_account", accountType), Pair("Mode", "Lite-horizontal"))
            )
            finishLandKLinePage()
            mViewModel.screenChangeNewGuideLiveData.postValue(Unit)
        }
        mBinding.ivSetting.clickNoRepeat {
            if (!mBinding.kCharView.isDragging) {// 拖动止盈止损中，不允许设置
                showSettingPopup()
            }
        }

        mBinding.drawView.onStep = fun(drawName: String?, currentPointCount: Int?, totalPointCount: Int?, isFinish: Boolean?) {
            LogUtil.v(TAG, "onStep: $drawName, $currentPointCount, $totalPointCount, $isFinish")
            if(!drawName.isNullOrEmpty() && isFinish == true) {
                ToastUtil.showToast(getString(R.string.completed))
            }
            showDrawTips(drawName, currentPointCount, totalPointCount, isFinish ?: true)
            val isContinuation = mBinding.drawView.getContinuation()
            mBinding.klineSideControlSideView.drawViewChange(drawName, currentPointCount, totalPointCount, isFinish, isContinuation)
        }

        mBinding.klineSideControlSideView.onDrawDeleteAll {
            showCenterActionDialog(
                title = getString(R.string.tips) ,
                content = getString(R.string.are_you_sure_your_lines),
                startText = getString(R.string.no),
                endText = getString(R.string.yes_confirm),
                endAction = {
                    mBinding.klineSideControlSideView.cleanAllDraw()
                    mBinding.layoutLandKlineDrawingTips.cardDrawingTipsContainer.isVisible = false
                    mBinding.drawView.deleteAll()
                })
        }

        mBinding.klineSideControlSideView.onShowDrawClick {
            val isShowDraw = if (mBinding.drawView.isVisible) {
                mViewModel.isShowDraw = false
                mBinding.drawView.isVisible = false
                false
            } else {
                mViewModel.isShowDraw = true
                mBinding.drawView.isVisible = true
                true
            }
            SpManager.putKlineShowDraw(isShowDraw)
            return@onShowDrawClick isShowDraw
        }

        mBinding.klineSideControlSideView.onKeepDrawClick {
            return@onKeepDrawClick if (mBinding.drawView.getContinuation()) {
                ToastUtil.showToast(getString(R.string.continuous_drawing_canceled))
                mBinding.drawView.setContinuation(false)
                false
            } else {
                ToastUtil.showToast(getString(R.string.continuous_drawing_is_enabled))
                mBinding.drawView.setContinuation(true)
                true
            }
        }

        mBinding.klineSideControlSideView.onMainItemClick {
            val type = it?.mainType
            when (type) {
                is KlineOtherEnum -> {
                    addOrRemoveKSub(type)
                    mBinding.klineSideControlSideView.changeSubSelected(mViewModel.subIndicatorList)
                }
                is KlineMainEnum -> {
                    addOrRemoveKMain(type)
                    mBinding.klineSideControlSideView.changeMainSelected(mViewModel.mainIndicatorList)
                }
            }
        }

        mBinding.klineSideControlSideView.onDrawingItemClick {
            mBinding.dlToolbar.isVisible = true
            when (it?.drawToolsType) {
                Keys.Markets_Kline_DrawRectangle -> mBinding.drawView.setCurrentType(DrawToolsType.Rect)
                Keys.Markets_Kline_DrawBezier -> mBinding.drawView.setCurrentType(DrawToolsType.Bezier)
                Keys.Markets_Kline_draw_horizontal_line -> mBinding.drawView.setCurrentType(DrawToolsType.HorizontalStraightLine)
                Keys.Markets_Kline_draw_vertical_line -> mBinding.drawView.setCurrentType(DrawToolsType.VerticalSegment)
                Keys.Markets_Kline_draw_trendline -> mBinding.drawView.setCurrentType(DrawToolsType.TrendLine)
                Keys.Markets_Kline_draw_rays -> mBinding.drawView.setCurrentType(DrawToolsType.Ray)
                Keys.Markets_Kline_draw_parallel_lines -> mBinding.drawView.setCurrentType(DrawToolsType.ParallelLine)
            }
        }

        mBinding.klineSideControlSideView.onSwitchMenu { showDraw ->
            mBinding.drawView.setDrawToolsOpen(showDraw)
            mBinding.kCharView.setShowCrossLineUntilTouch(false)
            klineChartTipsLayoutBinding?.clChartTipFloat?.isVisible = false
            if (showDraw) {
                mBinding.kCharView.setEnableCrossLine(false)
                mBinding.nestedScrollView.smoothScrollTo(0, 0)
            } else {
                mBinding.kCharView.setEnableCrossLine(true)
                mBinding.layoutLandKlineDrawingTips.cardDrawingTipsContainer.isVisible = false
                mBinding.dlToolbar.isVisible = false
            }
        }
        mBinding.dlToolbar.setOnViewReleased { lastX, lastY ->
            SpManager.putViewPosition(lastX, lastY)
        }
        mBinding.dlToolbar.setOnChildPosition {
            return@setOnChildPosition SpManager.getViewPosition()
        }
        positionAdapter.setOnItemClickListener { position ->
            KLineDataUtils.selectedOrderNo = mViewModel.shareOrderList.getOrNull(position)?.order ?: ""
            mViewModel.setOrderLine()
            mBinding.kCharView.orderData = mViewModel.getOrderData()
            positionDialog.dismissDialog()
        }
//        selectOrderPopUp.setOnItemClickListener { position: Int ->
//            KLineDataUtils.selectedOrderNo = mViewModel.shareOrderList.getOrNull(position)?.order ?: ""
//            mViewModel.setOrderLine()
//            mBinding.kCharView.orderData = mViewModel.getOrderData()
//        }
//        selectOrderPopUp.setOnDismissListener { requireActivity().setAlpha(1f) }
        //无数据
        mBinding.mVsNoData.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setBackgroundColor(mainLayoutBg)
                vs.mNoDataView.setIconResource(AttrResourceUtil.getDrawable(requireContext(), R.attr.icNoConnection))
                vs.mNoDataView.setHintMessage(getString(R.string.something_went_wrong_try_again))
                vs.mNoDataView.setBottomBtnText(getString(R.string.try_again))
                vs.mNoDataView.setBottomBtnViewClickListener {
                    requestKChartData()
                }
            }
        })
    }

    private fun showDrawTips(drawName: String?, currentPointCount: Int?, totalPointCount: Int?, isFinish: Boolean) {
        if (isFinish || drawName.isNullOrEmpty() || currentPointCount == null || totalPointCount == null) {
            if (isFinish) {
                mBinding.dlToolbar.isVisible = true
            }
            mBinding.layoutLandKlineDrawingTips.cardDrawingTipsContainer.isVisible = false
            return
        }
        if (currentDrawName != drawName) {//没有改变
            val drawNameUi = when (drawName) {
                Keys.Markets_Kline_DrawRectangle -> getString(R.string.rectangle)
                Keys.Markets_Kline_DrawBezier -> getString(R.string.kline_brush)
                Keys.Markets_Kline_draw_horizontal_line -> getString(R.string.horizontal_straight_line)
                Keys.Markets_Kline_draw_vertical_line -> getString(R.string.vertical_line)
                Keys.Markets_Kline_draw_trendline -> getString(R.string.trend_line)
                Keys.Markets_Kline_draw_rays -> getString(R.string.ray)
                Keys.Markets_Kline_draw_parallel_lines -> getString(R.string.parallel_lines)
                else -> {
                    mBinding.layoutLandKlineDrawingTips.cardDrawingTipsContainer.isVisible = false
                    return
                }
            }
            currentDrawName = drawName
            mBinding.layoutLandKlineDrawingTips.tvTipsSelectedDrawingType.text = "$drawNameUi ${getString(R.string.selected)}"
        }
        if (currentPointCount != 0) {
            mBinding.dlToolbar.isVisible = false
        }
        mBinding.layoutLandKlineDrawingTips.cardDrawingTipsContainer.isVisible = true

        if(drawName == Keys.Markets_Kline_DrawBezier) {
            mBinding.layoutLandKlineDrawingTips.tvTipsDrawingStep.setTextDiff(getString(R.string.tap_the_screen_slide_continuously))
        } else {
            val spannableString = SpannableString(getString(R.string.click_x_anchor_x_x, "$totalPointCount", "$currentPointCount", "$totalPointCount"))
            // 计算括号内内容的起始位置
            val startIndex = spannableString.indexOf("(") + 1
            val endIndex = spannableString.indexOf('/', startIndex)
            val colorSpan = ForegroundColorSpan(AttrResourceUtil.getColor(requireContext(), R.attr.color_c1e1e1e_cebffffff))
            spannableString.setSpan(colorSpan, startIndex, endIndex, Spanned.SPAN_INCLUSIVE_EXCLUSIVE)
            mBinding.layoutLandKlineDrawingTips.tvTipsDrawingStep.text = spannableString
        }
    }

    override fun createObserver() {
        super.createObserver()
        mViewModel.loadingChange.dialogLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            if (it) { //显示弹框
                showLoadDialog()
            } else { //关闭弹窗
                hideLoadDialog()
            }
        }
        mViewModel.noDataLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "noDataLiveData 横屏无数据 $it")
            mBinding.mVsNoData.isVisible = it
            mBinding.klineSideControlSideView.isVisible = !it
        }
        mViewModel.landScreenChangeRefreshLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "landScreenChangeRefreshLiveData 切换横屏 size:${it.size}")
            if (it.isEmpty()) {
                requestKChartData()
            } else {
                handleNewData(it)
            }
        }
        // 首次进入或切换时间请求数据
        mViewModel.newListLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "newListLiveData 横屏首次或切换时间 size:${it.size}")
            handleNewData(it)
        }
        // 定时刷新请求数据
        mViewModel.autoRefreshLiveData.observe(viewLifecycleOwner) { data ->
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "autoRefreshLiveData 横屏自动刷新 ${data.size}")
            mBinding.kCharView.setKlineOption(KLineDataUtils.userDataKV.getKlineSetting())
            if (data.isNotEmpty()) {
                mAdapter.verifyData(data)
            }
        }
        // 利用行情数据画出下一根蜡烛图
        mViewModel.drawNextDataLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            LogUtil.v(TAG, "drawNextDataLiveData 横屏行情数据画图")
            drawNextCandle()
        }
        // 分页加载请求数据
        mViewModel.addListLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            hideLoadDialog()
            LogUtil.v(TAG, "addListLiveData 横屏分页请求完成 ${it.size}")
            val data = it
            mViewModel.isLandEnableLoadMore = data.isNotEmpty()
            mBinding.kCharView.setKlineOption(KLineDataUtils.userDataKV.getKlineSetting())
            mAdapter.step = mViewModel.getSecondFromInterval()
            mAdapter.addData(data)
        }
        mViewModel.tokenErrorLiveData.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            CenterActionDialog.Builder(requireActivity())
                .setContent(it)
                .setSingleButton(true)
                .setOnDismissListener {
                    EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)// 退出登录
                }.build().showDialog()
        }
        activityViewModel.refreshCallBack.observe(viewLifecycleOwner) {
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            updateProdInfo()
        }
        mViewModel.resetLineLiveData.observe(viewLifecycleOwner) {isReloadOrderData ->
            if (mViewModel.isScreenPortrait) {
                return@observe
            }
            if(isReloadOrderData) {
                mViewModel.initOrderData()
                mBinding.kCharView.orderData = mViewModel.getOrderData()
            }
            mBinding.kCharView.resetMoveAbelLine()
        }
    }

    private fun initKline() {
        mBinding.kCharView.let {
            it.setOverScrollRange(75f.dp2px())
            it.setKChartScaleX(mViewModel.klineScale)
            it.font400 = FontResourceUtil.typefaceGilroyRegular(requireActivity())
            it.font500 = FontResourceUtil.typefaceGilroyMedium(requireActivity())
            it.font600 = FontResourceUtil.typefaceGilroySemiBold(requireActivity())
            it.setLogo(imgKlineLogo)
            it.setBackgroundColor(mainLayoutBg)
            it.setBuyPriceTextBgColor(mainLayoutBg)
            it.setSelectedLineColor(color_c1e1e1e_cebffffff)
            it.setSelectedBgColor(color_c1e1e1e_cebffffff)
            it.setSelectedValueTextColor(color_cebffffff_c1e1e1e)
            it.setGridLineColor(color_c0a1e1e1e_c262930)
            it.setPriceTextColor(color_ca61e1e1e_c99ffffff)
            it.setSimpleChartDrawTextColor(ChartUIParamUtil.candleMaxMinTextColor)
            it.setMaxMinTextColor(color_c1e1e1e_cebffffff)
            it.setPriceTextSize(9f.dp2px())
            it.setTextTimeColor(color_ca61e1e1e_c99ffffff)
            it.showFullScreen(true)
            it.setMainType(if (mViewModel.isTimeShare) MainDraw.TYPE_MAIN_MINUTE else MainDraw.TYPE_MAIN_CANDLE)
            updateKlineChild()
            updateKlineMainChild()
            it.setAdapter(mAdapter)
            it.dateTimeFormatter = DateFormatter()
            it.orderData = mViewModel.getOrderData()
            it.setGridRows(4)
            it.setGridColumns(4)
            it.setDigit(mViewModel.data?.digits.ifNull(2))
            it.setOrderRecordEntityList(mViewModel.getOrderRecordEntityList())
            it.isShowBuyLine = KLineDataUtils.userData?.askLineDisplay.ifNull()//买价线
            it.isShowSellLine = KLineDataUtils.userData?.bidLineDisplay.ifNull()//卖价线
            it.isShowTpLine = KLineDataUtils.userData?.tpLineDisplay.ifNull()//止盈线
            it.isShowSlLine = KLineDataUtils.userData?.slLineDisplay.ifNull()//止损线
            it.isDrawPositionLine = KLineDataUtils.userData?.positionLineDisplay.ifNull()//订单线
            it.setOnCrossMoveListener { view, point, index, isUserSelected, isLeft, isTouch ->
                if (!it.isEnableCrossLine) {
                    return@setOnCrossMoveListener
                }
                val data = point as? KLineEntity
                LogUtil.i(TAG, "onCrossMoved index:$index, closePrice:${data?.closePrice}")
                val timeZone = AppUtil.getTimeZoneRawOffsetToHour()
                val timeStamp = (data?.timestamp + "000").toLongCatching()
                val nowStamp = timeStamp - (timeZone * 3600 * 1000)
                mBinding.vsChartTipFloat.isVisible = isUserSelected
                if(klineChartTipsLayoutBinding == null){
                    return@setOnCrossMoveListener
                }
                klineChartTipsLayoutBinding?.clChartTipFloat?.isVisible = isUserSelected
                if (isUserSelected) {
                    (klineChartTipsLayoutBinding?.clChartTipFloat?.layoutParams as? FrameLayout.LayoutParams)?.run {
                        gravity = if (isLeft.not()) {
                            Gravity.START
                        } else {
                            Gravity.END
                        }
                        klineChartTipsLayoutBinding?.clChartTipFloat?.setLayoutParams(this)
                    }
                }
                klineChartTipsLayoutBinding?.tvChartTipTime?.text = TimeUtil.formatDateTime(nowStamp, "dd/MM/yyyy HH:mm")
                klineChartTipsLayoutBinding?.tvChartTipOpenValue?.text = data?.open.numFormat(ChartUIParamUtil.digits, false)
                klineChartTipsLayoutBinding?.tvChartTipHighValue?.text = data?.high.numFormat(ChartUIParamUtil.digits, false)
                klineChartTipsLayoutBinding?.tvChartTipLowValue?.text = data?.low.numFormat(ChartUIParamUtil.digits, false)
                klineChartTipsLayoutBinding?.tvChartTipCloseValue?.text = data?.close.numFormat(ChartUIParamUtil.digits, true)
                updateChartTip(data)
            }
            mBinding.vsChartTipFloat.setOnInflateListener { _ , inflated ->
                if(klineChartTipsLayoutBinding == null) {
                    klineChartTipsLayoutBinding = IncludeLandKlineChartTipsLayoutBinding.bind(inflated)
                    if (LanguageHelper.isRtlLanguage()){
                        klineChartTipsLayoutBinding?.clChartTipFloat?.layoutDirection = View.LAYOUT_DIRECTION_RTL
                    }else {
                        klineChartTipsLayoutBinding?.clChartTipFloat?.layoutDirection = View.LAYOUT_DIRECTION_LTR
                    }
                }
            }
            it.setOnScreenScrollChangeListener { startIndex, endIndex, timestamp ->
                if (!mViewModel.isScreenPortrait  && startIndex == 0 && !mViewModel.isLandRequestLoadMore && mViewModel.isLandEnableLoadMore) {
                    LogUtil.v(TAG, "横屏分页请求开始")
                    mViewModel.isLandRequestLoadMore = true
                    mViewModel.symbolsChart(true, true, false, timestamp)
                }
            }
            it.onTakeProfitStopLossChanged = object : OnTakeProfitStopLossChanged {
                override fun onTakeProfitStopLossChanged(price: Float, type: Int) {
                    LogUtil.v(TAG, "onTakeProfitStopLossChanged price:$price type:$type")
                    val isTP = type == OnTakeProfitStopLossChanged.TYPE_TP
                    val priceStr: String = price.numFormat(mViewModel.data?.digits ?: 2, true)
                    val content = if (isTP) getString(R.string.confirm_to_take_profit) + "\n" + getString(
                        R.string.take_profit_price) + ":" + priceStr else
                        getString(R.string.confirm_to_stop_loss) + "\n" + getString(R.string.stop_loss_price) + ":" + priceStr

                    showCenterActionDialog(
                        title = getString(R.string.use_dragn_drop_loss),
                        content = content,
                        startText = getString(R.string.no),
                        endText = getString(R.string.yes_confirm),
                        startAction = {
                            it.resetMoveAbelLine()
                        },
                        endAction = {
                            mViewModel.shareOrderData?.let { order ->
                                if (isTP) {
                                    mViewModel.setTakeProfitOrStopLoss(price, order.stopLoss?.toFloatCatching() ?: 0f, order)
                                } else {
                                    mViewModel.setTakeProfitOrStopLoss(order.takeProfit?.toFloatCatching() ?: 0f, price, order)
                                }
                            }
                        })
                }

                override fun onTpSlMoving(price: Float, openPrice: Float, volume: String, orderType: String): String {
                    mBinding.tvDragTip.isVisible = false
                    return VAUSdkUtil.getProfitLoss(
                        mViewModel.data ?: ShareProductData(),
                        "$openPrice",
                        volume,
                        orderType,
                        "$price"
                    ).numCurrencyFormat()
                }

                override fun onCancelSp() {
                    LogUtil.v(TAG, "onCancelSp")
                    showCenterActionDialog(
                        content = getString(R.string.cancel_stop_loss),
                        startText = getString(R.string.no),
                        endText = getString(R.string.yes_confirm),
                        endAction = {
                            mViewModel.shareOrderData?.let { order ->
                                mViewModel.setTakeProfitOrStopLoss(
                                    order.takeProfit?.toFloatCatching() ?: 0f, 0f, order
                                )
                            }
                        })
                }

                override fun onCancelTp() {
                    LogUtil.v(TAG, "onCancelTp")
                    showCenterActionDialog(
                        content = getString(R.string.cancel_take_profit),
                        startText = getString(R.string.no),
                        endText = getString(R.string.yes_confirm),
                        endAction = {
                            mViewModel.shareOrderData?.let { order ->
                                mViewModel.setTakeProfitOrStopLoss(
                                    0f,
                                    order.stopLoss?.toFloatCatching() ?: 0f,
                                    order
                                )
                            }
                        })
                }

                override fun onPositionLineClick() {
                    LogUtil.v(TAG, "onPositionLineClick")
                    if (!dragTipShowed) {
                        dragTipShowed = true
                        mBinding.tvDragTip.isVisible = true
                    }
                }

                override fun onSLLineClick() {
                    LogUtil.v(TAG, "onSLLineClick")
                    if (!dragTipShowed) {
                        dragTipShowed = true
                        mBinding.tvDragTip.isVisible = true
                    }
                }

                override fun onTPLineClick() {
                    LogUtil.v(TAG, "onTPLineClick")
                    if (!dragTipShowed) {
                        dragTipShowed = true
                        mBinding.tvDragTip.isVisible = true
                    }
                }
            }
        }
        mBinding.drawView.let {
            it.isVisible = mViewModel.isShowDraw
            it.setLightTheme(AppUtil.isLightTheme())
            it.initConfig(SpManager.getDrawToolShape())
            it.setDrawConfigSave {drawShape->
                SpManager.putDrawToolShape(drawShape)
            }
            it.pairName = mViewModel.symbol
            it.setDataManager(mViewModel.drawDataManager)
            it.setKChartView(mBinding.kCharView)
            it.digits = (mViewModel.data?.digits ?: 2)
            it.showBottomLayout = { show: Boolean ->
                if(show) {
                    val currentDraw = mBinding.drawView.getCurrentLineDrawView()
                    mBinding.viewToolbar.setData(
                        currentDraw?.line?.color ?: COLOR_4,
                        currentDraw?.line?.lineWidth ?: LINE_WIDTH_0,
                        DrawToolsLineStyle.LineStyle3,
                        true,
                    )
                }
                mBinding.dlToolbar.isVisible = show
            }
        }
    }

    private fun initDragTip() {
        val drawable = ContextCompat.getDrawable(requireContext(), R.drawable.draw_bitmap2_close16x16_c731e1e1e_c61ffffff)
        drawable?.setBounds(0, 0, 8.dp2px(), 8.dp2px())
        if (LanguageHelper.isRtlLanguage()){
            mBinding.tvDragTip.setCompoundDrawables(drawable, null, null, null)
        }else {
            mBinding.tvDragTip.setCompoundDrawables(null, null, drawable, null)
        }
        mBinding.tvOrders.visibility = if (mViewModel.shareOrderList.size > 1) View.VISIBLE else View.GONE
    }

    private fun initInterval() {
        mBinding.intervalRecyclerView.layoutManager = GridLayoutManager(requireActivity(), mViewModel.landIntervalList.size)
        mBinding.intervalRecyclerView.overScrollMode = View.OVER_SCROLL_NEVER
        mBinding.intervalRecyclerView.adapter = intervalAdapter
        intervalAdapter.setList(mViewModel.landIntervalList)
        selectInterval(SpManager.getChartTypeText("1D"), false)
        updateKlineChild()
        updateKlineMainChild()
        intervalAdapter.setOnItemClickListener { _, _, position ->
            if (mBinding.kCharView.isDragging) {
                return@setOnItemClickListener
            }
            val interval = intervalAdapter.getItem(position)
            selectInterval(interval, true)
        }
    }

    private fun initToolbar() {
        mBinding.viewToolbar.setToolbarCallback(object : ToolbarCallbackImpl() {
            override fun onColorPick(color: Int) {
                mBinding.drawView.setCurrentDrawLineColor(color)
            }

            override fun onLineThicknessPick(lineWidth: Int) {
                mBinding.drawView.setCurrentDrawLineWidth(lineWidth)
            }

            override fun onDeleteClick() {
                val isDeleted = mBinding.drawView.deleteOne()
                if(isDeleted) {
                    ToastUtil.showToast(getString(R.string.deleted))
                }
            }
        })

        mBinding.viewToolbar.setData(
            mBinding.drawView.getLineColor() ?: COLOR_4,
            mBinding.drawView.getLineWidth() ?: LINE_WIDTH_0,
            DrawToolsLineStyle.LineStyle3,
            true,
        )
    }

    private fun initSideControl() {
        mBinding.drawView.isVisible = mViewModel.isShowDraw
        mBinding.klineSideControlSideView.setSelectShowDrawView(mViewModel.isShowDraw)
        mBinding.klineSideControlSideView.setDrawingDataList(mViewModel.drawingList)
        mBinding.klineSideControlSideView.setMainDataList(
            mViewModel.chartTypeDataList,
            mViewModel.mainIndicatorList,
            mViewModel.subIndicatorList
        )
        // 判断k线是否可以滑动，非分时图并且存在超过一个副图时，k线view可以滑动超出一屏，并且显示主副图控制栏
        if (!mViewModel.isClickDrawSwitch) {
            mBinding.klineSideControlSideView.showMainView()//显示主副图控制栏
            mBinding.drawView.setDrawToolsOpen(false)//可以滑动
        } else {
            mBinding.kCharView.setEnableCrossLine(false)
            mBinding.kCharView.setShowCrossLineUntilTouch(false)
            klineChartTipsLayoutBinding?.clChartTipFloat?.isVisible = false
            mBinding.drawView.setDrawToolsOpen(true)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        klineChartTipsLayoutBinding = null
        mViewModel.klineScale = mBinding.kCharView.getmScaleX()
        EventBus.getDefault().unregister(this)
        mViewModel.updateAndSaveKLineSettingData()
    }

    @SuppressLint("SourceLockedOrientationActivity")
    private fun finishLandKLinePage() {
        val portraitChartData = if (!mBinding.mVsNoData.isVisible) {
            if (firstPageSize == 0) {
                mAdapter.datas
            } else {
                mAdapter.datas.takeLast(firstPageSize)
            }
        } else {
            mutableListOf<KLineEntity>()
        }
        mViewModel.klineScale = mBinding.kCharView.getmScaleX()
        mViewModel.isScreenPortrait = true
        activity?.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
        mViewModel.screenChangeRefreshLiveData.postValue(portraitChartData)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(eventTag: String) {
        LogUtil.v(TAG, "onMsgEvent 横屏-tag:$eventTag")
        when (eventTag) {
            NoticeConstants.APP_ON_PAUSE -> {
                LogUtil.v(TAG, "onMsgEvent 横屏-app回到后台")
                finishLandKLinePage()
            }
            NoticeConstants.Init.DATA_SUCCESS_ORDER -> {
                LogUtil.v(TAG, "横屏-刷新持仓订单列表 isScreenPortrait:${mViewModel.isScreenPortrait}")
                if (mViewModel.isScreenPortrait) {
                    return
                }
                mViewModel.initOrderData()// 刷新持仓订单列表
                mBinding.kCharView.orderData = mViewModel.getOrderData()
                mBinding.kCharView.resetMoveAbelLine()
            }
            NoticeConstants.Init.DATA_SUCCESS_GOODS -> {
                LogUtil.v(TAG, "横屏-刷新产品数据 isScreenPortrait:${mViewModel.isScreenPortrait}")
                if (mViewModel.isScreenPortrait) {
                    return
                }
                mViewModel.updateData()
            }
        }
    }

    private fun addOrRemoveKMain(klineMainEnum: KlineEnum) {
        if (mViewModel.mainIndicatorList.contains(klineMainEnum)) {
            mViewModel.mainIndicatorList.remove(klineMainEnum)
        } else {
            if (mViewModel.mainIndicatorList.size >= 2) {
                mViewModel.mainIndicatorList.removeAt(0)
            }
            mViewModel.mainIndicatorList.add(klineMainEnum as KlineMainEnum)
        }
        updateKlineMainChild()
    }

    private fun addOrRemoveKSub(klineOtherEnum: KlineOtherEnum) {
        if (mViewModel.subIndicatorList.contains(klineOtherEnum)) {
            mViewModel.subIndicatorList.remove(klineOtherEnum)
        } else {
            if (mViewModel.subIndicatorList.size >= 4) {
                mViewModel.subIndicatorList.removeAt(0)
            }
            mViewModel.subIndicatorList.add(klineOtherEnum)
        }
        changeKChartSize(isChangeSubChild = true)
        updateKlineChild()
    }

    private fun clearAndAddMain(klineMainEnum: KlineEnum): Boolean {
        mViewModel.mainIndicatorList.clear()
        val isAddMain = mViewModel.mainIndicatorList.add(klineMainEnum as KlineMainEnum)
        updateKlineMainChild()
        return isAddMain
    }

    private fun clearAndAddSub(klineOtherEnum: KlineEnum): Boolean {
        mViewModel.subIndicatorList.clear()
        val isAddSub = mViewModel.subIndicatorList.add(klineOtherEnum as KlineOtherEnum)
        changeKChartSize(isChangeSubChild = true)
        updateKlineChild()
        return isAddSub
    }

    private fun selectInterval(interval: String, isIntervalItemClick: Boolean = false) {
        // 切换加载数据
        intervalAdapter.changeSelected(interval)    // 同步选中状态
        changeChartInterval(interval, isIntervalItemClick)
        // 埋点
        if (interval != "Tick") {
            SensorsDataUtil.track(SensorsConstant.V3510.CANDLESTICK_CHART_PAGE_TIME_BTN_CLICK, JSONObject().apply {
                put("time_interval", interval)
                put(SensorsConstant.Key.SCREEN_ORIENTATION, "Portrait")
            })
        }
    }

    private fun changeChartInterval(interval: String, isIntervalItemClick: Boolean = false) {
        // 如果和上次点击条目一样直接返回
        if (isIntervalItemClick && interval == mViewModel.selectedInterval) {
            return
        }
        mViewModel.selectedInterval = interval
        mViewModel.isTimeShare = interval == "Tick"
        //隐藏十字线和十字线tips
        mBinding.kCharView.setShowCrossLineUntilTouch(false)
        klineChartTipsLayoutBinding?.clChartTipFloat?.isVisible = false
        // 重置isLandEnableLoadMore状态
        mViewModel.isLandEnableLoadMore = true
        SpManager.putChartTypeText(interval)
        if (isIntervalItemClick) {
            requestKChartData()
        }
    }

    private fun requestKChartData() {
        mViewModel.stopTimerRequest()
        mViewModel.switchChartPeriod(true)
    }

    /**
     * 改变k线View大小，分时图不显示副图，k线view一屏显示。非分时图并且存在超过一个副图时，k线view可以滑动m超出一屏。
     */
    private fun changeKChartSize(isChangeSubChild: Boolean = false) {
        var contentHeight = DisplayUtils.getScreenHeight(requireActivity()) - 102.dp2px()
        val lp = mBinding.kCharView.layoutParams
        if (mViewModel.isTimeShare) {
            lp.height = contentHeight
            mBinding.kCharView.layoutParams = lp
        } else {
            if (mViewModel.subIndicatorList.size > 1) {
                contentHeight += ((mViewModel.subIndicatorList.size - 1) * childH).toInt()
            }
            lp.height = contentHeight
            mBinding.kCharView.layoutParams = lp
            if (isChangeSubChild) {
                mBinding.nestedScrollView.post {
                    mBinding.nestedScrollView.smoothScrollTo(0, mBinding.kCharView.bottom)
                }
            }
        }
    }

    private fun updateKlineChild() {
        if (mViewModel.isTimeShare) {
            mBinding.kCharView.setKlineChild(mutableListOf<KlineOtherEnum>())
        } else {
            mBinding.kCharView.setKlineChild(mViewModel.subIndicatorList)
        }
    }

    private fun updateKlineMainChild() {
        if (mViewModel.isTimeShare) {
            mBinding.kCharView.setKlineMainChild(mutableListOf<KlineMainEnum>())
        } else {
            mBinding.kCharView.setKlineMainChild(mViewModel.mainIndicatorList)
        }
    }

    /**
     * 显示设置弹窗
     */
    private fun showSettingPopup() {
        settingPopup = LandDrawerKChartSettingsDialog.Builder(requireActivity()).build()
        settingPopup?.setOnLineSelect { position: Int, isSelect: Boolean ->
            var selectline = ""
            var toggle = ""
            val bundle = Bundle()
            when (position) {
                0 -> {
                    mBinding.kCharView.isShowBuyLine = isSelect
                    KLineDataUtils.userData?.askLineDisplay = isSelect
                    selectline = "Ask"
                    toggle = if (isSelect) "On" else "Off"
                }

                1 -> {
                    mBinding.kCharView.isShowSellLine = isSelect
                    KLineDataUtils.userData?.bidLineDisplay = isSelect
                    selectline = "Bid"
                    toggle = if (isSelect) "On" else "Off"
                }

                2 -> {
                    mBinding.kCharView.isDrawPositionLine = isSelect
                    KLineDataUtils.userData?.positionLineDisplay = isSelect
                    selectline = "Open"
                    toggle = if (isSelect) "On" else "Off"
                }

                3 -> {
                    mBinding.kCharView.isShowTpLine = isSelect
                    KLineDataUtils.userData?.tpLineDisplay = isSelect
                    selectline = "TP"
                    toggle = if (isSelect) "On" else "Off"
                }

                4 -> {
                    mBinding.kCharView.isShowSlLine = isSelect
                    KLineDataUtils.userData?.slLineDisplay = isSelect
                    selectline = "SL"
                    toggle = if (isSelect) "On" else "Off"
                }

                else -> {}
            }

            bundle.putString("Account_type", KLineActivity.getPointAccountType())
            bundle.putString("Mode", "Lite-vertical")
            bundle.putString("Line", selectline)
            bundle.putString("Toggle", toggle)
            LogEventUtil.setLogEvent(BuryPointConstant.V345.TRADE_KLINE_SETTINGS_LINE_BUTTON_CLICK, bundle)
        }

        settingPopup?.show()

        // 埋点
        LogEventUtil.setLogEvent(
            BuryPointConstant.V345.TRADE_KLINE_SETTINGS_BUTTON_CLICK, bundleOf(
                "Account_type" to KLineActivity.getPointAccountType(),
                "Mode" to "Lite-horizontal"
            )
        )
    }

    // 利用行情数据画出下一根蜡烛图
    private fun drawNextCandle() {
        val dataBean = mViewModel.data
        if (dataBean?.marketClose == false) {
            mAdapter.drawNextData(dataBean.originalBid, mViewModel.period)
        }
    }

    private fun handleNewData(data: List<KLineEntity>) {
        hideLoadDialog()
        LogUtil.v(TAG, "handleNewData 处理数据")
        firstPageSize = data.size
        mBinding.kCharView.setKlineOption(KLineDataUtils.userDataKV.getKlineSetting())
        mAdapter.step = mViewModel.getSecondFromInterval()
        mAdapter.setDatas(data, true)
        mBinding.kCharView.setMainType(if (mViewModel.isTimeShare) MainDraw.TYPE_MAIN_MINUTE else MainDraw.TYPE_MAIN_CANDLE)
        changeKChartSize()
        updateKlineChild()
        updateKlineMainChild()
        initTimeShareChart()
    }

    private fun showCenterActionDialog(
        title: String? = null, content: CharSequence?, startText: CharSequence?,
        endText: CharSequence?, startAction: (() -> Unit)? = null, endAction: (() -> Unit)? = null
    ) {
        val builder = CenterActionDialog.Builder(requireActivity())
        if (!title.isNullOrEmpty()) {
            builder.setTitle(title)
        }
        builder.setContent(content)
            .setStartText(startText)
            .setEndText(endText)
            .setOnStartListener {
                startAction?.invoke()
            }.setOnEndListener {
                endAction?.invoke()
            }.build().showDialog()
    }
}