package cn.com.vau.page.user.loginBind

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.*
import io.reactivex.disposables.Disposable


/**
 * Created by Haipeng on 2017/10/12.
 * 1
 *
 */
class LoginBindModel : LoginBindContract.Model {
    override fun phoneIsUsedApi(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().phoneIsUsedApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun getCodeApi(map: HashMap<String, Any>, baseObserver: BaseObserver<VerificationCodeData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getTelBindingSmsApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun bindEmailApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<EmailBindBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().userFirstLoginNewApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun thirdpartyBindPhoneApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<EmailBindBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().thirdpartyBindPhoneApi(map), baseObserver)
        return baseObserver.disposable
    }
}
