package cn.com.vau.page.user.openAccoGuide.lv3

import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import cn.com.vau.R
import cn.com.vau.page.user.openAccoGuide.OpenAccoGuideBaseActivity
import cn.com.vau.page.user.openAccoGuide.lv3.vm.OpenLv3ViewModel

class OpenAccoGuideLv3Activity : OpenAccoGuideBaseActivity<OpenLv3ViewModel>() {

    override fun initViewModels(): OpenLv3ViewModel =
        ViewModelProvider(this)[OpenLv3ViewModel::class.java]

    override fun setTitle(): String = getString(R.string.lv3_poa_authentication)

    override fun setTitleTip(): String = getString(R.string.open_account_annotation_title)

    override fun getTabIconSelector(): Array<Array<Int>> = arrayOf(
        arrayOf(
            R.attr.imgOpenStepPersonal,
            R.attr.imgOpenStepUnSelectedPersonal
        ),
        arrayOf(
            R.attr.imgOpenStepPOA,
            R.attr.imgOpenStepUnSelectedPOA
        )
    )

    override fun getTabText(): Array<String> = arrayOf(
        getString(R.string.poa_information),
        getString(R.string.poa_photo)
    )

    override fun getFragmentList(): Array<Fragment> = arrayOf(
        OpenLv3POAInfoFragment.newInstance(),
        OpenLv3POAUploadFragment.newInstance()
    )

    override fun scrollBack() {
        super.scrollBack()
        if (page == 0) {
            mViewModel.clearUploadData()
        }
    }

    fun gotoNext() {
        tabSelected(1)
        if (page == 1) {
            mViewModel.initUploadData()
        }
    }
}