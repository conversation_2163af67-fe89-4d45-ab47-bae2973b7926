package cn.com.vau.page.login.viewmodel.activity

import android.util.Base64
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.*
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.LoginObjBean
import cn.com.vau.page.login.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.FirebaseManager
import com.google.gson.JsonObject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.greenrobot.eventbus.EventBus

/**
 * author：lvy
 * date：2025/03/12
 * desc：ASIC：注册第四步 -> 输入姓、名、邮箱、密码
 */
class SignUpAsicPwdViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null // 注册的实体bean

    // 邮箱已存在，跳转到绑定邮箱页面
    private val _emailIsExistLiveData = MutableLiveData<String?>()
    val emailIsExistLiveData: LiveData<String?> = _emailIsExistLiveData

    // 注册成功
    private val _signUpSuccessLiveData = MutableLiveData<Boolean>()
    val signUpSuccessLiveData: LiveData<Boolean> = _signUpSuccessLiveData

    /**
     * 验证邮箱是否存在
     */
    fun isExistEmailApi(email: String) {
        val map = hashMapOf<String, Any?>()
        map["email"] = email
        requestNet({ baseService.isExistEmailApi(map) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            if (it.data?.obj?.emailStatus == true) { // 邮箱是否存在(true：存在，false：不存在)
                _emailIsExistLiveData.value = email
            }
        }, isShowDialog = true)
    }

    /**
     * 注册账号
     */
    fun registerAccountApi(firstName: String?, lastName: String?, email: String?) {
        if (!registerValidateInput(email)) return // 验证输入正确性

        val requestParams = registerCreateRequestParams(firstName, lastName, email)
        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(requestParams.json, AESUtil.PWD_AES_KEY)
        requestNet({
            if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
                baseService.thirdpartyRegister(aesMap)
            } else {
                baseService.registerNewApi(aesMap)
            }
        }, {
            if (it.getResponseCode() == "V10023") { // 邮箱已存在，跳转到绑定邮箱页面（三方注册没有这个返回）
                hideLoading()
                _emailIsExistLiveData.value = email
                return@requestNet
            }
            if (!it.isSuccess()) {
                hideLoading()
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 保存信息
            saveData(it.getResponseData()?.obj)
        }, isShowDialog = true, isAutoDismissDialog = false)
    }

    /**
     * 注册接口 -> 验证输入正确性
     */
    private fun registerValidateInput(email: String?): Boolean {
        val mobile = signUpRequestBean?.mobile
        val code = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
        if (mobile.isNullOrBlank() || (code == "86" && mobile.length != 11) || (code != "86" && mobile.length > 15)) {
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_number))
            return false
        }
        if (!RegexUtil.isEmail(email)) {
            ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_correct_mail))
            return false
        }
        return true
    }

    /**
     * 注册接口 -> 创建请求参数
     */
    private fun registerCreateRequestParams(firstName: String?, lastName: String?, email: String?): HashMap<String, Any?> {
        val map = hashMapOf<String, Any?>()
        map["phoneCountryCode"] = signUpRequestBean?.countryCode // 国家代号，如：FR
        map["code"] = signUpRequestBean?.countryNum // 手机区号，如：33
        map["userTel"] = signUpRequestBean?.mobile // 电话，如：00002054
        map["userPassword"] = signUpRequestBean?.pwd // 密码
        map["readingProtocol"] = "1" // 阅读协议（1：已读，2：未读）
        map["registerSource"] = SpManager.getResourceCodeRegister() // 注册来源
        map["demoRegisterStyle"] = signUpRequestBean?.styleType // demo注册样式
        map["inviteCode"] = signUpRequestBean?.referral.ifNull() // 交易账号，邮箱，邀请码
        map["usCitizen"] = "false" // 是否美国居民 是：true，否：false
        map["country"] = signUpRequestBean?.countryId.ifNull() // 国家id
        map["email"] = email // 邮箱
        map["firstName"] = firstName // 名
        map["lastName"] = lastName // 姓

        // appsflyer 相关参数值
        val cid = SpManager.getCid("")
        if (cid.isNotBlank()) {
            map["cid"] = Base64.encodeToString(cid.toByteArray(), Base64.DEFAULT) // 内部销售用id（base64加密），如：OA==
        }
        val cxd = SpManager.getCxd("")
        if (cxd.isNotBlank()) {
            map["cxd"] = cxd // 如：12345_12
        }
        val leadSource = SpManager.getLs("")
        if (leadSource.isNotBlank()) {
            map["leadSource"] = leadSource // 线索来源
        }
        val refereeEmail = SpManager.getRaf("")
        if (refereeEmail.isNotBlank()) {
            map["refereeEmail"] = refereeEmail // 推荐人邮箱、账户号、姓名
        }
        val cp = SpManager.getCp("")
        if (cp.isNotBlank()) {
            map["cp"] = cp // 优惠券code
        }
        val agentAccountNum = SpManager.getAgentAccountNum("")
        if (agentAccountNum.isNotBlank()) {
            map["agentAccountNum"] = agentAccountNum // 返佣账户号【明文】
        }
        // 三方登录触发的注册
        if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
            registerAddTelegramParams(map)
        }
        return map
    }

    /**
     * 注册接口 -> 添加tg参数
     */
    private fun registerAddTelegramParams(map: HashMap<String, Any?>) {
        map["randStr"] = signUpRequestBean?.smsCode // 上个页面的验证码
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
    }

    /**
     * 获取交易id
     */
    private fun tradeAccountLogin(account: String) {
        val jsonObject = JsonObject().apply {
            addProperty("login", account)
            addProperty("serverId", UserDataUtil.serverId())
            addProperty("token", UserDataUtil.loginToken())
            addProperty("accountType", UserDataUtil.accountType())
        }
        val jsonObject2 = JsonObject().apply {
            addProperty("data", jsonObject.toString())
        }
        val requestBody = jsonObject2.toString().toRequestBody("application/json".toMediaTypeOrNull())
        requestNet({ tradingService.tradeAccountLogin(requestBody) }, {
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                // 请求失败，清空缓存
                UserDataUtil.clearUserData()
                FirebaseManager.userLogout()
                return@requestNet
            }
            // 保存交易token
            UserDataUtil.setTradeToken(it.data?.token)

            // 埋点相关
            LogEventUtil.mFirebaseAnalytics.setUserId(UserDataUtil.areaCode() + UserDataUtil.userTel())

            // 广播
            EventBus.getDefault().post(NoticeConstants.SUBSCRIBE_TOPIC)
            EventBus.getDefault().post(NoticeConstants.SWITCH_ACCOUNT)
            // 三方注册后，刷新个人信息数据，目的是为了刷新顶部 telegram 的图标状态
            if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
                EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
            }

            _signUpSuccessLiveData.value = true
        }, onError = {
            // 请求失败，清空缓存
            UserDataUtil.clearUserData()
            FirebaseManager.userLogout()
        })
    }

    /**
     * 保存信息
     */
    private fun saveData(obj: LoginObjBean?) {
        // 用户信息缓存
        val account = obj?.accountMN
        UserDataUtil.setAccountCd(account?.acountCd.ifNull())
        UserDataUtil.setUserId(obj?.userId)
        UserDataUtil.setUserType(1)
        UserDataUtil.setMt4State("3")
        UserDataUtil.setLoginToken(obj?.token)
        UserDataUtil.setXToken(obj?.xtoken)
        UserDataUtil.setUserTel(signUpRequestBean?.mobile)
        UserDataUtil.setUserPassword(signUpRequestBean?.pwd)
        UserDataUtil.setUserNickName(obj?.userNick)
        UserDataUtil.setUserPic(obj?.pic)
        UserDataUtil.setServerId(account?.accountServer)
        UserDataUtil.setCurrencyType(account?.currencyType)
        UserDataUtil.setAccountType(account?.accountType)
        UserDataUtil.setPlatForm(account?.platform)
        UserDataUtil.setFastCloseState("2")
        UserDataUtil.setFastStopCopyState("2")
        UserDataUtil.setOrderConfirmState("2")
        UserDataUtil.setCountryCode(signUpRequestBean?.countryCode)
        UserDataUtil.setAreaCode(signUpRequestBean?.countryNum)
        UserDataUtil.setOpenLiveAccountState("0")
        UserDataUtil.setAccountDealType("3")

        // sp缓存
        SpManager.putSuperviseNum(obj?.regulator.ifNull("0"))

        // 账号保存到数据库
        if (signUpRequestBean?.handleType == LoginHandleType.EMAIL) {
            if (!obj?.email.isNullOrBlank()) {
                val userEmailHistory = UserEmailHistory()
                userEmailHistory.email = obj.email.ifNull()
                DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
            }
        } else {
            if (!obj?.userTel.isNullOrBlank()) {
                val userPhoneHistory = UserPhoneHistory()
                userPhoneHistory.phoneNumber = obj.userTel.ifNull()
                DbManager.getInstance().saveUserPhoneHistory(userPhoneHistory)
            }
            // sp缓存
            SpManager.putUserTel(signUpRequestBean?.mobile.ifNull())
            SpManager.putCountryCode(signUpRequestBean?.countryCode.ifNull())
            SpManager.putCountryNum(signUpRequestBean?.countryNum.ifNull())
            SpManager.putCountryName(signUpRequestBean?.countryName.ifNull())
        }

        // 埋点相关
        SensorsDataUtil.bindBusinessIdForRegister(obj?.crmUserId) // 绑定神策业务ID，场景1
        LogEventUtil.setLogEvent("demo_register_success")
        LogEventUtil.setLogEvent("demo_register_success_tROAS")

        // 登录firebase
        FirebaseManager.userLogin()

        // 获取交易id
        tradeAccountLogin(account?.acountCd.ifNull())
    }
}