package cn.com.vau.page

import android.os.Parcelable
import cn.com.vau.common.constants.Constants
import kotlinx.parcelize.Parcelize
import java.io.Serializable

@Parcelize
data class RegisterRequestBean(
//    var facebookNick: String? = null,
//    var facebookEmail: String? = null,
//    var facebookHeadImage: String? = null,
    var mobile: String? = null,
    var referral: String? = null,
    var randStr: String? = null,
    var msgInfo: String? = null,
    var countryCode: String? = null,
    var countryName: String? = null,
    var countryNum: String? = null,
    var styleType: Int = 1,
    var smsCode: String? = null,
    var accountType: Int? = 0,
    var handleType: Int = 0,
    var readingProtocol: Boolean? = null,
    var email: String? = null, // 邮箱
    var pwd: String? = null, // 密码
) : Serializable, Parcelable {
    fun jumpAsicModel() = countryNum == Constants.defaultCountryNum
}