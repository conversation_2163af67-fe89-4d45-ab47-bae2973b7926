package cn.com.vau.page.login.activity

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.common.view.PasswordView
import cn.com.vau.common.view.popup.bean.HintLocalData
import cn.com.vau.databinding.ActivityInputCodeBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.CountDownTextHelper
import cn.com.vau.page.login.SendCodeType
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.login.viewmodel.SendCodeViewModel
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.CaptchaUtil
import cn.com.vau.util.SendCodeUtil.SendCodeListener
import cn.com.vau.util.clickNoRepeat
import cn.com.vau.util.widget.dialog.BottomInfoWithIconListDialog
import com.netease.nis.captcha.Captcha
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * author：lvy
 * date：2025/03/14
 * desc：输入验证码页面
 */
class InputCodeActivity : BaseMvvmBindingActivity<ActivityInputCodeBinding>() {

    private val sendCodeViewModel by viewModels<SendCodeViewModel>()
    private var signUpRequestBean: SignUpRequestBean? = null // 注册的实体bean
    private var mCaptcha: Captcha? = null

    private val color_c1e1e1e_cebffffff by lazy { AttrResourceUtil.getColor(this, R.attr.color_c1e1e1e_cebffffff) }
    private val ce35728 by lazy { getColor(R.color.ce35728) }
    private val countDownTextHelper by lazy { CountDownTextHelper(ce35728) }

    override fun initParam(savedInstanceState: Bundle?) {
        signUpRequestBean = intent.getParcelableExtra("signUpRequestBean")
        sendCodeViewModel.signUpRequestBean = signUpRequestBean
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        addLoadingObserve(sendCodeViewModel)
        mBinding.tvLoginType.text = "${getString(R.string.the_verification_code_has_been_sent_to)}:"
        mBinding.tvPhoneNumber.text = "+${signUpRequestBean?.countryNum} ${signUpRequestBean?.mobile}"
        // 按钮默认不可点击
        mBinding.tvSendEms.isEnabled = false
        mBinding.llWhatsApp.isEnabled = false
        // 根据发送方式显示不同按钮
        showWhichBtn()

        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.RESUMED) {
                delay(500)
                mBinding.passwordView.showSoftInput()
            }
        }
    }

    override fun initData() {
        sendCodeViewModel.startSendCodeUtil()
    }

    override fun createObserver() {
        // 发送验证码成功
        sendCodeViewModel.sendCodeSuccessLiveData.observe(this) {
            sendCodeViewModel.startSendCodeUtil()
        }
        // 手机号存在跳转到登录页面
        sendCodeViewModel.accountExistsLiveData.observe(this) {
            openActivity(LoginPwdActivity::class.java, bundleOf().apply {
                putInt(Constants.HANDLE_TYPE, 99)
//                putString(Constants.DATA_MSG, it)
            })
        }
        // 触发网易易盾验证
        sendCodeViewModel.showCaptchaLiveData.observe(this) {
            showCaptcha()
        }
        // 验证sms验证码成功
        sendCodeViewModel.validateSmsCodeSuccessLiveData.observe(this) {
            openActivity(SignUpAsicPwdActivity::class.java, bundleOf().apply {
                putParcelable("signUpRequestBean", signUpRequestBean)
            })
        }
    }

    override fun initListener() {
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 验证码输入框回调
        mBinding.passwordView.setPasswordListener(passwordListener)
        // 发送验证码回调
        sendCodeViewModel.initSendCodeUtil(sendCodeListener)
        // 重新发送
        mBinding.tvReSendEms.clickNoRepeat {
            sendCodeViewModel.getTelRegisterSmsApi()
        }
        // 收不到验证码提示文案
        mBinding.tvNotReceiveCodeTips.setOnClickListener {
            showNotReceiveCodeDialog()
        }
        // sms
        mBinding.tvSendEms.clickNoRepeat {
            signUpRequestBean?.sendCodeType = SendCodeType.PHONE
            mBinding.passwordView.clearInput()
            sendCodeViewModel.getTelRegisterSmsApi()
        }
        // whatsApp
        mBinding.llWhatsApp.clickNoRepeat {
            signUpRequestBean?.sendCodeType = SendCodeType.WHATSAPP
            mBinding.passwordView.clearInput()
            sendCodeViewModel.getTelRegisterSmsApi()
        }
    }

    /**
     * 验证码输入框回调
     */
    private val passwordListener = object : PasswordView.PasswordListener {
        override fun passwordChange(changeText: String?) {

        }

        override fun passwordComplete() {
            val code = mBinding.passwordView.getPassword()
            if (code.length == 6) {
                sendCodeViewModel.signUpRequestBean?.smsCode = code
                sendCodeViewModel.smsValidateSmsRegisterCodeApi()
                mBinding.passwordView.hiddenSoftInputFromWindow()
            }
        }

        override fun keyEnterPress(password: String?, isComplete: Boolean) {

        }
    }

    /**
     * 发送验证码回调
     */
    private val sendCodeListener = object : SendCodeListener {
        override fun onTick(millisUntilFinished: Int) {
            val second = millisUntilFinished.toString() // 倒计时秒数
            val fullText = getString(R.string.resend_code_in_x_seconds, second)
            mBinding.tvReSendEms.setTextColor(color_c1e1e1e_cebffffff)
            mBinding.tvReSendEms.text = countDownTextHelper.updateCountDownText(fullText, second)
            mBinding.tvReSendEms.isEnabled = false
            showWhichBtn()
            if (mBinding.tvSendEms.isVisible) {
                mBinding.tvSendEms.isEnabled = false
            }
            if (mBinding.llWhatsApp.isVisible) {
                mBinding.llWhatsApp.isEnabled = false
                mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_c3325d366_r100)
            }
        }

        override fun onFinish() {
            mBinding.tvReSendEms.setTextColor(ce35728)
            mBinding.tvReSendEms.text = getString(R.string.resend)
            mBinding.tvReSendEms.isEnabled = true
            if (mBinding.tvSendEms.isVisible) {
                mBinding.tvSendEms.isEnabled = true
            }
            if (mBinding.llWhatsApp.isVisible) {
                mBinding.llWhatsApp.isEnabled = true
                mBinding.llWhatsApp.setBackgroundResource(R.drawable.shape_cbf25d366_r100)
            }
        }
    }

    /**
     * 不能收到验证码的提示弹框
     */
    private fun showNotReceiveCodeDialog() {
        mBinding.passwordView.hiddenSoftInputFromWindow()
        val data = if (signUpRequestBean?.sendCodeType == SendCodeType.EMAIL) {
            arrayListOf(
                HintLocalData(
                    getString(R.string.double_check_your_email_address),
                    getString(R.string.ensure_you_have_it_correctly),
                    AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips3)
                ),
                HintLocalData(
                    getString(R.string.check_your_spam_junk_folder),
                    getString(R.string.sometimes_the_email_by_mistake),
                    AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips4)
                ),
                HintLocalData(
                    getString(R.string.wait_a_few_minutes),
                    getString(R.string.delays_can_happen_occasionally),
                    AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips2)
                )
            )
        } else {
            arrayListOf(
                HintLocalData(
                    getString(R.string.double_check_your_phone_number),
                    getString(R.string.ensure_you_have_it_correctly),
                    AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips1)
                ),
                HintLocalData(
                    getString(R.string.wait_a_few_minutes),
                    getString(R.string.delays_can_happen_occasionally),
                    AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips2)
                ),
                HintLocalData(
                    getString(R.string.try_another_verification_method),
                    getString(R.string.switch_verification_methods_your_otp),
                    AttrResourceUtil.getDrawable(this, R.attr.imgNotReceiveCodeTips3)
                )
            )
        }
        BottomInfoWithIconListDialog.Builder(this)
            .setTitle(getString(R.string.did_not_receive_verification_code))
            .setDataList(data)
            .setLinkText(getString(R.string.contact_support_for_help))
            .setLinkListener {
                openActivity(HelpCenterActivity::class.java)
            }
            .build()
            .showDialog()
    }

    /**
     * 根据发送方式显示不同按钮
     */
    private fun showWhichBtn(){
        if (signUpRequestBean?.sendCodeType == SendCodeType.PHONE) {
            mBinding.tvSendEms.isVisible = false
            mBinding.llWhatsApp.isVisible = true
        } else {
            mBinding.tvSendEms.isVisible = true
            mBinding.llWhatsApp.isVisible = false
        }
    }

    private fun showCaptcha() {
        mCaptcha = CaptchaUtil.getCaptcha(this) {
            sendCodeViewModel.getTelRegisterSmsApi(it)
        }
        mCaptcha?.validate()
    }

    override fun onDestroy() {
        super.onDestroy()
        sendCodeViewModel.stopSendCodeUtil()
        mCaptcha?.destroy()
    }
}