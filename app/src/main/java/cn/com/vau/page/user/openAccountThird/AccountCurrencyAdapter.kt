package cn.com.vau.page.user.openAccountThird

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.PlatFormAccountData
import cn.com.vau.util.ImageLoaderUtil


/**
 * 输入描述
 * Created by THINKPAD on 2018/11/29.
 */
class AccountCurrencyAdapter(val context: Context,
                             var accountTypeList: MutableList<PlatFormAccountData.Currency>
) : RecyclerView.Adapter<AccountCurrencyAdapter.ViewHolder>() {

    var currentSelectAccountType = 0

    override fun getItemCount(): Int = accountTypeList.size

    override fun onBindViewHolder(holder: <PERSON>Holder, position: Int) {

        val dataBean = accountTypeList.elementAtOrNull(position)

        ImageLoaderUtil.loadImage(
            context,
            dataBean?.listImage?.elementAtOrNull(0)?.imgUrl,
            holder.ivAccountCurrency
        )

        holder.tvCurrency.text = dataBean?.currencyName ?: ""

        if (currentSelectAccountType == position){
            holder.llAccountType.setBackgroundResource(R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10)
        } else {
            holder.llAccountType.setBackgroundResource(R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10)
        }


        holder.ivAccountCurrency.setOnClickListener {
            if (currentSelectAccountType == position) return@setOnClickListener
            currentSelectAccountType = position
            if (mOnItemClickListener != null) mOnItemClickListener?.onItemClick(position)
//            notifyDataSetChanged()
        }

    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder = ViewHolder(LayoutInflater.from(context).inflate(R.layout.item_acount_currency, parent, false))

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val ivAccountCurrency = view.findViewById<ImageView>(R.id.ivAccountCurrency)
        val tvCurrency = view.findViewById<TextView>(R.id.tvCurrency)
        val llAccountType = view.findViewById<LinearLayout>(R.id.llAccountType)
    }

    private var mOnItemClickListener: OnItemClickListener? = null

    interface OnItemClickListener {
        fun onItemClick(position: Int)
    }

    fun setOnItemClickListener(onItemClickListener: OnItemClickListener) {
        mOnItemClickListener = onItemClickListener
    }

}