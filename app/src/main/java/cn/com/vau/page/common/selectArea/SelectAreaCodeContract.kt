package cn.com.vau.page.common.selectArea

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.account.SelectCountryNumberBean
import cn.com.vau.data.account.SelectCountryNumberObj
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface SelectAreaCodeContract {

    interface Model : BaseModel {
        fun getAreaCodeApi(baseObserver: BaseObserver<SelectCountryNumberBean>): Disposable
    }

    interface View : BaseView {
        fun updateData(dataList: List<SelectCountryNumberObj>)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getAreaCodeApi()
    }

}
