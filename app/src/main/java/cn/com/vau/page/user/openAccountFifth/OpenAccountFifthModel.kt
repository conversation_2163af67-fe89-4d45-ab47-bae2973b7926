package cn.com.vau.page.user.openAccountFifth

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.page.user.openAccountFirst.OpenAccountCacheModel
import cn.com.vau.data.account.RealAccountCacheBean
import cn.com.vau.data.account.UploadImageBean
import io.reactivex.disposables.Disposable
import okhttp3.MultipartBody


/**
 * Created by Haipeng on 2017/10/12.
 * 1
 *
 */
class OpenAccountFifthModel : OpenAccountFifthContract.Model, OpenAccountCacheModel() {
    override fun registerRealAccount(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().rgstRealMt4Account(map), baseObserver)
        return baseObserver.disposable
    }

    override fun uploadFile(body: MultipartBody, baseObserver: BaseObserver<UploadImageBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().fileFileUpload(body), baseObserver)
        return baseObserver.disposable
    }

    override fun getRealInfo(map: HashMap<String, Any>, baseObserver: BaseObserver<RealAccountCacheBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getData(map), baseObserver)
        return baseObserver.disposable
    }
}
