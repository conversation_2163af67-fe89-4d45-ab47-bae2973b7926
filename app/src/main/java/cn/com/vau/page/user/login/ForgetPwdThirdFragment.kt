package cn.com.vau.page.user.login

import android.annotation.SuppressLint
import android.os.*
import android.text.TextUtils
import android.view.*
import androidx.core.view.isVisible
import androidx.navigation.fragment.NavHostFragment
import cn.com.vau.R
import cn.com.vau.common.base.fragment.BaseFrameFragment
import cn.com.vau.common.constants.*
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.utils.EmojiFilter
import cn.com.vau.databinding.FragmentForgetPwdThirdBinding
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.bindEmail.BindEmailActivity
import cn.com.vau.page.user.forgotPwdSecond.*
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.util.*
import com.netease.nis.captcha.*
import org.greenrobot.eventbus.*

class ForgetPwdThirdFragment : BaseFrameFragment<ForgotPwdSecondPresenter, ForgetPwdSecondModel>(), ForgetPwdSecondContract.View {

    private val mBinding: FragmentForgetPwdThirdBinding by lazy { FragmentForgetPwdThirdBinding.inflate(layoutInflater) }

    private var captcha: Captcha? = null
    private var isNext = false

    private val color_c731e1e1e_c61ffffff by lazy {
        AttrResourceUtil.getColor(requireActivity(), R.attr.color_c731e1e1e_c61ffffff)
    }

    private val color_cffffff_c1e1e1e by lazy {
        AttrResourceUtil.getColor(requireActivity(), R.attr.color_cebffffff_c1e1e1e)
    }

    override fun initParam() {
        super.initParam()
        if (!EventBus.getDefault().isRegistered(this))
            EventBus.getDefault().register(this)
        arguments?.let {
            mPresenter.email = it.getString("email")
            mPresenter.txId = it.getString("txId")
            mPresenter.randStr = it.getString("randStr")
            mPresenter.mobile = it.getString("mobile")
            mPresenter.smsSendType = it.getString("smsSendType", VerificationActivity.TYPE_SEND_SMS)
            mPresenter.countryCode = it.getString("countryCode")
            mPresenter.code = it.getString("code")
            mPresenter.handleType = it.getInt(Constants.HANDLE_TYPE, 0)
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View = mBinding.root

    @SuppressLint("ObsoleteSdkInt", "SetTextI18n")
    override fun initView() {
        super.initView()
        mBinding.etPwd.setHint("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
        mBinding.etPwdRepeat.setHint("${getString(R.string.re_enter_password)}")
        mBinding.layoutPasswordCheck.tvPasswordSpecial.text = buildString {
            append(getString(R.string.at_least_1_following_characters))
            append(" !@#\$%^&*.()")
        }
        //过滤表情
        mBinding.etPwd.setFilter(EmojiFilter())
        mBinding.etPwdRepeat.setFilter(EmojiFilter())
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvNext.setOnClickListener(this)

        mBinding.etPwd.doAfterTextChanged {
            it?.let {
                checkNewPassword()
            }
        }

        mBinding.etPwdRepeat.doAfterTextChanged {
            it?.let {
                checkNewPassword()
            }
        }

        mBinding.mHeaderBar.setStartBackIconClickListener {
            NavHostFragment.findNavController(this).popBackStack()
        }
    }

    override fun initData() {
        //获取出金限制横幅文案
        mPresenter.getWithdrawRestrictionMsgApi(3)
    }

    private fun initCaptcha() {
        //易盾
        val loginCaptchaListener = object : CaptchaListener {
            override fun onReady() {}
            override fun onValidate(result: String, validate: String, msg: String) {
                if (!TextUtils.isEmpty(validate)) {
                    mPresenter.getVerificationCodeApi(validate)
                }
            }

            //建议直接打印错误码，便于排查问题
            override fun onError(code: Int, msg: String) {

            }

            override fun onClose(closeType: Captcha.CloseType) {
                if (closeType == Captcha.CloseType.VERIFY_SUCCESS_CLOSE) {
                    Handler(Looper.getMainLooper()).post {
                        //成功 + 关闭
                    }
                }
            }
        }

        captcha = CaptchaUtil.getCaptcha(requireContext(), loginCaptchaListener)
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvNext -> {
                if (!isNext)
                    return

                mPresenter.goEditPwdApi(
                    mBinding.etPwd.getText().trim(),
                    mBinding.etPwdRepeat.getText().trim(),
                    mPresenter.randStr
                )
            }
        }
    }

    override fun back() {
        loginOutSuc()
        ActivityManagerUtil.getInstance().finishActivity(BindEmailActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(AccountManagerActivity::class.java)
        ActivityManagerUtil.getInstance().finishActivity(LoginActivity::class.java)
        val count = ActivityManagerUtil.getInstance().getActivityCount(LoginPwdActivity::class.java)
        if (count > 1) {
            for (i in 1 until count) {
                ActivityManagerUtil.getInstance().finishActivity(LoginPwdActivity::class.java)
            }
        }

        NavHostFragment
            .findNavController(this)
            .popBackStack(R.id.forgetPwdFirstFragment, true)
    }

    private fun loginOutSuc() {
        // 设置退出状态为true
        SpManager.putExitStatus(true)
        // 通知到MainActivity做退出登录操作
        EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
    }

    override fun showCaptcha() {
        initCaptcha()
        captcha?.validate()
    }

    override fun goThird(validateCode: String?) {

    }

    /**
     * 出金限制横幅
     */
    override fun showWithdrawRestrictionMsg(msg: String?) {
        if (!msg.isNullOrBlank()) {
            mBinding.tvTips.isVisible = true
            mBinding.tvTips.text = msg
        }
    }

    // 因为用了MVP框架，复用了P，这个方法被迫得实现，不过这里用不到
    override fun goBindPhone(bundle: Bundle) {

    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(requireActivity().window)
        super.onDestroy()
        if (captcha != null) captcha?.destroy()
        EventBus.getDefault().unregister(this)
    }

    /**
     * 判断密码是否符合规范，并且会直接改变对应的状态
     */
    private fun checkNewPassword() {
        val password = mBinding.etPwd.getText()
        mBinding.layoutPasswordCheck.tvPasswordLength.isSelected = password.length in 8..16
        //正则表达式的意思是 包含字母数字和特殊字符
        mBinding.layoutPasswordCheck.tvPasswordContent.isSelected = RegexUtil.isContainsLetter(password)
        mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected = RegexUtil.isContainsNumber(password)
        mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected = RegexUtil.isContainsSpecial(password)
        mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected = password != "" && mBinding.etPwdRepeat.getText() == password

        updateButton()
    }

    private fun updateButton() {
        var bgRes = R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
        var textColor = color_c731e1e1e_c61ffffff
        if (mBinding.layoutPasswordCheck.tvPasswordLength.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordContent.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordNumber.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordSpecial.isSelected &&
            mBinding.layoutPasswordCheck.tvPasswordMatch.isSelected
        ) {
            isNext = true
            bgRes = R.drawable.draw_shape_c1e1e1e_cebffffff_r100
            textColor = color_cffffff_c1e1e1e
        } else {
            isNext = false
            bgRes = R.drawable.draw_shape_c0a1e1e1e_c0affffff_r100
            textColor = color_c731e1e1e_c61ffffff
        }
        mBinding.tvNext.setBackgroundResource(bgRes)
        mBinding.tvNext.setTextColor(textColor)
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onMsgEvent(tag: String) {
        when (tag) {
            //取消输入框的焦点
            NoticeConstants.EDITTEXT_CANCEL_FOCUS -> {
                mBinding.etPwd.clearFocus()
                mBinding.etPwdRepeat.clearFocus()
            }
        }
    }
}