package cn.com.vau.page.user.loginPwd

import android.os.Bundle
import cn.com.vau.common.base.mvp.*
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.account.*
import cn.com.vau.data.profile.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface LoginPwdContract {

    interface Model : BaseModel {
        fun pwdLoginApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable
        fun bindUserApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable
        fun getAreaCodeApi(baseObserver: BaseObserver<SelectCountryNumberBean>): Disposable
        fun getBindingTelSMSApi(
            map: HashMap<String, Any?>,
            baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
        ): Disposable

        fun passKeyLoginDataApi(map: HashMap<String, String?>, baseObserver: BaseObserver<PasskeyLoginData>): Disposable

        /**
         * 发送邮箱验证码 -> 首次登录验证邮箱
         */
        fun emailSendEmailCodeApi(
            map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>
        ): Disposable

        /**
         * 三方登录
         */
        fun thirdPartyLoginApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<LoginBean>): Disposable

        /**
         * telegram-获取botId
         */
        fun telegramGetBotIdApi(baseObserver: BaseObserver<TelegramGetBotBean>): Disposable
    }

    interface View : BaseView {
        fun goBind(bundle: Bundle)

        /**
         * 登录接口loginNew触发滑块验证
         */
        fun showCaptcha()

        /**
         * 短信验证码接口getTelSms触发滑块验证
         */
        fun showVeriCaptcha(userTel: String, mobile: String, countryCode: String, code: String, pwd: String)

        fun showDialogToChangePassword()
        fun showSoftInput(delay: Long = 100)
        fun startPasskey(jsonData: String, passkeyId: String)

        /**
         * 跳转邮箱OTP验证页面
         */
        fun goEmailOTPVerify(email: String, pwd: String, txId: String, type: String)

        /**
         * telegram-获取botId成功
         */
        fun telegramGetBotIdSuccess(bean: TelegramGetBotIdObjBean?)

        /**
         * 三方登录触发网易易盾
         */
        fun showThirdLoginCaptcha()

        /**
         * 三方登录成功后刷新布局
         */
        fun thirdLoginSuccessRefreshView()

        /**
         * 邮箱滑块验证
         */
        fun showVerifyEmail(email: String, pwd: String, bizType: String)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getLocalTel()
        abstract fun pwdLoginApi(mobile: String?, pwd: String?, recaptcha: String?)
        abstract fun dealLoginData(loginBean: LoginBean, userTel: String, userPassword: String, fromType: String, handleType: Int)
        abstract fun saveUserData(loginBean: LoginBean, userPassword: String)
        abstract fun setSelectAreaData(areaCodeData: SelectCountryNumberObjDetail)
        abstract fun getAreaCodeApi()
        abstract fun getBindingTelSMSApi(
            userTel: String,
            userPassword: String,
            mobile: String,
            phoneCountryCode: String,
            code: String,
            type: String,
            validateCode: String
        )

        abstract fun passKeyLoginDataApi(phone: String, email: String)
        abstract fun passkeyLoginApi(mobile: String?, challenge: String?, loginJsonResult: String?, passkeyId: String)

        /**
         * 发送邮箱验证码
         */
        abstract fun emailSendEmailCodeApi(type: String, email: String, pwd: String)

        /**
         * 三方登录 -> telegram
         */
        abstract fun thirdLoginTelegramApi(validate: String?, userPassword: String?)

        /**
         * telegram-获取botId
         */
        abstract fun telegramGetBotIdApi()
    }

}
