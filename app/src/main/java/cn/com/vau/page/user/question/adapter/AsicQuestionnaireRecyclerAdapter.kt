package cn.com.vau.page.user.question.adapter

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.QuestionObj

/**
 * 输入描述
 * Created by THINKPAD on 2019/4/1.
 */
class AsicQuestionnaireRecyclerAdapter(val mContext: Context, val dataList: MutableList<QuestionObj>) : RecyclerView.Adapter<AsicQuestionnaireRecyclerAdapter.ViewHolder>() {
    var selectMap = mutableMapOf<Int, String>()

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val questionData = dataList.getOrNull(position)?:return
        holder.tvIndex.text = "${questionData.subjectGuid}."
        holder.tvQuestion.text = questionData.description
        holder.tvAnswer.adapter = AnswerAdapter(mContext, questionData.answer, this, position)
    }

    override fun getItemCount(): Int = dataList.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
            ViewHolder(LayoutInflater.from(mContext).inflate(R.layout.item_recycler_asic_questionnaire, parent, false))

    class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val tvIndex: TextView = itemView.findViewById(R.id.tvIndex)
        val tvQuestion: TextView = itemView.findViewById(R.id.tvQuestion)
        val tvAnswer: RecyclerView = itemView.findViewById(R.id.tvAnswer)
    }

}