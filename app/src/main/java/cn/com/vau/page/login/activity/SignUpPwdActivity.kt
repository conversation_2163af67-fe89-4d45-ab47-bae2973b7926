package cn.com.vau.page.login.activity

import android.os.Bundle
import android.view.MotionEvent
import androidx.fragment.app.Fragment
import cn.com.vau.R
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivitySignUpPwdBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.*
import cn.com.vau.page.login.fragment.SignUpPwdFragment
import cn.com.vau.util.*

/**
 * author：lvy
 * date：2025/03/10
 * desc：kyc注册第二步 -> 输入手机号/邮箱、密码
 */
class SignUpPwdActivity : BaseMvvmBindingActivity<ActivitySignUpPwdBinding>() {

    private var signUpRequestBean: SignUpRequestBean? = null

    private var phoneFragment: SignUpPwdFragment? = null
    private var emailFragment: SignUpPwdFragment? = null

    override fun initParam(savedInstanceState: Bundle?) {
        signUpRequestBean = intent?.getParcelableExtra("signUpRequestBean")
    }

    override fun initView() {
        initTabLayout()
    }

    override fun initListener() {
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
    }

    private fun initTabLayout() {
        val fragments = arrayListOf<Fragment>()
        val titleList = mutableListOf(getString(R.string.phone_number), getString(R.string.email))
        phoneFragment = SignUpPwdFragment.newInstance(signUpRequestBean, LoginHandleType.PHONE)
        fragments.add(phoneFragment!!)
        emailFragment = SignUpPwdFragment.newInstance(signUpRequestBean, LoginHandleType.EMAIL)
        fragments.add(emailFragment!!)

        mBinding.vpLogin.init(fragments, titleList, supportFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.vpLogin, titleList, TabType.LINE_INDICATOR) {
            if (it == 1) { // 因为复用signUpRequestBean，内存地址是一样的，切换时要重新赋值，否则状态不对
                signUpRequestBean?.handleType = LoginHandleType.EMAIL
            } else {
                signUpRequestBean?.handleType = LoginHandleType.PHONE
            }
        }
    }

    /**
     * 切换tab
     */
    fun switchTab(position: Int) {
        mBinding.vpLogin.setCurrentItem(position, false)
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView, R.id.barrier)
        return super.dispatchTouchEvent(event)
    }
}