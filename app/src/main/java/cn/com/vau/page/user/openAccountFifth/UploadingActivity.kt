package cn.com.vau.page.user.openAccountFifth

import android.view.View
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.constants.NoticeConstants
import cn.com.vau.common.mvvm.base.BaseMvvmBindingActivity
import cn.com.vau.databinding.ActivityUploadingBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.util.AttrResourceUtil
import cn.com.vau.util.BarUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class UploadingActivity : BaseMvvmBindingActivity<ActivityUploadingBinding>() {

    private var mIsSucceed: Boolean = true

    override fun initView() {
        mBinding.tvNext.visibility = View.GONE
        BarUtil.setBarColorAndIconColor(window, false)
    }

    override fun initListener() {
        super.initListener()
        mBinding.tvNext.setOnClickListener(this)
        mBinding.mImageFilterView.setOnClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
    }

    override fun onClick(view: View?) {
        super.onClick(view)
        when (view?.id) {
            R.id.tvNext -> {
                if (mIsSucceed) {
                    setResult(Constants.SUBMIT_SUCCESS)
                }
                finish()
            }
        }
    }

    private fun changeStatus(isSucceed: Boolean) {
        mIsSucceed = isSucceed
        if (isSucceed) {
            mBinding.mImageFilterView.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.imgAlertOk))
            mBinding.tvTitle.setText(R.string.documents_received)
            mBinding.tvOpenAccountHint.setText(R.string.thank_you)
            mBinding.tvNext.visibility = View.VISIBLE
            mBinding.tvNext.setText(R.string.ok)
        } else {
            mBinding.mImageFilterView.setImageResource(AttrResourceUtil.getDrawable(this, R.attr.imgAlertWrong))
            mBinding.tvTitle.setText(R.string.upload_unsuccessful)
            mBinding.tvOpenAccountHint.setText(R.string.please_try_again)
            mBinding.tvNext.visibility = View.VISIBLE
            mBinding.tvNext.setText(R.string.upload_again)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    override fun onMsgEvent(tag: String) {
        if (tag == NoticeConstants.UPLOAD_PHOTO_SUCCEED) {
            changeStatus(true)
        }

        if (tag == NoticeConstants.UPLOAD_PHOTO_FAIL) {
            changeStatus(false)
        }
    }

    override fun useEventBus(): Boolean = true

}
