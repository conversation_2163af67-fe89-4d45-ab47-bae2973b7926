package cn.com.vau.page.login.viewmodel.fragment

import android.text.TextUtils
import android.util.Base64
import androidx.lifecycle.*
import cn.com.vau.R
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.baseService
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.mvvm.ext.requestNet
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.login.*
import cn.com.vau.util.*
import cn.com.vau.util.language.LanguageUtil
import cn.com.vau.util.tracking.*
import cn.com.vau.util.widget.FirebaseManager
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * author：lvy
 * date：2025/03/10
 * desc：kyc注册第二步 -> 输入手机号/邮箱、密码
 */
class SignUpPwdViewModel : BaseViewModel() {

    var signUpRequestBean: SignUpRequestBean? = null // 注册的实体bean

    // 国家区号设置成功
    private val _areaCodeLiveData = MutableLiveData<String>()
    val areaCodeLiveData: LiveData<String> = _areaCodeLiveData

    // 手机号已存在
    private val _phoneExistLiveData = MutableLiveData<Boolean>()
    val phoneExistLiveData: LiveData<Boolean> = _phoneExistLiveData

    // 触发网易易盾验证
    private val _showCaptchaLiveData = MutableLiveData<Boolean>()
    val showCaptchaLiveData: LiveData<Boolean> = _showCaptchaLiveData

    // 注册成功
    private val _signUpSuccessLiveData = MutableLiveData<Boolean>()
    val signUpSuccessLiveData: LiveData<Boolean> = _signUpSuccessLiveData

    /**
     * 初始化国家区号
     */
    fun initAreaCode() {
        signUpRequestBean?.styleType = (Math.random() * 3).toInt() + 1

        val countryCode = SpManager.getCountryCode(Constants.defaultCountryCode)
        val countryNum = SpManager.getCountryNum()
        val countryName = SpManager.getCountryName(
            if (countryNum == Constants.defaultCountryNum) Constants.defaultCountryName else ""
        )
        if (TextUtils.isEmpty(countryNum)) {
            getAreaCodeApi()
            return
        }
        // 刷新UI
        signUpRequestBean?.countryCode = countryCode
        signUpRequestBean?.countryNum = countryNum
        signUpRequestBean?.countryName = countryName
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(countryNum)
    }

    /**
     * 国家区号
     */
    private fun getAreaCodeApi() {
        runCatching {
            val systemCountry = LanguageUtil.getSystemLocal(UtilApp.getApp())?.country // 获取手机系统语言代码
            if (systemCountry.isNullOrBlank()) {
                setDefaultAreaCode()
                return
            }
            requestNet({ baseService.selectCountryNumberClassifyScreeningApi() }, onSuccess = {
                if (!it.isSuccess()) {
                    setDefaultAreaCode()
                    return@requestNet
                }
                val objList = it.getResponseData()?.obj
                out@ for (countryNumberObj in objList ?: ArrayList()) {
                    for (countryBean in countryNumberObj.list ?: ArrayList()) {
                        if (countryBean.countryCode == systemCountry) {
                            setAreaCodeData(countryBean)
                            break@out
                        }
                    }
                }
                // 没有匹配到接口返回的，就赋默认值
                if (signUpRequestBean?.countryCode.isNullOrBlank()) {
                    setDefaultAreaCode()
                }
            }, onError = {
                setDefaultAreaCode()
            }, isShowDialog = true)
        }.onFailure {
            setDefaultAreaCode()
        }
    }

    /**
     * 设置国家区号，在选择国家区号页面选择回调后会调用
     */
    fun setAreaCodeData(countryBean: SelectCountryNumberObjDetail) {
        signUpRequestBean?.countryCode = countryBean.countryCode
        signUpRequestBean?.countryNum = countryBean.countryNum
        signUpRequestBean?.countryName = countryBean.countryName
        SpManager.putCountryNum(countryBean.countryNum.ifNull())
        SpManager.putCountryName(countryBean.countryName.ifNull())
        SpManager.putCountryCode(countryBean.countryCode.ifNull())
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }

    /**
     * 设置默认的国家区号，一些特殊情况会走这里，如获取不到用户的 country，getAreaCode() 接口请求失败等
     */
    private fun setDefaultAreaCode() {
        signUpRequestBean?.countryCode = Constants.defaultCountryCode
        signUpRequestBean?.countryNum = Constants.defaultCountryNum
        signUpRequestBean?.countryName = Constants.defaultCountryName
        SpManager.putCountryNum(Constants.defaultCountryNum)
        SpManager.putCountryName(Constants.defaultCountryName)
        SpManager.putCountryCode(Constants.defaultCountryCode)
        _areaCodeLiveData.value = signUpRequestBean?.countryNum.ifNull(Constants.defaultCountryNum)
    }

    /**
     * 注册
     */
    fun appUserRegisterApi(validate: String? = null) {
        if (!registerValidateInput()) return // 验证输入正确性

        val requestParams = registerCreateRequestParams(validate)
        val aesMap = hashMapOf<String, Any?>()
        aesMap["data"] = AESUtil.encryptAES(requestParams.json, AESUtil.PWD_AES_KEY)
        requestNet({
            if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
                baseService.thirdPartyUserRegisterApi(aesMap) // 三方登录触发的注册
            } else {
                baseService.appUserRegisterApi(aesMap)
            }
        }, {
            if (signUpRequestBean?.handleType != LoginHandleType.EMAIL) {
                if (it.getResponseCode() == "V10030") { // 联系人手机号存在，引导用户用邮箱登录
                    _phoneExistLiveData.value = true
                    return@requestNet
                }
            }
            if (it.getResponseCode() == "V10060") { // 网易易盾验证
                _showCaptchaLiveData.value = true
                return@requestNet
            }
            if (!it.isSuccess()) {
                ToastUtil.showToast(it.getResponseMsg())
                return@requestNet
            }
            // 保存信息
            saveData(it.getResponseData()?.obj)
            // 注册成功通知ui层
            _signUpSuccessLiveData.value = true
        }, isShowDialog = true)
    }

    /**
     * 注册接口 -> 验证输入正确性
     */
    private fun registerValidateInput(): Boolean {
        if (signUpRequestBean?.handleType == LoginHandleType.PHONE) {
            val mobile = signUpRequestBean?.mobile
            val code = signUpRequestBean?.countryNum
            if (mobile.isNullOrBlank() || (code == "86" && mobile.length != 11) || (code != "86" && mobile.length > 15)) {
                ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_number))
                return false
            }
        } else {
            if (!RegexUtil.isEmail(signUpRequestBean?.email)) {
                ToastUtil.showToast(StringUtil.getString(R.string.please_enter_the_correct_mail))
                return false
            }
        }
        return true
    }

    /**
     * 注册接口 -> 创建请求参数
     */
    private fun registerCreateRequestParams(validate: String? = null): HashMap<String, Any?> {
        val map = hashMapOf<String, Any?>()
        if (signUpRequestBean?.handleType == LoginHandleType.PHONE) { // 手机号注册
            map["phoneCountryCode"] = signUpRequestBean?.countryCode // 国家代号，如：FR
            map["code"] = signUpRequestBean?.countryNum // 手机区号，如：33
            map["userTel"] = signUpRequestBean?.mobile // 电话，如：00002054
        } else { // 邮箱注册
            map["email"] = signUpRequestBean?.email // 邮箱
        }
        map["userPassword"] = signUpRequestBean?.pwd // 密码
        map["readingProtocol"] = "1" // 阅读协议（1：已读，2：未读）
        map["registerSource"] = SpManager.getResourceCodeRegister() // 注册来源
        map["demoRegisterStyle"] = signUpRequestBean?.styleType // demo注册样式
        map["inviteCode"] = signUpRequestBean?.referral.ifNull() // 交易账号，邮箱，邀请码
        map["usCitizen"] = "false" // 是否美国居民 是：true，否：false
        map["country"] = signUpRequestBean?.countryId.ifNull() // 国家id
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate
            map["smsCodeId"] = SpManager.getSmsCodeId()
        }

        // appsflyer 相关参数值
        val cid = SpManager.getCid("")
        if (cid.isNotBlank()) {
            map["cid"] = Base64.encodeToString(cid.toByteArray(), Base64.DEFAULT) // 内部销售用id（base64加密），如：OA==
        }
        val cxd = SpManager.getCxd("")
        if (cxd.isNotBlank()) {
            map["cxd"] = cxd // 如：12345_12
        }
        val leadSource = SpManager.getLs("")
        if (leadSource.isNotBlank()) {
            map["leadSource"] = leadSource // 线索来源
        }
        val refereeEmail = SpManager.getRaf("")
        if (refereeEmail.isNotBlank()) {
            map["refereeEmail"] = refereeEmail // 推荐人邮箱、账户号、姓名
        }
        val cp = SpManager.getCp("")
        if (cp.isNotBlank()) {
            map["cp"] = cp // 优惠券code
        }
        val agentAccountNum = SpManager.getAgentAccountNum("")
        if (agentAccountNum.isNotBlank()) {
            map["agentAccountNum"] = agentAccountNum // 返佣账户号【明文】
        }
        // 三方登录触发的注册
        if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
            registerAddTelegramParams(map)
        }
        return map
    }

    /**
     * 注册接口 -> 添加tg参数
     */
    private fun registerAddTelegramParams(map: HashMap<String, Any?>) {
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
    }

    /**
     * 保存信息
     */
    private fun saveData(obj: LoginObjBean?) {
        // 用户信息缓存
        UserDataUtil.setUserId(obj?.userId)
        UserDataUtil.setLoginToken(obj?.token)
        UserDataUtil.setXToken(obj?.xtoken)
        UserDataUtil.setUserNickName(obj?.userNick)
        UserDataUtil.setFastCloseState("2")
        UserDataUtil.setFastStopCopyState("2")
        UserDataUtil.setOrderConfirmState("2")
        UserDataUtil.setCountryCode(signUpRequestBean?.countryCode)
        UserDataUtil.setAreaCode(signUpRequestBean?.countryNum)
        UserDataUtil.setUserTel(signUpRequestBean?.mobile)
        UserDataUtil.setUserPassword(signUpRequestBean?.pwd)
        UserDataUtil.setOpenLiveAccountState("0")
        UserDataUtil.setAccountDealType(obj?.accountMN?.accountDealType) // 账户类别
        UserDataUtil.setAccountCd(obj?.accountMN?.acountCd) // 账户号
        UserDataUtil.setServerId(obj?.accountMN?.accountServer) // 服务器id

        // sp缓存
        SpManager.putSuperviseNum(obj?.regulator.ifNull("0"))

        // 账号保存到数据库
        if (signUpRequestBean?.handleType == LoginHandleType.EMAIL) {
            if (!signUpRequestBean?.email.isNullOrBlank()) {
                val userEmailHistory = UserEmailHistory()
                userEmailHistory.email = signUpRequestBean?.email.ifNull()
                DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
            }
        } else {
            if (!signUpRequestBean?.mobile.isNullOrBlank()) {
                val userPhoneHistory = UserPhoneHistory()
                userPhoneHistory.phoneNumber = signUpRequestBean?.mobile.ifNull()
                DbManager.getInstance().saveUserPhoneHistory(userPhoneHistory)
            }
            // sp缓存
            SpManager.putUserTel(signUpRequestBean?.mobile.ifNull())
            SpManager.putCountryCode(signUpRequestBean?.countryCode.ifNull())
            SpManager.putCountryNum(signUpRequestBean?.countryNum.ifNull())
            SpManager.putCountryName(signUpRequestBean?.countryName.ifNull())
        }

        // 埋点相关
        SensorsDataUtil.bindBusinessIdForRegister(obj?.crmUserId) // 绑定神策业务ID，场景1
        LogEventUtil.setLogEvent("demo_register_success")
        LogEventUtil.setLogEvent("demo_register_success_tROAS")
        LogEventUtil.mFirebaseAnalytics.setUserId(UserDataUtil.areaCode() + UserDataUtil.userTel())

        // 登录firebase
        FirebaseManager.userLogin()

        // 广播
        EventBus.getDefault().post(NoticeConstants.SUBSCRIBE_TOPIC)
        EventBus.getDefault().post(NoticeConstants.SWITCH_ACCOUNT)
        // 黄金开户逻辑根据这个广播来处理相应逻辑
        EventBus.getDefault().post(NoticeConstants.GOLD_REGISTER_SUCCESS)
        // 三方注册后，刷新个人信息数据，目的是为了刷新顶部 telegram 的图标状态
        if (signUpRequestBean?.thirdHandleType == ThirdHandleType.TELEGRAM) {
            EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
        }
    }

    /**
     * 神策埋点 -> 注册页面按钮点击（填写完成后点击确认注册时触发）
     */
    fun sensorsTrackClick() {
        SensorsDataUtil.track(SensorsConstant.V3700.REGISTER_PAGE_CLICK, JSONObject().apply {
            put(SensorsConstant.Key.BUTTON_NAME, "Sign Up")
        })
    }
}