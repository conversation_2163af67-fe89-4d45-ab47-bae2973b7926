package cn.com.vau.page.html

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.TextUtils
import android.webkit.JavascriptInterface
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.FileProvider
import androidx.core.os.bundleOf
import androidx.fragment.app.FragmentActivity
import cn.com.vau.MainActivity
import cn.com.vau.R
import cn.com.vau.common.application.VauApplication.Companion.context
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.constants.*
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.mvvm.base.BaseViewModel
import cn.com.vau.common.storage.SpManager
import cn.com.vau.common.view.share.ShareHelper
import cn.com.vau.page.StickyEvent
import cn.com.vau.page.coupon.CouponManagerActivity
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.depositNew.DepositDetailsActivity
import cn.com.vau.page.photopreview.PhotoActivity
import cn.com.vau.page.security.KycFundsPwdHelper
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFifthActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.openAccoGuide.lv1.OpenAccoGuideLv1Activity
import cn.com.vau.page.user.openAccount.OpenAccountSuccessAsicActivity
import cn.com.vau.page.user.openSameNameAccount.OpenSameNameAccountActivity
import cn.com.vau.page.user.question.AsicQuestionnaireActivity
import cn.com.vau.page.user.sumsub.SumSubJumpHelper
import cn.com.vau.page.user.sumsub.SumsubPromptActivity
import cn.com.vau.profile.activity.authentication.AuthenticationActivity
import cn.com.vau.profile.activity.kycAuth.KycAuthActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFABindActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity
import cn.com.vau.signals.stsignal.LiveEventData
import cn.com.vau.signals.stsignal.activity.StStrategyDetailsActivity
import cn.com.vau.util.*
import cn.com.vau.util.BitmapUtil.base64ToBitmap
import cn.com.vau.util.widget.dialog.CenterActionDialog
import org.greenrobot.eventbus.EventBus
import org.json.JSONObject

/**
 * Created by roy on 2018/4/9 0009.
 * js
 */
class JavaScriptInterface(private val mContext: AppCompatActivity, private val mViewModel: BaseViewModel, private val sensorsHelper: HtmlSensorsHelper? = null) {

    // 跳转图片预览页面
    @JavascriptInterface
    fun startPhotoActivity(imgUrlArray: Array<String>, position: Int) {
        val imagesList: MutableList<String> = ArrayList()
        imagesList.clear()
        for (i in imgUrlArray.indices) {
            imagesList.add(imgUrlArray[i])
        }
        val intent = Intent(mContext, PhotoActivity::class.java)
        intent.putStringArrayListExtra("imageslist", imagesList as ArrayList<String>)
        intent.putExtra("images_position", position)
        mContext.startActivity(intent)
        // h5流程神策埋点 -> 交互
        sensorsHelper?.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.JS_INTERFACE, jsValue = "startPhotoActivity")
    }

    // 弹窗提示登陆
    @JavascriptInterface
    fun showLoginDialog() {
        if (!UserDataUtil.isLogin()) {
            mContext.startActivity(Intent(mContext, LoginActivity::class.java))
//          mContext.openActivity(LoginActivity::class.java)
        } else {
            ToastUtil.showToast("User has logged in")
        }
        // h5流程神策埋点 -> 交互
        sensorsHelper?.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.JS_INTERFACE, jsValue = "showLoginDialog")
    }

    // 跳转注册页面 登陆页面
    @JavascriptInterface
    fun startRegisterActivity() {
        if (!UserDataUtil.isLogin()) {
            val intent = Intent(mContext, LoginActivity::class.java)
//            Bundle bundle = new Bundle();
//            bundle.putInt(Constants.SOURCE, 2);//1登陆  2web--JS
//            intent.putExtras(bundle);
            mContext.startActivity(intent)
        } else {
            ToastUtil.showToast("User has logged in")
        }
        // h5流程神策埋点 -> 交互
        sensorsHelper?.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.JS_INTERFACE, jsValue = "startRegisterActivity")
    }

    /**
     * 05 account的数据，本地是getMt4State()
     * 08 跳转到账户管理页面
     * 09 关闭loading弹窗，调用开户接口，跳转开户页面
     * 10 跳转到登录页面
     * 50 未登录跳转到登录，已登录就校验开户到第几步了，跳转到开户页面
     * 60 跳转到优惠券管理页面
     * 101 弹出raf的分享弹窗
     * 103 跳转到注册页面
     * 104 跳转asic的上传身份/地址证明页面 同时关闭h5页面
     * 105 跳转到优惠券管理页面 带isFrom参数为3
     * 106 跳转主页的行情页
     * 107 调用开户引导接口，跳转到相应开户页面
     * 200 调用系统文本分享功能
     * 201 跳转到账户管理页面，当前h5页面关闭
     * 204 跳转到新的h5页面，使用传入的url
     * 208 获取base64格式的图片，保存到本地
     * 210 跳转分析师观点页面
     * 211 跳转直播间页面
     * 220 跳转联系客服页面
     * 221 跳转首页行情页
     * 225 接收到通知以后，设置点击返回按钮直接退出当前h5页面
     * 232 未登录跳转登录页面，已登录跳转入金结果页面
     * 233 跳转信号源主页
     * 235 跳转问卷页面（greenid） 发235代表未答过问卷
     * 236 关闭green id 认证页面 并且关闭asic开户页面
     * 237 银行电汇与出金触发H5开户中的上传功能弹窗
     * 238 开(跟单)同名  平台账户里限制只有CopyTrading
     * 239 出金-绑定2fa
     * 240 出金-验证2fa
     * 241 跳 app 外部浏览器
     * 242 跳转免费订单页面 ，没有免费订单 就跳订单页
     * 243 通知h5账户类型
     * 244 跳转人脸识别页面
     * 245 h5调用截屏功能
     * 246 跳转开同名页面
     * 247 telegram登录回调
     * 248 关闭loading
     * 249 打开loading
     * 250 显示h5传过来的title
     *      history 按钮是否显示(historyIsShow)
     *      常见问题入口是否显示(questionIsShow)
     *      客服入口是否显示(customerIsShow)
     *      当前h5页面能否有上一级页面 ,能否返回上一级 (isCanBack)
     * 251 h5通知返回上一级页面
     * 254 跳转lv1开户页面
     * 255 跳转lv2开户页面
     * 256 跳转开同名页面
     * 257 跳转认证中心页面
     * 259 跳转忘记资金密码页面
     * 260 跳转设置资金密码 setFundPassword 方法  回调 isSetted
     * 261 跳转 h5 传入的url
     * 262 跳转入金三方页面
     * 500 弹出h5传过来的toast
     * 501 关闭当前页面
     * 503 h5触发退出登录
     * 600 黄金开户 删除虚拟MT5账户信息
     * 601 钱包h5调用开户或开同名，点击Got It会调
     * 602 调用补全用户信息半模态弹窗
     * 603 调用kyc等级半模态弹窗（附带nextLevel参数，表示期望等级，titleString,descriptionString分别表示对应标题和描述)
     * 604 h5修改个人信息（手机号、邮箱等）同步app拉取用户信息
     * 605 h5合并账户，会存在userid和token的变化。需带参数：reserveUserToken、reserveUserId、token、userId、userType
     * 606 kyc等级提交后。需同步给app
     * 607 跳转到 kyc 的认证中心
     * 608 用于通知支付或者钱包更新页面。触发回调方法 refreshPageState
     * 609 kyc h5触发人机验证
     */
    @JavascriptInterface
    fun webEvent(json: String): String {
        LogUtil.w("JavaScriptInterface webEvent ---- ", json)
        if (TextUtils.isEmpty(json) || json == "undefined") {
            return ""
        }
        try {
            val jsonObject = JSONObject(json)
            val code = jsonObject.optString("code")
            val message = jsonObject.optString("message")

            when (code) {
                // account的数据，本地是getMt4State()
                "05" -> {
                    val json1 = JSONObject()
                    if (UserDataUtil.isLogin()) {
                        json1.put("account", UserDataUtil.mt4State())
                        return json1.toString()
                    } else {
                        json1.put("account", "0")
                        return json1.toString()
                    }
                }
                // 跳转到账户管理页面
                "08" -> {
                    val intent = Intent(mContext, AccountManagerActivity::class.java)
                    mContext.startActivity(intent)
                }
                // 关闭loading弹窗，调用开户接口，跳转开户页面
                "09" -> {
                    EventBus.getDefault().post(NoticeConstants.JS.OPEN_ACCOUNT_OPEN_APP)
                }
                // 跳转到登录页面
                "10" -> {
                    if (!UserDataUtil.isLogin()) {
                        val intent = Intent(mContext, LoginActivity::class.java)
                        mContext.startActivity(intent)
                    } else {
                        ToastUtil.showToast("User has logged in")
                    }
                }
                // 未登录跳转到登录，已登录就校验开户到第几步了，跳转到开户页面
                "50" -> {
                    if (!UserDataUtil.isLogin()) {
                        val intent = Intent(mContext, LoginActivity::class.java)
                        mContext.startActivity(intent)
                    } else {
                        if (UserDataUtil.isLiveAccount()) {
                            NewHtmlActivity.openActivity(mContext, url = UrlConstants.HTML_FUND_DEPOSIT)
                        } else {
                            EventBus.getDefault().post(NoticeConstants.JS.INTERFACE_50)
                        }
                    }
                }
                // 弹出raf的分享弹窗
                "101" -> {
                    ShareHelper.rafShare(activity = mContext)
                }
                // 跳ASIC开户成功页（greenid）发103代表答过调查问卷
                "103" -> {
                    val intent = Intent(mContext, OpenAccountSuccessAsicActivity::class.java)
                    intent.putExtra("type", 4)
                    intent.putExtra("isAppraisal", true)
                    mContext.startActivity(intent)
                    EventBus.getDefault().post(NoticeConstants.REFRESH_ACCOUNT_MANAGER)
                }
                // 跳转asic的上传身份/地址证明页面 同时关闭h5页面
                "104" -> {
                    val intent = Intent(mContext, OpenAccountFifthActivity::class.java)
                    intent.putExtra("skipType", SpManager.getSkipTypeOpenAccount(1))
                    intent.putExtra("fromType", 1)
                    mContext.startActivity(intent)
                    EventBus.getDefault().post(NoticeConstants.REFRESH_OPEN_ACCOUNT_GUIDE)
                    EventBus.getDefault().post(NoticeConstants.JS.INTERFACE_104)
                }
                // 跳转到优惠券管理页面 带isFrom参数为3 经过排查 ，传 3 就设置 mViewPager2.currentItem = 0 ， 那和不设置 也没有区别了 ，
                // 不带传参的话， 和 60 直接跳转优惠券 也没有区别了就
                "60", "105" -> {
                    val intent = Intent(mContext, CouponManagerActivity::class.java)
                    mContext.startActivity(intent)
                }
                // 跳转主页的行情页
                "106" -> {
                    // 行情页
                    mContext.startActivity(Intent(mContext, MainActivity::class.java))
                    EventBus.getDefault().post(NoticeConstants.MAIN_SHOW_POSITION_FIRST)
                }
                // 调用开户引导接口，跳转到相应开户页面
                "107" -> {
                    EventBus.getDefault().post(NoticeConstants.JS.OPEN_ACCOUNT_GUIDE)
                }
                // 调用系统文本分享功能
                "200" -> {
                    val imageUrl = jsonObject.optString("imageUrl")
                    val textInfo = jsonObject.optString("textInfo")
                    // 检查图像 URL 是否不为空
                    if (imageUrl.isNotBlank()) {
                        BitmapUtil.saveImageFromUrl(mContext, imageUrl) { file ->
                            runCatching {
                                // 如果文件不为 null，生成文件的 URI 并分享带有图片的文本
                                if (file != null) {
                                    // 使用 FileProvider 获取文件的 URI，确保与你在 manifest 中的 authorities 相匹配
                                    val imageUri = FileProvider.getUriForFile(mContext, "${mContext.packageName}.installapkdemo", file)
                                    // 通过 Intent 分享文本和图片
                                    mContext.startActivity(IntentUtil.getShareTextImageIntent(textInfo, imageUri))
                                } else {
                                    // 如果文件为 null，则只分享文本
                                    mContext.startActivity(IntentUtil.getShareTextIntent(textInfo))
                                }
                            }.onFailure {
                                // 如果上面的操作失败（如获取文件 URI 或启动分享 Intent 时出错），只分享文本
                                mContext.startActivity(IntentUtil.getShareTextIntent(textInfo))
                            }
                        }
                    } else {
                        // 如果图像 URL 为空，只分享文本
                        mContext.startActivity(IntentUtil.getShareTextIntent(textInfo))
                    }
                }
                // 跳转到账户管理页面，当前h5页面关闭
                "201" -> {
                    //刷新账户列表
                    val intent = Intent(mContext, AccountManagerActivity::class.java)
                    val bundle = Bundle()
                    bundle.putInt(Constants.IS_FROM, 5)
                    intent.putExtras(bundle)
                    ActivityManagerUtil.getInstance().finishActivity(AccountManagerActivity::class.java)
                    mContext.finish()
                    mContext.startActivity(intent)
                }
                // 跳转到新的h5页面，使用传入的url
                "204" -> {
                    val url = jsonObject.optString("url")
                    val bundle = Bundle()
                    bundle.putString("url", url)
                    bundle.putBoolean("isNeedFresh", false)
                    bundle.putInt("tradeType", 3)
                    bundle.putBoolean("isNeedBack", false)
                    bundle.putBoolean("isProgress", true)
                    val intent = Intent(mContext, HtmlActivity::class.java)
                    intent.putExtras(bundle)

                    mContext.startActivity(intent)
                }
                // 获取base64格式的图片，保存到本地
                "208" -> {
                    checkPermission(mContext) {
                        //保存图库
                        val base64 = jsonObject.optString("base64")
                        BitmapUtil.saveImageByBitmap(base64ToBitmap(base64.replace("data:image/png;base64,", "")))
                        ToastUtil.showToast(StringUtil.getString(R.string.image_saved))
                    }
                }
                // 跳转分析师观点页面
                "210" -> {
                    EventBus.getDefault().postSticky(
                        StickyEvent(NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_ANALYSES, null)
                    )
                    mContext.finish()
                }
                // 跳转直播间页面
                "211" -> {
                    val liveStreamID = jsonObject.optString("liveStreamID")
                    val id = if (TextUtils.isEmpty(liveStreamID)) {
                        0
                    } else {
                        liveStreamID.toLong()
                    }
                    EventBus.getDefault().postSticky(
                        StickyEvent(
                            NoticeConstants.MAIN_SHOW_SIGNALS_ITEM_LIVE_ROOM,
                            LiveEventData(id, 0, 0)
                        )
                    )
                    mContext.finish()
                }
                // 跳转联系客服页面
                "220" -> {
                    val intent = Intent(mContext, HelpCenterActivity::class.java)
                    val bundle = Bundle()
//                        bundle.putString("type_from", "customer_support")     产品 Jayden Leong 授意修改为不直接进入客服聊天
                    intent.putExtras(bundle)
                    mContext.startActivity(intent)
                }
                // 跳转首页行情页
                "221" -> {
                    mContext.startActivity(Intent(mContext, MainActivity::class.java))
                    EventBus.getDefault().post(NoticeConstants.MAIN_SHOW_POSITION_FIRST)
                }
                // 接收到通知以后，设置点击返回按钮直接退出当前h5页面
                "225" -> {
                    EventBus.getDefault().post(NoticeConstants.HTML_HIDE_TITLE_BAR)
                }
                // 未登录跳转登录页面，已登录跳转入金结果页面
                "232" -> {
                    if (!UserDataUtil.isLogin()) {
                        val intent = Intent(mContext, LoginActivity::class.java)
                        mContext.startActivity(intent)
                        mContext.finish()
                    } else {
                        // 目前au 3.43.0 入金优化 code=232 只当入金结果页处理
                        val orderNo = jsonObject.optString("orderNo")
                        val accountID = jsonObject.optString("accountID")
                        val intent = Intent(mContext, DepositDetailsActivity::class.java)
                        val bundle = Bundle()
                        bundle.putString("orderNo", orderNo)
                        bundle.putString("accountID", accountID)
                        intent.putExtras(bundle)
                        mContext.startActivity(intent)
                        mContext.finish()
                    }
                }
                // 跳转策略详情，与iOS保持一致
                "233" -> {
                    val signalId = jsonObject.optString("signalId")
                    if (!signalId.isNullOrBlank()) {
                        StStrategyDetailsActivity.open(mContext, signalId)
                    }
                }
                // 跳转问卷页面（greenid） 发235代表未答过问卷
                "235" -> {
                    val bundle = Bundle()
                    bundle.putString("from", "235")
                    mContext.startActivity(Intent(mContext, AsicQuestionnaireActivity::class.java).apply { putExtras(bundle) })
                    mContext.finish()
                }
                // 关闭green id 认证页面 并且关闭asic开户页面
                "236" -> {
                    mContext.finish()
                }
                // 银行电汇与出金触发H5开户中的上传功能弹窗
                "237" -> {
                    val activity = mContext as? FragmentActivity?
                    activity?.let {
                        // 校验权限 （读写和相机权限）
                        PermissionUtil.checkPermissionWithCallback(activity, *Constants.PERMISSION_STORAGE, Constants.PERMISSION_CAMERA) { isGranted ->
                            if (isGranted) {
                                if (activity is HtmlActivity) {
                                    activity.showUploadBottomDialog()
                                } else if (activity is NewHtmlActivity) {
                                    activity.showUploadBottomDialog()
                                }
                            } else {
                                ToastUtil.showToast(activity.getString(R.string.please_give_us_settings))
                            }
                        }
                    }
                }
                // 开(跟单)同名  平台账户里限制只有CopyTrading
                "238" -> {
                    if (SpManager.isV1V2()) {
                        (mContext as? Activity)?.let {
                            KycVerifyHelper.showKycDialog(
                                it,
                                mapOf(
                                    Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                    Constants.GoldParam.CAN_ONLY_OPEN_COPY_TRADING to "1"
                                )
                            )
                        }
                    } else {
                        val intent = Intent(mContext, OpenSameNameAccountActivity::class.java)
                        intent.putExtras(bundleOf("from" to "238"))
                        mContext.startActivity(intent)
                    }
                }
                // 出金-绑定2fa
                "239" -> {
                    TFABindActivity.open(mContext, TFAVerifyActivity.FROM_WITHDRAWALS)
                }
                // 出金-验证2fa
                "240" -> {
                    TFAVerifyActivity.open(mContext, TFAVerifyActivity.FROM_WITHDRAWALS)
                }
                // 跳 app 外部浏览器
                "241" -> {
                    val url = jsonObject.optString("url")
                    val uri = Uri.parse(url)
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    mContext.startActivity(intent)
                }
                // 跳转免费订单页面 ，没有免费订单 就跳订单页
                "242" -> {
                    EventBus.getDefault().postSticky(StickyEvent(NoticeConstants.MAIN_SHOW_ORDERS_ITEM_FREE))
                    mContext.startActivity(Intent(mContext, MainActivity::class.java))
                }
                // 通知h5账户类型
                "243" -> {
                    EventBus.getDefault().post(NoticeConstants.NOTICE_H5_ACCOUNT_TYPE)
                }
                // 跳转人脸识别页面
                "244" -> {
                    SumsubPromptActivity.openActivity(mContext, type = jsonObject.optString("type"), campaignId = jsonObject.optString("campaignId"))
                }
                // h5调用截屏功能
                "245" -> {
                    if (mContext is HtmlActivity) {
                        mContext.screenShot()
                    }
                }
                // 跳转开同名页面
                "246" -> {
                    val typeNum = jsonObject.optString("accountTypeNum")
                    if (SpManager.isV1V2()) {
                        (mContext as? Activity)?.let {
                            KycVerifyHelper.showKycDialog(
                                it,
                                mapOf(
                                    Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                    Constants.GoldParam.DEFAULT_ACCOUNT_TYPE_NUM to typeNum
                                )
                            )
                        }
                    } else {
                        val intent = Intent(mContext, OpenSameNameAccountActivity::class.java)
                        if (typeNum.isNullOrBlank().not()) {
                            intent.putExtras(bundleOf("defaultSelectAccountTypeNum" to typeNum))
                        }
                        mContext.startActivity(intent)
                    }
                }
                // telegram登录回调
                "247" -> {
                    SpManager.putTelegramH5Data(jsonObject.optString("data").ifNull())
                    mContext.setResult(Activity.RESULT_OK)
                    mContext.finish()
                }
                // 关闭loading
                "248" -> {
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_CONTROL_LOADING, data = false))
                }
                // 打开load
                "249" -> {
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_CONTROL_LOADING, data = true))
                }
                // 显示h5传过来的title
                "250" -> {
                    val isUseAppTitle = jsonObject.optBoolean("isUseAppTitle")
                    // 是否使用app内传的title ，默认不使用
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_IS_USE_APP_TITLE, data = isUseAppTitle))
                    if (!isUseAppTitle)
                        mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_TITLE, data = jsonObject.optString("title")))
                    // 图标列表(iconList)   h5返回的icon显示列表 ， 顺序是从右到左
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_ICON_LIST, data = GsonUtil.fromJson(jsonObject.optString("iconList"), GsonUtil.getListType(String::class.java))))
                    // 当前h5页面能否有上一级页面 ,能否返回上一级 (isCanBack)
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_IS_CAN_BACK, data = jsonObject.optBoolean("isCanBack")))
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_IS_CAN_CLOSE, data = jsonObject.optBoolean("isCanClose")))
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_IS_HIDE_BACK, data = jsonObject.optBoolean("isHideBack")))
                }
                // h5通知返回上一级页面
                "251" -> {
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_BACK))
                }
                // 跳转lv1开户页面
                "254" -> {
                    checkLogin(mContext) {
                        if (SpManager.isV1V2()) {
                            KycVerifyHelper.showKycDialog(
                                mContext,
                                mapOf(
                                    Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                    Constants.GoldParam.FROM_H5_NEED_SHOW_ALERT to "1"
                                )
                            )
                        } else {
                            val intent = Intent(mContext, OpenAccoGuideLv1Activity::class.java)
                            intent.putExtra("fromWallet", true)
                            intent.putExtra("index", jsonObject.optInt("currentStep"))
                            mContext.startActivity(intent)
                        }
                    }
                }

                // 跳转lv2开户页面
                "255" -> {
                    checkLogin(mContext) {
                        SumSubJumpHelper.isJumpSumSub(mContext, Constants.SUMSUB_TYPE_POI, Bundle().apply {
                            putBoolean("isFromWallet", true)
                        })
                    }
                }
                // 跳转开同名页面
                "256" -> {
                    checkLogin(mContext) {
                        if (SpManager.isV1V2()) {
                            KycVerifyHelper.showKycDialog(
                                mContext,
                                mapOf(
                                    Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT,
                                    Constants.GoldParam.FROM_H5_NEED_SHOW_ALERT to "1"
                                )
                            )
                        } else {
                            val intent = Intent(mContext, OpenSameNameAccountActivity::class.java)
                            intent.putExtra("fromWallet", true)
                            mContext.startActivity(intent)
                        }
                    }
                }
                // 跳转认证中心页面
                "257" -> {
                    checkLogin(mContext) {
                        AuthenticationActivity.openActivity(mContext, defaultSelectedIndex = jsonObject.optInt("defaultSelectedIndex"))
                    }
                }

                "259" -> {
                    if (UserDataUtil.isLogin()) {
                        KycFundsPwdHelper(mContext).forgetFundsPwd()
//                        AddOrForgotSecurityPWDActivity.openActivity(
//                            mContext,
//                            AddOrForgotSecurityPWDActivity.TYPE_CHANGE,
//                            AddOrForgotSecurityPWDActivity.STATE_SETUP
//                        )
                    }
                }

                // 跳转设置资金密码 setFundPassword 方法  回调 isSetted
                "260" -> {
                    if (UserDataUtil.isLogin()) {
                        KycFundsPwdHelper(mContext).setFundsPwd()
//                        AddOrForgotSecurityPWDActivity.openActivity(
//                            mContext,
//                            AddOrForgotSecurityPWDActivity.TYPE_CONFIG,
//                            AddOrForgotSecurityPWDActivity.STATE_SETUP
//                        )
                    }
                }
                // 跳转 h5 传入的url
                "261" -> {
                    val url = jsonObject.optString("url")
                    NewHtmlActivity.openActivity(mContext, url = url)
                }

                // 跳转入金三方页面
                "262" -> {
                    NewHtmlActivity.openActivity(mContext, jsonObject.optString("url"), isDeposit3 = true)
                }

                //弹出h5传过来的toast
                "500" -> {
                    if (!TextUtils.isEmpty(message)) {
                        ToastUtil.showToast(message)
                    }
                }

                // 关闭当前页面
                "501" -> {
                    mContext.finish()
                }
                // h5触发退出登录
                "503" -> {
                    if (UserDataUtil.isLogin()) {
                        // 刷新fcm的状态
                        EventBus.getDefault().post(NoticeConstants.SUBSCRIBE_TOPIC)
                        // 设置退出状态为true
                        SpManager.putExitStatus(true)
                        // 通知到MainActivity做退出登录操作
                        EventBus.getDefault().post(NoticeConstants.LOGOUT_ACCOUNT)
                    }
                }

                // 黄金开户
                "600" -> {
                    // 退出虚拟MT5账户
                    UserDataUtil.exitVirtualAccount()
                }
                // 601 钱包h5调用开户或开同名，点击Got It会调
                "601" -> {
                    KycVerifyHelper.showKycDialog(mContext, mapOf(Constants.GoldParam.CODE to Constants.GoldParam.CODE_OPEN_ACCOUNT, Constants.GoldParam.NEXT_LEVEL to jsonObject.optString("nextLevel"), "source" to jsonObject.optString("source")))
                }
                // 602 调用 补全 用户信息半模态弹窗
                "602" -> {
                    KycVerifyHelper.showSetupTradingAccountDialog(mContext, content = jsonObject.optString("descriptionString"))
                }
                // 603 调用kyc等级半模态弹窗（附带nextLevel参数，表示期望等级，titleString,descriptionString分别表示对应标题和描述)
                "603" -> {
                    KycVerifyHelper.checkKycVerify(mContext, nextLevel = jsonObject.optString("nextLevel").toIntCatching(), title = jsonObject.optString("titleString"), content = jsonObject.optString("descriptionString"), source = jsonObject.optString("source"), isJumpFace = jsonObject.optBoolean("isJumpFace"))
                }
                // 604 h5修改个人信息（手机号、邮箱等）同步app拉取用户信息
                "604" -> {
                    val email = jsonObject.optString("email")
                    if (!email.isNullOrBlank())
                        UserDataUtil.setEmail(email)
                    val phone = jsonObject.optString("phone")
                    if (!phone.isNullOrBlank())
                        UserDataUtil.setUserTel(phone)
                }
                // todo 需求暂时砍掉，所以没有这个交互了
                // 605 h5合并账户，会存在userid和token的变化。需带参数：reserveUserToken、reserveUserId、token、userId、userType
                "605" -> {

                }
                // 606 kyc等级提交后。需同步给app
                "606" -> {
                    EventBus.getDefault().post(NoticeConstants.WS.SOCKET_KYC_LEVEL_CHANGED)
                }
                // 607 kyc 的认证中心
                "607" -> {
                    KycAuthActivity.openActivity(mContext)
                }
                // 608 用于通知支付或者钱包更新页面。触发回调方法 refreshPageState
                "608" -> {
                    val source = jsonObject.optString("source")
                    when (source) {
                        // "wallet", "payment" 通知钱包 跳转入金
                        "wallet", "payment" -> {
                            EventBus.getDefault().post(DataEvent(tag = NoticeConstants.WS.SOCKET_KYC_JUMP_DEPOSIT, data = jsonObject.optString("way")))
                        }
                        // else里包括的 "promo" 和 "App" 都是跳转新的入金页面
                        else -> {
                            NewHtmlActivity.openActivity(mContext, url = UrlConstants.HTML_FUND_DEPOSIT)
                        }
                    }
                }
                // 609 kyc h5触发人机验证
                "609" -> {
                    mViewModel.sendEvent(DataEvent(tag = NoticeConstants.JS.HTML_SHOW_CAPTCHA, data = json))
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        // h5流程神策埋点 -> 交互
        sensorsHelper?.sensorsTrackH5Process(step = HtmlSensorsHelper.SensorsStep.JS_INTERFACE, jsValue = json)
        return ""
    }

    /**
     * 分享等操作前进行权限检查
     */
    private fun checkPermission(activity: Activity, save: () -> Unit) {
        PermissionUtil.checkPermissionWithCallback(activity, *Constants.PERMISSION_STORAGE) {
            if (it)
                save.invoke()
            else
                CenterActionDialog.Builder(activity)
                    .setTitle(context.getString(R.string.save_failed))
                    .setContent(context.getString(R.string.to_save_images))
                    .setEndText(context.getString(R.string.go_settings))
                    .setOnEndListener {
                        IntentUtil.launchAppDetailsSettings()
                    }
                    .build()
                    .showDialog()
        }
    }

    private fun checkLogin(context: Context?, after: (() -> Unit)? = null) {
        if (UserDataUtil.isLogin()) {
            after?.invoke()
        } else {
            context?.startActivity(
                Intent(context, LoginActivity::class.java).setFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            )
        }
    }
}
