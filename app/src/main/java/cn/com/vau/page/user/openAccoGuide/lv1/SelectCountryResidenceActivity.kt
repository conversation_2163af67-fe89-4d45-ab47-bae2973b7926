package cn.com.vau.page.user.openAccoGuide.lv1

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.view.ViewStub
import android.widget.AbsListView
import android.widget.ExpandableListView
import androidx.activity.viewModels
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.core.widget.doAfterTextChanged
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseActivity
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.view.WrapContentLinearLayoutManager
import cn.com.vau.data.account.ResidenceObj
import cn.com.vau.data.account.ResidenceObjList
import cn.com.vau.databinding.*
import cn.com.vau.page.common.selectResidence.adapter.*
import cn.com.vau.page.user.openAccoGuide.lv1.vm.OpenLv1ViewModel
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull

class SelectCountryResidenceActivity : BaseActivity() {

    private val mBinding: ActivitySelectCountryResidenceBinding by lazy { ActivitySelectCountryResidenceBinding.inflate(layoutInflater) }
    private val mergeSearchBinding by lazy { MergeSearchBinding.bind(mBinding.root) }

    private val viewModel: OpenLv1ViewModel by viewModels()

    private val mList = arrayListOf<ResidenceObj>()
    private val searchList = arrayListOf<ResidenceObjList>()
    private val allData = arrayListOf<ResidenceObjList>()
    private var residenceAdapter: ResidenceAdapter = ResidenceAdapter(this, mList, 0)  //内容数据adapter
    private var bigLetterAdapter: BigLetterAdapter = BigLetterAdapter(this, mList)  //大写字母adapter
    private var residenceResultAdapter: SelectRegionAdapter = SelectRegionAdapter(this, searchList, 0)
    private var tempCountryEn = "" //国家英文
    private var tempCountryId = "" //国家Id
    private var searchText = ""
    private var filterClick = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initView() {
        super.initView()
        mBinding.elvResidenceList.setOnGroupClickListener { _, _, _, _ -> true }
        mergeSearchBinding.etSearch.hint = getString(R.string.search_for_country)
        mBinding.mVsNoData.setOnInflateListener(object : ViewStub.OnInflateListener {
            override fun onInflate(stub: ViewStub?, inflated: View) {
                val vs = VsLayoutNoDataBinding.bind(inflated)
                vs.mNoDataView.setHintMessage("${getString(R.string.not_found_desc1)}\n${getString(R.string.not_found_desc2)}")
            }
        })
        val linearLayoutManager = WrapContentLinearLayoutManager(context)
        linearLayoutManager.orientation = RecyclerView.VERTICAL
        mBinding.searchRecyclerView.layoutManager = linearLayoutManager
//        residenceResultAdapter = SelectRegionAdapter(context, searchList, 0)
        mBinding.searchRecyclerView.adapter = residenceResultAdapter
        mBinding.searchRecyclerView.setEmptyView(mBinding.mVsNoData)

    }

    override fun initData() {
        super.initData()
        viewModel.queryResidence(intent.getStringExtra(Constants.IS_FROM))
    }

    override fun initListener() {
        super.initListener()
        mBinding.mHeaderBar.setStartBackIconClickListener {
            finish()
        }
        viewModel.showLoadingData.observe(this) {
            if (it) showNetDialog() else hideNetDialog()
        }
        mergeSearchBinding.ivClear.setOnClickListener {
            mergeSearchBinding.etSearch.setText("")
        }
        mergeSearchBinding.etSearch.doAfterTextChanged {
            searchText = it?.toString().ifNull()
            if (it?.length == 0) {
                mBinding.ctlSearch.visibility = View.GONE
                mergeSearchBinding.ivClear.isVisible = false
            } else {
                mergeSearchBinding.ivClear.isVisible = true
                mBinding.ctlSearch.visibility = View.VISIBLE
                searchResidence(searchText)
            }
        }

        viewModel.residenceLiveData.observe(this) {
            if ("V00000" == it.resultCode) {
                mList.clear()
                mList.addAll(it.data?.obj ?: arrayListOf())
//                residenceAdapter = ResidenceAdapter(this, mList, 0)

                mBinding.elvResidenceList.setAdapter(residenceAdapter)
                for ((index, value) in mList.withIndex()) {
                    if (index != 0) {
                        allData.addAll(value.list ?: arrayListOf())
                    }
                    mBinding.elvResidenceList.expandGroup(index)
                }

                //大写字母
                val linearLayoutManager = WrapContentLinearLayoutManager(this)
                linearLayoutManager.orientation = RecyclerView.VERTICAL
                mBinding.rcyBigLetter.layoutManager = linearLayoutManager
//                bigLetterAdapter = BigLetterAdapter(this, mList)
                mBinding.rcyBigLetter.adapter = bigLetterAdapter
                mBinding.rcyBigLetter.itemAnimator = null
            } else {
                ToastUtil.showToast(it.msgInfo)
            }
        }

        residenceAdapter.setOnNationSelectedListener { groupPosition, childPosition ->
            tempCountryEn = mList.getOrNull(groupPosition)?.list?.getOrNull(childPosition)?.countryNameEn.ifNull()
            tempCountryId = mList.getOrNull(groupPosition)?.list?.getOrNull(childPosition)?.id.toString()
            setResult(
                RESULT_OK, Intent().putExtras(
                    bundleOf(
                        "name" to tempCountryEn,
                        "id" to tempCountryId,
                        "requestCode" to intent.getIntExtra("requestCode", -1)
                    )
                )
            )
            finish()
        }

        bigLetterAdapter.setOnItemClickListener { _, position ->
            mBinding.elvResidenceList.setSelectionFromTop(
                mBinding.elvResidenceList.getFlatListPosition(
                    ExpandableListView.getPackedPositionForGroup(position)
                ), 0
            )
            val letterName = mList.elementAtOrNull(position)?.lettername ?: ""
            bigLetterAdapter.showSelectedName(letterName)
            filterClick = true
        }

        residenceResultAdapter.setOnItemClickListener { view: View?, position: Int ->
            tempCountryEn = searchList.elementAtOrNull(position)?.countryNameEn ?: ""
            tempCountryId = searchList.elementAtOrNull(position)?.id.toString()
            setResult(
                RESULT_OK, Intent().putExtras(
                    bundleOf(
                        "name" to tempCountryEn,
                        "id" to tempCountryId,
                        "requestCode" to intent.getIntExtra("requestCode", -1)
                    )
                )
            )
            finish()
        }

        mBinding.elvResidenceList.setOnScrollListener(object : AbsListView.OnScrollListener {
            override fun onScrollStateChanged(view: AbsListView, scrollState: Int) {}
            override fun onScroll(
                view: AbsListView,
                firstVisibleItem: Int,
                visibleItemCount: Int,
                totalItemCount: Int
            ) {
                val packedPosition: Long =
                    mBinding.elvResidenceList.getExpandableListPosition(firstVisibleItem)
                val positionType = ExpandableListView.getPackedPositionType(packedPosition)
                if (positionType != ExpandableListView.PACKED_POSITION_TYPE_NULL) {
                    val groupPosition = ExpandableListView.getPackedPositionGroup(packedPosition)
                    if (filterClick) {
                        filterClick = false
                    } else {   //透過點擊選擇快捷字母  事後滑動到該字母頁面後就不必再重設定點擊的快捷字母
                        val letterName: String = mList.getOrNull(groupPosition)?.lettername.ifNull()
                        bigLetterAdapter.showSelectedName(letterName)
                    }
                }
            }
        })
    }

    private fun searchResidence(searchText: String) {
        if (allData.size > 0) {
            synchronized(allData) {
                val list = allData.filter { it.countryNameEn?.lowercase()?.contains(searchText.lowercase()) == true }
                if (list.isNotEmpty()) {
                    searchList.clear()
                    searchList.addAll(list)
                    residenceResultAdapter.notifyDataSetChanged()
                } else { //无数据
                    searchList.clear()
                    residenceResultAdapter.notifyDataSetChanged()
                }
            }
        } else { //无数据
            searchList.clear()
            residenceResultAdapter.notifyDataSetChanged()
        }
    }

    companion object {

        const val TYPE_FORM_SIGN_UP = "from_sign_up" // 注册
        const val TYPE_FORM_NOT_ASIC_LV1 = "from_not_asic_lv1" // 非asic lv1

        fun createIntent(context: Context, fromPage: String, requestCode: Int? = null) =
            Intent(context, SelectCountryResidenceActivity::class.java).apply {
                putExtra(Constants.IS_FROM, fromPage)
                putExtra("requestCode", requestCode)
            }
    }
}