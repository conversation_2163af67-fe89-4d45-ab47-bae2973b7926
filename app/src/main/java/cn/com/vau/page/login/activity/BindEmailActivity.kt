package cn.com.vau.page.login.activity

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.MotionEvent
import androidx.core.os.bundleOf
import cn.com.vau.R
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityBindEmailBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.login.ThirdHandleType
import cn.com.vau.page.login.viewmodel.activity.BindEmailViewModel
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.asicOpenAccount.activity.OpenAccountFirstActivity
import cn.com.vau.page.user.login.LoginActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.page.user.openAccoGuide.lv1.OpenAccoGuideLv1Activity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFABindActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity
import cn.com.vau.util.ActivityManagerUtil
import cn.com.vau.util.KeyboardUtil
import cn.com.vau.util.clickNoRepeat

/**
 * author：lvy
 * date：2025/03/18
 * desc：绑定邮箱，包括三方登录方式绑定
 */
class BindEmailActivity : BaseMvvmActivity<ActivityBindEmailBinding, BindEmailViewModel>() {

    override fun initParam(savedInstanceState: Bundle?) {
        mViewModel.signUpRequestBean = intent.getParcelableExtra("signUpRequestBean") // 只有注册页面跳转过来的才会携带这个参数
        mBinding.emailView.setInputText(intent?.getStringExtra("email"))
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        mBinding.tvForgetPwd.text = "${getString(R.string.forgot_password)}?"
        mBinding.pwdView.setHintText("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
    }

    override fun createObserver() {
        // 是否绑定了2fa
        mViewModel.isBind2Fa.observe(this) {
            if (it) { // 跳转账户列表页面
                openActivity(AccountManagerActivity::class.java, bundleOf().apply {
                    putInt(Constants.IS_FROM, 1)
                })
            } else {  // 未绑定 走绑定流程
                TFABindActivity.open(this, TFAVerifyActivity.FROM_LOGIN, mViewModel.signUpRequestBean)
            }
            finish()
            // 关闭来源页面
            ActivityManagerUtil.getInstance().finishActivity(LoginPwdActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(LoginActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(SignUpActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(SignUpAsicActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(InputCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(VerifyEmailCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(VerifySmsCodeActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(SignUpAsicPwdActivity::class.java)
        }
        // 绑定邮箱成功
        mViewModel.bindEmailSuccessLiveData.observe(this) {
            finish()
            ActivityManagerUtil.getInstance().finishActivity(OpenAccountFirstActivity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(OpenAccoGuideLv1Activity::class.java)
            ActivityManagerUtil.getInstance().finishActivity(AccountManagerActivity::class.java)
            // 跳转账户列表页面
            openActivity(AccountManagerActivity::class.java, bundleOf().apply {
                putInt(Constants.IS_FROM, 2)
            })
        }
    }

    override fun initListener() {
        super.initListener()
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 邮箱
        mBinding.emailView.afterTextChangedListener {
            checkNextBtn()
        }
        // 密码
        mBinding.pwdView.afterTextChangedListener {
            checkNextBtn()
        }
        // 忘记密码
        mBinding.tvForgetPwd.clickNoRepeat {
            openActivity(LoginPwdActivity::class.java, bundleOf().apply {
                putBoolean("isShowEmail", false)
                putInt("isFrom", 1)
            })
        }
        // 下一步
        mBinding.tvNext.clickNoRepeat {
            when (mViewModel.signUpRequestBean?.thirdHandleType) {
                ThirdHandleType.TELEGRAM -> { // 只有注册页面 SignUpAsicPwdActivity 才存在三方登录情况，其他都是走 else
                    mViewModel.thirdPartyBindApi(mBinding.emailView.getInputText(), mBinding.pwdView.getInputText())
                }

                else -> {
                    mViewModel.bindEmailApi(mBinding.emailView.getInputText(), mBinding.pwdView.getInputText())
                }
            }
        }
    }

    /**
     * 检测按钮是否可点击
     */
    private fun checkNextBtn() {
        mBinding.tvNext.isEnabled = mBinding.emailView.getInputText().isNotBlank() && mBinding.pwdView.getInputText().isNotBlank()
    }

    override fun dispatchTouchEvent(event: MotionEvent?): Boolean {
        KeyboardUtil.hideSoftKeyboard(this, currentFocus?.rootView, event, R.id.bgView)
        return super.dispatchTouchEvent(event)
    }
}