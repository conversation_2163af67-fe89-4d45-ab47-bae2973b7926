package cn.com.vau.page.user.accountManager.adapter

import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.util.*
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class DemoAccountAdapter: BaseQuickAdapter<AccountTradeBean, BaseViewHolder>(R.layout.item_manage_account_live_normal) {

    private val selectedAccountCd by lazy { UserDataUtil.accountCd() }

    var demoAccountNum = 0

    init {
        addChildClickViewIds(R.id.tvReset, R.id.tvDemoReset, R.id.llExt, R.id.ivSwitch, R.id.linearLayout, R.id.ivClose, R.id.ivSave)
    }

    override fun convert(holder: BaseViewHolder, item: AccountTradeBean) {
        holder.setText(R.id.tvAccountType, context.getString(R.string.demo))
        holder.setBackgroundResource(R.id.tvAccountType, R.drawable.shape_ce35728_r100)
        holder.setGone(R.id.llEditNick, true)
        holder.setGone(R.id.linearLayout, true)     // Demo账号不显示账户昵称
        holder.setText(R.id.tvAccountNo, item.acountCd)
        holder.setText(R.id.tvAccountAmount, when (item.equitySuccess) {
            "1" -> { item.detailData?.equity?.numCurrencyFormat(item.detailData?.currencyType.ifNull()) }
            "2" -> { "-" }
            else -> { "..." }
        })
        holder.setText(R.id.tvAccountAmountUnit, when (item.equitySuccess) {
            "1" -> { item.detailData?.currencyType}
            else -> { "" }
        })
        holder.setVisible(R.id.tvReset, item.isArchive == true && !item.showAccountInfo)
        holder.setVisible(R.id.tvDemoReset, true)
        holder.setGone(R.id.ivSwitch, demoAccountNum <= 1)
        //demo账户不涉及监管Upgrade逻辑
        holder.setGone(R.id.tvLiveUpgrade,true)

        holder.setBackgroundResource(
            R.id.clAccountCard,
            if (selectedAccountCd == item.acountCd)
                R.drawable.draw_shape_stroke_c1e1e1e_cebffffff_r10
            else
                R.drawable.draw_shape_stroke_c331e1e1e_c33ffffff_r10
        )
        // Demo账户不涉及Read Only

        when (item.secondSuccess) {
            "1" -> { // 成功
                val detailData = item.detailData
                // 可用保证金
                val freeMargin = detailData?.guarantee.ifNull()
                // 净值
                val equity = detailData?.equity.ifNull()
                // Win Rate
                holder.setText(R.id.tvWinRateNum, "${detailData?.profitRate?.numFormat(2)}%")
                // Free Margin
                holder.setText(R.id.tvFreeMarginNum, "${freeMargin.numCurrencyFormat(detailData?.currencyType ?: "")} ${detailData?.currencyType}".arabicReverseTextByFlag(" "))
                // Profit
                holder.setText(R.id.tvProfitNum, "${(detailData?.profit ?: "").numCurrencyFormat(detailData?.currencyType ?: "")} ${detailData?.currencyType}".arabicReverseTextByFlag(" "))
                // Margin Level
                holder.setText(R.id.tvMarginLevelNum, if (freeMargin == equity) "---"
                else "${detailData?.payRate?.numFormat(2)}%")
                // Last Login
                val lastLoginDate = if(detailData?.lastLoginDate.isNullOrBlank()) "" else detailData?.lastLoginDate
//                holder.setGone(R.id.tvLoginTime, TextUtils.isEmpty(lastLoginDate))
                holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, lastLoginDate))
                // Type
                holder.setText(R.id.tvTypeNum, detailData?.accountTypeName ?: "-")
                // Leverage
                holder.setText(R.id.tvLeverageNum, "${detailData?.leverage}:1") //杠杆
            }
            "2" -> { // 失败
                holder.setText(R.id.tvWinRateNum, "-")
                holder.setText(R.id.tvFreeMarginNum, "-")
                holder.setText(R.id.tvProfitNum, "-")
                holder.setText(R.id.tvMarginLevelNum, "-")
                holder.setText(R.id.tvTypeNum, "-")
                holder.setText(R.id.tvLeverageNum, "-")
                holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, ""))
            }
            else -> { // 未加载
                holder.setText(R.id.tvWinRateNum, "...")
                holder.setText(R.id.tvFreeMarginNum, "...")
                holder.setText(R.id.tvProfitNum, "...")
                holder.setText(R.id.tvMarginLevelNum, "...")
                holder.setText(R.id.tvTypeNum, "...")
                holder.setText(R.id.tvLeverageNum, "...")
                holder.setText(R.id.tvLoginTime, context.getString(R.string.last_log_in_x, ""))
            }
        }
        // Demo账户不涉及归档状态

        val clAccountInfo = holder.getView<ConstraintLayout>(R.id.clAccountInfo)
        if (item.showAccountInfo) {
            if (clAccountInfo.visibility == View.GONE) {
                clAccountInfo.visibility = View.VISIBLE
                holder.setImageResource(R.id.ivExtent, R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff)
            }
        } else {
            if (clAccountInfo.visibility == View.VISIBLE) {
                clAccountInfo.visibility = View.GONE
                holder.setImageResource(R.id.ivExtent, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
            }
        }
    }
}