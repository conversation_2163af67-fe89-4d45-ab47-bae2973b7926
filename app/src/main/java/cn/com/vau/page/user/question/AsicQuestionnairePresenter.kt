package cn.com.vau.page.user.question

import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.data.account.AuditQuestionData
import cn.com.vau.data.account.QuestionObj
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.widget.dialog.CenterActionDialog
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class AsicQuestionnairePresenter : AsicQuestionnaireContract.Presenter() {
    var dataList: MutableList<QuestionObj> = mutableListOf()
    var questionNum = ""

    override fun getQustionList() {
        val map = hashMapOf<String, Any>()
        mView?.showNetDialog()
        map["userId"] = UserDataUtil.userId()
        mModel?.getQustionList(map,
            object : BaseObserver<AuditQuestionData>() {
                override fun onNext(data: AuditQuestionData) {
                    mView?.hideNetDialog()
                    if (data.resultCode == "V00000") {
                        val list = data.data.obj?.list
                        questionNum = data.data.obj?.questionNum.ifNull()

                        if (list?.isNotEmpty() == true) {
                            dataList.clear()
                            dataList.addAll(list)
                            mView?.updateView()
                        }
                    } else if (data.resultCode == "V10034") {
                        mView?.showFailedDialog2(data.data.obj?.incorrectAnswerPrompt, data.data.obj?.jumpLink)
                    } else {
                        ToastUtil.showToast(data.msgInfo)
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            })
    }

    override fun submitAnswer(selectMap: MutableMap<Int, String>?) {
        if (selectMap?.size.ifNull() < 10) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_finish_the_questionnaire))
            return
        }
        val map = hashMapOf<String, Any>()
        map["userId"] = UserDataUtil.userId()
        map["questionNum"] = questionNum
        dataList.forEachIndexed { index, _ ->
            val str = selectMap?.get(index).ifNull()
            if (!TextUtils.isEmpty(str))
                map["userAnswer[$index]"] = str
        }
        if (map.size < 11) {
            ToastUtil.showToast(mView?.ac?.getString(R.string.please_finish_the_questionnaire))
            return
        }
        mView?.showNetDialog()
        mModel?.submitAnswer(map,
            object : BaseObserver<AuditQuestionData>() {
                override fun onNext(data: AuditQuestionData) {
                    mView?.hideNetDialog()
                    if (data.resultCode != "V10033" && data.resultCode != "V10034") {
                        CenterActionDialog.Builder(context)
                            .setContent(data.msgInfo)
                            .setSingleButton(true)
                            .setSingleButtonText(context.getString(R.string.ok))
                            .setOnSingleButtonListener {
                                mView?.onSubmitResult(data)
                            }
                            .build().showDialog()
                        return
                    }
                    mView?.onSubmitResult(data)
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            })
    }
}