package cn.com.vau.page.user.loginPwd

import android.os.Bundle
import android.text.TextUtils
import cn.com.vau.R
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.DbManager
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.greendao.dbUtils.UserEmailHistory
import cn.com.vau.common.greendao.dbUtils.UserPhoneHistory
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean
import cn.com.vau.data.account.LoginBean
import cn.com.vau.data.account.PasskeyLoginData
import cn.com.vau.data.account.SelectCountryNumberBean
import cn.com.vau.data.account.SelectCountryNumberObjDetail
import cn.com.vau.data.profile.TelegramGetBotBean
import cn.com.vau.page.login.LoginHandleType
import cn.com.vau.page.login.SignUpRequestBean
import cn.com.vau.page.login.ThirdHandleType
import cn.com.vau.page.login.activity.SignUpActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.login.EnterPasswordActivity
import cn.com.vau.page.user.login.VerificationActivity
import cn.com.vau.page.user.login.model.LoginPwdVM
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFABindActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.TFAVerifyActivity
import cn.com.vau.util.AESUtil
import cn.com.vau.util.RegexUtil
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.ifNull
import cn.com.vau.util.json
import cn.com.vau.util.language.LanguageUtil
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.FirebaseManager
import io.reactivex.disposables.Disposable
import org.json.JSONObject

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class LoginPwdPresenter : LoginPwdContract.Presenter() {

    var areaCodeData: SelectCountryNumberObjDetail = SelectCountryNumberObjDetail()

    var viewModel: LoginPwdVM? = null

    /**
     * 1:Facebook登录后    0:非三方登录
     */
    var handleType = 0

    /**
     * 0:手机号 1:邮箱
     */
    var nextType = 0

    var isFromLoginActivity = false // 因为Presenter是复用的，所以需要区分是哪个页面调用的。主要用于三方登录成功后绑定账号使用

    override fun getLocalTel() {
        val countryCode = SpManager.getCountryCode(Constants.defaultCountryCode)
        val countryNum = SpManager.getCountryNum()
        val countryName = SpManager.getCountryName(
            if (countryNum == Constants.defaultCountryNum) Constants.defaultCountryName else ""
        )

        if (TextUtils.isEmpty(countryNum)) {
            getAreaCodeApi()
            return
        }

        areaCodeData.countryCode = countryCode
        areaCodeData.countryNum = countryNum
        areaCodeData.countryName = countryName

        viewModel?.setAreaCode(countryNum)
    }

    override fun passkeyLoginApi(mobile: String?, challenge: String?, loginJsonResult: String?, passkeyId: String) {
        val phoneCountryCode =
            if (nextType == 0) areaCodeData.countryCode ?: Constants.defaultCountryCode else ""
        val code = if (nextType == 0) areaCodeData.countryNum ?: Constants.defaultCountryNum else ""

        if (nextType == 0) {
            if (TextUtils.isEmpty(mobile) ||
                (code == "86" && mobile?.length != 11) ||
                (code != "86" && (mobile?.length ?: 0) > 15)
            ) {
                ToastUtil.showToast(context.getString(R.string.please_enter_the_number))
                return
            }
        } else {
            if (!RegexUtil.isEmail(mobile)) {
                ToastUtil.showToast(context.getString(R.string.please_enter_the_correct_mail))
                return
            }
        }
        mView?.showNetDialog()
        val map = hashMapOf<String, Any>()
        map["count"] = mobile ?: ""
        map["countryCode"] = phoneCountryCode
        map["code"] = code
        map["type"] = 17
        map["passKeyId"] = passkeyId
        map["challenge"] = challenge ?: ""
        map["loginJsonResult"] = loginJsonResult ?: ""

        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mModel?.pwdLoginApi(
            paramMap,
            object : BaseObserver<LoginBean>() {
                override fun onNext(data: LoginBean) {
                    mView?.hideNetDialog()
                    dealLoginData(data, mobile ?: "", "", TFAVerifyActivity.FROM_LOGIN, 0)
                    if (data.resultCode != "V10017" && data.resultCode != "V10016") {
                        mView?.showSoftInput()
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                    mView?.showSoftInput()
                }
            }
        )
    }

    override fun pwdLoginApi(mobile: String?, pwd: String?, recaptcha: String?) {
        val phoneCountryCode = if (nextType == 0) areaCodeData.countryCode ?: Constants.defaultCountryCode else ""
        val code = if (nextType == 0) areaCodeData.countryNum ?: Constants.defaultCountryNum else ""

        if (nextType == 0) {
            if (TextUtils.isEmpty(mobile) ||
                (code == "86" && mobile?.length != 11) ||
                (code != "86" && (mobile?.length ?: 0) > 15)
            ) {
                ToastUtil.showToast(context.getString(R.string.please_enter_the_number))
                return
            }
        } else {
            if (!RegexUtil.isEmail(mobile)) {
                ToastUtil.showToast(context.getString(R.string.please_enter_the_correct_mail))
                return
            }
        }

        mView?.showNetDialog()

        val map = hashMapOf<String, Any?>()
        map["count"] = mobile ?: ""
        map["userPassword"] = pwd ?: ""
        map["recaptcha"] = recaptcha ?: ""
        if (nextType == 0) {
            map["countryCode"] = phoneCountryCode
            map["code"] = code
        }
        if (handleType == 1) {
            if (nextType == 0) {
                map["userTel"] = mobile ?: ""
            } else {
                map["userEmail"] = mobile ?: ""
            }
            SpManager.getTelegramH5Data()?.let { bean ->
                map["thirdpartyType"] = "4"
                map["telegramId"] = bean.id.ifNull()
                map["first_name"] = bean.first_name.ifNull()
                map["last_name"] = bean.last_name.ifNull()
                map["auth_date"] = bean.auth_date.ifNull()
                map["hash"] = bean.hash.ifNull()
                map["username"] = bean.username.ifNull()
                map["photo_url"] = bean.photo_url.ifNull()
            }
            // 三方账号绑定
            val paramMap = hashMapOf<String, Any?>()
            paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
            mModel?.bindUserApi(
                paramMap,
                object : BaseObserver<LoginBean>() {
                    override fun onNext(data: LoginBean) {
                        mView?.hideNetDialog()
                        dealLoginData(data, mobile ?: "", pwd ?: "", TFAVerifyActivity.FROM_TELEGRAM_BIND, 1)
                    }

                    override fun onHandleSubscribe(d: Disposable?) {
                        mRxManager.add(d)
                    }

                    override fun onError(e: Throwable?) {
                        super.onError(e)
                        mView?.hideNetDialog()
                    }
                }
            )
        } else {
            // loginNew
            val paramMap = hashMapOf<String, Any?>()
            paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
            mModel?.pwdLoginApi(
                paramMap,
                object : BaseObserver<LoginBean>() {
                    override fun onNext(data: LoginBean) {
                        mView?.hideNetDialog()
                        dealLoginData(data, mobile ?: "", pwd ?: "", TFAVerifyActivity.FROM_LOGIN, 0)
                    }

                    override fun onHandleSubscribe(d: Disposable?) {
                        mRxManager.add(d)
                    }

                    override fun onError(e: Throwable?) {
                        super.onError(e)
                        mView?.hideNetDialog()
                    }
                }
            )
        }
    }

    override fun dealLoginData(loginBean: LoginBean, userTel: String, userPassword: String, fromType: String, handleType: Int) {
        this.handleType = handleType
        if (loginBean.data?.obj?.crmUserId.isNullOrBlank().not()) {
            SpManager.putCrmUserId(loginBean.data?.obj?.crmUserId.ifNull())
        }
        SpManager.putSuperviseNum(loginBean.data?.obj?.regulator ?: "0")
        val bundle = Bundle()
        when (loginBean.resultCode) {
            /*
            16,17 账户列表
            05 绑定页面
             */
            "V10017", "V10016" -> { // 用户登陆成功
                saveUserData(loginBean, userPassword)
                // Fcm上报推送设备号
                val localFcmToken = SpManager.getTokenFcm()
                FirebaseManager.bindFcmToken(localFcmToken)

                val isBind2FA = loginBean.data?.obj?.twoFactorUser == true
                val userid = loginBean.data?.obj?.userId.ifNull()
                val isBinded = SpManager.getUser2faBindEd(userid, false)
                if (isBind2FA || isBinded) {
                    // 走原逻辑
                    bundle.putInt(Constants.IS_FROM, 1)
                    openActivity(AccountManagerActivity::class.java, bundle)
                } else {
                    // 未绑定 走绑定流程
                    TFABindActivity.open(context, fromType)
                }
            }
            // 老用户第一登录，绑定手机号码
            // PS: 没有绑定邮箱场景，只是在注册时，填一个通过CRM注册的邮箱未绑定过账户的手机号，就会提示用邮箱登录
            "V10005" -> {
                val userBean = loginBean.data?.obj
//                bundle.putString(Constants.USER_EMAIL, userTel)
//                bundle.putString(Constants.USER_PWD, userPassword)
//                bundle.putInt(Constants.HANDLE_TYPE, handleType)
//                mView?.goBind(bundle)
                emailSendEmailCodeApi("4", userBean?.email ?: userTel, userPassword)
                // 绑定神策业务ID，场景3账号合并前
                SensorsDataUtil.bindBusinessIdForLogin(
                    userBean?.userTel,
                    userBean?.email ?: userTel,
                    userBean?.emailEventID,
                    userBean?.crmUserId
                )
            }

            "V10020" -> {
                mView?.showDialogToChangePassword()
            }

            "V10060" -> {
                if (handleType == 1) {
                    mView?.showThirdLoginCaptcha()
                } else {
                    mView?.showCaptcha()
                }
            }

            "V10035" -> { // 更换设备会走这里
                val bean = loginBean.data?.obj
                if (SpManager.isV1V2()) {
                    if (nextType == 0) { // 手机号OTP
                        val mobile = bean?.userTel.ifNull()
                        val countryCode = bean?.countryCode.ifNull()
                        val code = bean?.code.ifNull()
                        getBindingTelSMSApi(userTel, userPassword, mobile, countryCode, code, "10", "")
                    } else { // 邮箱OTP
                        val email = bean?.email.ifNull()
                        emailSendEmailCodeApi("10", email, userPassword)
                    }
                } else {
                    val mobile = bean?.userTel.ifNull()
                    val countryCode = bean?.countryCode.ifNull()
                    val code = bean?.code.ifNull()
                    getBindingTelSMSApi(userTel, userPassword, mobile, countryCode, code, "10", "")
                }
            }

            // 需要走 2fa 验证流程
            "V10036" -> {
                val obj = loginBean.data?.obj
                try {
                    val signUpRequestBean = SignUpRequestBean(
                        mobile = obj?.userTel.ifNull(userTel),
                        countryCode = obj?.countryCode,
                        countryNum = obj?.code,
                        email = obj?.email,
                        pwd = userPassword,
                        handleType = if (nextType == 0) LoginHandleType.PHONE else LoginHandleType.EMAIL,
                        thirdHandleType = if (this.handleType == 0) null else ThirdHandleType.TELEGRAM
                    )
                    TFAVerifyActivity.open(
                        context,
                        fromType,
                        LoginVeriParam(
                            email = if (nextType == 1) userTel else "",
                            mobile = obj?.userTel ?: userTel,
                            countryCode = obj?.countryCode,
                            areaCode = obj?.code,
                            userPassword = userPassword,
                            handleType = handleType,
                            nextType = nextType,
                            needAuthOptList = obj?.needAuthOptList
                        ),
                        signUpRequestBean = signUpRequestBean
                    )
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

//            else -> {
//                ToastUtil.showToast(loginBean.msgInfo)
//            }
            "V50000" -> { // 三方登录会走到这里
                val userBean = loginBean.data?.obj
                bundle.putInt(Constants.HANDLE_TYPE, handleType)
                // 失败时返回,0:接口错误, 4:绑定手机, 5:账号绑定, 6:注册, 8:用户首次登录或者密码变更
                when (userBean?.status) {
                    "4" -> {
                        emailSendEmailCodeApi("4", userBean.email.ifNull(), "")
                    }

                    "5" -> {
                        if (isFromLoginActivity) {
                            openActivity(LoginPwdActivity::class.java, bundle)
                        } else {
                            mView?.thirdLoginSuccessRefreshView()
                        }
                    }

                    "6" -> openActivity(SignUpActivity::class.java, bundle)
                    "8" -> openActivity(EnterPasswordActivity::class.java, bundle)

                    else -> {
                        this.handleType = 0
                        ToastUtil.showToast(loginBean.msgInfo)
                    }
                }
            }

            else -> {
                this.handleType = 0
                ToastUtil.showToast(loginBean.msgInfo)
            }
        }
    }

    override fun getBindingTelSMSApi(userTel: String, userPassword: String, mobile: String, phoneCountryCode: String, code: String, type: String, validateCode: String) {
        mView?.showNetDialog()
        val params = HashMap<String, Any>()
        if (validateCode.isNotEmpty()) {
            params["recaptcha"] = validateCode
            params["smsCodeId"] = SpManager.getSmsCodeId("")
        }
        params["userTel"] = mobile
        params["type"] = type // 类型
        params["phoneCountryCode"] = phoneCountryCode
        params["userPassword"] = userPassword
        params["code"] = code
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(params.json, AESUtil.PWD_AES_KEY)
        mModel?.getBindingTelSMSApi(
            paramMap,
            object : BaseObserver<ForgetPwdVerificationCodeBean>() {
                override fun onHandleSubscribe(d: Disposable) {
                    mRxManager.add(d)
                }

                override fun onNext(baseBean: ForgetPwdVerificationCodeBean) {
                    mView?.hideNetDialog()
                    SpManager.putSmsCodeId("")
                    if ("V00000" == baseBean.resultCode) {
                        if (baseBean.msgInfo.isNullOrEmpty().not()) {
                            ToastUtil.showToast(baseBean.msgInfo)
                        }
                        VerificationActivity.openActivity(
                            context,
                            VerificationActivity.TYPE_LOGIN,
                            VerificationActivity.TYPE_SEND_SMS,
                            LoginVeriParam(userTel, userPassword, mobile, phoneCountryCode, code, handleType, nextType)
                        )
                    } else {
                        if ("V10060" == baseBean.resultCode) { // 易盾 需要滑动窗口
                            if (baseBean.data != null && baseBean.data?.obj != null) {
                                SpManager.putSmsCodeId(baseBean.data?.obj?.smsCodeId)
                            }
                            mView?.showVeriCaptcha(userTel, mobile, phoneCountryCode, code, userPassword)
                            return
                        }
                        ToastUtil.showToast(baseBean.msgInfo)
                    }
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            }
        )
    }

    /**
     * 发送邮箱验证码
     */
    override fun emailSendEmailCodeApi(type: String, email: String, pwd: String) {
        mView?.showNetDialog()
        val params = HashMap<String, Any?>()
        params["email"] = email // 邮箱
        params["bizType"] = type // 4-首次登录验证邮箱
        mModel?.emailSendEmailCodeApi(
            params,
            object : BaseObserver<ForgetPwdVerificationCodeBean>() {
                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onNext(baseBean: ForgetPwdVerificationCodeBean) {
                    mView?.hideNetDialog()
                    if (baseBean.resultCode == "V10060") { // 易盾 需要滑动窗口
                        mView?.showVerifyEmail(email, pwd, type)
                        return
                    }
                    if ("V00000" != baseBean.resultCode) {
                        ToastUtil.showToast(baseBean.msgInfo)
                        return
                    }
                    ToastUtil.showToast(baseBean.msgInfo)
                    val txId = baseBean.data?.obj?.txId.ifNull()
                    // 跳转验证邮箱OTP
                    mView?.goEmailOTPVerify(email, pwd, txId, type)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            }
        )
    }

    /**
     * 三方登录 -> telegram
     */
    override fun thirdLoginTelegramApi(validate: String?, userPassword: String?) {
        val map = hashMapOf<String, Any?>()
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        if (!validate.isNullOrBlank()) {
            map["recaptcha"] = validate.ifNull()
        }
        if (!userPassword.isNullOrBlank()) {
            map["userPassword"] = userPassword.ifNull()
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        mView?.showNetDialog()
        mModel.thirdPartyLoginApi(
            paramMap,
            object : BaseObserver<LoginBean>() {
                override fun onHandleSubscribe(d: Disposable) {
                    mRxManager.add(d)
                }

                override fun onNext(data: LoginBean) {
                    mView?.hideNetDialog()
                    dealLoginData(data, "", userPassword.ifNull(), TFAVerifyActivity.FROM_TELEGRAM_LOGIN, 1)
                }

                override fun onError(e: Throwable) {
                    super.onError(e)
                    mView?.hideNetDialog()
                }
            }
        )
    }

    /**
     * telegram-获取botId
     */
    override fun telegramGetBotIdApi() {
        mView?.showNetDialog()
        mModel.telegramGetBotIdApi(object : BaseObserver<TelegramGetBotBean>() {
            override fun onHandleSubscribe(d: Disposable) {
                mRxManager.add(d)
            }

            override fun onNext(bean: TelegramGetBotBean) {
                mView?.hideNetDialog()
                if ("V00000" != bean.resultCode) {
                    ToastUtil.showToast(bean.msgInfo)
                    return
                }
                mView?.telegramGetBotIdSuccess(bean.data?.obj)
            }

            override fun onError(e: Throwable) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    /**
     * 获取用户是否设置了passkey
     * jsonData为空走密码登录流程，不为空走passkey登录流程
     */
    override fun passKeyLoginDataApi(phone: String, email: String) {
        val params = HashMap<String, String?>()
        if (phone.isNotEmpty()) {
            params["phone"] = phone
            params["code"] = areaCodeData.countryNum
        }
        if (email.isNotEmpty()) {
            params["email"] = email
        }
        mModel?.passKeyLoginDataApi(
            params,
            object : BaseObserver<PasskeyLoginData>() {
                override fun onNext(t: PasskeyLoginData?) {
                    if (t?.data?.obj?.jsonData.isNullOrEmpty()) {
//                    mView?.hideNetDialog()
//                    mView?.showSoftInput()
                        return
                    }
                    mView?.startPasskey(t?.data?.obj?.jsonData ?: "", t?.data?.obj?.passKeyId ?: "")
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                    mView?.showSoftInput()
                }
            }
        )
    }

    override fun saveUserData(loginBean: LoginBean, userPassword: String) {
        val userTel = loginBean.data?.obj?.userTel
        val areaCode = loginBean.data?.obj?.code
        val countryCode = loginBean.data?.obj?.countryCode
        val email = loginBean.data?.obj?.email
        UserDataUtil.setUserTel(userTel)
        UserDataUtil.setCountryCode(countryCode)
        UserDataUtil.setAreaCode(areaCode)
        UserDataUtil.setUserId(loginBean.data?.obj?.userId)
        UserDataUtil.setUserType(if (loginBean.resultCode == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(loginBean.data?.obj?.token)
        UserDataUtil.setXToken(loginBean.data?.obj?.xtoken)
        val fastCloseState = loginBean.data?.obj?.fastCloseState
        UserDataUtil.setFastCloseState(if (TextUtils.isEmpty(fastCloseState)) "2" else fastCloseState)
        val fastCloseCopyOrder = loginBean.data?.obj?.fastCloseCopyOrder // 快速停止跟单
        UserDataUtil.setFastStopCopyState(if (TextUtils.isEmpty(fastCloseCopyOrder)) "2" else fastCloseCopyOrder)
        val orderConfirmation = loginBean.data?.obj?.orderConfirmation
        UserDataUtil.setOrderConfirmState(if (TextUtils.isEmpty(orderConfirmation)) "2" else orderConfirmation)
        UserDataUtil.setEmail(email)
        UserDataUtil.setUserNickName(loginBean.data?.obj?.userNick)
        UserDataUtil.setUserPic(loginBean.data?.obj?.pic)
        UserDataUtil.setUserPassword(userPassword)

        LogEventUtil.mFirebaseAnalytics.setUserId(areaCode + userTel)
        SpManager.putScreenshotShareEnable(loginBean.data?.obj?.screenShare != "0")
        if (nextType == 1) {
            // 邮箱
            if (!email.isNullOrBlank()) {
                val userEmailHistory = UserEmailHistory()
                userEmailHistory.email = email
                DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
            }
        } else {
            // 手机号
            if (!userTel.isNullOrBlank()) {
                val userPhoneHistory = UserPhoneHistory()
                userPhoneHistory.phoneNumber = userTel
                DbManager.getInstance().saveUserPhoneHistory(userPhoneHistory)
                SpManager.putUserTel(userTel.ifNull())
                SpManager.putCountryCode(countryCode.ifNull()) // 国家code
                SpManager.putCountryNum(areaCode.ifNull())
                SpManager.putCountryName(areaCodeData.countryName.ifNull())
            }
        }

        // 绑定神策业务ID，场景4、场景2
        val userBean = loginBean.data?.obj
        SensorsDataUtil.bindBusinessIdForLogin(
            userBean?.userTel,
            userBean?.email,
            userBean?.emailEventID,
            userBean?.crmUserId
        )

        // 登录firebase
        FirebaseManager.userLogin()

//        if (handleType == 1) EventBus.getDefault().post(NoticeConstants.REFRESH_PERSONAL_INFO_DATA)
    }

    override fun setSelectAreaData(areaCodeData: SelectCountryNumberObjDetail) {
        this.areaCodeData = areaCodeData
    }

    override fun getAreaCodeApi() {
        runCatching {
            val systemCountry = LanguageUtil.getSystemLocal(context)?.country // 获取手机系统语言代码
            if (systemCountry.isNullOrBlank()) {
                initAreaData()
                return
            }
            mView?.showNetDialog()
            mModel?.getAreaCodeApi(object : BaseObserver<SelectCountryNumberBean>() {
                override fun onNext(dataBean: SelectCountryNumberBean?) {
                    mView?.hideNetDialog()
                    if (dataBean?.resultCode != "V00000") {
                        initAreaData()
                        return
                    }
                    val objList = dataBean.data?.obj
                    out@ for (countryNumberObj in objList ?: ArrayList()) {
                        for (countryBean in countryNumberObj.list ?: ArrayList()) {
                            if (countryBean.countryCode == systemCountry) {
                                areaCodeData.countryCode = countryBean.countryCode
                                areaCodeData.countryNum = countryBean.countryNum
                                areaCodeData.countryName = countryBean.countryName
                                viewModel?.setAreaCode(areaCodeData.countryNum)
                                SpManager.putCountryNum(countryBean.countryNum.ifNull())
                                SpManager.putCountryName(countryBean.countryName.ifNull())
                                SpManager.putCountryCode(countryBean.countryCode.ifNull())
                                break@out
                            }
                        }
                    }

                    if (areaCodeData.countryCode.isNullOrBlank()) {
                        initAreaData()
                    }
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    mRxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    mView?.hideNetDialog()
                    initAreaData()
                }
            })
        }.onFailure {
            initAreaData()
        }
    }

    private fun initAreaData() {
        areaCodeData.countryCode = Constants.defaultCountryCode
        areaCodeData.countryNum = Constants.defaultCountryNum
        areaCodeData.countryName = Constants.defaultCountryName

        viewModel?.setAreaCode(areaCodeData.countryNum)

        SpManager.putCountryNum(Constants.defaultCountryNum)
        SpManager.putCountryName(Constants.defaultCountryName)
        SpManager.putCountryCode(Constants.defaultCountryCode)
    }

    /**
     * 神策自定义埋点（ThirdLogin）
     * Telegram绑定点击 -> 用户点击绑定Telegram
     */
    fun sensorsTrackThirdLogin(position: String) {
        SensorsDataUtil.track(
            SensorsConstant.ThirdLogin.LOGIN_TELEGRAM_CLICK,
            JSONObject().apply {
                put(SensorsConstant.Key.POSITION, position)
            }
        )
    }
}