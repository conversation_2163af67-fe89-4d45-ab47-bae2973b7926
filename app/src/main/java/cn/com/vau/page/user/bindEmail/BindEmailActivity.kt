package cn.com.vau.page.user.bindEmail

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import cn.com.vau.R
import cn.com.vau.common.base.activity.BaseFrameActivity
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.constants.Constants
import cn.com.vau.common.greendao.dbUtils.*
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.LoginBean
import cn.com.vau.databinding.ActivityBindEmailBinding
import cn.com.vau.page.customerservice.HelpCenterActivity
import cn.com.vau.page.user.accountManager.AccountManagerActivity
import cn.com.vau.page.user.loginPwd.LoginPwdActivity
import cn.com.vau.profile.activity.twoFactorAuth.activity.*
import cn.com.vau.util.*
import cn.com.vau.util.tracking.LogEventUtil
import cn.com.vau.util.widget.FirebaseManager
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng.
 */
class BindEmailActivity : BaseFrameActivity<BindEmailPresenter, BindEmailModel>(), BindEmailContract.View {

    private val mBinding: ActivityBindEmailBinding by lazy { ActivityBindEmailBinding.inflate(layoutInflater) }

    private var loginInfo: LoginBean? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(mBinding.root)
    }

    override fun initParam() {
        super.initParam()
        mPresenter.handleType = intent?.extras?.getInt(Constants.HANDLE_TYPE) ?: 0
        mPresenter.isFrom = intent?.extras?.getInt("isFrom") ?: 0
        mPresenter.phoneNum = intent?.extras?.getString("phoneNum") ?: ""
        mPresenter.phoneCode = intent?.extras?.getString("phoneCode") ?: ""
        mPresenter.countryCode = intent?.extras?.getString("countryCode") ?: ""
        mBinding.emailView.setInputText(intent?.extras?.getString("email", "") ?: "")
    }

    @SuppressLint("SetTextI18n")
    override fun initView() {
        super.initView()
        mBinding.tvForgetPwd.text = "${getString(R.string.forgot_password)}?"
        mBinding.pwdView.setHintText("${getString(R.string.password)} ${getString(R.string._8_16_characters)}")
    }

    override fun initListener() {
        super.initListener()
        // 客服
        mBinding.mHeaderBar.setEndIconClickListener {
            openActivity(HelpCenterActivity::class.java)
        }
        // 邮箱
        mBinding.emailView.afterTextChangedListener {
            initNextView()
        }
        // 密码
        mBinding.pwdView.afterTextChangedListener {
            initNextView()
        }
        mBinding.tvForgetPwd.setOnClickListener(this)
        mBinding.tvNext.setOnClickListener(this)
    }

    private fun initNextView() {
        mBinding.tvNext.isEnabled = mBinding.emailView.getInputText().isNotEmpty() && mBinding.pwdView.getInputText().isNotEmpty()
    }

    override fun onClick(view: View) {
        super.onClick(view)
        when (view.id) {
            R.id.tvNext -> {
                if (mPresenter.isFrom == 10) {
                    // fb绑定
                    thirdpartyBindApi(mBinding.emailView.getInputText(), mBinding.pwdView.getInputText())
                    return
                }
                mPresenter.pwdLoginApi(mBinding.emailView.getInputText(), mBinding.pwdView.getInputText())
            }

            R.id.tvForgetPwd -> {
                val bundle = Bundle()
                bundle.putBoolean("isShowEmail", false)
                bundle.putInt("isFrom", 1)
                openActivity(LoginPwdActivity::class.java, bundle)
            }
        }
    }

    private fun thirdpartyBindApi(email: String?, pwd: String?) {
        if (!RegexUtil.isEmail(email)) {
            // 请输入正确的邮箱
            ToastUtil.showToast(getString(R.string.please_enter_the_correct_mail))
            return
        }

        showNetDialog()
        val map = hashMapOf<String, Any?>()
        map["userEmail"] = email ?: ""
        map["userPassword"] = pwd ?: ""
        SpManager.getTelegramH5Data()?.let { bean ->
            map["thirdpartyType"] = "4"
            map["telegramId"] = bean.id.ifNull()
            map["first_name"] = bean.first_name.ifNull()
            map["last_name"] = bean.last_name.ifNull()
            map["auth_date"] = bean.auth_date.ifNull()
            map["hash"] = bean.hash.ifNull()
            map["username"] = bean.username.ifNull()
            map["photo_url"] = bean.photo_url.ifNull()
        }
        val paramMap = hashMapOf<String, Any?>()
        paramMap["data"] = AESUtil.encryptAES(map.json, AESUtil.PWD_AES_KEY)
        HttpUtils.getData(
            RetrofitHelper.getHttpService().thirdpartyBindApi(paramMap),
            object : BaseObserver<LoginBean>() {
                override fun onNext(data: LoginBean) {
                    loginInfo = data
                    mPresenter.bindEmailPhoneApi(email, pwd)
                }

                override fun onHandleSubscribe(d: Disposable?) {
                    rxManager.add(d)
                }

                override fun onError(e: Throwable?) {
                    super.onError(e)
                    hideNetDialog()
                }
            })
    }

    override fun dealLoginData(email: String?, userPassword: String?, data: LoginBean) {
        // 保存用户信息等操作
        val bundle = Bundle()
        when (data.resultCode) {
            // 一般用户登录成功 , IB用户登录成功
            "V10017", "V10016" -> {
                userPassword?.let { saveUserData(data, it) }

                val isBind2FA = data.data?.obj?.twoFactorUser == true
                val userid = data.data?.obj?.userId.ifNull()
                val isBinded = SpManager.getUser2faBindEd(userid, false)
                if (isBind2FA || isBinded) {
                    // 走原逻辑
                    bundle.putInt(Constants.IS_FROM, 1)
                    openActivity(AccountManagerActivity::class.java, bundle)
                } else {
                    // 未绑定 走绑定流程
                    TFABindActivity.open(this, TFAVerifyActivity.FROM_LOGIN)
                }
                finish()
            }

            else -> ToastUtil.showToast(data.msgInfo)
        }
    }

    private fun saveUserData(loginBean: LoginBean, userPassword: String) {
        val userTel = loginBean.data?.obj?.userTel
        val areaCode = loginBean.data?.obj?.code
        val countryCode = loginBean.data?.obj?.countryCode
        val email = loginBean.data?.obj?.email
        UserDataUtil.setUserTel(userTel)
        UserDataUtil.setCountryCode(countryCode)
        UserDataUtil.setAreaCode(areaCode)
        UserDataUtil.setUserId(loginBean.data?.obj?.userId)
        UserDataUtil.setUserType(if (loginBean.resultCode == "V10017") 1 else 0)
        UserDataUtil.setLoginToken(loginBean.data?.obj?.token)
        UserDataUtil.setXToken(loginBean.data?.obj?.xtoken)
        val fastCloseState = loginBean.data?.obj?.fastCloseState
        UserDataUtil.setFastCloseState(if (TextUtils.isEmpty(fastCloseState)) "2" else fastCloseState)
        val fastCloseCopyOrder = loginBean.data?.obj?.fastCloseCopyOrder // 快速停止跟单
        UserDataUtil.setFastStopCopyState(if (TextUtils.isEmpty(fastCloseCopyOrder)) "2" else fastCloseCopyOrder)
        val orderConfirmation = loginBean.data?.obj?.orderConfirmation
        UserDataUtil.setOrderConfirmState(if (TextUtils.isEmpty(orderConfirmation)) "2" else orderConfirmation)
        UserDataUtil.setEmail(email)
        UserDataUtil.setUserNickName(loginBean.data?.obj?.userNick)
        UserDataUtil.setUserPic(loginBean.data?.obj?.pic)
        UserDataUtil.setUserPassword(userPassword)

        LogEventUtil.mFirebaseAnalytics.setUserId(areaCode + userTel)

        val userEmailHistory = UserEmailHistory()
        userEmailHistory.email = email
        DbManager.getInstance().saveUserEmailHistory(userEmailHistory)
        SpManager.putUserTel(userTel.ifNull())
        SpManager.putCountryCode(countryCode.ifNull()) // 国家code
        SpManager.putCountryNum(areaCode.ifNull())     // 区号

        // 登录firebase
        FirebaseManager.userLogin()
    }

    override fun onDestroy() {
        KeyboardUtil.unregisterSoftInputChangedListener(this.window)
        super.onDestroy()
    }

}
