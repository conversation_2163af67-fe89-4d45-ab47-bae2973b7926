package cn.com.vau.page.user.openSameNameAccount

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.BaseBean
import cn.com.vau.data.account.*
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 */
class OpenSameNameAccountModel : OpenSameNameAccountContract.Model {
    override fun openSameAccount(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().applyTradeAccount(map), baseObserver)
        return baseObserver.disposable
    }

    override fun getPlatFormAccountTypeCurrency(map: HashMap<String, String>, baseObserver: BaseObserver<PlatFormAccountData>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().getPlatFormAccountTypeCurrency(map), baseObserver)
        return baseObserver.disposable
    }

    override fun checkNdbPromoApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<NDBStatusBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().newCreditIsParticipatingApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun exitNdbPromoApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<BaseBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().newCreditManualExitActivityApi(map), baseObserver)
        return baseObserver.disposable
    }

    override fun accountOpenAccountValidate(baseObserver: BaseObserver<OpenWalletBean>): Disposable {
        HttpUtils.loadData(RetrofitHelper.getHttpService().accountOpenAccountValidate(), baseObserver)
        return baseObserver.disposable
    }
}
