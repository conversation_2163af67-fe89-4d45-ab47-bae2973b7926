package cn.com.vau.page.user.openAccountThird

import android.text.TextUtils
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.greendao.dbUtils.UserDataUtil
import cn.com.vau.common.storage.SpManager
import cn.com.vau.data.account.*
import cn.com.vau.page.user.openAccountFirst.OpenAccountCacheContract
import cn.com.vau.page.user.openAccountForth.OpenAccountForthActivity
import cn.com.vau.util.ToastUtil
import cn.com.vau.util.tracking.SensorsDataUtil
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 */
class OpenAccountThirdPresenter : OpenAccountCacheContract.Presenter() {
    var isFrom = -1

    // 是否跳过下一步(true：跳过，fasle：不跳过)
    var skipNextStep: Boolean? = null

    // 平台 ( 多个平台显示平台选择,单个不现实 )
    var platFormDataList: ArrayList<PlatFormAccountData.Obj> = ArrayList()
    var platFormIndex = 0

    // 某平台下账户数据集合
    var accountTypeDataList: ArrayList<PlatFormAccountData.PlatFormAccountType> = ArrayList()
    var accountTypeIndex = 0

    // 某账户下货币数据集合
    var accountCurrencyDataList: ArrayList<PlatFormAccountData.Currency> = ArrayList()
    var accountCurrencyIndex = 0

    override fun getPlatFormAccountTypeCurrency() {

        val map = hashMapOf<String, String>()
        map["userId"] = UserDataUtil.userId()
        // 查询类型（1：live注册，2：开同名）
        map["type"] = "1"

        mView?.showNetDialog()
        mModel?.getPlatFormAccountTypeCurrency(map, object : BaseObserver<PlatFormAccountData>() {
            override fun onNext(data: PlatFormAccountData) {

                if (data.resultCode != "V00000") {
                    mView?.hideNetDialog()
                    ToastUtil.showToast(data.msgInfo)
                    return
                }
                platFormDataList.clear()
                platFormDataList.addAll(data.data?.obj ?: arrayListOf())
                platFormIndex = 0
                accountTypeIndex = 0
                accountCurrencyIndex = 0
                initAdapterData()

                // 获取缓存信息
                getRealInfo()

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    fun initAdapterData() {

        accountTypeDataList.clear()
        accountTypeDataList.addAll(
            platFormDataList.elementAtOrNull(platFormIndex)?.listPlatFormAccountType
                ?: ArrayList()
        )

        accountCurrencyDataList.clear()
        accountCurrencyDataList.addAll(
            accountTypeDataList.elementAtOrNull(accountTypeIndex)?.listCurrency
                ?: ArrayList()
        )

        mView?.refreshAdapter()
    }

    // 获取缓存信息
    override fun getRealInfo() {

        val params = hashMapOf<String, Any>()
        params["token"] = UserDataUtil.loginToken()
        params["step"] = "3"
        mModel?.getRealInfo(params, object : BaseObserver<RealAccountCacheBean>() {
            override fun onNext(data: RealAccountCacheBean) {

                mView?.hideNetDialog()

                if (data.resultCode != "V00000") {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                val cacheData = data.data?.obj
                skipNextStep = cacheData?.skipNextStep

                SpManager.putSuperviseNum(cacheData?.supervisionType ?: "-1")

                // 平台
                platFormIndex = platFormDataList.indexOfFirst {
                    it.platFormName == cacheData?.tradingPlatform
                }
                accountTypeIndex = accountTypeDataList.indexOfFirst {
                    it.accountTypeNum == cacheData?.accountType
                }
                // 货币
                accountCurrencyIndex = accountCurrencyDataList.indexOfFirst {
                    TextUtils.equals(it.currencyName, cacheData?.currency)
                }
                if (platFormIndex == -1) platFormIndex = 0
                if (accountTypeIndex == -1) accountTypeIndex = 0
                if (accountCurrencyIndex == -1) accountCurrencyIndex = 0

                mView?.showRealInfo(cacheData)

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })
    }

    override fun saveRealInfo() {

        mView?.showNetDialog()

        val accountTypeNum = accountTypeDataList.getOrNull(accountTypeIndex)?.accountTypeNum ?: ""
        val params = hashMapOf<String, Any>()
        params.put("token", UserDataUtil.loginToken())
        params.put("step", "3")
        params.put("accountType", accountTypeNum)
        params.put("currency", accountCurrencyDataList.getOrNull(accountCurrencyIndex)?.currencyName ?: "")
        params.put("tradingPlatform", platFormDataList.getOrNull(platFormIndex)?.platFormName ?: "") // 交易平台默认为mt4

        mModel?.saveRealInfo(params, object : BaseObserver<RealAccountCacheBean>() {
            override fun onNext(data: RealAccountCacheBean) {

                mView?.hideNetDialog()

                if (data.resultCode != "V00000") {
                    ToastUtil.showToast(data.msgInfo)
                    return
                }

                //绑定神策业务ID，场景5
                SensorsDataUtil.bindBusinessIdForMerge(data.data?.obj?.emailEventID)

                openActivity(OpenAccountForthActivity::class.java)
                mView?.refreshOpenGuide()

            }

            override fun onHandleSubscribe(d: Disposable?) {
                mRxManager.add(d)
            }

            override fun onError(e: Throwable?) {
                super.onError(e)
                mView?.hideNetDialog()
            }
        })

    }
}
