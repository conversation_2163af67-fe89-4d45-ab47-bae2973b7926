package cn.com.vau.page.user.question


import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.account.AuditQuestionData
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
interface AsicQuestionnaireContract {
    interface Model : BaseModel {
        fun getQustionList(map: HashMap<String, Any>, baseObserver: BaseObserver<AuditQuestionData>): Disposable
        fun submitAnswer(map: HashMap<String, Any>, baseObserver: BaseObserver<AuditQuestionData>): Disposable
    }

    interface View : BaseView {
        fun updateView()
        fun onSubmitResult(result: AuditQuestionData)
        fun showFailedDialog2(incorrectAnswerPrompt: String?, jumpLink: String?)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getQustionList()
        abstract fun submitAnswer(selectMap: MutableMap<Int, String>?)
    }

}
