package cn.com.vau.page.html

import android.content.*
import android.net.Uri
import android.text.TextUtils
import android.webkit.DownloadListener

internal class WebDownLoadListener(private val mContext: Context) : DownloadListener {

    override fun onDownloadStart(url: String, userAgent: String, contentDisposition: String, mimetype: String, contentLength: Long) {
        if (TextUtils.isEmpty(url)) return
        try {
            mContext.startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(url)))
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}