package cn.com.vau.page.user.accountManager

import cn.com.vau.common.base.mvp.BaseModel
import cn.com.vau.common.base.mvp.BasePresenter
import cn.com.vau.common.base.mvp.BaseView
import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.data.BaseBean
import cn.com.vau.data.DataObjBooleanBean
import cn.com.vau.data.DataObjStringBean
import cn.com.vau.data.account.AccountListFirstBean
import cn.com.vau.data.account.AccountListFirstObj
import cn.com.vau.data.account.AccountOpeningGuideBean
import cn.com.vau.data.account.AccountTradeBean
import cn.com.vau.data.account.AccountsEquityData
import cn.com.vau.data.account.AloneAccountInfoData
import cn.com.vau.data.account.DemoAccountBean
import cn.com.vau.data.account.DemoAccountListData
import cn.com.vau.data.account.MT4AccountTypeBean
import cn.com.vau.data.account.StAccMarginBean
import cn.com.vau.data.account.StAccountLoginBean
import cn.com.vau.data.init.TradeAccountLoginBean
import io.reactivex.disposables.Disposable
import okhttp3.RequestBody

/**
 * Created by Haipeng on 2017/10/12.
 */
interface AccountManagerContract {

    interface Model : BaseModel {
        fun getAccountFirst(
            map: HashMap<String, Any>,
            baseObserver: BaseObserver<AccountListFirstBean>
        ): Disposable

        fun modifyNickName(
            map: HashMap<String, String>,
            baseObserver: BaseObserver<BaseBean>
        ): Disposable

        fun modifyStNickName(
            map: HashMap<String, String>,
            baseObserver: BaseObserver<BaseBean>
        ): Disposable

        fun tradeAccountLogin(body: RequestBody, baseObserver: BaseObserver<TradeAccountLoginBean>): Disposable

        fun synDemo(map: HashMap<String, Any>, baseObserver: BaseObserver<BaseBean>): Disposable

        fun queryMT4AccountType(
            map: HashMap<String, String>,
            baseObserver: BaseObserver<MT4AccountTypeBean>
        ): Disposable

        fun accountOpeningGuide(
            map: HashMap<String, Any>,
            baseObserver: BaseObserver<AccountOpeningGuideBean>
        ): Disposable

        fun queryDemoAccountList(
            map: HashMap<String, Any>,
            baseObserver: BaseObserver<DemoAccountListData>
        ): Disposable

        fun queryDemoAccount(
            map: HashMap<String, Any>,
            baseObserver: BaseObserver<DemoAccountBean>
        ): Disposable

        fun stAccountLogin(
            requestBody: RequestBody,
            baseObserver: BaseObserver<StAccountLoginBean>
        ): Disposable

        fun stAccountAccMargin(
            stToken: String,
            baseObserver: BaseObserver<StAccMarginBean>
        ): Disposable

        fun queryAccountEquitList(
            token: String,
            baseObserver: BaseObserver<AccountsEquityData>
        ): Disposable

        fun queryAccountInfo(
            paramMap: HashMap<String, Any>,
            baseObserver: BaseObserver<AloneAccountInfoData>
        ): Disposable

        fun getCopyTradingDefaultImg(
            token: String,
            baseObserver: BaseObserver<DataObjStringBean>
        ): Disposable

        //升级账户类型
        fun accountUpgradeGroup(
            paramMap: HashMap<String, Any>,
            baseObserver: BaseObserver<DataObjBooleanBean>
        ): Disposable
    }

    interface View : BaseView {
        fun selectCommonAcount(data: AccountTradeBean)
        fun selectRakebackAcount(position: Int)
        fun selectSocialTradingAccount(data: AccountTradeBean)
        fun queryAccountInfo(adapterType: Int, position: Int)
        fun modifyNickNameFinish(position: Int)
        fun showModifyNameDialog(position: Int, isModifyStAccount: Boolean)
        fun refreshAccountState(position: Int)
        fun showDemoDialog()
        fun showQuestionDialog()
        fun freshDemoAdapter(position: Int)
        fun freshLiveAdapter(position: Int)
        fun freshCopyTradingAdapter(position: Int)
        fun setAdapterData()
        fun freshAllAdapter()
        fun setDemoAccountNum(number: Int)
        fun popupWindow()
        fun goBind()
        fun refreshBottomButton()
        fun showGuidangDialog()
        fun initRetryView(isShowRetry: Boolean)
        fun hideBackBtn()
        /**
         * 会跳转一个新的MainActivity，旧的MainActivity会被销毁，销毁前会收到SWITCH_ACCOUNT通知)
         */
        fun reStartApp()
        fun syncLiveCard()
        fun refreshCopyTradingImg()
        fun accountUpgradeIsSuccess(isSuccess: Boolean, clickPos: Int)
    }

    abstract class Presenter : BasePresenter<Model, View>() {
        abstract fun getAccountFirst()
        abstract fun queryAccountEquitList()
        abstract fun queryDemoAccountList(openPop: Boolean)
        abstract fun queryAccountInfo(adapterType: Int, position: Int, bean: AccountTradeBean?)
        abstract fun synDemo(data: AccountTradeBean)
        abstract fun selectCommonAccount(data: AccountTradeBean)
        abstract fun selectSocialTradingAccount(data: AccountTradeBean)
        abstract fun modifyNickName(data: AccountTradeBean, newNick: String?, adapterType: Int, position: Int)
        abstract fun modifyStNickName(data: AccountTradeBean, newNick: String?, position: Int)
        abstract fun queryMT4AccountType(isSelectedCopyTrading: Boolean = false)
        abstract fun queryStAccountType(isSelectedCopyTrading: Boolean = false)
        abstract fun dealFirstAccountData(data: AccountListFirstObj?)
        abstract fun accountOpeningGuide()
        abstract fun stAccountLogin(data: AccountTradeBean)
        abstract fun getStAccountInfo(data: AccountTradeBean, stToken: String)
        abstract fun getCopyTradingDefaultImg()
        abstract fun accountUpgradeGroup(clickPos: Int, mt4Account: String?) //升级账户类型
    }

}
