package cn.com.vau.page.html

import android.text.TextUtils
import cn.com.vau.util.tracking.SensorsConstant
import cn.com.vau.util.tracking.SensorsDataUtil
import cn.com.vau.util.widget.webview.offline.type.ResourceTypeUtil
import cn.com.vau.util.widget.webview.utils.WebViewLogUtil
import org.json.JSONObject

/**
 * author：lvy
 * date：2025/01/13
 * desc：神策h5流程埋点 辅助类
 */
class HtmlSensorsHelper {

    private var mTimestamp = 0L // 打开h5需要赋值一个新的时间戳，目的是标记是同一个用户的同一套流程
    private var mTargetUrl = "" // 打开h5页面时需要赋值
    private var mCurStep = SensorsStep.OPEN_PAGE

    private var loadStartTime = 0L // 打开页面时的时间戳

    private var isFinishUpload = false //用来标记是否上报过成功或者失败

    private var isInit = false

    fun init(targetUrl: String) {
        if (isInit) {
            return // 限制只有刚打开页面时才进行一次初始化
        }
        mTimestamp = System.currentTimeMillis()
        mTargetUrl = targetUrl

        isFinishUpload = false
        isInit = true
    }

    fun sensorsTrackH5Process(step: SensorsStep, jsValue: String? = "", url: String? = "", isNewHtml: Boolean = false) {
        if (mTimestamp == 0L || mTargetUrl.isBlank() || url.isNullOrEmpty()) {
            return
        }
        //如果上报过成功或者失败的埋点，就不再上报
        if ((step == SensorsStep.LOAD_FAIL || step == SensorsStep.LOAD_SUCCESS)) {
            if (isFinishUpload) {
                return
            }
            WebViewLogUtil.e("targetUrl:$mTargetUrl")
            WebViewLogUtil.e("      Url:$url")
            isFinishUpload = true
        }
        mCurStep = step
        SensorsDataUtil.track(SensorsConstant.Opt.H5_PROCESS, JSONObject().apply {
            put(SensorsConstant.Key.TIMESTAMP, mTimestamp) // 时间戳
            put(SensorsConstant.Key.TARGET_URL, mTargetUrl) // url
            put(SensorsConstant.Key.STEP, step.value) // 第几步 见SensorsStep
            put(SensorsConstant.Key.JS_VALUE, jsValue) // js交互的json值，只有step为js时才有值，其他情况传空字符串
            // 加载成功时，需要上报页面的加载时间
            if (step == SensorsStep.OPEN_PAGE) {
                loadStartTime = System.currentTimeMillis()
            }
            if (step == SensorsStep.LOAD_SUCCESS && loadStartTime != 0L
                && isNewHtml
                && !ResourceTypeUtil.isPdfUrl(url)
                //重定向的时候，过滤掉时间
                && TextUtils.equals(url, mTargetUrl)
            ) {
                val endTime = System.currentTimeMillis()
                put(SensorsConstant.Key.LOAD_TIME, endTime - loadStartTime)
                put(SensorsConstant.Key.IS_FIRST, NewHtmlActivity.isFirst)
            }
        })
        if (isNewHtml) {
            NewHtmlActivity.isFirst = false
        }
    }

    enum class SensorsStep(var value: String) {
        OPEN_PAGE("open_page"), // 打开页面
        LOAD_SUCCESS("load_success"), // 加载成功
        LOAD_FAIL("load_fail"), // 加载失败
        JS_INTERFACE("js"), // js交互
        CLOSE_PAGE("close_page") // 关闭页面
    }
}