package cn.com.vau.page.common.selectArea

import android.annotation.SuppressLint
import android.content.Context
import android.view.*
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import cn.com.vau.R
import cn.com.vau.data.account.SelectCountryNumberObj
import cn.com.vau.util.*

/**
 * 首字母快速查询
 * Created by wj
 */
class LetterNavAdapter(
    val context: Context,
    val listData: List<SelectCountryNumberObj>,
    val listener: OnLetterClickListener
) : RecyclerView.Adapter<LetterNavAdapter.ViewHolder>() {

    var selectedName = ""

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val tvNavLetterRoot = holder.itemView.findViewById<TextView>(R.id.tvNavLetterRoot)
        val layoutParams = tvNavLetterRoot.layoutParams
        layoutParams.height = (screenHeight - 100.dp2px() - context.getStatusHeight()) / 27
        tvNavLetterRoot.layoutParams = layoutParams
        if (position == 0) {
            tvNavLetterRoot.text = ""
//            tvNavLetterRoot.setCompoundDrawablesWithIntrinsicBounds(null, context.resources.getDrawable(R.drawable.hot), null, null)
        } else {
            tvNavLetterRoot.text =
                " ${listData.elementAtOrNull(position)?.lettername ?: ""}"
            tvNavLetterRoot.setCompoundDrawablesWithIntrinsicBounds(
                null,
                null,
                null,
                null
            )
        }
        if (tvNavLetterRoot.text == " ${selectedName}") {
//            tvNavLetterRoot.setTextColor(context.resources.getColor(R.color.main_red))
            tvNavLetterRoot.setTextColor(context.resources.getColor(R.color.ce35728))
        } else {
//            tvNavLetterRoot.setTextColor(context.resources.getColor(R.color.gray_646C70)) colorBottomTextMain
            tvNavLetterRoot.setTextColor(
                AttrResourceUtil.getColor(context, R.attr.color_ca61e1e1e_c99ffffff)
            )
        }
        tvNavLetterRoot.setOnClickListener {
            listener.onClick(position)
        }
    }

    fun showSelectedName(name: String) {
//        selectedName = name
//        notifyDataSetChanged()
        if (name != selectedName) {
            selectedName = name
            listData.forEachIndexed each@{ index, selectCountryNumberObj ->
                if (selectCountryNumberObj.lettername == selectedName) {
                    notifyItemRangeChanged(index - 1, 3)
                    return@each
                }
            }
//            notifyDataSetChanged();
        }
    }

    fun showSelectedName(position: Int) {
        if (listData.getOrNull(position)?.lettername != null && selectedName != listData.getOrNull(position)?.lettername) {
            selectedName = listData.getOrNull(position)?.lettername ?: selectedName
            notifyDataSetChanged()
        }
    }

    override fun getItemCount(): Int = listData.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder =
        ViewHolder(LayoutInflater.from(context).inflate(R.layout.item_nav_letter, parent, false))

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view)
    interface OnLetterClickListener {
        fun onClick(position: Int)
    }
}