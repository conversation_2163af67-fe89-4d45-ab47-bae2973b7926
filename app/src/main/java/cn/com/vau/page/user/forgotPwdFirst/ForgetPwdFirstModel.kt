package cn.com.vau.page.user.forgotPwdFirst

import cn.com.vau.common.base.rx.BaseObserver
import cn.com.vau.common.http.HttpUtils
import cn.com.vau.common.http.utils.RetrofitHelper
import cn.com.vau.data.account.ForgetPwdVerificationCodeBean
import io.reactivex.disposables.Disposable

/**
 * Created by Haipeng on 2017/10/12.
 * 1
 */
class ForgetPwdFirstModel : ForgetPwdFirstContract.Model {
    override fun getVerificationCodeApi(map: HashMap<String, Any?>, baseObserver: BaseObserver<ForgetPwdVerificationCodeBean>): Disposable {
        HttpUtils.loadData(
                RetrofitHelper.getHttpService().forgetpwdGetVerificationCodeApi(map),
                baseObserver
        )
        return baseObserver.disposable
    }
}
