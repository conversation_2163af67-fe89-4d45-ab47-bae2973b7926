package cn.com.vau.page.coupon

import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import cn.com.vau.R
import cn.com.vau.common.base.DataEvent
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.data.depositcoupon.CouponOutDateObj
import cn.com.vau.databinding.ActivityCouponsManagerBinding
import cn.com.vau.util.*
import cn.com.vau.util.widget.dialog.CenterActionDialog
import cn.com.vau.util.widget.dialog.CenterInputDialog
import cn.com.vau.util.widget.dialog.base.IDialog
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

/**
 * @description:
 * @author: GG
 * @createDate: 2025 4月 02 14:52
 * @updateUser:
 * @updateDate: 2025 4月 02 14:52
 */
class CouponManagerActivity : BaseMvvmActivity<ActivityCouponsManagerBinding, CouponManagerViewModel>() {

    private var dialog: IDialog<*>? = null

    override fun initView() {
        mBinding.mHeaderBar.setEndTextClickListener {
            showExchangeCouponDialog()
            mViewModel.sensorsTrack()
        }

        val titleList = listOf(
            getString(R.string.active_coupon_activity),
            getString(R.string.used),
            getString(R.string.expired)
        )
        val fragmentList = listOf(
            CouponFragment.newInstance(CouponFragment.TYPE_ACTIVE),
            CouponFragment.newInstance(CouponFragment.TYPE_USED),
            CouponFragment.newInstance(CouponFragment.TYPE_EXPIRED)
        )

        mBinding.mViewPager2.init(fragmentList.toMutableList(), titleList.toMutableList(), supportFragmentManager, this)
        mBinding.mTabLayout.setVp(mBinding.mViewPager2, titleList, TabType.LINE_INDICATOR)

    }

    override fun initData() {
        super.initData()
        mViewModel.inviteCoupon()
        mViewModel.queryStAccountType()
    }

    override fun createObserver() {
        super.createObserver()
        lifecycleScope.launch {
            mViewModel.eventFlow.flowWithLifecycle(lifecycle).collectLatest {
                if (it !is DataEvent) return@collectLatest
                when (it.tag) {
                    CouponManagerViewModel.EVENT_INVITE_COUPON -> {
                        if (it.data is CouponOutDateObj) {
                            showCouponOutDateDialog(it.data)
                        }
                    }

                    CouponManagerViewModel.EVENT_USERCOUPON_EXCHANGE_SUCCESS -> {
                        // 关闭兑换码弹窗
                        dialog?.dismissDialog()
                        ToastUtil.showToast(getString(R.string.redeemed_successfully_account_now))
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        mViewModel.sendEvent(DataEvent(CouponManagerViewModel.EVENT_RESUME_REFRESH))
    }

    private fun showExchangeCouponDialog() {
        CenterInputDialog.Builder(this)
            .setContent(getString(R.string.please_enter_voucher_code)) //设置内容
            .setHint(getString(R.string.enter_code))
            .setStartText(getString(R.string.cancel))
            .setEndText(getString(R.string.confirm))
            .setOnEndListener { dialog, text ->
                if (text.isNullOrEmpty()) {
                    ToastUtil.showToast(getString(R.string.enter_your_promotion_code))
                    return@setOnEndListener
                }
                this.dialog = dialog
                mViewModel.usercouponExchange(text)
            }
            .build()
            .showDialog()
    }

    private fun showCouponOutDateDialog(data: CouponOutDateObj) {
        val type = data.type ?: 1
        val msg = if (type == 1)
            getString(R.string.x_invited_by_you_the_and_removed, data.name)
        else
            getString(R.string.you_are_not_the_removed)

        CenterActionDialog.Builder(this)
            .setContent(msg) //设置内容
            .setSingleButton(true) //展示一个按钮，默认两个按钮
            .setSingleButtonText(getString(R.string.confirm)) //设置单个按钮文本
            .setOnSingleButtonListener { mViewModel.inviteCouponAgree(type) }
            .build()
            .showDialog()
    }

}