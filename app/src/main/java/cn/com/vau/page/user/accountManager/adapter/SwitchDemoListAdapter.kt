package cn.com.vau.page.user.accountManager.adapter

import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import cn.com.vau.R
import cn.com.vau.data.account.DemoAccountDetail
import cn.com.vau.util.ifNull
import cn.com.vau.util.numCurrencyFormat
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder

class SwitchDemoListAdapter: BaseQuickAdapter<DemoAccountDetail, BaseViewHolder>(R.layout.item_switch_demo_list) {

    init {
        addChildClickViewIds(R.id.llExt)
    }

    override fun convert(holder: BaseViewHolder, item: DemoAccountDetail) {
        holder.setText(R.id.tvAccountNo, item.mt4AccountId.ifNull())
        holder.setText(R.id.tvAccountAmount, item.equity?.numCurrencyFormat(item.currency.ifNull()))
        holder.setText(R.id.tvAccountAmountUnit, item.currency.ifNull())
        holder.setText(R.id.tvTypeNum, item.accountTypeName.ifNull())
        holder.setText(R.id.tvLeverageNum, if (item.leverage.isNullOrBlank()) "" else "${item.leverage}:1")

        val clAccountInfo = holder.getView<ConstraintLayout>(R.id.clAccountInfo)
        val ivExtent = holder.getView<ImageView>(R.id.ivExtent)
        if (item.showAccountInfo) {
            if (clAccountInfo.visibility == View.GONE) {
                clAccountInfo.visibility = View.VISIBLE
                holder.setImageResource(R.id.ivExtent, R.drawable.draw_bitmap2_arrow_top10x10_c1e1e1e_cebffffff)
            }
        } else {
            if (clAccountInfo.visibility == View.VISIBLE) {
                clAccountInfo.visibility = View.GONE
                holder.setImageResource(R.id.ivExtent, R.drawable.draw_bitmap2_arrow_bottom10x10_c1e1e1e_cebffffff)
            }
        }
    }
}