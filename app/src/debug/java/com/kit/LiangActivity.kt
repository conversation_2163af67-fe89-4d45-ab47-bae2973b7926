package com.kit

import android.graphics.Color
import cn.com.vau.common.mvvm.base.BaseMvvmActivity
import cn.com.vau.databinding.ActivityLiangBinding

class LiangActivity : BaseMvvmActivity<ActivityLiangBinding, ZLViewModel>() {

    override fun initView() {

        mBinding.loadingView1.setOnClickListener {
            mBinding.loadingView1.postDelayed({
                mBinding.loadingView1.stopLoading()
//                mBinding.loadingView1.setButtonTextColor(Color.RED)
//                mBinding.loadingView1.setButtonText("1111")
//                mBinding.loadingView1.setButtonTextSize(20)
//                mBinding.loadingView1.setStartIconRes(cn.com.vau.R.drawable.img_open_step_id_d)
//                mBinding.loadingView1.setStartIconSize(500,500)
//                mBinding.loadingView1.setEndIconRes(cn.com.vau.R.drawable.img_open_step_id_d)
//                mBinding.loadingView1.setBackgroundColor(Color.BLACK)
//                mBinding.loadingView1.setButtonAlpha(0.9f)
//                mBinding.loadingView1.setButtonCorner(10)
//                mBinding.loadingView1.setButtonFontFamilyRes(cn.com.vau.R.font.gilroy_semi_bold)
            },2000)
        }

        mBinding.loadingView2.setOnClickListener {
            mBinding.loadingView2.postDelayed({
                mBinding.loadingView2.stopLoading()
            },2000)
        }

        mBinding.loadingView3.setOnClickListener {
            mBinding.loadingView3.postDelayed({
                mBinding.loadingView3.stopLoading()
            },2000)
        }
        mBinding.loadingView4.setOnClickListener {
            mBinding.loadingView4.postDelayed({
                mBinding.loadingView4.stopLoading()
            },2000)
        }
        mBinding.loadingView5.setOnClickListener {
            mBinding.loadingView5.postDelayed({
                mBinding.loadingView5.stopLoading()
            },2000)
        }
        mBinding.loadingView6.setOnClickListener {
            mBinding.loadingView6.postDelayed({
                mBinding.loadingView6.stopLoading()
            },2000)
        }
        mBinding.loadingView7.setOnClickListener {
            mBinding.loadingView7.postDelayed({
                mBinding.loadingView7.stopLoading()
            },2000)
        }
    }

}




