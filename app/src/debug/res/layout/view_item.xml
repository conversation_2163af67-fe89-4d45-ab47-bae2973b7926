<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="15dip"
    android:paddingBottom="15dip"
    tools:divider="#FF0000"
    tools:showDividers="beginning">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#FF5E5E5E"
        android:textSize="14dip"
        tools:text="APP信息" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/tvContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:textColor="#FF141414"
        android:textSize="14dip"
        tools:text="APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息APP信息" />

    <cn.com.vau.util.widget.DashLineTextView
        android:id="@+id/tvCharges2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="#f5f5f5"
        android:paddingHorizontal="20dp"
        android:paddingBottom="20dp"
        android:text="ChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesChargesCharges"
        android:textDirection="locale"
        app:dlDashEnabled="true"
        app:dlDashColor="#ff0000" />

    <!--    <EditText-->
    <!--        android:id="@+id/etInput"-->
    <!--        android:layout_width="match_parent"-->
    <!--        android:layout_height="wrap_content"-->
    <!--        android:layout_marginTop="8dp"-->
    <!--        android:background="@null"-->
    <!--        android:hint="输入手机号尝试解锁"-->
    <!--        android:singleLine="true"-->
    <!--        android:textColor="#292929"-->
    <!--        android:textColorHint="#CCCCCC"-->
    <!--        android:textSize="16dp"-->
    <!--        tools:text="" />-->

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btnAction"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAllCaps="false"
        tools:text="按钮" />
</androidx.appcompat.widget.LinearLayoutCompat>
