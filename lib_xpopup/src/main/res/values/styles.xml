<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="_XPopup_TransparentDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowBackground">@null</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowTranslucentStatus">true</item>
        <item name="android:colorBackground">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@null</item>
    </style>
</resources>