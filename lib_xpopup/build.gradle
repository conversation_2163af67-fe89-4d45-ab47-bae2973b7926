apply plugin: 'com.android.library'

android {
    compileSdkVersion 34
    namespace 'com.lxj.xpopup'
    defaultConfig {
        minSdkVersion 23
        targetSdkVersion 34
        versionCode 1
        versionName "1.0"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation 'androidx.appcompat:appcompat:1.3.1'
    implementation 'com.google.android.material:material:1.4.0'
    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'com.github.li-xiaojun:EasyAdapter:1.2.8'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    implementation 'com.davemorrissey.labs:subsampling-scale-image-view-androidx:3.10.0'
    annotationProcessor "androidx.lifecycle:lifecycle-compiler:2.3.1"
}
